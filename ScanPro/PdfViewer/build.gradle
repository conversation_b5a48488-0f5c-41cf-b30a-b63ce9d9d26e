buildscript {
    repositories {
        mavenCentral()
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
//        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.7.3'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
    }
}

apply plugin: 'com.android.library'

ext {
    bintrayRepo = 'maven'
    bintrayName = 'android-pdf-viewer'

    publishedGroupId = 'com.github.barteksc'
    libraryName = 'AndroidPdfViewer'
    artifact = 'android-pdf-viewer'

    libraryDescription = 'Android view for displaying PDFs rendered with PdfiumAndroid'

    siteUrl = 'https://github.com/barteksc/AndroidPdfViewer'
    gitUrl = 'https://github.com/barteksc/AndroidPdfViewer.git'

    libraryVersion = '3.0.0-beta.5'

    developerId = 'barteksc'
    developerName = '<PERSON><PERSON><PERSON>'
    developerEmail = '<EMAIL>'

    licenseName = 'The Apache Software License, Version 2.0'
    licenseUrl = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
    allLicenses = ["Apache-2.0"]
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion
    namespace 'com.czur.scanpro'
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
    }
    buildTypes {
        release {

        }
        releaseTest {

        }
        debug {


        }
    }
}

dependencies {
    implementation project(':PdfiumAndroid-master')
    implementation rootProject.ext.deps.supportV4
}
