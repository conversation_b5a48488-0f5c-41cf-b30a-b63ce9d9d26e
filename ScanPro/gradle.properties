# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m
android.injected.testOnly = false
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true
# android.useDeprecatedNdk=true  # ?????????Android 16???

MobSDK.spEdition=FP

MIN_SDK_VERSION=26
TARGET_SDK_VERSION=36

VERSION_NAME=5.5.3
VERSION_CODE=53
COMPILE_SDK_VERSION=36

android.useAndroidX=true
android.enableJetifier=true
android.defaults.buildfeatures.buildconfig=true