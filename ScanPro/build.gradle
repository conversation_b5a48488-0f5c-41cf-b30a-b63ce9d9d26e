// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        mavenCentral()
        google()
        jcenter()
        maven { url 'https://oss.jfrog.org/artifactory/oss-snapshot-local' }
        maven { url 'https://mvn.mob.com/android' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
        classpath 'io.realm:realm-gradle-plugin:10.18.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'com.mob.sdk:MobSDK:2018.0319.1724'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files

    }
}

allprojects {
    repositories {
        google()
        mavenCentral ()
        maven { url 'https://jitpack.io' }
        jcenter()
        // 支付宝 SDK AAR 包所需的配置
        flatDir { dirs 'libs' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

project.ext {

    android = [

            compileSdkVersion: 36,
            buildToolsVersion: '36.0.0',
            minSdkVersion    : 26,
            targetSdkVersion : 36,

            versionName      : '2.5.1',
            versionCode      : 99
    ]

    deps = [
            // ------------- Android -------------
            supportV7: 'androidx.appcompat:appcompat:1.7.0',
            supportV4: 'androidx.legacy:legacy-support-v4:1.0.0',
            design   : 'com.google.android.material:material:1.0.0',
    ]
}
