// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()  // 优先使用Google仓库
        mavenCentral()
        // jcenter()  // 已弃用，移除以提高Android 16兼容性
        maven { url 'https://oss.jfrog.org/artifactory/oss-snapshot-local' }
        maven { url 'https://mvn.mob.com/android' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        // maven { url 'https://maven.aliyun.com/repository/jcenter' }  // 移除jcenter镜像
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
        classpath 'io.realm:realm-gradle-plugin:10.18.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'com.mob.sdk:MobSDK:2018.0319.1724'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files

    }
}

allprojects {
    repositories {
        google()  // 优先使用Google仓库
        mavenCentral()
        maven { url 'https://jitpack.io' }
        // jcenter()  // 已弃用，移除以提高Android 16兼容性
        // 支付宝 SDK AAR 包所需的配置
        flatDir { dirs 'libs' }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        // maven { url 'https://maven.aliyun.com/repository/jcenter' }  // 移除jcenter镜像
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

project.ext {

    android = [

            compileSdkVersion: 36,
            buildToolsVersion: '36.0.0',
            minSdkVersion    : 26,
            targetSdkVersion : 36,

            versionName      : '2.5.1',
            versionCode      : 99
    ]

    deps = [
            // ------------- Android -------------
            supportV7: 'androidx.appcompat:appcompat:1.7.0',
            supportV4: 'androidx.legacy:legacy-support-v4:1.0.0',
            design   : 'com.google.android.material:material:1.12.0',  // 升级到最新版本
    ]
}
