apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'realm-android'
apply plugin: 'com.mob.sdk'


android {
//    dexOptions { javaMaxHeapSize "8g" }
    dexOptions {
        //使用增量模式构建
        incremental true
        //最大堆内存
        javaMaxHeapSize "16g"  //注意内存是自己电脑内存大小配置
        //是否支持大工程模式
        jumboMode = true
        //预编译
        preDexLibraries = true
        //线程数
        threadCount = 8
    }

    buildFeatures {
        viewBinding true
    }

    configurations.all {
        resolutionStrategy.force 'com.google.code.findbugs:jsr305:2.0.1'
    }
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion
    useLibrary 'org.apache.http.legacy'
    namespace 'com.czur.scanpro'
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        renderscriptTargetApi 18
        renderscriptSupportModeEnabled true
        applicationId "com.czur.scanpro"
        versionCode rootProject.ext.android.versionCode
        versionName rootProject.ext.android.versionName
        multiDexEnabled true
        flavorDimensions "default"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }

    }

    realm {
        kotlinExtensionsEnabled = true
    }
    // enable @ParametersAreNonnullByDefault annotation. See https://blog.jetbrains.com/kotlin/2017/09/kotlin-1-1-50-is-out/
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).all {
        kotlinOptions {
            freeCompilerArgs = ["-Xjsr305=strict"]
            jvmTarget = '17'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    signingConfigs {
        debug {
            keyAlias 'scan_pro'
            keyPassword 'changer'
            storeFile file('keystore/czur.jks')
            storePassword 'changer'
        }
        release {
            keyAlias 'scan_pro'
            keyPassword 'changer'
            storeFile file('keystore/czur.jks')
            storePassword 'changer'
        }
    }

    buildTypes {
        release {
            debuggable false
//            resValue("string", "app_name", "极简扫描")
            resValue("string", "price_url", "https://resource.czur.cc/static/saomiao/saomiaoPrice.v2.json")
            buildConfigField("com.czur.scanpro.config.PhaseEnum", "PHASE", "com.czur.scanpro.config.PhaseEnum.RELEASE")
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //是否清理无用资源
            shrinkResources true
            //是否启用zipAlign压缩
            zipAlignEnabled true
            signingConfig signingConfigs.release
        }
        releaseTest {
            debuggable false
//            resValue("string", "app_name", "极简扫描")
            resValue("string", "price_url", "https://resource.czur.cc/static/saomiao/saomiaoPrice.v2.json")
            buildConfigField("com.czur.scanpro.config.PhaseEnum", "PHASE", "com.czur.scanpro.config.PhaseEnum.RELEASE_TEST")
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //是否清理无用资源
            shrinkResources true
            //是否启用zipAlign压缩
            zipAlignEnabled true
            signingConfig signingConfigs.release
        }
        debug {
            debuggable true
            manifestPlaceholders = ["umeng_app_key": "5b46cdbf8f4a9d754400010a"]
//            resValue("string", "app_name", "极简扫描")
            resValue("string", "price_url", "https://resource.czur.cc/static/saomiao/saomiaoPriceTest.v2.json")
            buildConfigField("com.czur.scanpro.config.PhaseEnum", "PHASE", "com.czur.scanpro.config.PhaseEnum.BETA")
            //true：启用混淆,false:不启用
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'



            //TODO 正式环境需要解开一下限制
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            shrinkResources true
            zipAlignEnabled true
            signingConfig signingConfigs.debug
        }
    }
    applicationVariants.all {
        variant ->
            variant.outputs.all { output ->
                def newName
                newName = "${applicationId}-" + variant.buildType.name  + "-${defaultConfig.versionName}" + ".apk"
                outputFileName = new File(newName)
                println "outputPath:" + variant.getPackageApplication().outputDirectory
            }
            variant.assemble.doLast {
                println "outputPath:" + variant.getPackageApplication().outputDirectory + "/output.json"
                delete "${variant.getPackageApplication().outputDirectory}/output.json"
            }
    }

    productFlavors {
        czur {
            resValue("string", "app_name", "极简扫描")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "czur"]
        }
        huawei {
            resValue("string", "app_name", "成者极简扫描")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "huawei"]
        }
        vivo {
            resValue("string", "app_name", "极简扫描")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "vivo"]
        }
        xiaomi {
            resValue("string", "app_name", "极简扫描")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "xiaomi"]
        }
        oppo {
            resValue("string", "app_name", "极简扫描")

            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "oppo"]
        }
        qihoo {
            resValue("string", "app_name", "极简扫描")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "qihoo"]
        }
        yingyongbao {
            resValue("string", "app_name", "极简扫描")
            manifestPlaceholders = [UMENG_CHANNEL_VALUE: "yingyongbao"]
        }
    }
}
MobSDK {
    appKey "b5623a0e08b5"
    appSecret "dca1285a9acf8143fe8fc87fa7df179b"
    ShareSDK {
        gui true
        //平台配置信息
        devInfo {
            SinaWeibo {
                appKey "3976849238"
                appSecret "5d00dfea24394e08e4ac8e48f43f3baf"
                callbackUri "http://www.weibo.com"
                shareByAppClient true
                enable true
            }
            Wechat {
                appId "wx1e22b31e63df0112"
                appSecret "fb45d98f5b22e7fe38275f4beae42243"
                shareByAppClient true
                enable true
            }
            WechatMoments {
                appId "wx1e22b31e63df0112"
                appSecret "fb45d98f5b22e7fe38275f4beae42243"
                shareByAppClient true
                enable true
            }
            QQ {
                appId "1107159243"
                appKey "zeoWjRdvN8CCgZ03"
                shareByAppClient true
                enable true
            }

            QZone {
                appId "1105466927"
                appKey "bUttf3gjJXP9q3lz"
                shareByAppClient true
                enable true
            }

        }
    }
}

kotlin {
    jvmToolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "com.google.android.material:material:1.0.0"
    implementation 'commons-codec:commons-codec:1.11'
    implementation "androidx.appcompat:appcompat:1.7.0"
    implementation "androidx.legacy:legacy-support-v4:1.0.0"
    implementation 'com.github.barteksc:android-pdf-viewer:3.2.0-beta.1'
    implementation 'com.github.vicpinm:krealmextensions:2.5.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    //OkHttp3
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'

    //Gson
    implementation 'com.google.code.gson:gson:2.10'

    //EventBus
    implementation 'org.greenrobot:eventbus:3.3.1'

    //工具类
//    implementation 'com.blankj:utilcode:1.29.0'
    implementation 'com.blankj:utilcodex:1.31.1'

    //Weak Handler
    implementation 'com.badoo.mobile:android-weak-handler:1.1'

    //Fresco
    implementation 'com.facebook.fresco:fresco:2.6.0'

    //ios SwitchButton
    implementation 'com.github.zcweng:switch-button:0.0.3@aar'

    //生成PDF
    implementation 'com.itextpdf:itextg:5.5.10'

    //aliyun OSS
    implementation 'com.aliyun.dpa:oss-android-sdk:2.9.19'

    //二维码扫描
    implementation 'cn.bingoogolapple:bga-qrcode-zxing:1.3.4'

    //下拉刷新
    implementation 'com.baoyz.pullrefreshlayout:library:1.2.0'

    //gif
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.23'


    //通过标签直接生成shape
    implementation 'com.noober.background:core:1.6.0'
//    implementation 'com.ms-square:etsyblur:0.2.1'
    implementation 'de.hdodenhof:circleimageview:3.0.0'

    // 支付宝支付
//    implementation(name: 'alipaySdk-15-6-0-20190226104053', ext: 'aar')

    // 微信支付
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0'
    implementation 'org.tensorflow:tensorflow-android:1.7.0'

    //bugly
    implementation 'com.tencent.bugly:crashreport:4.0.4'
//    implementation 'com.tencent.bugly:nativecrashreport:latest.release'

    //umeng
//    implementation 'com.umeng.umsdk:analytics:7.5.4'
//    implementation 'com.umeng.umsdk:common:1.5.4'
//    implementation  'com.umeng.umsdk:common:9.4.7'// 必选
//    implementation  'com.umeng.umsdk:asms:1.4.1'// 必选
    implementation  'com.umeng.umsdk:common:9.6.4'// 必选
    implementation  'com.umeng.umsdk:asms:1.8.0'// 必选
    implementation project(path: ':JniBitmapOperationsLibrary')
    //大图原图浏览
//    implementation 'com.github.CarGuo:FrescoUtils:v1.0.4'
    implementation 'com.davemorrissey.labs:subsampling-scale-image-view:3.10.0'
    //刷新和越界回弹
    implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.2'
    // 解除android p反射限制
//    implementation 'me.weishu:free_reflection:2.2.0'
//    implementation 'org.lsposed.hiddenapibypass:hiddenapibypass:2.0'

    //居中滚动的tab
    implementation 'com.github.SherlockShi:CenterSelectionTabLayout:1.0.5'
    implementation project(path: ':doodle')

    implementation 'com.nshmura:snappysmoothscroller:1.0.0'
    implementation 'com.truizlop.sectionedrecyclerview:library:1.2.0'

    // android13 notification
    implementation 'androidx.work:work-runtime-ktx:2.10.0'

    implementation 'com.github.bumptech.glide:glide:4.16.0'

    def lifecycle_version = "2.6.1"
    //协程
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"//viewModelScope
// LiveData
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    // Lifecycles only (without ViewModel or LiveData)
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    // helpers for implementing LifecycleOwner in a Service
    implementation "androidx.lifecycle:lifecycle-service:$lifecycle_version"
}
