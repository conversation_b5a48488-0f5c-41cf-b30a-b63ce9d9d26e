package com.czur.scanpro.ui.account

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityThirdBindBinding
import com.czur.scanpro.databinding.ActivityThirdRegisterBinding
import com.czur.scanpro.entity.model.ChannelModel
import com.czur.scanpro.entity.model.RegisterModel
import com.czur.scanpro.entity.realm.*
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.LoginEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.file.FilePreviewActivity
import com.czur.scanpro.ui.home.FileActivity
import com.czur.scanpro.ui.user.UserActivity
import com.czur.scanpro.utils.MD5Utils
import com.czur.scanpro.utils.validator.Validator
import com.facebook.drawee.backends.pipeline.Fresco
import com.google.gson.Gson
import com.mob.tools.utils.FileUtils
import com.noober.background.drawable.DrawableCreator
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import java.io.File

class ThirdPartyRegisterActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityThirdRegisterBinding by lazy{
        ActivityThirdRegisterBinding.inflate(layoutInflater)
    }

    private var hasFirstPassword = false
    private var httpManager: HttpManager? = null
    private var account: String? = null
    private var code: String? = null
    private var userPreferences: UserPreferences? = null
    private var userId: String? = null
    private var platName: String? = null
    private var thirdPartyToken: String? = null
    private var thirdPartyOpenId: String? = null
    private var thirdPartyPlatName: String? = null
    private var thirdPartyRefreshToken: String? = null
    private var commonPopup: ScanProCommonPopup? = null
    private var realm: Realm? = null
    private var currentTime: Long = 0
    private var registerEntity: MiaoHttpEntity<RegisterModel>? = null
    //0：默认，1：图片浏览
    private var type: Int = 0
//    private val onProgressFinish = object : ProgressButton.OnProgressFinish() {
//        fun onFinish() {
//            confirmToClearLastUserData(registerEntity!!)
//        }
//    }


    private val firstPswTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            if (s.length > 0) {
                hasFirstPassword = true
            } else {
                hasFirstPassword = false
            }

            checkNextStepButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            if (s.length > 0) {
                hasFirstPassword = true
            } else {
                hasFirstPassword = false
            }
            checkNextStepButtonToClick()
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this,true)
        setContentView(R.layout.activity_third_register)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        realm = Realm.getDefaultInstance()


        platName = intent.getStringExtra("platName")
        thirdPartyToken = intent.getStringExtra("thirdPartyToken")
        thirdPartyOpenId = intent.getStringExtra("thirdPartyOpenId")
        thirdPartyPlatName = intent.getStringExtra("thirdPartyPlatName")
        thirdPartyRefreshToken = intent.getStringExtra("thirdPartyRefreshToken")
        type = intent.getIntExtra("type", 0)
        account = intent.getStringExtra("account")
        code = intent.getStringExtra("code")
        userId = intent.getStringExtra("userId")
        LogUtils.i("account:$account /// code:$code /// userId:$userId")
        httpManager = HttpManager.getInstance()
        userPreferences = UserPreferences.getInstance(this)


        //设置标题
        binding.registerTopBar.normalTitle.setText(R.string.third_party_set_password)

    }

    private fun registerEvent() {
       binding.registerTopBar.normalBackBtn.setOnClickListener(this)
       binding.confirmBtn.setOnClickListener(this)
       binding.confirmBtn.isSelected = false
       binding.confirmBtn.isClickable = false
       binding.pswEdt.addTextChangedListener(firstPswTextWatcher)

    }

    /**
     * @des: 第三方账号注册
     * @params:[]
     * @return:void
     */
    private fun thirdPartyRegister(hasChannel: Boolean) {
        val password = MD5Utils.md5(binding.pswEdt.text.toString())
        HttpManager.getInstance().requestPassport().thirdPartyRegister(userPreferences!!.channel, userPreferences!!.imei, Constants.SCAN_PRO, code, password,
                account, userId, RegisterModel::class.java, object : MiaoHttpManager.Callback<RegisterModel> {
            override fun onStart() {
                if (hasChannel) {
                    showProgressDialog(false)
                }
            }

            override fun onResponse(entity: MiaoHttpEntity<RegisterModel>) {
                hideProgressDialog()
                registerEntity = entity
                confirmToClearLastUserData(entity)


            }

            override fun onFailure(entity: MiaoHttpEntity<RegisterModel>) {
                hideProgressDialog()
                when (entity.code) {

                    MiaoHttpManager.STATUS_NOT_USER -> showMessage(R.string.toast_user_no_exist)
                    MiaoHttpManager.STATUS_USER_EXISTS -> showMessage(R.string.toast_user_existing)
                    MiaoHttpManager.STATUS_INVALID_CODE -> showMessage(R.string.toast_code_error)
                    MiaoHttpManager.STATUS_ERROR -> showMessage(R.string.toast_internal_error)
                    else -> showMessage(R.string.request_failed_alert)
                }
            }

            override fun onError(e: Exception) {
                hideProgressDialog()

                showMessage(R.string.request_failed_alert)
            }
        })


    }


    /**
     * @des: 确认删除用户信息
     * @params:
     * @return:
     */

    @SuppressLint("StringFormatInvalid")
    private fun confirmToClearLastUserData(entity: MiaoHttpEntity<RegisterModel>) {
        val currentUserId = entity.body.id
        LogUtils.i(currentUserId, userId)
        if (!StringUtils.equals(userPreferences!!.lastUserId, currentUserId) && Validator.isNotEmpty(userPreferences!!.lastUserId)) {

            val builder = ScanProCommonPopup.Builder(this@ThirdPartyRegisterActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
            builder.setTitle(resources.getString(R.string.prompt))
            val title = String.format(getString(R.string.confirm_to_clear_account), userPreferences!!.getUserName())
            builder.setMessage(title)
            builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
                if (commonPopup != null) {
                    commonPopup!!.dismiss()
                }
                clearLastUserDataAndSetCurrentData(entity)
            })
            builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, which ->
                dialog.dismiss()
                ActivityUtils.finishActivity(this@ThirdPartyRegisterActivity)
            })
            commonPopup = builder.create()
            commonPopup!!.show()

        } else {
            setCurrentUserData(entity)
        }

    }

    /**
     * @des: 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     * @params:
     * @return:
     */

    private fun clearLastUserDataAndSetCurrentData(entity: MiaoHttpEntity<RegisterModel>) {
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                LogUtils.i("clean last user file and sp")
                val filePath = filesDir.toString() + File.separator + userPreferences!!.lastUserId
                FileUtils.deleteDir(File(filePath))
                runOnUiThread {
                    //清空sp
                    userPreferences!!.resetUser()
                    //清空数据库
                    realm!!.executeTransaction {
                        realm!!.where(DocEntity::class.java).notEqualTo("userID", Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm!!.where(CategoryEntity::class.java).notEqualTo("userID", Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm!!.where(TagEntity::class.java).notEqualTo("userID", Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm!!.where(PdfEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(OcrModeEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncTagEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncDocEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncCategoryEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(DownloadEntity::class.java).findAll().deleteAllFromRealm()
                    }
                    Fresco.getImagePipeline().clearCaches()
                    CleanUtils.cleanCustomDir(Utils.getApp().filesDir.toString() + File.separator + Constants.PDF_PATH)
                    setCurrentUserData(entity)
                }
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })
    }

    /**
     * @des: 显示登录成功并且跳转到主页
     * @params:
     * @return:
     */
    private fun showLoginSuccessAndGoIndex() {
        requestUserInfo()

        showMessage(R.string.register_success)
        EventBus.getDefault().post(LoginEvent(EventType.THIRD_PARTY_REGISTER_SUCCESS))
        when (type) {
            0 -> {
                if (ActivityUtils.isActivityExistsInStack(UserActivity::class.java)) {
                    ActivityUtils.finishToActivity(UserActivity::class.java, false)
                }else{
                    val intent = Intent(this@ThirdPartyRegisterActivity, UserActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    ActivityUtils.startActivity(intent)
                }
            }
            1 -> {
                ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
            }
            2 -> {
                ActivityUtils.finishToActivity(IndexActivity::class.java, false)
            }
            3->{
                ActivityUtils.finishToActivity(FileActivity::class.java, false)
            }
        }

    }

    /**
     * @des: 存储当前用户信息到SP
     * @params:
     * @return:
     */
    private fun setCurrentUserData(entity: MiaoHttpEntity<RegisterModel>) {
        userPreferences!!.user = entity.body
        userPreferences!!.setIsUserLogin(true)
        userPreferences!!.lastUserId = userPreferences!!.userId
        LogUtils.i(Gson().toJson(userPreferences!!.user))
        userPreferences!!.setIsThirdParty(true)
        userPreferences!!.thirdPartyOpenid = thirdPartyOpenId
        userPreferences!!.thirdPartyToken = thirdPartyToken
        userPreferences!!.thirdPartyRefreshToken = thirdPartyRefreshToken
        userPreferences!!.thirdPartyPlatName = thirdPartyPlatName
        userPreferences!!.servicePlatName = platName
        LogUtils.i(userPreferences!!.isThirdParty, userPreferences!!.thirdPartyOpenid, userPreferences!!.thirdPartyPlatName, userPreferences!!.thirdPartyToken, userPreferences!!.servicePlatName)

        transformCategory(realm!!)
        showProgressDialog(false)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {

                transformRealCategory()
                return null
            }

            override fun onSuccess(result: Void?) {
                hideProgressDialog()
                showLoginSuccessAndGoIndex()

            }
        })
    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.confirmBtn -> confirmPassword()
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            else -> {
            }
        }
    }

    /**
     * @des: 确认新密码
     * @params:[]
     * @return:void
     */
    private fun confirmPassword() {
        val firstPassword = binding.pswEdt.getText().toString()

        if (firstPassword.length <= 5) {
            showMessage(R.string.login_alert_pwd_length)
        } else {
            currentTime = System.currentTimeMillis()
            KeyboardUtils.hideSoftInput(this)
            if (TextUtils.isEmpty(userPreferences!!.getChannel())) {
                getChannel()
            } else {
                thirdPartyRegister(true)
            }

        }
    }

    /**
     * @des:获取channel
     * @params:[userId, token, platformName, isThirdParty, mobileMail, pwd]
     * @return:void
     */
    private fun getChannel() {


        HttpManager.getInstance().request().channel(ChannelModel::class.java, object : MiaoHttpManager.Callback<ChannelModel> {
            override fun onStart() {
                showProgressDialog(false)
            }

            override fun onResponse(entity: MiaoHttpEntity<ChannelModel>) {
                hideProgressDialog()
                userPreferences!!.setChannel(entity.getBody().getChannel())
                userPreferences!!.setEndpoint(entity.getBody().getEndPoint())
                LogUtils.i(entity.getBody())
                thirdPartyRegister(false)
            }

            override fun onFailure(entity: MiaoHttpEntity<ChannelModel>) {
                hideProgressDialog()

                showMessage(R.string.request_failed_alert)
            }

            override fun onError(e: Exception) {
                hideProgressDialog()

                showMessage(R.string.request_failed_alert)
            }
        })
    }

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkNextStepButtonToClick() {

        if (hasFirstPassword) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
            binding.confirmBtn.background = drawable
            binding.confirmBtn.isSelected = true
            binding.confirmBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
            binding.confirmBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
            binding.confirmBtn.background = drawable
            binding.confirmBtn.isSelected = false
            binding.confirmBtn.setTextColor(resources.getColor(R.color.white))
            binding.confirmBtn.isClickable = false
        }
    }
}