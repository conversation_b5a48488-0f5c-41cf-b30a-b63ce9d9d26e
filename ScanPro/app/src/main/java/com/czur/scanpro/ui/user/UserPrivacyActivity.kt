package com.czur.scanpro.ui.user

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.WebViewActivity
import com.czur.scanpro.ui.component.MediumBoldTextView
import java.util.*

/**
 * Created by shaojun
 */
class UserPrivacyActivity : BaseActivity(), View.OnClickListener {

    private lateinit var backBtn: ImageView
    private lateinit var normalTitle: MediumBoldTextView
    private lateinit var btn_cancel_account: LinearLayout
    private lateinit var userPrivacyRl: RelativeLayout
    private lateinit var user_privacy_rl2: RelativeLayout
    private lateinit var private_checklist_rl: RelativeLayout
    private lateinit var thrid_sharelist_rl: RelativeLayout


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_fa)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_user_privacy)
        initView()
        normalTitle.text = getString(R.string.privacy_policy)
        backBtn.setOnClickListener(this)
        btn_cancel_account.setOnClickListener(this)
        userPrivacyRl.setOnClickListener(this)
        user_privacy_rl2.setOnClickListener(this)
        private_checklist_rl.setOnClickListener(this)
        thrid_sharelist_rl.setOnClickListener(this)
    }

    private fun initView() {
        normalTitle = findViewById(R.id.normalTitle)
        backBtn = findViewById(R.id.backBtn)
        btn_cancel_account = findViewById(R.id.btn_cancel_account)
        userPrivacyRl = findViewById(R.id.userPrivacyRl)
        user_privacy_rl2 = findViewById(R.id.user_privacy_rl2)
        private_checklist_rl = findViewById(R.id.private_checklist_rl)
        thrid_sharelist_rl = findViewById(R.id.thrid_sharelist_rl)

    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.btn_cancel_account->{
                ActivityUtils.startActivity(UserRemoveAccountActivity::class.java)
            }
            R.id.userPrivacyRl->{
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_policy1))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", Constants.PRIVACY_AGREEMENT)
                ActivityUtils.startActivity(intent)
            }

            R.id.user_privacy_rl2->{
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_terms))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", Constants.PRIVACY_TERMS)
                ActivityUtils.startActivity(intent)
            }

            R.id.private_checklist_rl->{
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.private_checklist))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", Constants.PRAVATE_INFO_LIST + "?timeStep=" + cTime + "")
                ActivityUtils.startActivity(intent)
            }

            R.id.thrid_sharelist_rl->{
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.thrid_sharelist))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", Constants.THIRD_SHARE_INFO_LIST + "?timeStep=" + cTime + "")
                ActivityUtils.startActivity(intent)
            }
        }
    }

}
