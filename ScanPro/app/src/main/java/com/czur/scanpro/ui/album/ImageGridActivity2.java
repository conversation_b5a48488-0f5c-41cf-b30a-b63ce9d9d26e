package com.czur.scanpro.ui.album;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.ui.base.BaseActivity;
import com.czur.scanpro.ui.base.IndexActivity;
import com.czur.scanpro.ui.file.adjust.AdjustEdgeSimpleActivity;
import com.czur.scanpro.utils.validator.Validator;

import java.util.UUID;

/**
 * Created by Yz on 2018/3/15
 * Email：<EMAIL>
 */

public class ImageGridActivity2 extends BaseActivity implements ImageRecyclerAdapter2.OnImageItemClickListener, View.OnClickListener {


    private ImagePicker imagePicker;
    private RecyclerView mRecyclerView;
    private ImageRecyclerAdapter2 mRecyclerAdapter;
    private ImageFolder imageFolder;
    private TextView tvDes;
    private RelativeLayout noBackTopBarCancel;
    private LinearLayout btnBack;
    private boolean isImport = false;
    private String tagId;
    private String tagName;
    private String categoryID;
    private String categoryName;
    private int type = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_image_grid2);
        initComponent();
        initRecyclerView();
        registerEvent();


    }

    private void initComponent() {
        isImport = getIntent().getBooleanExtra("isImport", false);
        tagId = getIntent().getStringExtra("tagId");
        tagName = getIntent().getStringExtra("tagName");
        categoryID = getIntent().getStringExtra("categoryID");
        categoryName = getIntent().getStringExtra("categoryName");
        type = getIntent().getIntExtra("type", 0);
        imageFolder = (ImageFolder) getIntent().getSerializableExtra("imageFolder");
        imagePicker = ImagePicker.getInstance();
        mRecyclerView = (RecyclerView) findViewById(R.id.recycler);
        btnBack = (LinearLayout) findViewById(R.id.btn_back_ll);
        tvDes = (TextView) findViewById(R.id.tv_des);
        noBackTopBarCancel = (RelativeLayout) findViewById(R.id.album_top_bar_cancel);
        if (Validator.isNotEmpty(imageFolder)) {
            tvDes.setText(imageFolder.name);
        }

    }

    private void registerEvent() {
        btnBack.setOnClickListener(this);
        noBackTopBarCancel.setOnClickListener(this);
    }


    private void initRecyclerView() {
        mRecyclerAdapter = new ImageRecyclerAdapter2(this, imageFolder.images);
        mRecyclerAdapter.setOnImageItemClickListener(this);
        mRecyclerView.setLayoutManager(new GridLayoutManager(this, 3));
        mRecyclerView.addItemDecoration(new GridSpacingItemDecoration(3, SizeUtils.dp2px(10), false));
        mRecyclerView.setAdapter(mRecyclerAdapter);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_back_ll) {//点击返回按钮
            ActivityUtils.finishActivity(this);
        } else if (id == R.id.album_top_bar_cancel) {
            ActivityUtils.startActivity(IndexActivity.class);
        }
    }


    @Override
    public void onImageItemClick(View view, ImageItem imageItem, int position) {
        if (isImport) {
            //跳转调整画面
            Intent intent = new Intent(ImageGridActivity2.this, AdjustEdgeSimpleActivity.class);
            intent.putExtra("isImport", isImport);
            intent.putExtra("type", type);
            if (type == 2) {
                intent.putExtra("tagName", tagName);
                intent.putExtra("tagId", tagId);
            } else if (type == 1) {
                intent.putExtra("categoryID", categoryID);
                intent.putExtra("categoryName", categoryName);
            }
            intent.putExtra("fileID", UUID.randomUUID().toString());
            intent.putExtra("originalImagePath", imageItem.path);
            ActivityUtils.startActivity(intent);
        } else {
            Intent intent = new Intent(ImageGridActivity2.this, ImageCropActivity.class);
            intent.putExtra("path", imageItem.path);
            LogUtils.i(imageItem.path);
            startActivityForResult(intent, ImagePicker.REQUEST_CODE_CROP);  //单选需要裁剪，进入裁剪界面
        }
    }
}