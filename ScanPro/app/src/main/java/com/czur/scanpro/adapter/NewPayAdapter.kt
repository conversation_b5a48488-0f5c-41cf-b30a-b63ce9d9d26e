package com.czur.scanpro.adapter

import android.content.Context
import android.graphics.Paint
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.SizeUtils
import com.czur.scanpro.R
import com.czur.scanpro.entity.model.ProductModel

class NewPayAdapter(val context: Context, val datas: MutableList<ProductModel>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var width: Int = 0

    init {
        this.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(58f)) / 3
    }

    override fun onCreateViewHolder(viewGroup: ViewGroup, position: Int): RecyclerView.ViewHolder {
        return PayHolder(LayoutInflater.from(viewGroup.context).inflate(R.layout.item_product, viewGroup, false))
    }

    override fun getItemCount(): Int {
        return datas.size
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val payHolder: PayHolder = holder as PayHolder
        val layoutParams: RecyclerView.LayoutParams = payHolder.itemView.layoutParams as RecyclerView.LayoutParams
        layoutParams.width = this.width
        if (position != 0) {
            layoutParams.leftMargin = SizeUtils.dp2px(12f)
        } else {
            layoutParams.leftMargin = SizeUtils.dp2px(0f)
        }
        payHolder.itemView.layoutParams = layoutParams
        if (datas[position].price == datas[position].showPrice){
            payHolder.tvDiscount.visibility = View.INVISIBLE
            payHolder.tvOriginalPrice.visibility = View.INVISIBLE
        }else{
            payHolder.tvDiscount.visibility = View.VISIBLE
            payHolder.tvOriginalPrice.visibility = View.VISIBLE
        }
        payHolder.tvDuration.text = datas[position].name
        payHolder.tvCurrentPrice.paint.isFakeBoldText = true
        payHolder.tvCurrentPrice.text = datas[position].price.toString()
        payHolder.tvOriginalPrice.text = datas[position].showPrice.toString()
        val discount = String.format("%.1f", datas[position].price * 10 / datas[position].showPrice)
        payHolder.tvDiscount.text = String.format(context.getString(R.string.discount), discount)
        payHolder.tvOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG
        payHolder.tvOriginalPrice.paint.isAntiAlias = true
        if (datas[position].vipType == 0) {
            //普通会员
            payHolder.rlPrice.setBackgroundResource(R.drawable.selector_red_bg)
            payHolder.imgSelect.setImageResource(R.mipmap.red_select)
            payHolder.tvCurrentPrice.setTextColor(context.resources.getColor(R.color.red_d54146))
            payHolder.tvUnit.setTextColor(context.resources.getColor(R.color.red_d54146))
            payHolder.tvDiscount.setBackgroundResource(R.drawable.view_rec_3_red_without_right_bottom)
        } else {
            payHolder.rlPrice.setBackgroundResource(R.drawable.selector_gold_bg)
            payHolder.imgSelect.setImageResource(R.mipmap.gold_select)
            payHolder.tvCurrentPrice.setTextColor(context.resources.getColor(R.color.gold_ecc382))
            payHolder.tvUnit.setTextColor(context.resources.getColor(R.color.gold_ecc382))
            payHolder.tvDiscount.setBackgroundResource(R.drawable.view_rec_3_gold_without_right_bottom)
        }
        if (datas[position].isSelect) {
            payHolder.imgSelect.visibility = View.VISIBLE
            payHolder.rlPrice.isSelected = true
        } else {
            payHolder.imgSelect.visibility = View.GONE
            payHolder.rlPrice.isSelected = false
        }
        payHolder.rlPrice.setOnClickListener {
            datas.forEach {
                it.isSelect = datas.indexOf(it)==position
                notifyDataSetChanged()
            }
            onItemClickListener?.onPayItemClick(datas[position], position)
        }
    }

    private inner class PayHolder internal constructor(mView: View) : RecyclerView.ViewHolder(mView) {
        internal var tvDuration: TextView = mView.findViewById(R.id.tv_duration)
        internal var tvCurrentPrice: TextView = mView.findViewById(R.id.tv_current_price)
        internal var tvOriginalPrice: TextView = mView.findViewById(R.id.tv_original_price)
        internal var tvDiscount: TextView = mView.findViewById(R.id.tv_discount)
        internal val rlPrice: RelativeLayout = mView.findViewById(R.id.rl_price)
        internal val imgSelect: ImageView = mView.findViewById(R.id.img_select)
        internal val tvUnit: TextView = mView.findViewById(R.id.tv_unit)
    }

    private var onItemClickListener: OnItemClickListener? = null
    interface OnItemClickListener {
        fun onPayItemClick(ProductModel: ProductModel, position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }
}