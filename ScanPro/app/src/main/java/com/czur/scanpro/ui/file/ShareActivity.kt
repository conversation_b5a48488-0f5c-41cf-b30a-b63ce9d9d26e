package com.czur.scanpro.ui.file

import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.View
import cn.sharesdk.framework.Platform
import cn.sharesdk.framework.Platform.SHARE_WEBPAGE
import cn.sharesdk.framework.PlatformActionListener
import cn.sharesdk.framework.ShareSDK
import cn.sharesdk.tencent.qq.QQ
import cn.sharesdk.wechat.friends.Wechat
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityShareBinding
import com.czur.scanpro.databinding.FragmentBottomPropertiesDialogBinding
import com.czur.scanpro.entity.model.ShareModel
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.ShareEvent
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.popup.ShareBottomDialog
import com.czur.scanpro.ui.sync.SyncService.JSON
import com.czur.scanpro.utils.MD5Utils
import com.google.gson.Gson
import okhttp3.*
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import java.io.IOException
import java.util.*

/**
 * Created by Yz on 2019/3/26.
 * Email：<EMAIL>
 */
class ShareActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityShareBinding by lazy{
        ActivityShareBinding.inflate(layoutInflater)
    }

    private var sharePaths: ArrayList<String> = arrayListOf<String>()
    private var expireType = 0L
    private var userPreferences: UserPreferences? = null
    private var shareBottomDialog: ShareBottomDialog? = null
    private var url: String? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_share)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }


    private fun initComponent() {
        createBottomSheetDialog()
        sharePaths = intent.getStringArrayListExtra("sharePaths") as ArrayList<String>
        LogUtils.e(Gson().toJson(sharePaths))
        userPreferences = UserPreferences.getInstance(this)
    }

    private fun createBottomSheetDialog() {


        shareBottomDialog = ShareBottomDialog(this, onBottomSheetClickListener)
    }

    private val onBottomSheetClickListener = ShareBottomDialog.ShareDialogOnClickListener { viewId ->
        when (viewId) {
            R.id.weixin_account -> {
                showShare(R.id.weixin_account)
                shareBottomDialog!!.dismiss()
            }
            R.id.qq_account -> {
                showShare(R.id.qq_account)
                shareBottomDialog!!.dismiss()
            }

            R.id.share_cancel_btn -> shareBottomDialog!!.dismiss()
            else -> {
            }
        }
    }

    private fun createShareLink() {
        if (binding.codeEdt.text.toString().length > 5) {
            showProgressDialog()
            val shareModel = ShareModel()
            shareModel.expireType = expireType
            shareModel.ossKey = sharePaths
            shareModel.userId = userPreferences!!.userId
            shareModel.password = MD5Utils.md5(binding.codeEdt.text.toString()).toUpperCase()
            val request = Gson().toJson(shareModel)
            val requestBody = RequestBody.create(JSON, request)
            val httpRequest = Request.Builder()
                    .header("udid", userPreferences!!.imei)
                    .header("App-Key", Constants.SCAN_PRO)
                    .header("T-ID", userPreferences!!.token)
                    .header("U-ID", userPreferences!!.userId)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.PHASE.serviceUrl + Constants.SHARE_URL)
                    .post(requestBody)
                    .build()

            MiaoHttpManager.getInstance().httpClient.newCall(httpRequest).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    hideProgressDialog()
                    LogUtils.e(e)
                }

                @Throws(IOException::class)
                override fun onResponse(call: Call, response: Response) {
                    //此方法运行在子线程中，不能在此方法中进行UI操作。

                    runOnUiThread {
                        hideProgressDialog()
                        val result = response.body!!.string()
                        val jsonObject = JSONObject(result)

                        val body = jsonObject.getJSONObject("body")
                        url = body.getString("link")
                        LogUtils.e(url)
                        shareBottomDialog!!.show()

                    }


                }
            })

        } else {
            showMessage(R.string.share_cant_empty)
        }

    }

    private fun showShare(viewId: Int) {
        EventBus.getDefault().post(ShareEvent(EventType.SHARE))
        val sp = Platform.ShareParams()
        val logo = BitmapFactory.decodeResource(resources, R.mipmap.ic_launcher)
        sp.imageData = logo
        sp.title = getString(R.string.share_title);
        sp.titleUrl = url;// 标题的超链接
        sp.site = getString(R.string.app_name)
        when (viewId) {
            R.id.weixin_account -> {
                sp.shareType = SHARE_WEBPAGE
                sp.url = url
                val weixin = ShareSDK.getPlatform(Wechat.NAME)
                weixin.platformActionListener = object : PlatformActionListener {
                    override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {

                    }

                    override fun onError(platform: Platform, i: Int, throwable: Throwable) {
                        runOnUiThread(Runnable {
                            if (i == 9) {
                                showMessage(R.string.WEXIN_NOT_INSTALL)
                            } else{
                                showMessage(R.string.share_failed)
                            }
                        })
                    }

                    override fun onCancel(platform: Platform, i: Int) {

                    }
                }
                weixin.share(sp)
            }

            R.id.qq_account -> {
                sp.text = url
                val qq = ShareSDK.getPlatform(QQ.NAME)
                qq.platformActionListener = object : PlatformActionListener {
                    override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {
                    }

                    override fun onError(platform: Platform, i: Int, throwable: Throwable) {
                        runOnUiThread(Runnable {
                            if (i == 9) {
                                showMessage(R.string.QQ_NOT_INSTALL)
                            } else{
                                showMessage(R.string.share_failed)
                            }
                        })

                    }

                    override fun onCancel(platform: Platform, i: Int) {
                    }
                }
                qq.share(sp)
            }

            else -> {
            }
        }

    }

    private fun registerEvent() {
        binding.cancelBtn.setOnClickListener(this)
        binding.commitBtn.setOnClickListener(this)
        binding.oneDayRl.setOnClickListener(this)
        binding.sevenDayRl.setOnClickListener(this)
        binding.thirtyDayRl.setOnClickListener(this)

    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.oneDayRl -> {
                binding.oneDayImg.visibility = View.VISIBLE
                binding.sevenDayImg.visibility = View.GONE
                binding.thirtyDayImg.visibility = View.GONE
                expireType = 0L
            }
            R.id.sevenDayRl -> {
                binding.oneDayImg.visibility = View.GONE
                binding.sevenDayImg.visibility = View.VISIBLE
                binding.thirtyDayImg.visibility = View.GONE
                expireType = 1L
            }
            R.id.thirtyDayRl -> {
                binding.oneDayImg.visibility = View.GONE
                binding.sevenDayImg.visibility = View.GONE
                binding.thirtyDayImg.visibility = View.VISIBLE
                expireType = 2L
            }
            R.id.commitBtn -> {
                if (!binding.codeEdt.text.isNullOrEmpty()) {
                    createShareLink()

                } else {
                    showMessage(R.string.password_empty)
                }
            }
            R.id.cancelBtn -> {
                ActivityUtils.finishActivity(this)
            }
            else -> {

            }
        }
    }


}
