package com.czur.scanpro.common

import android.app.Application
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.common.OSSLog
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider
import com.czur.scanpro.entity.model.OssModel
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.*

class OSSInstance private constructor() {

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            OSSInstance()
        }
    }

    private var application: Application? = null
    private var oss: OSS? = null
    private var ossModel: OssModel? = null

    fun init(application: Application) {
        this.application = application
    }

    fun oss(): OSS? {
        if (oss == null || getCurrentUTCDate().time >= ossModel!!.expirationMillisecond - 30 * 1000) {
            oss = makeOSSClient()
        }
        return oss
    }

    private fun makeOSSClient(): OSSClient? {
        ossModel = getOSSTokenInfo()
        if (ossModel == null) {
            return null
        }
        val utcDateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
        ossModel!!.expirationMillisecond = utcDateFormat.parse(ossModel!!.expiration).time
        val credentialProvider = OSSStsTokenCredentialProvider(ossModel!!.accessKeyId, ossModel!!.accessKeySecret, ossModel!!.securityToken)
        val conf = ClientConfiguration()
        // 连接超时，默认15秒
        conf.connectionTimeout = 15 * 1000
        // socket超时，默认15秒
        conf.socketTimeout = 15 * 1000
        // 最大并发请求数，默认5个
        conf.maxConcurrentRequest = 10
        // 失败后最大重试次数，默认2次
        conf.maxErrorRetry = 2
        OSSLog.enableLog()
        return OSSClient(application, UserPreferences.getInstance(application).endpoint, credentialProvider, conf)
    }

    private fun getOSSTokenInfo(): OssModel? {
        return try {
            val ossEntity = HttpManager.getInstance().request()
                .getOssInfo(UserPreferences.getInstance(application).imei.substring(0, 18), OssModel::class.java)
            if (ossEntity.code == MiaoHttpManager.STATUS_SUCCESS) {
                ossEntity.body
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    private fun getCurrentUTCDate(): Date {
        val calendar = Calendar.getInstance()
        val zoneOffset = calendar.get(Calendar.ZONE_OFFSET)
        val dstOffset = calendar.get(Calendar.DST_OFFSET)
        calendar.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset))
        return calendar.time
    }
}