package com.czur.scanpro.adapter;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.net.Uri;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.entity.realm.DocEntity;
import com.czur.scanpro.utils.ScreenAdaptationUtils;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.core.ImagePipeline;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class CameraPreviewAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    private List<DocEntity> datas;
    private boolean isListAdd;
    private boolean isSmallScreen;
    private int position;
    private boolean isVivoNotch = ScreenAdaptationUtils.hasLiuHaiInVivo();

    /**
     * 构造方法
     */
    public CameraPreviewAdapter(Activity activity, List<DocEntity> entities, boolean isSmallScreen) {
        this.mActivity = activity;
        isListAdd = false;
        if (entities != null) {
            this.datas = entities;
        } else {
            this.datas = new ArrayList<>();
        }

        this.isSmallScreen = isSmallScreen;
    }

    public void refreshData(List<DocEntity> entities, boolean isListAdd, int position) {
        this.isListAdd = isListAdd;
        this.datas = entities;
        this.position = position;
        LogUtils.i("refresh" + position);
        notifyDataSetChanged();


    }

    public void refreshData(List<DocEntity> entities) {
        this.datas = entities;
        LogUtils.i("refresh" + position);
        notifyDataSetChanged();


    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_NORMA) {
            View view;
            if (isVivoNotch) {
                view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_camera_preview_vivo_notch, parent, false);
            } else {
                view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_camera_preview, parent, false);
            }
            return new NormalViewHolder(view);
        } else {
            return null;
        }
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            ViewGroup.LayoutParams layoutParams = mHolder.cameraPreviewItemRl.getLayoutParams();
            int remainHeight;
            if (!isSmallScreen) {
                remainHeight = ScreenUtils.getScreenHeight() - ScreenUtils.getScreenWidth() * 4 / 3 - SizeUtils.dp2px(150);
            } else {
                remainHeight = ScreenUtils.getScreenHeight() - ScreenUtils.getScreenWidth() - SizeUtils.dp2px(150);
            }
            layoutParams.height = remainHeight;
            layoutParams.width = (int) (remainHeight * 0.75f);
            mHolder.cameraPreviewItemRl.setLayoutParams(layoutParams);
            if (mHolder.mItem.getProcessSmallImagePath() == null) {
                mHolder.cameraPreviewItemImg.setImageBitmap(null);
                mHolder.imgLoading.setVisibility(View.VISIBLE);
                Animation rotate = AnimationUtils.loadAnimation(mActivity, R.anim.rotate_anim);
                LinearInterpolator interpolator = new LinearInterpolator();
                rotate.setInterpolator(interpolator);
                mHolder.cameraPreviewItemImg.setOnClickListener(null);
                mHolder.imgLoading.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
                    @Override
                    public void onViewAttachedToWindow(View v) {
                        if (mHolder.mItem.getProcessSmallImagePath() == null) {
                            mHolder.imgLoading.startAnimation(rotate);
                        } else {
                            mHolder.imgLoading.clearAnimation();
                            mHolder.imgLoading.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onViewDetachedFromWindow(View v) {
                    }
                });
            } else {
                mHolder.imgLoading.clearAnimation();
                mHolder.imgLoading.setVisibility(View.GONE);
                ImagePipeline imagePipeline = Fresco.getImagePipeline();
                Uri uri = Uri.fromFile(new File(mHolder.mItem.getProcessSmallImagePath()));
                imagePipeline.evictFromCache(uri);
                mHolder.cameraPreviewItemImg.setImageURI(uri);
                mHolder.cameraPreviewItemImg.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onItemClickListener != null) {
                            onItemClickListener.onItemClick(mHolder.mItem, position);
                        }
                    }
                });
            }
        }
    }

    /**
     * @des: 条目添加动画
     * @params:
     * @return:
     */

    private void addItemAnim(NormalViewHolder mHolder) {
        AnimatorSet animatorSet = new AnimatorSet();//组合动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mHolder.cameraPreviewItemImg, "scaleX", 0, 1.1f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mHolder.cameraPreviewItemImg, "scaleY", 0, 1.1f, 1.0f);
        animatorSet.setDuration(600);
        animatorSet.setInterpolator(new DecelerateInterpolator());
        animatorSet.play(scaleX).with(scaleY);//两个动画同时开始
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
            }
        });
        animatorSet.start();
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private static class NormalViewHolder extends ViewHolder {
        public final View mView;
        DocEntity mItem;
        SimpleDraweeView cameraPreviewItemImg;
        RelativeLayout cameraPreviewItemRl;
        TextView cameraPageNumTv;
        ImageView imgLoading;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            cameraPreviewItemImg = (SimpleDraweeView) itemView.findViewById(R.id.camera_preview_item_img);
            cameraPreviewItemRl = (RelativeLayout) itemView.findViewById(R.id.item_camera_rl);
            imgLoading = itemView.findViewById(R.id.img_loading);
        }


    }


    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(DocEntity DocEntity, int position);
    }


}
