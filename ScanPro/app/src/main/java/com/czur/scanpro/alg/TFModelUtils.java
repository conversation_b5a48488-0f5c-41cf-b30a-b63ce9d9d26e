package com.czur.scanpro.alg;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.util.Log;

import org.tensorflow.contrib.android.TensorFlowInferenceInterface;

import java.util.Map;

public class TFModelUtils {

    private static final int PIC_WH = 512;
    private static final String INPUT = "input";
    private static final String[] OUTPUT = {"output"};
    private static final String MODEL_NAME = "frozen_graph_text_orient.pb";

    private TensorFlowInferenceInterface inferenceInterface;
    private int[] inputIntData;
    private float[] inputFloatData;
    private int inputWH;
    private String inputName;
    private String[] outputNames;
    private Map<Integer, String> dict;

    public TFModelUtils(AssetManager assetMngr){
        this.inputWH=PIC_WH;
        this.inputName=INPUT;
        this.outputNames=OUTPUT;
        this.inputIntData=new int[inputWH*inputWH];
        this.inputFloatData = new float[inputWH*inputWH*3];
        //从assets目录加载模型
        inferenceInterface= new TensorFlowInferenceInterface(assetMngr, MODEL_NAME);
    }

    public int run(Bitmap bitmap){
        int ori_result = -1;
        float[] inputData = getFloatImage(bitmap);
        inferenceInterface.feed(inputName, inputData, 1, inputWH, inputWH, 3);
//        Log.i("cxc", "feed ok");
        inferenceInterface.run( outputNames );
//        Log.i("cxc", "run ok");
        float[] scores=new float[4];
        inferenceInterface.fetch(outputNames[0], scores);
//        Log.i("cxc", "fetch ok");
        ori_result = getMaxIndex(scores);
        return ori_result;
    }

    private float[] getFloatImage(Bitmap bitmap){
        Bitmap bm = getResizedBitmap(bitmap,inputWH,inputWH);
        bm.getPixels(inputIntData, 0, bm.getWidth(), 0, 0, bm.getWidth(), bm.getHeight());
//        int maxValue = Integer.MIN_VALUE;
//        int minValue = 0;
//        for (int i = 0; i < inputIntData.length; ++i) {
//            if (inputIntData[i] > maxValue){
//                maxValue = inputIntData[i];
//            }
//            if (inputIntData[i] < minValue){
//                minValue = inputIntData[i];
//            }
//
////            final int val = inputIntData[i];
////            inputFloatData[i * 3 + 0] =(float) (((val >> 16) & 0xFF)/255.0-0.5)*2;
////            inputFloatData[i * 3 + 1] = (float)(((val >> 8) & 0xFF)/255.0-0.5)*2;
////            inputFloatData[i * 3 + 2] = (float)(( val & 0xFF)/255.0-0.5)*2 ;
//        }
//
//        int temp = maxValue - minValue;

        for (int i = 0; i < inputIntData.length; ++i) {

            final int val = inputIntData[i];
            inputFloatData[i * 3 + 0] =(float) (((val >> 16) & 0xFF));
            inputFloatData[i * 3 + 1] = (float)(((val >> 8) & 0xFF));
            inputFloatData[i * 3 + 2] = (float)(( val & 0xFF));
        }
        return inputFloatData;
    }

    public Bitmap getResizedBitmap(Bitmap bm, int newWidth, int newHeight) {
        int width = bm.getWidth();
        int height = bm.getHeight();
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        Bitmap resizedBitmap = Bitmap.createBitmap( bm, 0, 0, width, height, matrix, false);
        bm.recycle();
        return resizedBitmap;
    }

    private int getMaxIndex(float[] array){
        int indexMax = -1;
        float valueMax = array[0];
        for (int i = 0; i < array.length; i++) {
            Log.i("cxc result ", String.valueOf(array[i]));
            if (array[i] >= valueMax){
                indexMax = i;
                valueMax = array[i];
            }
        }
        return indexMax;
    }
}
