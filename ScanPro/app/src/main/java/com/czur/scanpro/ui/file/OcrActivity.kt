package com.czur.scanpro.ui.file

import android.content.DialogInterface
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.util.Log
import android.view.View
import android.widget.TextView
import com.android.docsmatter.engine.DOcrEngine
import com.android.docsmatter.vo.Document
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityFilePreviewBinding
import com.czur.scanpro.databinding.ActivityOcrBinding
import com.czur.scanpro.entity.model.BaiduTokenModel
import com.czur.scanpro.entity.model.BaiduWordModel
import com.czur.scanpro.entity.model.HandwritingEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.OcrModeEntity
import com.czur.scanpro.event.ConsumptionEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.callback.ProgressHelper
import com.czur.scanpro.network.callback.UIProgressRequestListener
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.EmptyCountPopup
import com.czur.scanpro.ui.component.popup.OcrSheetBottomDialog
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.sync.SyncService
import com.czur.scanpro.utils.SignV3Utils
import com.czur.scanpro.utils.validator.Validator
import com.facebook.common.executors.CallerThreadExecutor
import com.facebook.common.references.CloseableReference
import com.facebook.datasource.DataSource
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.imagepipeline.datasource.BaseBitmapDataSubscriber
import com.facebook.imagepipeline.image.CloseableImage
import com.facebook.imagepipeline.request.ImageRequestBuilder
import com.google.gson.Gson
import io.realm.Realm
import okhttp3.*
import org.greenrobot.eventbus.EventBus
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Created by Yz on 2019/4/9.
 * Email：<EMAIL>
 */
class OcrActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityOcrBinding by lazy{
        ActivityOcrBinding.inflate(layoutInflater)
    }

    private var lastClickTime: Long = 0
    private var isRun = false
    private var x: Int = 0
    private var y: Int = 0
    private var width: Int = 0
    private var height: Int = 0
    private var originalBitmap: Bitmap? = null
    private val rotated: Int = 0
    private var position: Int = 0
    private var url: String? = null
    private val etOcrBtn: TextView? = null
    private var bottomDialog: OcrSheetBottomDialog? = null
    private var type = 2

    private var languageFlag = 0

    private var realm: Realm? = null
    private var fileId: String? = null
    private val typeId: Int = 0
    private var ocrType: Int = 0
    private var userPreferences: UserPreferences? = null
    private var viewId: Int = 0
    private var isOCR: Boolean = false

    private var bigBitmap: Bitmap? = null
    private var resultText: String? = null
    private var isCloud: Boolean = false
    private var okHttpClient: OkHttpClient? = null

    private val onOcrLanguageClickListener = object : OcrSheetBottomDialog.OnOcrLanguageClickListener {
        override fun onItemClick(viewId: Int) {
            chooseType(viewId, false, true)
        }
    }

    private val handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
        }
    }

    private fun finishOcr(resultText: String?, ocrType: Int, bitmap: Bitmap?) {
        val tempPath = cacheDir.path + Constants.TEMP + UUID.randomUUID().toString() + Constants.JPG
        ImageUtils.save(bitmap, tempPath, Bitmap.CompressFormat.JPEG)
        if (Validator.isNotEmpty(resultText)) {
            val realm = Realm.getDefaultInstance()
            realm.beginTransaction()
            val docEntity = realm.where(DocEntity::class.java).equalTo("fileID", fileId).equalTo("isDelete", 0.toInt()).findFirst()
            docEntity!!.ocrResult = resultText
            realm.commitTransaction()

            val intent = Intent(this@OcrActivity, OcrResultActivity::class.java)
            intent.putExtra("fileId", fileId)
            intent.putExtra("position", position)
            intent.putExtra("url", url)
            intent.putExtra("ocrType", ocrType)
            intent.putExtra("resultText", resultText)
            intent.putExtra("fromOcr", true)
            intent.putExtra("tempPath", tempPath)
            ActivityUtils.startActivity(intent)
            ActivityUtils.finishActivity(this)
        } else {
//            if(ocrType==3){
//
//                LogUtils.e("11111111111111111111111111111")
//            }else if(ocrType==1){
//                LogUtils.e("2222222222222222222222222222222")
//                requestCloudOcrToken(bitmap, false)
//            }
            showMessage(R.string.recognition_defeat)
            ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_ocr)
        setContentView(binding.root)
        okHttpClient = OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.MINUTES)
                .readTimeout(10, TimeUnit.MINUTES)
                .build()
        realm = Realm.getDefaultInstance()
        userPreferences = UserPreferences.getInstance(this)
        url = intent.getStringExtra("url")
        ocrType = intent.getIntExtra("ocrType", 0)
        isCloud = intent.getBooleanExtra("isCloud", false)
        fileId = intent.getStringExtra("fileId")
        isOCR = intent.getBooleanExtra("isOCR", false)
        position = intent.getIntExtra("position", 0)
        binding.fileOcrChooseBtn.setOnClickListener(this)
        binding.imgBack.setOnClickListener(this)
        binding.etOcrBtn.setOnClickListener(this)
        binding.fileOcrChooseBtn.visibility = if (ocrType == 1) View.VISIBLE else View.GONE

        binding.etOcrBtn.text = getString(R.string.crop_image_ocr)
        binding.ocrTitle.text = if (ocrType == 2) getString(R.string.handwriting) else getString(R.string.ocr)

        val imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse("file://$url")).build()
        val imagePipeline = Fresco.getImagePipeline()
        imagePipeline.evictFromCache(Uri.parse("file://$url"))
        val dataSource = imagePipeline.fetchDecodedImage(imageRequest, this)
        dataSource.subscribe(object : BaseBitmapDataSubscriber() {
            override fun onNewResultImpl(bitmap: Bitmap?) {
                runOnUiThread(Runnable {
                    originalBitmap = bitmap
                    binding.cropImageView.setImageBitmap(originalBitmap)
                    hideProgressDialog()
                })
            }

            override fun onFailureImpl(dataSource: DataSource<CloseableReference<CloseableImage>>) {
                runOnUiThread(Runnable { hideProgressDialog() })
            }
        }, CallerThreadExecutor.getInstance())
        createBottomSheetDialog()

    }

    override fun onResume() {
        super.onResume()
        bottomDialog!!.show()
        checkTypeInRealm(true)
        bottomDialog!!.dismiss()
    }


    private fun createBottomSheetDialog() {
        bottomDialog = OcrSheetBottomDialog(this, onOcrLanguageClickListener)
    }

    /**
     * @des: 手写识别dialog
     * @params:
     * @return:
     */

    private fun confirmDialog(bitmap: Bitmap?) {
        val builder = ScanProCommonPopup.Builder(this@OcrActivity, CloudCommonPopupConstants.OK_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.writing_prompt))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            userPreferences!!.setIsHandwritingGuide(false)
            goHandwriting(bitmap)
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.setCanceledOnTouchOutside(false)
        commonPopup.setCancelable(false)
        commonPopup.show()
    }

    private fun goHandwriting(bitmap: Bitmap?) {
        if (isHasCount(2)) {
            Thread(Runnable {
                requestUserInfoSync()
                runOnUiThread {
                    val path = userPreferences!!.sdPath + Constants.HANDWRITING_PATH + UUID.randomUUID().toString() + Constants.JPG
                    LogUtils.e(path)
                    showProgressDialog(true)
                    FileIOUtils.writeFileFromBytesByStream(path, ConvertUtils.bitmap2Bytes(bitmap, Bitmap.CompressFormat.JPEG, 90), true)
                    handwritingRecognition(FileUtils.getFileByPath(path))
                }
            }).start()
        } else {
            showNoCountEmptyMessage(2)
        }


    }
    private fun isHasCount(ocrType: Int): Boolean {
        return if (getSp().isVip) {
            when (ocrType) {
                1 -> true
                2 -> getSp().remainHandwriting > 0
                3 -> getSp().remainCloudOcr > 0
                else -> getSp().remainCard > 0
            }
        } else {
            when (ocrType) {
                1 -> getSp().remainOcr > 0
                2 -> getSp().remainHandwriting > 0
                3 -> getSp().remainCloudOcr > 0
                else -> getSp().remainCard > 0
            }
        }
    }
    private fun showNoCountEmptyMessage(ocrType: Int) {
        return when (ocrType) {
            1 -> showOcrEmptyCountDialog()
            2 -> showEmptyCountDialog(this@OcrActivity, EmptyCountPopup.EmptyType.HANDWRTING)
            else -> showEmptyCountDialog(this@OcrActivity, EmptyCountPopup.EmptyType.CLOUD)
        }

    }

    private fun showOcrEmptyCountDialog() {
        if (isActive) {
            val builder = EmptyCountPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
            builder.setMessage(EmptyCountPopup.EmptyType.OCR)
            builder.setOnPositiveListener { dialog, _ ->
                dialog.cancel()
                Thread(Runnable {
                    requestUserInfoSync()
                    runOnUiThread {
                        ocrType = 3;
                        if (isHasCount(ocrType)) {
                            val currentTime = Calendar.getInstance().timeInMillis
                            if (currentTime - lastClickTime > MIN_CLICK_DELAY_TIME && !isRun) {
                                lastClickTime = currentTime
                                checkTypeInRealm(false)
                                cropImage()
                            }
                        } else {
                            showNoCountEmptyMessage(ocrType)
                        }
                    }
                }).start()
            }
            val emptyCountPopup = builder.create()
            emptyCountPopup.show()
        }
    }
    /**
     * @des: 手写识别
     * @params:
     * @return:
     */

    private fun handwritingRecognition(file: File) {
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                upload(file)
//                handwritingOcr(file)
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })
    }

//    private fun handwritingOcr(path: String) {
//        val requestBody = MultipartBody.Builder()
//                .setType(MultipartBody.FORM)
//                .addFormDataPart("Version", "2018-11-19")
//                .addFormDataPart("Region", "ap-guangzhou")
//                .addFormDataPart("Action", "GeneralHandwritingOCR")
//                .addFormDataPart("ImageBase64", SignV3Utils.imageToBase64(path))
//                .build()
//        val request = Request.Builder()
//                .header("host", Constants.HANDWRITING_HOST)
//                .header("authorization", SignV3Utils.appSign())
//                .url(Constants.HANDWRITING_URL)
//                .post(requestBody)
//                .build()
//        val okHttpClient = OkHttpClient.Builder()
//                .connectTimeout(30, TimeUnit.SECONDS)
//                .writeTimeout(20, TimeUnit.SECONDS)
//                .readTimeout(20, TimeUnit.SECONDS)
//                .build()
//        val response = okHttpClient.newCall(request).execute()
//        LogUtils.i("-- 上传手写体识别图片 start --")
//        val jsonString = response.body!!.string()
//        LogUtils.i(jsonString)
//        val handwritingEntity = Gson().fromJson<HandwritingEntity>(jsonString, HandwritingEntity::class.java)
//        LogUtils.i("-- 上传手写体识别图 end --")
//        // 请求成功
//        if (response.isSuccessful) {
//            val code = handwritingEntity.code
//            LogUtils.e(code)
//            // 上传成功
//            if (code == 0) {
//                afterUpload(handwritingEntity, jsonString)
//            } else if (code == -9011) {
//                FailedRecognizedToast()
//            } else {
//                FailedToast()
//            }
//        } else {
//            FailedToast()
//        }// 请求失败
//    }

    /**
     * @des: 上传手写体识别image
     * @params:
     * @return:
     */

    private fun upload(file: File) {
        LogUtils.i("path:" + file.absolutePath)
        try {
            //这个是ui线程回调，可直接操作UI
            val uiProgressRequestListener = object : UIProgressRequestListener() {
                override fun onUIRequestProgress(bytesWrite: Long, contentLength: Long, done: Boolean) {
                    Log.i("czurxx", "bytesWrite:$bytesWrite")
                    Log.i("czurxx", "contentLength$contentLength")
                    Log.i("czurxx", (100 * bytesWrite / contentLength).toString() + " % done ")
                    Log.i("czurxx", "done:$done")
                    Log.i("czurxx", "================================")

                }
            }

//            val requestBody = MultipartBody.Builder()
//                    .setType(MultipartBody.FORM)
//                    .addFormDataPart("appid", Constants.HANDWRITING_APP_ID.toString())
//                    //                    .addFormDataPart("url", "http://changer-resource.oss-cn-beijing.aliyuncs.com/static/aura/Books.png")
//                    .addFormDataPart("image", "test.jpg", RequestBody.create("image/jpeg".toMediaTypeOrNull(), file))
//                    .build()
//
//            val request = Request.Builder()
//                    .header("host", Constants.HANDWRITING_HOST)
//                    .header("authorization", SignUtils.appSign(Constants.HANDWRITING_APP_ID, Constants.HANDWRITING_SECRET_ID, Constants.HANDWRITING_SECRET_KEY, Constants.HANDWRITING_BUCKET_NAME, Constants.HANDWRITING_EXPIRED))
//                    .url(Constants.HANDWRITING_URL)
//                    .post(ProgressHelper.addProgressRequestListener(requestBody, uiProgressRequestListener))
//                    .build()
            //创建一个json,在里面放入base64的string参数
            val jsonObject = JSONObject()
            jsonObject.put("base64", SignV3Utils.imageToBase64(file.path))
            val syncJson = jsonObject.toString()
            val requestBody: RequestBody = RequestBody.create(SyncService.JSON, syncJson)

//            val requestBody = MultipartBody.Builder()
//                .setType(MultipartBody.FORM)
//                //图片转base64
//                .addFormDataPart("base64",  SignV3Utils.imageToBase64(file.path))
//                //                    .addFormDataPart("url", "http://changer-resource.oss-cn-beijing.aliyuncs.com/static/aura/Books.png")
////                .addFormDataPart("base64", "test.jpg", RequestBody.create("image/jpeg".toMediaTypeOrNull(), file))
//                .build()
            val request = Request.Builder()
                .header("Content-Type", "application/json")
                .header("T-ID", userPreferences!!.token)
                .header("U-ID", userPreferences!!.userId)
                .url(Constants.HANDWRITING_URL)
                .post(ProgressHelper.addProgressRequestListener(requestBody, uiProgressRequestListener))
                .build()
            val okHttpClient = OkHttpClient.Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .writeTimeout(15, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build()

            val response = okHttpClient.newCall(request).execute()
            LogUtils.i("-- 上传手写体识别图片 start --")
            val jsonString = response.body!!.string()
            LogUtils.i(jsonString)
            val handwritingEntity = Gson().fromJson<HandwritingEntity>(jsonString, HandwritingEntity::class.java)
            LogUtils.i("-- 上传手写体识别图 end --")
            // 请求成功
            if (response.isSuccessful) {
                val code = handwritingEntity.code
                LogUtils.e(code)
                // 上传成功
                if (code == 1000) {
                    val textDetectionsBean = Gson().fromJson<HandwritingEntity.DataBean>(handwritingEntity.data, HandwritingEntity.DataBean::class.java)
                    handwritingEntity.databean = textDetectionsBean
                    afterUpload(handwritingEntity, jsonString)
                } else if (code == -9011) {
                    FailedRecognizedToast()
                } else {
                    FailedToast()
                }
            } else {
                FailedToast()
            }// 请求失败
        } catch (e: IOException) {
            LogUtils.e(e)
            FailedToast()
        } catch (e: JSONException) {
            LogUtils.e(e)
            FailedToast()
        } catch (e: Exception) {
            LogUtils.e(e)
            FailedToast()
        }

    }


    private fun afterUpload(handwritingEntity: HandwritingEntity, jsonString: String) {
        runOnUiThread(Runnable {
            consumption(handwritingEntity, jsonString, "handwriting", 2, null)

        })
    }

    /**
     * @des: 请求用户信息certificate
     * @params:
     * @return:
     */

    private fun consumption(handwritingEntity: HandwritingEntity?, result: String?, func: String, type: Int, bitmap: Bitmap?) {
        HttpManager.getInstance().request().consumption(userPreferences!!.userId, func, String::class.java, object : MiaoHttpManager.CallbackNetwork<String> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                LogUtils.i(Gson().toJson(entity.body))
                if (type == 2) {
                    val stringSpan = SpanUtils()
                    val resultItems = handwritingEntity!!.databean.textDetections
                    for (i in resultItems.indices) {
                        stringSpan.appendLine(resultItems[i].detectedText)
                    }
                    val resultString = stringSpan.create()
                    realm!!.executeTransaction(Realm.Transaction {
                        //先查找后得到BookEntity对象
                        val docEntity = realm!!.where(DocEntity::class.java).equalTo("fileID", fileId).findFirst()
                        docEntity!!.ocrHandResult = resultString.toString()
                        val intent = Intent(this@OcrActivity, OcrResultActivity::class.java)
                        intent.putExtra("resultText", resultString.toString())
                        intent.putExtra("url", url)
                        intent.putExtra("fileId", fileId)
                        intent.putExtra("ocrType", 2)
                        intent.putExtra("position", position)
                        ActivityUtils.startActivity(intent)
                        commonRequestUserInfoNow()
                        EventBus.getDefault().post(ConsumptionEvent(EventType.HANDWRITING_COUNT_REDUCE))
                    })
                } else {
                    when (type) {
                        1 -> {
                            finishOcr(result, 1, bitmap)
                            commonRequestUserInfoNow()
                            EventBus.getDefault().post(ConsumptionEvent(EventType.OCR_COUNT_REDUCE))
                        }
                        3 -> {
                            finishOcr(result, 3, bitmap)
                            commonRequestUserInfoNow()
                            EventBus.getDefault().post(ConsumptionEvent(EventType.BAIDU_OCR_COUNT_REDUCE))
                        }
                        4 -> {
                            finishOcr(result, 4, bitmap)
                            commonRequestUserInfoNow()
                            EventBus.getDefault().post(ConsumptionEvent(EventType.CARD_COUNT_REDUCE))
                        }
                        else -> {
                            finishOcr(result, 5, bitmap)
                            commonRequestUserInfoNow()
                            EventBus.getDefault().post(ConsumptionEvent(EventType.CARD_COUNT_REDUCE))
                        }
                    }
                }

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
            }

            override fun onError(e: Exception) {
                hideProgressDialog()
                showMessage(R.string.request_failed_alert)

            }
        })
    }

    private fun FailedRecognizedToast() {
        runOnUiThread(Runnable {
            hideProgressDialog()
            showMessage(R.string.recognize_failed_alert)
        })
    }

    private fun FailedToast() {
        runOnUiThread(Runnable {
            hideProgressDialog()
            showMessage(R.string.recognize_failed_alert)
        })
    }

    /**
     * @des: 从数据库读取
     * @params:
     * @return:
     */

    private fun checkTypeInRealm(isShow: Boolean) {
        val ocrModeEntity = realm!!.where(OcrModeEntity::class.java).equalTo("fileId", fileId).findFirst()
        if (ocrModeEntity != null) {
            val viewId = ocrModeEntity.viewId
            chooseType(userPreferences!!.lastOcrViewId, isShow, false)
            LogUtils.i(viewId, type, "xxxx")
        } else {
            chooseType(userPreferences!!.lastOcrViewId, isShow, true)
            LogUtils.i("sp", userPreferences!!.lastOcrViewId, type, "xxxx")
        }

    }


    private fun setTypeToRealm(viewId: Int) {
        userPreferences!!.lastOcrViewId = viewId
        realm!!.executeTransaction { realm ->
            var ocrModeEntity: OcrModeEntity? = realm.where(OcrModeEntity::class.java).equalTo("fileId", fileId).findFirst()
            if (ocrModeEntity == null) {
                ocrModeEntity = realm.createObject(OcrModeEntity::class.java, fileId)
                ocrModeEntity!!.viewId = viewId
            } else {
                ocrModeEntity.viewId = viewId
            }
            LogUtils.i(fileId, type, viewId)
        }
    }

    private fun chooseType(viewId: Int, isShow: Boolean, isSave: Boolean) {
        when (viewId) {
            R.id.ocr_chinese_btn -> {
                this.viewId = viewId
                type = 2
                languageFlag = 0
                if (isShow) {
                    bottomDialog!!.setChinese()
                    handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_chinese))
                    }
                }
            }
            R.id.ocr_chinese_taiwan_btn -> {
                this.viewId = viewId
                type = 21
                languageFlag = 1
                if (isShow) {
                    bottomDialog!!.setChineseTaiwan()
                    handler.post{
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_chinese_taiwan))
                    }
                }
            }
            R.id.ocr_english_btn -> {
                this.viewId = viewId
                type = 1
                languageFlag = 2
                if (isShow) {
                    bottomDialog!!.setEnglish()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_english))
                    }
                }
            }
            R.id.ocr_french_btn -> {
                this.viewId = viewId
                type = 3
                languageFlag = 3
                if (isShow) {
                    bottomDialog!!.setFrench()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_french))
                    }
                }
            }
            R.id.ocr_italian_btn -> {
                this.viewId = viewId
                type = 3
                languageFlag = 4
                if (isShow) {
                    bottomDialog!!.setItalian()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_italian))
                    }
                }
            }
            R.id.ocr_spanish_btn -> {
                this.viewId = viewId
                type = 3
                languageFlag = 5
                if (isShow) {
                    bottomDialog!!.setSpanish()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_spanish))
                    }
                }
            }
            R.id.ocr_portuguese_btn -> {
                this.viewId = viewId
                type = 3
                languageFlag = 6
                if (isShow) {
                    bottomDialog!!.setPortuguese()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_portuguese))
                    }
                }
            }
            R.id.ocr_swedish_btn -> {
                this.viewId = viewId
                type = 3
                languageFlag = 7
                if (isShow) {
                    bottomDialog!!.setSwedish()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_swedish))
                    }
                }
            }
            R.id.ocr_danish_btn -> {
                this.viewId = viewId
                type = 3
                languageFlag = 8
                if (isShow) {
                    bottomDialog!!.setDanish()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_danish))
                    }
                }
            }
            R.id.ocr_russian_btn -> {
                this.viewId = viewId
                type = 4
                languageFlag = 9
                if (isShow) {
                    bottomDialog!!.setRussian()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_russian))
                    }
                }
            }
            R.id.ocr_japaneses_btn -> {
                this.viewId = viewId
                type = 6
                languageFlag = 10
                if (isShow) {
                    bottomDialog!!.setJapanese()
                    handler.post {
                        binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_japaneses))
                    }
                }
            }
            R.id.ocr_confirm_btn -> {
                setTypeToRealm(this.viewId)
                bottomDialog!!.dismiss()
//                if (!isShow) {
                    SetLanguage(languageFlag)
//                }
            }
            R.id.ocr_cancel_btn -> bottomDialog!!.dismiss()
            else -> {
            }
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.imgBack -> finish()
            R.id.etOcrBtn -> {
                Thread(Runnable {
                    requestUserInfoSync()
                    runOnUiThread {
                        if (isHasCount(ocrType)) {
                            val currentTime = Calendar.getInstance().timeInMillis
                            if (currentTime - lastClickTime > MIN_CLICK_DELAY_TIME && !isRun) {
                                lastClickTime = currentTime
                                checkTypeInRealm(false)
                                cropImage()
                            }
                        } else {
                            showNoCountEmptyMessage(ocrType)
                        }
                    }
                }).start()
            }
            R.id.fileOcrChooseBtn -> {
                bottomDialog!!.show()
                checkTypeInRealm(true)
            }
        }
    }

    private fun cropImage() {
        isRun = false
        val cropped = binding.cropImageView.croppedImage
        cropped?.let {
            val points = binding.cropImageView.cropPoints
            x = points[0].toInt()
            y = points[1].toInt()
            width = it.width
            height = it.height
            if (width < 400 && height < 400) {
                bigBitmap = getBigBitmap(cropped)
                judgeExecuteBitmap(bigBitmap)
            } else {
                judgeExecuteBitmap(cropped)
            }
        }
    }

    private fun cloudOcr(access_token: String, image: String, bitmap: Bitmap?, isRealCloudOcr: Boolean) {
        val formBody = FormBody.Builder()
                .add("access_token", access_token)
                .add("image", image)
                .build()
        val request = Request.Builder().url(Constants.BAIDU_OCR_URL)
                .post(formBody).build()

        okHttpClient!!.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.code == 200) {
                    val body = response.body!!.string()
                    LogUtils.e(body)
                    val baiduWordModel = Gson().fromJson(body, BaiduWordModel::class.java)
                    val words_result = baiduWordModel.words_result
                    if (words_result != null) {
                        val stringSpan = SpanUtils()
                        for (wordsResultBean in baiduWordModel.words_result) {
                            stringSpan.appendLine(wordsResultBean.words)
                        }
                        val realm1 = Realm.getDefaultInstance()
                        val string = stringSpan.create().toString()
                        realm1!!.executeTransaction {
                            val docEntity = realm1.where(DocEntity::class.java)
                                    .equalTo("fileID", fileId)
                                    .equalTo("isDelete", 0.toInt())
                                    .findFirst()

                            docEntity!!.ocrResult = string
                        }
                        if (isRealCloudOcr) {
                            consumption(null, string, "cloudocr", 3, bitmap)
                        } else {
                            consumption(null, string, "ocr", 1, bitmap);

                        }

                    } else {
                        runOnUiThread {
                            hideProgressDialog()
                            showMessage(R.string.recognition_defeat)
                        }

                    }

                } else {
                    runOnUiThread {
                        hideProgressDialog()
                        showMessage(R.string.request_failed_alert)
                    }

                }


            }
        })
    }

    private fun judgeExecuteBitmap(bitmap: Bitmap?) {
        when (ocrType) {
            1 -> executeBitmap(bitmap)
            2 -> executeHandwritingBitmap(bitmap)
            else -> requestCloudOcrToken(bitmap, true)

        }
    }

    private fun requestCloudOcrToken(bitmap: Bitmap?, isRealCloudOcr: Boolean) {
        if (isRealCloudOcr) {
            showProgressDialog(true)
        }
        val formBody = FormBody.Builder()
                .add("grant_type", Constants.BAIDU_GRANT_TYPE)
                .add("client_id", Constants.BAIDU_CLIENT_ID)
                .add("client_secret", Constants.BAIDU_CLIENT_SECRET)
                .build()
        val request = Request.Builder().url(Constants.BAIDU_TOKEN_URL)
                .post(formBody).build()

        okHttpClient!!.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                LogUtils.d(e)
                runOnUiThread {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)
                }

            }

            override fun onResponse(call: Call, response: Response) {
                if (response.code == 200) {
                    val json = Gson().fromJson(response.body!!.string(), BaiduTokenModel::class.java)
                    LogUtils.e(json.access_token)
                    val urlEncode = EncodeUtils.base64Encode2String(ImageUtils.bitmap2Bytes(bitmap, Bitmap.CompressFormat.JPEG, 90))
                    cloudOcr(json.access_token, urlEncode, bitmap, isRealCloudOcr)
                } else {
                    runOnUiThread {
                        hideProgressDialog()
                        showMessage(R.string.request_failed_alert)
                    }
                }
            }
        })


    }

    private fun failedDelay(failedText: Int) {

        runOnUiThread(Runnable {
            showMessage(failedText)
            hideProgressDialog()
            recycleBitmap()
            ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
        })

    }

    private fun executeHandwritingBitmap(bitmap: Bitmap?) {
        if (userPreferences!!.isHandwritingGuide) {
            confirmDialog(bitmap)
        } else {
            goHandwriting(bitmap)
        }
    }


    private fun executeBitmap(bitmap: Bitmap?) {
        showProgressDialog(true)
        var needCloudOcr = false
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<String>() {

            override fun doInBackground(): String? {
                var ocrEngine: DOcrEngine? = DOcrEngine(this@OcrActivity)
                try {
                    val doc = Document()
                    //mode 0通用，1多行
                    // 1:英文 2：中英文 21:繁体 3:欧文 4.俄文
                    // 6.日文
                    LogUtils.e(type)
                    val result = ocrEngine!!.startOCR(type, 0)

                    if (1 == result) {
                        val res = ocrEngine.recognize(ImageUtils.bitmap2Bytes(bitmap, Bitmap.CompressFormat.JPEG, 90), doc, false, null, true) // 第一参数：图片数据
                        // 第二参数：传入一个对象
                        // 第三个数据是是否设置模糊判断

                        if (1 == res) {
                            consumption(null, doc.content, "ocr", 1, bitmap);
                            return doc.content
                        } else {
                            ocrFailed(res, bitmap)
                            needCloudOcr = true
                            return null
                        }
                    } else {
                        ocrEngine.closeOCR()
                        needCloudOcr = true
                        ocrFailed(-2, bitmap)
                        return null
                    }

                } catch (e: Exception) {
                    LogUtils.i(e)
                    needCloudOcr = true
                    ocrFailed(-2, bitmap)
                    return null
                } finally {
                    LogUtils.i("finally")
                    ocrEngine!!.finalize()
                    ocrEngine = null
                }

            }

            override fun onSuccess(result: String?) {
                LogUtils.i(result)
                resultText = result
                if (!needCloudOcr) {
                    hideProgressDialog()
                }
              //  recycleBitmap()
            }

            override fun onFail(t: Throwable) {
                super.onFail(t)
                ocrFailed(-2, bitmap)
            }
        })
    }

    private fun recycleBitmap() {
        bigBitmap?.let {
            bigBitmap?.isRecycled?.let {
                if (!it) {
                    bigBitmap?.recycle()
                    bigBitmap = null
                }
            }
        }
    }

    private fun getBigBitmap(src: Bitmap?): Bitmap? {
        if (src == null) {
            LogUtils.i("src is null")
            return src
        }
        var bitmap: Bitmap? = Bitmap.createBitmap(800, 800, Bitmap.Config.ARGB_8888)
        bitmap!!.eraseColor(Color.parseColor("#FFFFFF"))
        try {
            val cv = Canvas(bitmap)
            cv.drawBitmap(src, 0f, 0f, null)
            cv.save()
            cv.restore()
        } catch (e: Exception) {
            bitmap = null
            e.stackTrace
        }

        return bitmap
    }

    private fun SetLanguage(flag: Int){
        when(flag){
            0 ->{
                bottomDialog!!.setChinese()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_chinese))
                }
            }
            1->{
                bottomDialog!!.setChineseTaiwan()
                handler.post{
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_chinese_taiwan))
                }
            }
            2->{
                bottomDialog!!.setEnglish()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_english))
                }
            }
            3->{
                bottomDialog!!.setFrench()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_french))
                }
            }
            4->{
                bottomDialog!!.setItalian()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_italian))
                }
            }
            5->{
                bottomDialog!!.setSpanish()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_spanish))
                }
            }
            6->{
                bottomDialog!!.setPortuguese()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_portuguese))
                }
            }
            7->{
                bottomDialog!!.setSwedish()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_swedish))
                }
            }
            8->{
                bottomDialog!!.setDanish()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_danish))
                }
            }
            9->{
                bottomDialog!!.setRussian()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_russian))
                }
            }
            10->{
                bottomDialog!!.setJapanese()
                handler.post {
                    binding.fileOcrChooseBtn.setText(resources.getString(R.string.et_ocr_japaneses))
                }
            }

        }
    }

    override fun onDestroy() {
        super.onDestroy()
        recycleBitmap()
        realm?.close()

    }

    private fun ocrFailed(code: Int, bitmap: Bitmap?) {
//        runOnUiThread(Runnable {
//            if (code == 2) {
//                failedDelay(R.string.ocr_language_failed)
//            } else if (code == 3) {
//                failedDelay(R.string.ocr_blur_failed)
//            } else {
//                failedDelay(R.string.ocr_failed)
//            }
//        })
        requestCloudOcrToken(bitmap, false)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        super.onActivityResult(requestCode, resultCode, data)

    }


    override fun onBackPressed() {
        if (!isRun) {
            super.onBackPressed()
        }
    }

    companion object {


        val MIN_CLICK_DELAY_TIME = 3000
    }

}