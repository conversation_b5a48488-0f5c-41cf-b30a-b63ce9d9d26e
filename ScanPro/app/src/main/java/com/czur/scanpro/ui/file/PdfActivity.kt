package com.czur.scanpro.ui.file

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.adapter.PdfAdapter
import com.czur.scanpro.databinding.ActivityMyPdfBinding
import com.czur.scanpro.databinding.ActivityOtherResultBinding
import com.czur.scanpro.entity.realm.PdfEntity
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.utils.PermissionUtil
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import io.realm.Realm
import io.realm.Sort
import java.util.*

/**
 * Created by Yz on 2019/4/15.
 * Email：<EMAIL>
 */
class PdfActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityMyPdfBinding by lazy{
        ActivityMyPdfBinding.inflate(layoutInflater)
    }


    private var myPdfBackBtn: ImageView? = null
    private var myPdfTopSelectAllBtn: TextView? = null
    private var myPdfTitle: TextView? = null
    private var myPdfCancelBtn: TextView? = null
    private var myPdfDeleteRl: RelativeLayout? = null
    private var pdfEmptyRl: RelativeLayout? = null
    private var myPdfMultiSelectBtn: RelativeLayout? = null
    private var myPdfRecyclerView: RecyclerView? = null
    private var bookPdfAdapter: PdfAdapter? = null
    private var pdfEntities: List<PdfEntity>? = null
    private var myPdfBottomLl: LinearLayout? = null
    private var isCheckedMap: LinkedHashMap<String, Boolean>? = null
    private var realm: Realm? = null

    /**
     * @des: 长按监听
     * @params:
     * @return:
     */

    private val onItemLongClickListener = PdfAdapter.OnItemLongClickListener { _, _, isCheckedMap, totalSize ->
        multiSelect()
        checkSize(isCheckedMap, totalSize)
        myPdfTitle!!.setText(R.string.select_one_pdf)
        bookPdfAdapter!!.refreshData(true)
    }

    /**
     * @des: item点击监听
     * @params:
     * @return:
     */
    private val onItemClickListener = PdfAdapter.OnItemClickListener { pdfEntity, _, checkBox ->
        if (isMultiSelect) {
            checkBox.isChecked = !checkBox.isChecked
        } else {
            requestPermission(pdfEntity)
        }
    }

    /**
     * @des: item选择监听
     * @params:
     * @return:
     */
    private val onItemCheckListener = PdfAdapter.OnItemCheckListener { _, _, isCheckedMap, totalSize ->
        <EMAIL> = isCheckedMap
        LogUtils.i(Gson().toJson(<EMAIL>))
        //如果选中一个 文案变为已选中1个
        if (isCheckedMap.size == 1) {
            showDelete()
            myPdfTitle!!.setText(R.string.select_one_pdf)
        } else if (isCheckedMap.size > 1) {
            showDelete()
            myPdfTitle!!.text = String.format(getString(R.string.select_num_pdf), isCheckedMap.size.toString() + "")
        } else {
            darkDelete()
            if (isMultiSelect) {
                myPdfTitle!!.text = String.format(getString(R.string.select_num_pdf), isCheckedMap.size.toString() + "")
            }
        }
        //如果选择不是全部Item  text变为取消全选
        checkSize(isCheckedMap, totalSize)
    }


    private var isMultiSelect = false
    private var isSelectAll = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_my_pdf)
        setContentView(binding.root)
        initComponent()
        initRecyclerView()
        registerEvent()
    }

    private fun initComponent() {
        pdfEmptyRl = findViewById<RelativeLayout>(R.id.pdf_empty_rl)
        myPdfDeleteRl = findViewById<RelativeLayout>(R.id.myPdfDeleteRl)
        myPdfBottomLl = findViewById<LinearLayout>(R.id.my_pdf_bottom_ll)
        myPdfBackBtn = findViewById<ImageView>(R.id.my_pdf_back_btn)
        myPdfTopSelectAllBtn = findViewById<com.czur.scanpro.ui.component.MediumBoldTextView>(R.id.my_pdf_top_select_all_btn)
        myPdfTitle = findViewById<com.czur.scanpro.ui.component.MediumBoldTextView>(R.id.my_pdf_title)
        myPdfCancelBtn = findViewById<com.czur.scanpro.ui.component.MediumBoldTextView>(R.id.my_pdf_cancel_btn)
        myPdfMultiSelectBtn = findViewById<RelativeLayout>(R.id.my_pdf_multi_select_btn)
        myPdfRecyclerView = findViewById<RecyclerView>(R.id.my_pdf_recyclerView)
        myPdfTitle!!.setText(R.string.my_pdf)
        realm = Realm.getDefaultInstance()
        darkDelete()
    }

    private fun registerEvent() {
        myPdfTopSelectAllBtn!!.setOnClickListener(this)
        myPdfCancelBtn!!.setOnClickListener(this)
        myPdfMultiSelectBtn!!.setOnClickListener(this)
        myPdfBackBtn!!.setOnClickListener(this)
        myPdfDeleteRl!!.setOnClickListener(this)
    }

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private fun isShowEmptyPrompt() {
        if (pdfEntities!!.isEmpty()) {
            myPdfRecyclerView!!.visibility = View.GONE
            pdfEmptyRl!!.visibility = View.VISIBLE
        } else {
            myPdfRecyclerView!!.visibility = View.VISIBLE
            pdfEmptyRl!!.visibility = View.GONE
        }
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private fun initRecyclerView() {
        pdfEntities = ArrayList<PdfEntity>()
        isCheckedMap = LinkedHashMap()
        pdfEntities = realm!!.where(PdfEntity::class.java).findAll().sort("createTime", Sort.DESCENDING)
        scanSdPdfIsExist()

        bookPdfAdapter = PdfAdapter(this, pdfEntities, false)
        bookPdfAdapter!!.setOnItemCheckListener(onItemCheckListener)
        bookPdfAdapter!!.setOnItemClickListener(onItemClickListener)
        bookPdfAdapter!!.setOnItemLongClickListener(onItemLongClickListener)
        myPdfRecyclerView!!.adapter = bookPdfAdapter
        myPdfRecyclerView!!.setHasFixedSize(true)
        myPdfRecyclerView!!.layoutManager =
            LinearLayoutManager(this)

        isShowEmptyPrompt()
    }

    /**
     * @des: 扫描本地PDF是否存在，不存在删除数据库
     * @params:
     * @return:
     */

    private fun scanSdPdfIsExist() {
        val pdfExistPaths = ArrayList<String>()
        for (i in pdfEntities!!.indices) {
            val pdfPath = pdfEntities!![i].pdfPath

            ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                override fun doInBackground(): Void? {
                    if (!FileUtils.isFileExists(pdfPath)) {
                        LogUtils.i("pdf not exist$pdfPath")
                        pdfExistPaths.add(pdfPath)
                        runOnUiThread(Runnable {
                            realm!!.executeTransaction {
                                //先查找后得到PageEntity对象
                                realm!!.where(PdfEntity::class.java).equalTo("pdfPath", pdfPath).findAll().deleteFirstFromRealm()
                            }
                        })
                    } else {
                        LogUtils.i("pdf exist$pdfPath")
                    }
                    return null
                }

                override fun onSuccess(result: Void?) {

                }
            })
        }


    }

    /**
     * @des: 请求权限
     * @params:[]
     * @return:void
     */
    private fun requestPermission(pdfEntity: PdfEntity) {

        PermissionUtils.permission(PermissionUtil.getStoragePermission()[0],PermissionUtil.getStoragePermission()[1])
                .rationale { _, shouldRequest ->
                    showMessage(R.string.denied_PDF_preview)
                    shouldRequest.again(true)
                }
                .callback(object : PermissionUtils.FullCallback {
                    override fun onGranted(permissionsGranted: List<String>) {
                        LogUtils.i(permissionsGranted)
                        val intent = Intent(this@PdfActivity, PdfPreviewActivity::class.java)
                        intent.putExtra("pdfPath", pdfEntity.pdfPath)
                        intent.putExtra("pdfName", pdfEntity.pdfName)
                        ActivityUtils.startActivity(intent)
                    }

                    override fun onDenied(permissionsDeniedForever: List<String>,
                                          permissionsDenied: List<String>) {
                        showMessage(R.string.denied_PDF_preview)
                        LogUtils.i(permissionsDeniedForever, permissionsDenied)
                    }
                })
                .request()
    }

    private fun checkSize(isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int) {
        if (isCheckedMap.size < totalSize) {
            myPdfTopSelectAllBtn!!.setText(R.string.select_all)
            isSelectAll = false
        } else {
            myPdfTopSelectAllBtn!!.setText(R.string.not_select_all)
            isSelectAll = true
        }
    }

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private fun multiSelect() {
        if (Validator.isNotEmpty(pdfEntities)) {
            isMultiSelect = !isMultiSelect
            bookPdfAdapter!!.refreshData(isMultiSelect)
            if (isMultiSelect) {
                showSelectTopBar()
            } else {
                hideSelectTopBar()
            }
        }
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private fun selectAll() {
        if (!isSelectAll) {
            for (i in pdfEntities!!.indices) {

                if (!isCheckedMap!!.containsKey(pdfEntities!![i].pdfName)) {
                    isCheckedMap!![pdfEntities!![i].pdfName] = true
                }
            }
            myPdfBottomLl!!.visibility = View.VISIBLE
            myPdfTopSelectAllBtn!!.setText(R.string.not_select_all)
            isSelectAll = true

        } else {
            isCheckedMap!!.clear()
            isCheckedMap = LinkedHashMap()
            myPdfTopSelectAllBtn!!.setText(R.string.select_all)
            isSelectAll = false
        }
        myPdfTitle!!.text = String.format(getString(R.string.select_num_pdf), isCheckedMap!!.size.toString() + "")
        LogUtils.eTag("select all", Gson().toJson(isCheckedMap))
        bookPdfAdapter!!.refreshData(pdfEntities, true, isCheckedMap)
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private fun cancelEvent() {
        darkDelete()
        myPdfBottomLl!!.visibility = View.GONE
        isMultiSelect = false
        isSelectAll = false
        isCheckedMap!!.clear()
        isCheckedMap = LinkedHashMap()
        bookPdfAdapter!!.refreshData(pdfEntities, false, isCheckedMap)
        hideSelectTopBar()
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private fun showSelectTopBar() {
        myPdfBottomLl!!.visibility = View.VISIBLE
        myPdfMultiSelectBtn!!.visibility = View.GONE
        myPdfBackBtn!!.visibility = View.GONE
        myPdfCancelBtn!!.visibility = View.VISIBLE
        myPdfTopSelectAllBtn!!.visibility = View.VISIBLE
        myPdfCancelBtn!!.setText(R.string.quit)
        myPdfTopSelectAllBtn!!.setText(R.string.select_all)
        myPdfTitle!!.text = String.format(getString(R.string.select_num_pdf), isCheckedMap!!.size.toString() + "")
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private fun hideSelectTopBar() {
        myPdfBottomLl!!.visibility = View.GONE
        myPdfMultiSelectBtn!!.visibility = View.VISIBLE
        myPdfBackBtn!!.visibility = View.VISIBLE
        myPdfCancelBtn!!.visibility = View.GONE
        myPdfTopSelectAllBtn!!.visibility = View.GONE
        myPdfTitle!!.setText(R.string.my_pdf)
    }

    /**
     * @des: 删除PDF
     * @params:
     * @return:
     */

    private fun deletePdf() {

        val needDeletePaths = ArrayList<String>()
        for (pdfName in isCheckedMap!!.keys) {
            //得到待删除pdf paths
            val pdfEntity = realm!!.where(PdfEntity::class.java).equalTo("pdfName", pdfName).findAll()
            LogUtils.i(pdfEntity[0]!!.pdfPath)
            needDeletePaths.add(pdfEntity[0]!!.pdfPath)
        }
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                //删除sd卡上的pdf
                for (needDeletePath in needDeletePaths) {
                    FileUtils.delete(needDeletePath)
                }
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })

        //删除数据库中的pdf
        realm!!.executeTransaction {
            //先查找后得到PageEntity对象
            for (pdfName in isCheckedMap!!.keys) {
                //先查找后得到PageEntity对象
                realm!!.where(PdfEntity::class.java).equalTo("pdfName", pdfName).findAll().deleteFirstFromRealm()
            }
        }

        //刷新pdf页面
        cancelEvent()
        isShowEmptyPrompt()

    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.my_pdf_back_btn -> ActivityUtils.finishActivity(this)
            R.id.my_pdf_cancel_btn -> cancelEvent()
            R.id.my_pdf_top_select_all_btn -> selectAll()
            R.id.my_pdf_multi_select_btn -> multiSelect()
            R.id.myPdfDeleteRl -> confirmDeleteDialog()
            else -> {
            }
        }
    }

    private fun showDelete() {
        binding.myPdfDeleteRl.isClickable = true
        binding.myPdfDeleteRl.isEnabled = true

        binding.myPdfDeleteImg.isSelected = true
        binding.myPdfDeleteTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkDelete() {
        binding.myPdfDeleteRl.isClickable = false
        binding.myPdfDeleteRl.isEnabled = false

        binding.myPdfDeleteImg.isSelected = false
        binding.myPdfDeleteTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private fun confirmDeleteDialog() {
        val builder = ScanProCommonPopup.Builder(this@PdfActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.confirm_delete))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            dialog.dismiss()
            deletePdf()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    override fun onDestroy() {
        super.onDestroy()
        realm!!.close()
    }
}