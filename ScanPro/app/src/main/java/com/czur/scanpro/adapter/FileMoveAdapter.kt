package com.czur.scanpro.adapter

import android.content.Context
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.UriUtils
import com.czur.scanpro.R
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.fresco.cache.CustomImageRequest
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.drawee.view.SimpleDraweeView
import com.facebook.imagepipeline.request.ImageRequestBuilder
import io.realm.Realm
import io.realm.Sort


/**
 * Created by Yz on 2019/1/5.
 * Email：<EMAIL>
 */
class FileMoveAdapter
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<CategoryEntity>?) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    val realm = Realm.getDefaultInstance()
    var categoryName: String? = null

    companion object {
        private const val ITEM_TYPE_CATEGORY = 0
    }



    fun refreshData(Categorys: List<CategoryEntity>) {
        this.datas = Categorys
        notifyDataSetChanged()

    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_move_folder, parent, false)
        return CategoryHolder(view)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        if (holder is CategoryHolder) {
            holder.mItem = datas!![position]


            holder.categoryName.text = holder.mItem!!.categoryName
            val results = realm.where(DocEntity::class.java)
                    .equalTo("isDelete", 0.toInt())
                    .equalTo("categoryName", holder.mItem?.categoryName)
                    .findAll()
            val first = realm.where(DocEntity::class.java)
                    .equalTo("isDelete", 0.toInt())
                    .equalTo("categoryName", holder.mItem?.categoryName)
                    .sort("createTime", Sort.ASCENDING)
                    .findFirst()


            if (first != null) {
                val controller = Fresco.newDraweeControllerBuilder()
                        .setImageRequest(CustomImageRequest(ImageRequestBuilder.newBuilderWithSource(UriUtils.file2Uri(FileUtils.getFileByPath(first.processSmallImagePath)))))
                        .setOldController(holder.categoryImage.controller)
                        .build()
                holder.categoryImage.controller = controller

            } else {
                val controller = Fresco.newDraweeControllerBuilder()
                        .setImageRequest(CustomImageRequest(ImageRequestBuilder.newBuilderWithResourceId(R.mipmap.default_category_icon)))
                        .setOldController(holder.categoryImage.controller)
                        .build()
                holder.categoryImage.controller = controller

            }

            holder.itemView.setOnClickListener {
                if (onItemClickListener != null) {
                    onItemClickListener!!.onCategoryClick(holder.mItem, position)
                }
            }


        }

    }

    override fun getItemViewType(position: Int): Int = ITEM_TYPE_CATEGORY

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int {
        return datas!!.size
    }


    private inner class CategoryHolder internal constructor(val mView: View) : RecyclerView.ViewHolder(mView) {
        internal var mItem: CategoryEntity? = null
        internal var categoryItem: ConstraintLayout
        internal var categoryName: TextView
        internal var categoryImage: SimpleDraweeView


        init {
            categoryItem = mView.findViewById<View>(R.id.item_file_move_folder_rl) as ConstraintLayout
            categoryName = mView.findViewById<View>(R.id.item_file_move_folder_tv) as TextView
            categoryImage = mView.findViewById<View>(R.id.item_file_move_folder_img) as SimpleDraweeView
        }


    }


    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    interface OnItemClickListener {
        fun onCategoryClick(CategoryEntity: CategoryEntity?, position: Int)
    }

    private var onItemClickListener: OnItemClickListener? = null


}
