package com.czur.scanpro.ui.account

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityChangePasswordBinding
import com.czur.scanpro.databinding.ActivityResetNameBinding
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.MD5Utils
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator

/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class UserChangePasswordActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityChangePasswordBinding by lazy{
        ActivityChangePasswordBinding.inflate(layoutInflater)
    }



    private var currentPasswordHasContent = false
    private var firstPasswordHasContent = false
    private var httpManager: HttpManager? = null
    private var userPreferences: UserPreferences? = null
    private var currentTime: Long = 0

    private val currentPswTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            if (s.length > 0) {
                currentPasswordHasContent = true
            } else {
                currentPasswordHasContent = false
            }

            checkUpdatePasswordToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            if (s.length > 0) {
                currentPasswordHasContent = true
            } else {
                currentPasswordHasContent = false
            }

            checkUpdatePasswordToClick()
        }
    }
    private val firstPswTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            if (s.length > 0) {
                firstPasswordHasContent = true
            } else {
                firstPasswordHasContent = false
            }

            checkUpdatePasswordToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            firstPasswordHasContent = s.isNotEmpty()
            checkUpdatePasswordToClick()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_change_password)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {



        httpManager = HttpManager.getInstance()
        userPreferences = UserPreferences.getInstance(this)
        binding.registerTopBar.normalTitle.setText(R.string.user_change_password)


    }

    private fun registerEvent() {
        binding.oldPswEdt.addTextChangedListener(currentPswTextWatcher)
        binding.newPswEdt.addTextChangedListener(firstPswTextWatcher)
        binding.registerTopBar.normalBackBtn.setOnClickListener(this)
        binding.nextStepBtn.setOnClickListener(this)
        binding.nextStepBtn.isSelected = false
        binding.nextStepBtn.isClickable = false

    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            R.id.nextStepBtn -> updatePassword()
            else -> {
            }
        }
    }

    /**
     * @des: 修改密码
     * @params:
     * @return:
     */

    private fun updatePassword() {
        val current = binding.oldPswEdt.text.toString()
        val nPwd = binding.newPswEdt.text.toString()

        if (current.length <= 5 || nPwd.length <= 5) {
            showMessage(R.string.login_alert_pwd_length)
        } else if (current == nPwd) {
            showMessage(R.string.toast_input_new_pwd)
        } else {
            currentTime = System.currentTimeMillis()
            KeyboardUtils.hideSoftInput(this)
            httpManager!!.requestPassport().updatePwd(userPreferences!!.imei, Constants.SCAN_PRO,
                    userPreferences!!.channel, userPreferences!!.userId, userPreferences!!.token,
                    userPreferences!!.userId, MD5Utils.md5(current), MD5Utils.md5(nPwd), String::class.java, object : MiaoHttpManager.Callback<String> {
               override fun onStart() {
                  showProgressDialog(false)
                }

                override  fun onResponse(entity: MiaoHttpEntity<String>) {
                    hideProgressDialog()
                    LogUtils.i(Gson().toJson(entity))
                    userPreferences!!.loginPassword = nPwd
                    ActivityUtils.finishActivity(this@UserChangePasswordActivity)
                }

                override fun onFailure(entity: MiaoHttpEntity<String>) {
                    hideProgressDialog()

                    if (entity.getCode() === MiaoHttpManager.STATUS_OLD_PWD_FAIL) {
                        showMessage(R.string.toast_old_pwe_fail)
                    } else {
                        showMessage(R.string.request_failed_alert)
                    }

                }

                override fun onError(e: Exception) {
                    showMessage(R.string.request_failed_alert)
                }
            })
        }
    }






    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkUpdatePasswordToClick() {

        if (firstPasswordHasContent && currentPasswordHasContent) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
           binding.nextStepBtn.background = drawable
           binding.nextStepBtn.isSelected = true
           binding.nextStepBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
           binding.nextStepBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
           binding.nextStepBtn.background = drawable
           binding.nextStepBtn.isSelected = false
           binding.nextStepBtn.setTextColor(resources.getColor(R.color.white))
           binding.nextStepBtn.isClickable = false
        }
    }
}
