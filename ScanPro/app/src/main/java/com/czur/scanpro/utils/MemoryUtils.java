package com.czur.scanpro.utils;

import android.app.ActivityManager;
import android.content.Context;

import com.blankj.utilcode.util.LogUtils;

public class MemoryUtils {
    public static float getAvailMemoryPercent(Context context) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
        am.getMemoryInfo(mi);
        LogUtils.d("可用内存百分比", mi.availMem * 1.0f / mi.totalMem);
        return mi.availMem * 1.0f / mi.totalMem;
    }

    public static int getAvailMemory(Context context) {
        int kb = 1024;
        int mb = kb * 1024;

        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
        am.getMemoryInfo(mi);
        // mi.availMem; 当前系统的可用内存
        LogUtils.d(mi.availMem);
        return (int) mi.availMem / mb;
    }

}
