package com.czur.scanpro.utils.sharesdk

import android.content.Context
import android.graphics.Bitmap
import android.text.TextUtils
import android.util.Log
import cn.sharesdk.framework.Platform
import cn.sharesdk.framework.Platform.ShareParams
import cn.sharesdk.framework.PlatformActionListener
import cn.sharesdk.framework.ShareSDK
import cn.sharesdk.sina.weibo.SinaWeibo
import cn.sharesdk.tencent.qq.QQ
import cn.sharesdk.tencent.qzone.QZone
import cn.sharesdk.wechat.friends.Wechat
import cn.sharesdk.wechat.moments.WechatMoments
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.R
import com.mob.MobSDK
import java.util.*

object ShareSDKUtils {

    fun init(context: Context) {
        Log.i("Jason", "ShareSDKUtils.init()")
        MobSDK.init(context.applicationContext)
        MobSDK.submitPolicyGrantResult(true, null)
    }

    fun share(params: ShareSDKParams) {
        params.mPlatform ?: return
        val sp = ShareParams()
        params.mSite?.let {
            sp.site = it
        }
        params.mTitle?.let {
            sp.title = it
        }
        params.mTitleUrl?.let {
            sp.titleUrl = it
        }
        params.mUrl?.let {
            sp.url = it
        }
        params.mImageData?.let {
            sp.imageData = it
        }
        params.mImageUrl?.let {
            sp.imageUrl = it
        }
        params.mText?.let {
            sp.text = it
        }
        params.mShareType?.let {
            sp.shareType = it.value
        }
        val platform = when(params.mPlatform) {
            ShareSDKPlatforms.WECHAT -> ShareSDK.getPlatform(Wechat.NAME)
            ShareSDKPlatforms.WECHAT_MOMENTS -> ShareSDK.getPlatform(WechatMoments.NAME)
            ShareSDKPlatforms.QQ -> ShareSDK.getPlatform(QQ.NAME)
            ShareSDKPlatforms.QZONE -> ShareSDK.getPlatform(QZone.NAME)
            ShareSDKPlatforms.WEIBO -> ShareSDK.getPlatform(SinaWeibo.NAME)
            else -> return
        }
        platform.platformActionListener = object : PlatformActionListener {
            override fun onComplete(platform: Platform?, i: Int, hashMap: java.util.HashMap<String, Any>?) {
                params.mCallback?.onComplete()
            }

            override fun onError(platform: Platform?, i: Int, throwable: Throwable?) {
                params.mCallback?.onError()
            }

            override fun onCancel(platform: Platform?, i: Int) {
                params.mCallback?.onCancel()
            }
        }
        platform.share(sp)
    }

    private var thirdPartyCallback: ThirdPartyCallback? = null
    private var channel: String? = null

    fun thirdPartyLogin(platform: ShareSDKPlatforms, channel: String, thirdPartyCallback: ThirdPartyCallback?) {
        this.channel = channel
        this.thirdPartyCallback = thirdPartyCallback
        val plat = when(platform) {
            ShareSDKPlatforms.WECHAT -> ShareSDK.getPlatform(Wechat.NAME)
            ShareSDKPlatforms.QQ -> ShareSDK.getPlatform(QQ.NAME)
            ShareSDKPlatforms.WEIBO -> ShareSDK.getPlatform(SinaWeibo.NAME)
            else -> return
        }
        if (plat.isAuthValid()) {
            plat.removeAccount(true);
        }
        plat.SSOSetting(false)
        plat.platformActionListener = platformActionListener
//        plat.authorize()
        plat.showUser(null)
    }

    private var currentTimeMillis: Long = 0

    private val platformActionListener: PlatformActionListener = object : PlatformActionListener {
        override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {
            LogUtils.i(
                platform.name, platform.db["unionid"], platform.db.userId, platform.db.token
            )
            if (currentTimeMillis != 0L && System.currentTimeMillis() - currentTimeMillis <= 1600) {
                LogUtils.i("xxxxxxxxxxxxxxxxxxxx")
                return
            }
            currentTimeMillis = System.currentTimeMillis()
            //            hideProgressDialog();
            thirdPartyCallback?.showLongMessage("授权成功")
            val platformName = platform.name
            val openId = if (platform.name == Wechat.NAME) platform.db["unionid"] else platform.db.userId
            val token = platform.db.token
            // RefreshToken 微信和微博有
            val refreshToken = platform.db["refresh_token"]
            LogUtils.i(
                platformName
                        + "///success UserID///" + openId
                        + "///success token///" + token
            )
            if (TextUtils.isEmpty(channel)) {
                thirdPartyCallback?.getChannel(openId, token, platformName, true, null, null, refreshToken)
            } else {
                thirdPartyCallback?.loginByThirdParty(platformName, openId, token, refreshToken)
            }
        }

        override fun onError(platform: Platform, i: Int, throwable: Throwable) {
            thirdPartyCallback?.showMessage(R.string.request_third_party_failed_alert)
            LogUtils.i("$i///$throwable")
        }

        override fun onCancel(platform: Platform, i: Int) {
            thirdPartyCallback?.showLongMessage("授权取消")
        }
    }

    fun getPlatform(platName: String): ShareSDKPlatforms? {
        return when(platName) {
            Wechat.NAME -> ShareSDKPlatforms.WECHAT
            QQ.NAME -> ShareSDKPlatforms.QQ
            SinaWeibo.NAME -> ShareSDKPlatforms.WEIBO
            else -> null
        }
    }
}

interface ThirdPartyCallback {
    fun showLongMessage(message: String)

    fun showMessage(messageRes: Int)

    fun getChannel(
        userId: String,
        token: String,
        platformNname: String,
        isThirdParty: Boolean,
        mobileMail: String?,
        pwd: String?,
        refreshToken: String
    )

    fun loginByThirdParty(platformName: String, openId: String, token: String, refreshToken: String)
}

class ShareSDKParams {

    var mPlatform: ShareSDKPlatforms? = null

    fun setPlatform(platform: ShareSDKPlatforms): ShareSDKParams {
        mPlatform = platform
        return this
    }

    var mShareType: ShareSDKType? = null

    fun setShareType(shareType: ShareSDKType?): ShareSDKParams {
        mShareType = shareType
        return this
    }

    var mImageUrl: String? = null

    fun setImageUrl(imageUrl: String?): ShareSDKParams {
        mImageUrl = imageUrl
        return this
    }

    var mSite: String? = null

    fun setSite(site: String?): ShareSDKParams {
        mSite = site
        return this
    }

    var mTitle: String? = null

    fun setTitle(title: String?): ShareSDKParams {
        mTitle = title
        return this
    }

    var mTitleUrl: String? = null

    fun setTitleUrl(titleUrl: String?): ShareSDKParams {
        mTitleUrl = titleUrl
        return this
    }

    var mUrl: String? = null

    fun setUrl(url: String?): ShareSDKParams {
        mUrl = url
        return this
    }

    var mImageData: Bitmap? = null

    fun setImageData(imageData: Bitmap?): ShareSDKParams {
        mImageData = imageData
        return this
    }

    var mText: String? = null

    fun setText(text: String?): ShareSDKParams {
        mText = text
        return this
    }

    var mCallback: ShareSDKCallback? = null

    fun setCallback(callback: ShareSDKCallback?): ShareSDKParams {
        mCallback = callback
        return this
    }
}

interface ShareSDKCallback {
    fun onComplete()//(platform: Platform?, i: Int, hashMap: HashMap<String, Any>?)

    fun onError()//(platform: Platform?, i: Int, throwable: Throwable?)

    fun onCancel()//(platform: Platform?, i: Int)
}

enum class ShareSDKPlatforms {
    WECHAT,
    WECHAT_MOMENTS,
    QQ,
    QZONE,
    WEIBO
}

enum class ShareSDKType(val value: Int) {
    SHARE_IMAGE(2),
    SHARE_WEBPAGE(4),
    SHARE_TEXT(1)
}