package com.czur.scanpro.ui.file

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import android.widget.EditText
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.scanpro.R
import com.czur.scanpro.adapter.FileMoveAdapter
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityFileMarkBinding
import com.czur.scanpro.databinding.ActivityFileMoveBinding
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.NoHintEditText
import com.czur.scanpro.ui.component.popup.AddCategoryPopup
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import io.realm.Realm
import io.realm.Sort
import java.text.SimpleDateFormat
import java.util.*


class FileMoveActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityFileMoveBinding by lazy{
        ActivityFileMoveBinding.inflate(layoutInflater)
    }

    private var categoryEntities: List<CategoryEntity> = java.util.ArrayList<CategoryEntity>()
    private var realm: Realm? = null
    private var moveAdapter: FileMoveAdapter? = null
    private var fileIds: List<String>? = null
    private var categoryName: String? = null
    private var dialogEdt1: EditText? = null
    private var finalCategoryName: String? = null
    private var finalCategoryId: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this,true)
        setContentView(R.layout.activity_file_move)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        categoryName = intent.getStringExtra("categoryName")
        fileIds = intent.getStringArrayListExtra("fileIds")
        LogUtils.e(Gson().toJson(fileIds))
        realm = Realm.getDefaultInstance()
        realm!!.addChangeListener {
            categoryEntities = realm!!.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).sort("createTime", Sort.DESCENDING).notEqualTo("categoryName", categoryName).findAll()
            binding.moveEmptyLayout.visibility = if (categoryEntities.isEmpty()) View.VISIBLE else View.GONE
            moveAdapter!!.refreshData(categoryEntities)
        }
        categoryEntities = realm!!.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).sort("createTime", Sort.DESCENDING).notEqualTo("categoryName", categoryName).findAll()
        binding.moveEmptyLayout.visibility = if (categoryEntities.isEmpty()) View.VISIBLE else View.GONE
        initRecyclerView()
    }

    /**
     *
     * 初始化列表
     * @param: []
     * @return: []
     */

    private fun initRecyclerView() {
        moveAdapter = FileMoveAdapter(this@FileMoveActivity, categoryEntities)
        moveAdapter!!.setOnItemClickListener(object : FileMoveAdapter.OnItemClickListener {
            override fun onCategoryClick(categoryEntity: CategoryEntity?, position: Int) {
                realm!!.executeTransaction {
                    for (fileId in fileIds.orEmpty()) {
                        realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("fileID", fileId).findFirst()!!.run {
                            categoryName = categoryEntity!!.categoryName
                            categoryID = categoryEntity.categoryID
                            isDirty = 1
                            updateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(System.currentTimeMillis()))
                        }
                    }
                    ActivityUtils.finishActivity(this@FileMoveActivity)
                }

            }
        })
        binding.fileMoveRecyclerView.run {
            adapter = moveAdapter
            setHasFixedSize(true)
            layoutManager =
                LinearLayoutManager(this@FileMoveActivity)
        }

    }

    private fun registerEvent() {
        binding.fileMoveCreateBtn.setOnClickListener(this)
        binding.fileMoveBackBtn.setOnClickListener(this)
    }


    override fun onClick(v: View) {
        when (v.id) {
            R.id.fileMoveCreateBtn -> {
                showAddFilePopup()
            }
            R.id.fileMoveBackBtn -> ActivityUtils.finishActivity(this)
            else -> {
            }
        }
    }

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private fun showAddFilePopup() {
        getNewCategoryName()
        val builder = AddCategoryPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setTitle(getString(R.string.add_file))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            realm!!.executeTransaction {
                if (!EtUtils.containsEmoji(dialogEdt1!!.text.toString())) {
                    createCategory()
                    startAutoSync()

                } else {
                    ToastUtils.showShort(R.string.nickname_toast_symbol)
                }
            }

            dialog.dismiss()
        })
        val commonPopup = builder.create()
        dialogEdt1 = commonPopup.window?.findViewById<NoHintEditText>(R.id.edt)
        dialogEdt1!!.hint = finalCategoryName
        commonPopup.show()
    }
    private fun createCategory() {
        val categoryEntity = realm!!.createObject(CategoryEntity::class.java, finalCategoryId)
        categoryEntity.userID = getUserIdIsLogin()
        categoryEntity.isDirty = 1
        categoryEntity.categoryName = if (dialogEdt1!!.text.toString().isNullOrEmpty()) finalCategoryName else dialogEdt1!!.text.toString()
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        val curDate = formatter.format(Date(System.currentTimeMillis()))
        categoryEntity.createTime = curDate
        categoryEntity.updateTime = curDate
        finalCategoryName = null

    }
    private fun getNewCategoryName() {
        if (finalCategoryName.isNullOrEmpty()) {
            var i = 0
            val bookName = Constants.CATEGORY_DEFAULT_NAME
            var finalName = bookName
            val realm = Realm.getDefaultInstance()
            var sameCategory = realm.where(CategoryEntity::class.java)
                    .equalTo("categoryName", Constants.CATEGORY_DEFAULT_NAME)
                    .equalTo("isDelete", 0.toInt())
                    .findFirst()
            while (Validator.isNotEmpty(sameCategory)) {
                val current = System.currentTimeMillis()
                i++
                finalName = bookName + i
                sameCategory = realm.where(CategoryEntity::class.java)
                        .equalTo("categoryName", finalName)
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                LogUtils.i(System.currentTimeMillis() - current)
            }
            finalCategoryId = UUID.randomUUID().toString()

            finalCategoryName = finalName

        }
    }
    override fun onBackPressed() {
        super.onBackPressed()
//        ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

    }


}