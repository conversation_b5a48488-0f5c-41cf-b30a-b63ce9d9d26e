package com.czur.scanpro.ui.base

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.NetworkUtils
import com.czur.scanpro.R

/**
 * Created by Yz on 2019/3/19.
 * Email：<EMAIL>
 */
class WebViewActivity : BaseActivity(), View.OnClickListener {

    private val PRIVACY_AGREEMENT = "https://passport.czur.cc/privacy_part.html"

    private var webContainer: FrameLayout? = null
    private var webView: WebView? = null
    private var webviewBackBtn: ImageView? = null
    private var webviewTitleTv: TextView? = null
    private var title: String? = null
    private var url: String = PRIVACY_AGREEMENT
    private var reloadWebviewRl: RelativeLayout? = null
    private var reloadBtn: TextView? = null
    private var isSuccess = false
    private var isError = false
    private var isLocal = false

    private var isFirstFinish = true
    private var handler: WeakHandler? = null
    /***
     * 设置Web视图的方法
     */
    private val webClient = object : WebViewClient() {


        //处理网页加载失败时
        @SuppressLint("NewApi")
        override fun onReceivedError(view: WebView, request: WebResourceRequest, error: WebResourceError) {
            super.onReceivedError(view, request, error)
            reloadProgress()

            isError = true
            isSuccess = false
            reloadWebviewRl!!.visibility = View.VISIBLE
            webContainer!!.visibility = View.GONE
            LogUtils.i("above 6.0 error callback", error.description)
        }

        override fun onReceivedError(view: WebView, errorCode: Int, description: String, failingUrl: String) {
            super.onReceivedError(view, errorCode, description, failingUrl)
            reloadProgress()
            isError = true
            isSuccess = false
            reloadWebviewRl!!.visibility = View.VISIBLE
            webContainer!!.visibility = View.GONE
            LogUtils.i("below 6.0 error callback", errorCode)
        }

        override fun onPageFinished(view: WebView, url: String) {
            LogUtils.i("WebView load finish")
            reloadProgress()
            if (!isFirstFinish) {
                return
            }
            isFirstFinish = false

            LogUtils.i(isError)
            if (!isError) {
                isSuccess = true
                //回调成功后的相关操作
                reloadWebviewRl!!.visibility = View.GONE
                webContainer!!.visibility = View.VISIBLE
            } else {
                isError = false
                reloadWebviewRl!!.visibility = View.VISIBLE
                webContainer!!.visibility = View.GONE
            }
        }

        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            var url1 = url
            Log.d("webview", "url1: $url1")

            if (url1.startsWith("mailto:")) {
                url1 = url1.replaceFirst("mailto:".toRegex(), "")
                url1 = url1.trim { it <= ' ' }
                val i = Intent(Intent.ACTION_SEND)
                i.setType("plain/text").putExtra(Intent.EXTRA_EMAIL, arrayOf(url1))
                startActivity(i)
                return true
            }
            view.loadUrl(url1)
            return true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_webview)
        showProgressDialog()
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        title = intent.getStringExtra("title")
        url = intent.getStringExtra("url") ?: PRIVACY_AGREEMENT
        isLocal=intent.getBooleanExtra("isLocal",false)
        handler = WeakHandler()
        reloadWebviewRl = findViewById(R.id.reload_webview_rl)
        reloadBtn = findViewById(R.id.reload_btn)
        webviewBackBtn = findViewById(R.id.webview_back_btn)
        webviewTitleTv = findViewById(R.id.webview_title_tv)
        webContainer = findViewById(R.id.web_frame)
        webviewTitleTv?.text = title

        webView = WebView(this)
        val settings = webView!!.settings
        settings.domStorageEnabled = true
        //解决一些图片加载问题
        settings.javaScriptEnabled = true
        settings.blockNetworkImage = false
        webView?.webViewClient = webClient
        webView?.webChromeClient = object : WebChromeClient() {

            override fun onProgressChanged(view: WebView, progress: Int) {
                //当进度走到100的时候做自己的操作，我这边是弹出dialog
                if (progress == 100) {
                    hideProgressDialog()
                }
            }
        }
        webContainer?.addView(webView)
        webView?.addJavascriptInterface(JSCallAndroidObject(), "jsCallAndroidObject")
//        webView!!.loadUrl(PRIVACY_AGREEMENT)
        webView?.loadUrl(url)
        Log.d("WebViewActivity", "url: $url")
    }

    private fun registerEvent() {
        if (NetworkUtils.isConnected()) {
            reloadWebviewRl!!.visibility = View.GONE
        } else {
            reloadWebviewRl!!.visibility = View.VISIBLE
        }

        webviewBackBtn!!.setOnClickListener {
            if (webView!!.canGoBack()) {
                webView!!.goBack() //goBack()表示返回WebView的上一页面

            } else {
                ActivityUtils.finishActivity(this@WebViewActivity)
            }
        }
        reloadBtn!!.setOnClickListener {
            showProgressDialog()
            isFirstFinish = true
            webView!!.reload()
        }
    }

    private fun reloadProgress() {
        handler!!.postDelayed({ hideProgressDialog() }, 600)
    }

    /**
     * @des: js交互
     * @params:
     * @return:
     */

    inner class JSCallAndroidObject {
        private val TAG = JSCallAndroidObject::class.java.simpleName

        @JavascriptInterface
        fun jsCallAndroid(msg: String): String {
            val uri = Uri.parse(msg)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            startActivity(intent)
            LogUtils.i(msg)
            return "from Android"
        }

    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)

            else -> {
            }
        }
    }

    override fun onDestroy() {
        if (webView != null) {
            webView!!.loadDataWithBaseURL(null, "", "text/html", "uft-8", null)
            webView!!.clearHistory()
            (webView!!.parent as ViewGroup).removeView(webView)
            webView!!.destroy()
            webView = null
        }
        super.onDestroy()
    }
}
