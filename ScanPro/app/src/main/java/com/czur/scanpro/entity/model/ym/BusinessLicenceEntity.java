package com.czur.scanpro.entity.model.ym;

public class BusinessLicenceEntity {

    /**
     * Title : 营业执照(副本)
     * RegeditNo : 9121023107156716XL
     * Name : 人连12322有限公司
     * Type : 有限123司
     * Address :  1L宁省人连高新123个
     * Legal : 册康
     * RegisteredCapital : 册康
     * PaidCapital : 册康
     * EstablishDate : 册康
     * RegeditDate : 册康
     * Period : 册康
     * Business : 册康
     * IssueUnit : 册康
     * Date : 册康
     * Form : 册康
     */

    private String Title;
    private String RegeditNo;
    private String Name;
    private String Type;
    private String Address;
    private String Legal;
    private String RegisteredCapital;
    private String PaidCapital;
    private String EstablishDate;
    private String RegeditDate;
    private String Period;
    private String Business;
    private String IssueUnit;
    private String Date;
    private String Form;

    public String getTitle() {
        return Title;
    }

    public void setTitle(String Title) {
        this.Title = Title;
    }

    public String getRegeditNo() {
        return RegeditNo;
    }

    public void setRegeditNo(String RegeditNo) {
        this.RegeditNo = RegeditNo;
    }

    public String getName() {
        return Name;
    }

    public void setName(String Name) {
        this.Name = Name;
    }

    public String getType() {
        return Type;
    }

    public void setType(String Type) {
        this.Type = Type;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String Address) {
        this.Address = Address;
    }

    public String getLegal() {
        return Legal;
    }

    public void setLegal(String Legal) {
        this.Legal = Legal;
    }

    public String getRegisteredCapital() {
        return RegisteredCapital;
    }

    public void setRegisteredCapital(String RegisteredCapital) {
        this.RegisteredCapital = RegisteredCapital;
    }

    public String getPaidCapital() {
        return PaidCapital;
    }

    public void setPaidCapital(String PaidCapital) {
        this.PaidCapital = PaidCapital;
    }

    public String getEstablishDate() {
        return EstablishDate;
    }

    public void setEstablishDate(String EstablishDate) {
        this.EstablishDate = EstablishDate;
    }

    public String getRegeditDate() {
        return RegeditDate;
    }

    public void setRegeditDate(String RegeditDate) {
        this.RegeditDate = RegeditDate;
    }

    public String getPeriod() {
        return Period;
    }

    public void setPeriod(String Period) {
        this.Period = Period;
    }

    public String getBusiness() {
        return Business;
    }

    public void setBusiness(String Business) {
        this.Business = Business;
    }

    public String getIssueUnit() {
        return IssueUnit;
    }

    public void setIssueUnit(String IssueUnit) {
        this.IssueUnit = IssueUnit;
    }

    public String getDate() {
        return Date;
    }

    public void setDate(String Date) {
        this.Date = Date;
    }

    public String getForm() {
        return Form;
    }

    public void setForm(String Form) {
        this.Form = Form;
    }
}
