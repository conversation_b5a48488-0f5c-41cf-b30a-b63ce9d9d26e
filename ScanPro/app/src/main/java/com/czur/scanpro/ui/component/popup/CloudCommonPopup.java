package com.czur.scanpro.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.BarUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.utils.validator.StringUtils;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class CloudCommonPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public CloudCommonPopup(Context context) {
        super(context);
    }

    public CloudCommonPopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String message;
        private String title;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }
        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setOnNegativeListener(OnClickListener onNegativeListener) {
            this.onNegativeListener = onNegativeListener;
            return this;
        }

        public Builder setOnDismissListener(OnDismissListener onDismissListener) {
            this.onDismissListener = onDismissListener;
            return this;
        }


        public CloudCommonPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final CloudCommonPopup dialog = new CloudCommonPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            BarUtils.setStatusBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode( dialog.getWindow(), true);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final CloudCommonPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.common_custom_popup, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);
            TextView title = (TextView) layout.findViewById(R.id.title);
            TextView message = (TextView) layout.findViewById(R.id.message);
            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);
            TextView negativeBtn = (TextView) layout.findViewById(R.id.negative_button);
            EditText editText= (EditText) layout.findViewById(R.id.edt);
            if (contentsView == null) {
                if (StringUtils.isNotEmpty(this.message)) {
                    message.setText(this.message + StringUtils.EMPTY);
                } else if (constants.getMessage() > 0) {
                    message.setText(context.getResources().getString(constants.getMessage()));

                }

                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }
            } else {
                message.setVisibility(View.GONE);
                title.setVisibility(View.GONE);
            }

            if (constants.getPositiveBtn() > 0) {
                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
            } else {
                positiveBtn.setVisibility(View.GONE);
            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }

            if (constants.getNegativeBtn() > 0) {
                negativeBtn.setText(context.getResources().getString(constants.getNegativeBtn()));
            } else {
                negativeBtn.setVisibility(View.GONE);
            }

            if (constants.getEditText() > 0) {
                RelativeLayout.LayoutParams layoutParams=(RelativeLayout.LayoutParams)message.getLayoutParams();
                layoutParams.setMargins(0,0,0,20);
            } else {
                editText.setVisibility(View.GONE);
            }


            if (onNegativeListener != null) {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, constants.getNegativeBtn());
                    }
                });
            } else {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener);
            }

            return layout;
        }
    }
}
