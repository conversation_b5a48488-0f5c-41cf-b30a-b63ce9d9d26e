package com.czur.scanpro.alg;

import android.graphics.Bitmap;
import android.util.Log;

public class Callbacks {
    /**
     * @param isSucess  是否裁剪成功
     * @param cuttedImg 裁剪好的图像
     * @param savedPath 内部保存到的路径
     * @brief 裁剪完成后native 会回调此方法
     */

    public void on_cut_finished(boolean isSucess,
                                Bitmap cuttedImg,
                                String savedPath) {
        Log.e("on_cut_finished", "isSucess: " + isSucess);
        Log.e("on_cut_finished", "cuttedImg width: " + cuttedImg.getWidth());
        Log.e("on_cut_finished", "cuttedImg height: " + cuttedImg.getHeight());
        Log.e("on_cut_finished", "savedPath: " + savedPath);

    }

//    /**
//     * @param isSucess  是否成功
//     * @param dstImg    颜色模式转换好的图像
//     * @param savedPath 内部保存到的路径
//     * @brief 颜色模式完成后native 会回调此方法
//     */
//
//    public void on_color_trans_finished(boolean isSucess,
//                                        Bitmap dstImg,
//                                        String savedPath) {
//        Log.e("on_color_trans_finished", "isSucess: " + isSucess);
//        Log.e("on_color_trans_finished", "cuttedImg width: " + dstImg.getWidth());
//        Log.e("on_color_trans_finished", "cuttedImg height: " + dstImg.getHeight());
//        Log.e("on_color_trans_finished", "savedPath: " + savedPath);
//
//    }

    /**
     * @param isSucess  是否成功
     * @param dstImg    颜色模式转换好的图像
     * @param savedPath 内部保存到的路径
     * @param //        左上，右上， 左下， 右下
     * @brief 颜色模式完成后native 会回调此方法
     */

    public void on_color_trans_finished_with_keypoints(boolean isSucess,
                                                       Bitmap dstImg,
                                                       String savedPath,
                                                       float leftTopX,
                                                       float leftTopY,
                                                       float rightTopX,
                                                       float rightTopY,
                                                       float leftDownX,
                                                       float leftDownY,
                                                       float rightDownX,
                                                       float rightDownY
    ) {

    }


    public void on_notify_angle(int angle) {
        Log.e("on_notify_angle", "angle:  " + angle);

    }


    public void on_save_success() {
    }

    public void on_need_focus() {

    }

}
