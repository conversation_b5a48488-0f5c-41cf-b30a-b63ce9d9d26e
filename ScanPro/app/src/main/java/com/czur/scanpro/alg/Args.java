package com.czur.scanpro.alg;

import android.graphics.Bitmap;

import java.util.List;

public class Args
{
    //返回检测的四个点 左上，左下，　右上，右下
    private CZPoint leftTopP, leftDownP, rightTopP, rightDownP;
    //是否有目标
    private boolean hasObject;
    //更多信息  可能用于远近提示
    private String infos;
    // 书本的上边缘关键点，书本的下边缘关键点
    private List<CZPoint> upPtss,downPtss;
    //书本上下边缘曲线 68点
    private List<CZPoint> upCurve,downCurve;

    private  CZSaoMiao_Alg.ScanType  scanType = CZSaoMiao_Alg.ScanType.SINGLE; // 扫描类型
    private  CZSaoMiao_Alg.ColorType colorType = CZSaoMiao_Alg.ColorType.AUTO;//颜色模式类型

    //陀螺仪姿态角度
    private float angleX=0;
    private float angleY=0;

    private boolean isOnlyGetKeyPoints = false;// 静态处理时是否仅获取关键点

    private Bitmap contentImg;

    private boolean hasSorption;

    public boolean isHasSorption() {
        return hasSorption;
    }
    public void setHasSorption(boolean hasSorption)
    {
        this.hasSorption = hasSorption;
    }

    /**
     * @brief 手动调整时可方便获取
     * @return Bitmap
     */
    public Bitmap getContentImg() {
        return contentImg;
    }

    public void setContentImg(Bitmap contentImg) {
        this.contentImg = contentImg;
    }

    public  Args(){}

    public Args(CZPoint leftTopP,
                CZPoint leftDownP,
                CZPoint rightTopP,
                CZPoint rightDownP,
                boolean hasObj,
                String infos)
    {
        this.leftTopP   = leftTopP;
        this.leftDownP  = leftDownP;
        this.rightTopP  = rightTopP;
        this.rightDownP = rightDownP;
        this.hasObject = hasObj;
        this.infos = infos;
    }

    @Override
    public String toString()
    {
        return  "##########################################\n"+
                "hasObject: "+hasObject+"\n"+
                "infos: "+infos+"\n"+
                "leftTopP: "+leftTopP+"\n"+
                "leftDownP: "+leftDownP+"\n"+
                "rightTopP: "+rightTopP+"\n"+
                "rightDownP: "+rightDownP+"\n\n"+
                "#############################################\n";
    }
    public void setPtss(List<CZPoint>  upPtss,List<CZPoint> downPtss)
    {
        this.upPtss = upPtss;
        this.downPtss = downPtss;
    }

    public void setCurve(List<CZPoint>  upCurve,List<CZPoint> downCurve)
    {
        this.upCurve = upCurve;
        this.downCurve = downCurve;
    }

    public List<CZPoint> getDownCurve() {
        return downCurve;
    }

    public List<CZPoint> getUpCurve() {
        return upCurve;
    }

    public void setUpCurve(List<CZPoint> upCurve) {
        this.upCurve = upCurve;
    }

    public void setDownCurve(List<CZPoint> downCurve) {
        this.downCurve = downCurve;
    }

    public List<CZPoint> getDownPtss() {
        return downPtss;
    }

    public List<CZPoint> getUpPtss() {
        return upPtss;
    }

    public String getInfos() {
        return infos;
    }

    public float getAngleX() {
        return angleX;
    }

    public float getAngleY() {
        return angleY;
    }

    public CZPoint getLeftTopP() {
        return leftTopP;
    }

    public CZPoint getLeftDownP() {
        return leftDownP;
    }

    public CZPoint getRightTopP() {
        return rightTopP;
    }

    public CZPoint getRightDownP() {
        return rightDownP;
    }

    public void setAngleX(float angleX) {
        this.angleX = angleX;
    }

    public void setAngleY(float angleY) {
        this.angleY = angleY;
    }

    public void setLeftDownP(CZPoint leftDownP) {
        this.leftDownP = leftDownP;
    }

    public void setLeftTopP(CZPoint leftTopP) {
        this.leftTopP = leftTopP;
    }

    public void setRightDownP(CZPoint rightDownP) {
        this.rightDownP = rightDownP;
    }


    public void setRightTopP(CZPoint rightTopP) {
        this.rightTopP = rightTopP;
    }

    public void setDownPtss(List<CZPoint> downPtss) {
        this.downPtss = downPtss;
    }

    public void setUpPtss(List<CZPoint> upPtss) {
        this.upPtss = upPtss;
    }

    public CZSaoMiao_Alg.ColorType getColorType() {
        return colorType;
    }

    public void setColorType(CZSaoMiao_Alg.ColorType colorType) {
        this.colorType = colorType;
    }

    public CZSaoMiao_Alg.ScanType getScanType() {
        return scanType;
    }

    public void setScanType(CZSaoMiao_Alg.ScanType scanType) {
        this.scanType = scanType;
    }

    public boolean isOnlyGetKeyPoints() {
        return isOnlyGetKeyPoints;
    }

    public void setOnlyGetKeyPoints(boolean onlyGetKeyPoints) {
        isOnlyGetKeyPoints = onlyGetKeyPoints;
    }

    public boolean isHasObject() {
        return hasObject;
    }


    public CZPoint leftTopP0=new CZPoint(), leftDownP0=new CZPoint(), rightTopP0=new CZPoint(), rightDownP0=new CZPoint();
    public CZPoint leftTopP1=new CZPoint(), leftDownP1=new CZPoint(), rightTopP1=new CZPoint(), rightDownP1=new CZPoint();


    public double dis_bt_point(CZPoint p1, CZPoint p2)
    {
        return Math.sqrt((p1.getX() - p2.getX())*(p1.getX() - p2.getX()) +
                (p1.getY() - p2.getY())*(p1.getY() - p2.getY()));
    }

    void on_extend_ui_points()
    {
        double w1 = dis_bt_point(leftTopP, rightTopP);
        double w2 = dis_bt_point(leftDownP, rightDownP);
        double h1 = dis_bt_point(leftTopP, leftDownP);
        double h2  = dis_bt_point(rightTopP, rightDownP);
        double ratio = 0.1;
        double ratioW1 = ratio;//pt / dis_bt_point(pts4[0],pts4[1]);
        double ratioW2 = ratio*w1 / w2;//pt / dis_bt_point(pts4[2], pts4[3]);
        double ratioH1 = ratio*w1 / h1;//pt / dis_bt_point(pts4[0], pts4[2]);
        double ratioH2 = ratio*w1 / h2;//pt / dis_bt_point(pts4[1], pts4[3]);

        leftTopP0.setX( (int)((1-ratioW1)*leftTopP.getX() + ratioW1*rightTopP.getX()) );
        leftTopP0.setY( (int)((1-ratioW1)*leftTopP.getY() + ratioW1*rightTopP.getY()) );
        leftTopP1.setX( (int)((1-ratioH1)*leftTopP.getX() + ratioH1*leftDownP.getX()) );
        leftTopP1.setY( (int)((1-ratioH1)*leftTopP.getY() + ratioH1*leftDownP.getY()) );


        rightTopP0.setX( (int)((ratioW1)*leftTopP.getX() + (1-ratioW1)*rightTopP.getX()) );
        rightTopP0.setY( (int)((ratioW1)*leftTopP.getY() + (1-ratioW1)*rightTopP.getY()) );
        rightTopP1.setX( (int)((1-ratioH2)*rightTopP.getX() + ratioH2*rightDownP.getX()) );
        rightTopP1.setY( (int)((1-ratioH2)*rightTopP.getY() + ratioH2*rightDownP.getY()) );

        leftDownP0.setX( (int)((ratioH1)*leftTopP.getX() + (1-ratioH1)*leftDownP.getX()) );
        leftDownP0.setY( (int)((ratioH1)*leftTopP.getY() + (1-ratioH1)*leftDownP.getY()) );
        leftDownP1.setX( (int)((1-ratioW2)*leftDownP.getX() + ratioW2*rightDownP.getX()) );
        leftDownP1.setY( (int)((1-ratioW2)*leftDownP.getY() + ratioW2*rightDownP.getY()) );


        rightDownP0.setX( (int)((ratioH2)*rightTopP.getX() + (1-ratioH2)*rightDownP.getX()) );
        rightDownP0.setY( (int)((ratioH2)*rightTopP.getY() + (1-ratioH2)*rightDownP.getY()) );
        rightDownP1.setX( (int)((ratioW2)*leftDownP.getX() + (1-ratioW2)*rightDownP.getX()) );
        rightDownP1.setY( (int)((ratioW2)*leftDownP.getY() + (1-ratioW2)*rightDownP.getY()) );



    }






}
