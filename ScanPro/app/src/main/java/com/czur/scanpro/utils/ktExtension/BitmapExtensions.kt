package com.czur.scanpro.utils.ktExtension

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Point
import android.graphics.Rect

fun Bitmap.createBitmapSquareOutOfBounds(x: Int, y: Int, boundsWidth: Int): Bitmap? {
    val leftTopPoint = Point(x - boundsWidth / 2, y - boundsWidth / 2)
    val rightBottomPoint = Point(x + boundsWidth / 2, y + boundsWidth / 2)

    val isLeftOutOfBounds = leftTopPoint.x < 0
    val isTopOutOfBounds = leftTopPoint.y < 0
    val isRightOutOfBounds = rightBottomPoint.x > this.width
    val isBottomOutOfBounds = rightBottomPoint.y > this.height

    if (!isLeftOutOfBounds && !isTopOutOfBounds && !isRightOutOfBounds && !isBottomOutOfBounds) {
        return Bitmap.createBitmap(this, leftTopPoint.x, leftTopPoint.y, boundsWidth, boundsWidth)
    } else {
        val cutRect = Rect()
        cutRect.left = if (isLeftOutOfBounds) 0 else leftTopPoint.x
        cutRect.top = if (isTopOutOfBounds) 0 else leftTopPoint.y
        cutRect.right = if (isRightOutOfBounds) this.width else rightBottomPoint.x
        cutRect.bottom = if (isBottomOutOfBounds) this.height else rightBottomPoint.y
        val cutBitmap: Bitmap = Bitmap.createBitmap(this, cutRect.left, cutRect.top, cutRect.width(), cutRect.height())
        val tempBitmap = Bitmap.createBitmap(boundsWidth, boundsWidth, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(tempBitmap)
        val rect = Rect()
        rect.left = if (isLeftOutOfBounds) boundsWidth - (cutRect.right - cutRect.left) else 0
        rect.top = if (isTopOutOfBounds) boundsWidth - (cutRect.bottom - cutRect.top) else 0
        rect.right = if (isRightOutOfBounds) cutRect.right - cutRect.left else boundsWidth
        rect.bottom = if (isBottomOutOfBounds) cutRect.bottom - cutRect.top else boundsWidth
        canvas.drawBitmap(cutBitmap, null, rect, null)
        cutBitmap.recycle()
        return tempBitmap
    }
}