package com.czur.scanpro.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Point;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.view.WindowInsets;
import android.view.WindowManager;

/**
 * Created by Yz on 2018/3/15
 * Email：<EMAIL>
 */

public class NavBarUtils {

    /**
     * 检查是否存在虚拟按键栏 - Android 16优化版本
     * 完全去除反射，使用官方API实现
     * @param context 上下文
     * @return true表示存在虚拟导航栏，false表示不存在
     */
    public static boolean hasNavBar(Context context) {
        // Android 11+ (API 30+) 使用现代API
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return hasNavBarModern(context);
        }
        // Android 5.0+ (API 21+) 使用屏幕尺寸检测
        else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            return hasNavBarByScreenSize(context);
        }
        // 旧版本回退方案
        else {
            return hasNavBarLegacy(context);
        }
    }

    /**
     * 现代方式检测导航栏 (Android 11+ API 30+)
     * 使用WindowInsets API，最可靠的方式
     */
    private static boolean hasNavBarModern(Context context) {
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            WindowInsets windowInsets = activity.getWindow().getDecorView().getRootWindowInsets();
            if (windowInsets != null) {
                // 检查导航栏是否存在且可见
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    return windowInsets.isVisible(WindowInsets.Type.navigationBars()) ||
                           windowInsets.getInsets(WindowInsets.Type.navigationBars()).bottom > 0;
                }
            }
        }

        // 如果无法获取WindowInsets，回退到屏幕尺寸检测
        return hasNavBarByScreenSize(context);
    }

    /**
     * 通过屏幕尺寸检测导航栏 (Android 5.0+ API 21+)
     * 比较实际屏幕尺寸和可用屏幕尺寸
     */
    private static boolean hasNavBarByScreenSize(Context context) {
        // 首先检查硬件按键
        if (hasHardwareKeys(context)) {
            return false; // 有硬件按键，通常不显示导航栏
        }

        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        if (windowManager == null) {
            return hasNavBarLegacy(context);
        }

        Display display = windowManager.getDefaultDisplay();
        Point realSize = new Point();
        Point screenSize = new Point();

        // 获取真实屏幕尺寸（包括导航栏）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            display.getRealSize(realSize);
        } else {
            realSize.set(display.getWidth(), display.getHeight());
        }

        // 获取可用屏幕尺寸（不包括导航栏）
        display.getSize(screenSize);

        // 如果真实高度大于可用高度，说明有导航栏
        return realSize.y > screenSize.y || realSize.x > screenSize.x;
    }

    /**
     * 传统方式检测导航栏 (兼容旧版本)
     * 使用系统配置和硬件按键检测
     */
    private static boolean hasNavBarLegacy(Context context) {
        // 检查硬件按键
        if (hasHardwareKeys(context)) {
            return false;
        }

        // 检查系统配置
        Resources res = context.getResources();
        int resourceId = res.getIdentifier("config_showNavigationBar", "bool", "android");
        if (resourceId != 0) {
            return res.getBoolean(resourceId);
        }

        // 最后的回退方案
        return !ViewConfiguration.get(context).hasPermanentMenuKey();
    }

    /**
     * 检查设备是否有硬件按键
     * 替代原来的反射方式，使用官方API
     */
    private static boolean hasHardwareKeys(Context context) {
        // 检查是否有物理菜单键
        boolean hasMenuKey = ViewConfiguration.get(context).hasPermanentMenuKey();

        // 检查是否有物理返回键和Home键
        boolean hasBackKey = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_BACK);
        boolean hasHomeKey = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_HOME);

        // 如果有菜单键，或者同时有返回键和Home键，认为有完整的硬件导航
        return hasMenuKey || (hasBackKey && hasHomeKey);
    }

    /**
     * 获取导航栏高度
     * @param context 上下文
     * @return 导航栏高度（像素）
     */
    public static int getNavBarHeight(Context context) {
        if (!hasNavBar(context)) {
            return 0;
        }

        // Android 11+ 使用WindowInsets获取
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && context instanceof Activity) {
            Activity activity = (Activity) context;
            WindowInsets windowInsets = activity.getWindow().getDecorView().getRootWindowInsets();
            if (windowInsets != null) {
                return windowInsets.getInsets(WindowInsets.Type.navigationBars()).bottom;
            }
        }

        // 传统方式：通过资源获取
        Resources resources = context.getResources();
        int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return resources.getDimensionPixelSize(resourceId);
        }

        return 0;
    }

    /**
     * 检查当前是否为横屏模式
     * @param context 上下文
     * @return true表示横屏，false表示竖屏
     */
    public static boolean isLandscape(Context context) {
        return context.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    /**
     * 获取状态栏高度
     * @param context 上下文
     * @return 状态栏高度（像素）
     */
    public static int getStatusBarHeight(Context context) {
        Resources resources = context.getResources();
        int resourceId = resources.getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return resources.getDimensionPixelSize(resourceId);
        }
        return 0;
    }
}
