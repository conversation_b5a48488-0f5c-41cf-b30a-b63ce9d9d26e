package com.czur.scanpro.ui.download;

import android.util.Log;

import com.blankj.utilcode.util.FileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class HttpDownloader {
    String SD_PATH = "";
    private URL url = null;

    public int downFile(String urlStr, String path, String fileName) {
        SD_PATH = path;
        if (isFileExist(path + fileName)) {
            return 1;
        } else {

            try {
                InputStream input = null;
                input = getInputStream(urlStr);
                File resultFile = write2SDFromInput(path, fileName, input);
                if (resultFile == null) {
                    return -1;
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

        }
        return 0;
    }

    //由于得到一个InputStream对象是所有文件处理前必须的操作，所以将这个操作封装成了一个方法
    public InputStream getInputStream(String urlStr) throws IOException {
        InputStream is = null;
        try {
            url = new URL(urlStr);
            HttpURLConnection urlConn = (HttpURLConnection) url.openConnection();
            is = urlConn.getInputStream();

        } catch (MalformedURLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        return is;
    }

    /**
     *  
     * 在SD卡上创建文件 
     *
     * @throws IOException 
     */
    public File creatSDFile(String fileName) throws IOException {
        File fileSD = new File(SD_PATH);
        // 如果路径不存在，就创建
        if (!fileSD.exists()) {
            fileSD.mkdirs();
        }

        File file = new File(SD_PATH + fileName);
        file.createNewFile();
        return file;
    }

    /**
     *  
     * 在SD卡上创建目录 
     *
     * @param dirName 
     */
    public File creatSDDir(String dirName) {

        File dir = new File(SD_PATH + dirName);
        dir.mkdir();
        return dir;
    }

    /**
     *  
     * 判断SD卡上的文件夹是否存在 
     */
    public boolean isFileExist(String fileName) {
        File file = new File(SD_PATH + fileName);
        return file.exists();
    }

    /**
     *  
     * 将一个InputStream里面的数据写入到SD卡中 
     */
    public File write2SDFromInput(String path, String fileName, InputStream input) {
        File file = null;
        OutputStream output = null;
        try {
            int num = 0;
            creatSDDir(path);
            file = creatSDFile("downloading_" + fileName);
            output = new FileOutputStream(file);
            byte buffer[] = new byte[4 * 1024];
            while ((num = (input.read(buffer))) != -1) {
                output.write(buffer, 0, num);
            }

            output.flush();
            //确保文件的完整性,下载完成后,更名为原来名字
            FileUtils.rename(file,fileName);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                output.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    // 将字符串写入到文本文件中
    public static void writeTxtToFile(String strcontent, String filePath, String fileName) {
        //生成文件夹之后，再生成文件，不然会出错
        makeFilePath(filePath, fileName);
        String strFilePath = filePath + fileName;
        // 每次写入时，都换行写
        String strContent = strcontent + "\r\n";
        try {
            File file = new File(strFilePath);
            if (!file.exists()) {
                Log.d("TestFile", "Create the file:" + strFilePath);
                file.getParentFile().mkdirs();
                file.createNewFile();
            }
            RandomAccessFile raf = new RandomAccessFile(file, "rwd");
            raf.seek(file.length());
            raf.write(strContent.getBytes());
            raf.close();
        } catch (Exception e) {
            Log.e("TestFile", "Error on write File:" + e);
        }
    }


    //生成文件
    public static File makeFilePath(String filePath, String fileName) {
        File file = null;
        makeRootDirectory(filePath);
        try {
            file = new File(filePath + fileName);
            if (!file.exists()) {
                file.createNewFile();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return file;
    }

    //生成文件夹
    public static void makeRootDirectory(String filePath) {
        File file = null;
        try {
            file = new File(filePath);
            if (!file.exists()) {
                file.mkdirs();
            }
        } catch (Exception e) {
            Log.i("error:", e + "");
        }
    }

    //读取指定目录下的所有TXT文件的文件内容
    public static String getFileContent(File file) {
        String content = "";
        if (!file.isDirectory()) { //检查此路径名的文件是否是一个目录(文件夹)
            if (file.getName().endsWith("txt")) {//文件格式为""文件
                try {
                    InputStream instream = new FileInputStream(file);
                    if (instream != null) {
                        InputStreamReader inputreader
                                = new InputStreamReader(instream, "UTF-8");
                        BufferedReader buffreader = new BufferedReader(inputreader);
                        String line = "";
                        //分行读取
                        while ((line = buffreader.readLine()) != null) {
                            content += line + "\n";
                        }
                        instream.close();//关闭输入流
                    }
                } catch (java.io.FileNotFoundException e) {
                    Log.d("TestFile", "The File doesn't not exist.");
                } catch (IOException e) {
                    Log.d("TestFile", e.getMessage());
                }
            }
        }
        return content;
    }

    public static String readTxtFile(String filepath) {
        String result = "";

        try {
            InputStream in = new FileInputStream(filepath);
            InputStreamReader isr = new InputStreamReader(in);
            BufferedReader br = new BufferedReader(isr);
            String str = "";

            while ((str = br.readLine()) != null) {
                result += str;
            }

            in.close();
            isr.close();
            br.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    // 将字符串写入到文本文件中
//    public static void writeTxtToFile(String content, String filePath, String fileName) {
//        //生成文件夹之后，再生成文件，不然会出错
//        makeFile(filePath, fileName);
//        String mFilePath = filePath + fileName;
//        // 每次写入时，都换行写
//        String mContent = content + "\r\n";
//        try {
//            File file = new File(mFilePath);
//            if (!file.exists()) {
////                LogUtils.logD(TAG, "创建文件: " + mFilePath);
//                file.getParentFile().mkdirs();
//                file.createNewFile();
//            }
//            RandomAccessFile mRandomAccessFile = new RandomAccessFile(file, "rwd");
//            mRandomAccessFile.seek(file.length());
//            mRandomAccessFile.write(mContent.getBytes());
//            mRandomAccessFile.close();
//        } catch (IOException e) {
////            LogUtils.logD(TAG, "写入错误: " + e.toString());
//        }
//    }
//    //生成文件
//    public static File makeFile(String filePath, String fileName) {
//        File file = null;
//        makeDirectory(filePath);
//        try {
//            file = new File(filePath + fileName);
//            if (!file.exists()) {
//                file.createNewFile();
//            }
//        } catch (IOException e) {
////            LogUtils.logD(TAG, "生成文件错误: " + e.toString());
//        }
//        return file;
//    }
//    //生成文件夹
//    public static void makeDirectory(String filePath) {
//        File file = null;
//        try {
//            file = new File(filePath);
//            if (!file.exists()) {
//                file.mkdir();
//            }
//        } catch (Exception e) {
////            LogUtils.logD(TAG, "生成文件夹错误: " + e.toString());
//        }
//    }
//    public static File makeFilePath(String filePath, String fileName) {
//        File file = null;
//        makeRootDirectory(filePath);
//        try {
//            file = new File(filePath + fileName);
//            if (!file.exists()) {
//                file.createNewFile();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return file;
//    }


    public static ArrayList<String> readAllFileName(String path) {
        ArrayList<String> list = new ArrayList<String>();
        File file = new File(path);
        if (!file.exists()) {
            return list;
        }
        if (!file.isDirectory()) {
            return list;
        }
        String[] tempList = file.list();

        list.addAll(Arrays.asList(tempList));

        return list;
    }

    public static List<File> readAllFile(String path){
        ArrayList<File> list = new ArrayList<>();
        File file = new File(path);
        if (!file.exists()){
            return list;
        }
        if (!file.isDirectory()){
            return list;
        }
        String[] tempList = file.list();
        File temp = null;
        for (int i = 0; i < tempList.length; i++) {


        if (path.endsWith(File.separator)) {
            temp =  new File(path + tempList[i]);
            } else {
            temp =  new File(path + File.separator + tempList[i]);
            }
            if (temp.isFile()) {
                list.add(temp);
            }
            if (temp.isDirectory()) {
                list.addAll(readAllFile(temp.getAbsolutePath()));
            }
        }

        return list;

    }

}
