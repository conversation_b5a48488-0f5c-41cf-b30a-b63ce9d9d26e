package com.czur.scanpro.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.blankj.utilcode.util.SizeUtils
import com.czur.scanpro.R

class VipDetailAdapter(val context: Context, val datas: MutableList<String>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    override fun onCreateViewHolder(viewGroup: ViewGroup, position: Int): RecyclerView.ViewHolder {
        return DetailHolder(LayoutInflater.from(viewGroup.context).inflate(R.layout.item_vip_detail, viewGroup, false))
    }

    override fun getItemCount(): Int {
        return datas.size
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val detailHolder: DetailHolder = holder as DetailHolder
        detailHolder.tv.text = datas[position]
        if (position == 0) {
            detailHolder.tv.paint.isFakeBoldText = true
            detailHolder.tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
            val layoutParams = detailHolder.tv.layoutParams
            layoutParams.height = SizeUtils.dp2px(38f)
            detailHolder.tv.layoutParams = layoutParams
            when (datas[position]) {
                "VIP" -> {
                    detailHolder.tv.setTextColor(context.resources.getColor(R.color.red_de4d4d))
                }
                "SVIP" -> {
                    detailHolder.tv.setTextColor(context.resources.getColor(R.color.gold_ecc382))
                }
                else -> {
                    detailHolder.tv.setTextColor(context.resources.getColor(R.color.black_24))
                }
            }
        } else {
            detailHolder.tv.paint.isFakeBoldText = false
            val layoutParams = detailHolder.tv.layoutParams
            layoutParams.height = SizeUtils.dp2px(25f)
            detailHolder.tv.layoutParams = layoutParams
            detailHolder.tv.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
            detailHolder.tv.setTextColor(context.resources.getColor(R.color.black_24))
        }
    }

    private inner class DetailHolder internal constructor(mView: View) : RecyclerView.ViewHolder(mView) {
        internal var tv: TextView = mView.findViewById(R.id.tv_vip_detail)
    }
}