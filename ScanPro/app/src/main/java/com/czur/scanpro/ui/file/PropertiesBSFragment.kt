package com.czur.scanpro.ui.file

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.*
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import androidx.fragment.app.DialogFragment
import com.blankj.utilcode.util.ColorUtils
import com.czur.scanpro.R
import com.czur.scanpro.adapter.ColorPickerAdapter
import com.czur.scanpro.adapter.ColorPickerAdapter.OnColorPickerClickListener
import com.czur.scanpro.databinding.ActivityMyPdfBinding
import com.czur.scanpro.databinding.FragmentBottomPropertiesDialogBinding

class PropertiesBSFragment : DialogFragment(), OnSeekBarChangeListener {
    private lateinit var binding: FragmentBottomPropertiesDialogBinding


    private var mProperties: Properties? = null
    private var currentColorCode = ColorUtils.getColor(R.color.red_de4d4d);

    interface Properties {
        fun onColorChanged(colorCode: Int)
        fun onOpacityChanged(opacity: Int)
        fun onBrushSizeChanged(brushSize: Int)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        this.dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val window: Window? = this.dialog?.window
        window?.decorView?.setPadding(0, 0, 0, 0)
        window?.setDimAmount(0f)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT));
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        lp?.height = WindowManager.LayoutParams.WRAP_CONTENT
        lp?.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        window?.attributes = lp
        window?.setGravity(Gravity.BOTTOM)
        window?.setWindowAnimations(R.style.BottomAnimation)
        binding = FragmentBottomPropertiesDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val rvColor: RecyclerView = view.findViewById(R.id.rvColors)
        val sbOpacity = view.findViewById<SeekBar>(R.id.sbOpacity)
        val sbBrushSize = view.findViewById<SeekBar>(R.id.sbSize)
        sbOpacity.setOnSeekBarChangeListener(this)
        sbBrushSize.setOnSeekBarChangeListener(this)
        val layoutManager = LinearLayoutManager(
            activity,
            LinearLayoutManager.HORIZONTAL,
            false
        )
        rvColor.layoutManager = layoutManager
        rvColor.setHasFixedSize(true)
        if (rvColor.adapter == null) {
            val colorPickerAdapter = ColorPickerAdapter(requireActivity(), currentColorCode, true)
            colorPickerAdapter.setOnColorPickerClickListener(object : OnColorPickerClickListener {
                override fun onColorPickerClickListener(colorCode: Int) {
                    mProperties?.onColorChanged(colorCode)
                    currentColorCode = colorCode
                }
            })
            rvColor.adapter = colorPickerAdapter
        }
    }


    fun setPropertiesChangeListener(properties: Properties?) {
        mProperties = properties
    }

    override fun onProgressChanged(seekBar: SeekBar, i: Int, b: Boolean) {
        when (seekBar.id) {
            R.id.sbOpacity -> if (mProperties != null) {
                mProperties?.onOpacityChanged(i)
                binding.tvOpacity.text = i.toString()
            }
            R.id.sbSize -> if (mProperties != null) {
                mProperties?.onBrushSizeChanged(i)
                binding.tvBrushSize.text = i.toString()
            }
        }
    }

    override fun onStartTrackingTouch(seekBar: SeekBar) {}
    override fun onStopTrackingTouch(seekBar: SeekBar) {}
}