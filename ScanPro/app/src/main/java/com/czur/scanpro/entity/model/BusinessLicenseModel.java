package com.czur.scanpro.entity.model;

import java.util.List;

/**
 * Created by Yz on 2018/3/30.
 * Email：<EMAIL>
 */

public class BusinessLicenseModel {


    /**
     * code : 0
     * message : OK
     * data : {"session_id":"1000001-**********",
     * "items":[
     * {"item":"注册号","itemcoord":
     * {"x":600,"y":590,"width":205,"height":24},"itemconf":0.9469727277755736,"itemstring":"913709027242123456","coords":[],"words":[],"candword":[]},
     * {"item":"法定代表人","itemcoord":{"x":358,"y":804,"width":98,"height":27},"itemconf":0.999782145023346,"itemstring":"腾小云","coords":[],"words":[],"candword":[]},
     * {"item":"公司名称","itemcoord":{"x":358,"y":652,"width":302,"height":28},"itemconf":0.9998672008514404,"itemstring":"腾讯科技（深圳）有限公司","coords":[],"words":[],"candword":[]},
     * {"item":"地址","itemcoord":{"x":361,"y":750,"width":207,"height":28},"itemconf":0.999684989452362,"itemstring":"深圳市南山区深南大道10000","coords":[],"words":[],"candword":[]},{"item":"营业期限","itemcoord":{"x":347,"y":957,"width":424,"height":28},"itemconf":0.**********969972,"itemstring":"自1998年11月11日至长期","coords":[],"words":[],"candword":[]}]}
     */

    private int code;
    private String message;
    private String data;

    private DataBean dataBean;

    public DataBean getDataBean() {
        return dataBean;
    }

    public void setDataBean(DataBean dataBean) {
        this.dataBean = dataBean;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public static class DataBean {

        /**
         * RegNum : 9121023107156716XL
         * Name : 大连成者科技有限公司
         * Capital : 人民币柒拾伍万壹仟柒佰柒拾捌元整
         * Person : 周康
         * Address : 辽宁省大连高新技术产业园区火炬路32A号A座10层1001室
         * Business : 扫描仪、计算机软硬件技术开发;信息技术管理咨询服务;电子产品、办公设备技术开发及销售;物联网技术服务;网络技术开发;通讯设备销售:通讯技术开发、技术咨询、技术服务;货物进出口:国内一般贸易.(依法须经批准的项目,经相关部门批准后方可开展经营活动).
         * Type : 有限责任公司(法人独资)
         * Period : 2013年07月12日至2023年07月11日
         * ComposingForm :
         * SetDate : 2013年07月12日
         * RecognizeWarnCode : []
         * RecognizeWarnMsg : []
         * IsDuplication : 1
         * RegistrationDate :
         * RequestId : fbe17f4a-c750-4aa2-be59-c6779f0347dc
         */

        private String RegNum;
        private String Name;
        private String Capital;
        private String Person;
        private String Address;
        private String Business;
        private String Type;
        private String Period;
        private String ComposingForm;
        private String SetDate;
        private int IsDuplication;
        private String RegistrationDate;
        private String RequestId;
        private List<?> RecognizeWarnCode;
        private List<?> RecognizeWarnMsg;

        public String getRegNum() {
            return RegNum;
        }

        public void setRegNum(String RegNum) {
            this.RegNum = RegNum;
        }

        public String getName() {
            return Name;
        }

        public void setName(String Name) {
            this.Name = Name;
        }

        public String getCapital() {
            return Capital;
        }

        public void setCapital(String Capital) {
            this.Capital = Capital;
        }

        public String getPerson() {
            return Person;
        }

        public void setPerson(String Person) {
            this.Person = Person;
        }

        public String getAddress() {
            return Address;
        }

        public void setAddress(String Address) {
            this.Address = Address;
        }

        public String getBusiness() {
            return Business;
        }

        public void setBusiness(String Business) {
            this.Business = Business;
        }

        public String getType() {
            return Type;
        }

        public void setType(String Type) {
            this.Type = Type;
        }

        public String getPeriod() {
            return Period;
        }

        public void setPeriod(String Period) {
            this.Period = Period;
        }

        public String getComposingForm() {
            return ComposingForm;
        }

        public void setComposingForm(String ComposingForm) {
            this.ComposingForm = ComposingForm;
        }

        public String getSetDate() {
            return SetDate;
        }

        public void setSetDate(String SetDate) {
            this.SetDate = SetDate;
        }

        public int getIsDuplication() {
            return IsDuplication;
        }

        public void setIsDuplication(int IsDuplication) {
            this.IsDuplication = IsDuplication;
        }

        public String getRegistrationDate() {
            return RegistrationDate;
        }

        public void setRegistrationDate(String RegistrationDate) {
            this.RegistrationDate = RegistrationDate;
        }

        public String getRequestId() {
            return RequestId;
        }

        public void setRequestId(String RequestId) {
            this.RequestId = RequestId;
        }

        public List<?> getRecognizeWarnCode() {
            return RecognizeWarnCode;
        }

        public void setRecognizeWarnCode(List<?> RecognizeWarnCode) {
            this.RecognizeWarnCode = RecognizeWarnCode;
        }

        public List<?> getRecognizeWarnMsg() {
            return RecognizeWarnMsg;
        }

        public void setRecognizeWarnMsg(List<?> RecognizeWarnMsg) {
            this.RecognizeWarnMsg = RecognizeWarnMsg;
        }
    }
}
