package com.czur.scanpro.ui.file

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.alg.Args
import com.czur.scanpro.alg.CZSaoMiao_Alg
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityEditBinding
import com.czur.scanpro.databinding.ActivityEditCutBinding
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EditEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.file.adjust.AdjustEdgeBookActivity
import com.czur.scanpro.ui.file.adjust.AdjustEdgeSimpleActivity
import com.czur.scanpro.ui.file.adjust.AdjustGuideActivity
import com.facebook.drawee.backends.pipeline.Fresco
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean


class FileEditActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityEditBinding by lazy{
        ActivityEditBinding.inflate(layoutInflater)
    }
    private var isSingle: Boolean = false

    private var fileId: String? = null
    private var realm: Realm? = null
    private var alg: CZSaoMiao_Alg? = null
    private var userPreferences: UserPreferences? = null
    private var dirPath: String? = null
    private var tempPath: String? = null

    private var bigImagePath: String? = null
    private var smallImagePath: String? = null
    private var baseImagePath: String? = null
    private var smallBasePath: String? = null

    private var docEntity: DocEntity? = null
    private var rotate: Float? = 0f
    private var colorRotate: Float? = 0f

    private var isHorizontal: Boolean = true
    private var isScale: Boolean = true
    private var isColorHorizontal: Boolean = true
    private var isColorScale: Boolean = true

    private var isColor: Boolean = false

    private var blackPath: String? = null
    private var bgPath: String? = null
    private var colorPath: String? = null
    private var canRotate: AtomicBoolean? = null
    private var clickTime = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_edit)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        canRotate = AtomicBoolean(true)
        binding.editTopBar.z = 1000f
        alg = CZSaoMiao_Alg.getInstance(this)
        fileId = intent.getStringExtra("fileId")
        isSingle = intent.getBooleanExtra("isSingle", false);

        EventBus.getDefault().register(this)
        userPreferences = UserPreferences.getInstance(this)
        tempPath = cacheDir.path + Constants.TEMP
        dirPath = <EMAIL> + File.separator + userPreferences!!.userId + File.separator
        realm = Realm.getDefaultInstance()
        docEntity = realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("fileID", fileId).findFirst()
        checkFileType()
        setImage()

    }
    private fun canClick(): Boolean {
        if (System.currentTimeMillis() - clickTime > 600) {
            clickTime = System.currentTimeMillis()
            return true
        }
        return false
    }
    private fun setImage() {
        blackPath = tempPath + UUID.randomUUID() + Constants.SMALL_JPG
        bgPath = tempPath + UUID.randomUUID() + Constants.SMALL_JPG
        colorPath = tempPath + UUID.randomUUID() + Constants.SMALL_JPG

        baseImagePath = docEntity!!.baseImagePath
        smallBasePath = docEntity!!.baseSmallImagePath

        bigImagePath = docEntity!!.processImagePath
        smallImagePath = docEntity!!.processSmallImagePath

        val bitmap = ImageUtils.getBitmap(bigImagePath,2048,2048)
        isScale = bitmap.width > bitmap.height
        isHorizontal = bitmap.width > bitmap.height

        isColorHorizontal = bitmap.width > bitmap.height
        isColorScale = bitmap.width > bitmap.height

        when (docEntity!!.enhanceMode) {
            0 -> {
                setColorCheck()
            }
            1 -> {
                setBwCheck()
            }
            2 -> {
                setBgCheck()
            }
            else -> {
                setOriginalCheck()
            }
        }

        makeSmallColorPreview()
    }

    private fun checkFileType() {
        if (docEntity!!.originalImagePath.isNullOrEmpty()) darkAdjust() else showAdjust()
    }

    private fun showAdjust() {
        binding.handRl.isClickable = true
        binding.handRl.isEnabled = true

        binding.handImg.isSelected = true
        binding.handTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkAdjust() {
        binding.handRl.isClickable = false
        binding.handRl.isEnabled = false

        binding.handImg.isSelected = false
        binding.handTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun makeSmallColorPreview() {
        Fresco.getImagePipeline().evictFromCache(Uri.parse("file://$bigImagePath"))
        binding.editBigImg.setImageURI(Uri.parse("file://$bigImagePath"))
        Fresco.getImagePipeline().evictFromCache(Uri.parse("file://$baseImagePath"))
        binding.originalImg.setImageURI(Uri.parse("file://$baseImagePath"))
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = CZSaoMiao_Alg.ColorType.BW
                alg!!.miao_static_process(smallBasePath, blackPath, null, null, args)

                val args1 = Args()
                args1.scanType = getScanType()
                args1.colorType = CZSaoMiao_Alg.ColorType.BG_WHITEN
                alg!!.miao_static_process(smallBasePath, bgPath, null, null, args1)

                val args2 = Args()
                args2.scanType = getScanType()
                args2.colorType = CZSaoMiao_Alg.ColorType.AUTO
                alg!!.miao_static_process(smallBasePath, colorPath, null, null, args2)
                return null
            }

            override fun onSuccess(path: Void?) {
                Fresco.getImagePipeline().evictFromCache(Uri.parse("file://$blackPath"))
                binding.blackImg.setImageURI(Uri.parse("file://$blackPath"))

                Fresco.getImagePipeline().evictFromCache(Uri.parse("file://$colorPath"))
                binding.colorImg.setImageURI(Uri.parse("file://$colorPath"))

                Fresco.getImagePipeline().evictFromCache(Uri.parse("file://$bgPath"))
                binding.purifyImg.setImageURI(Uri.parse("file://$bgPath"))

            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                LogUtils.e(t!!.stackTrace)
            }

            override fun onCancel() {
                super.onCancel()
                LogUtils.e("onCancel")

            }
        })
    }

    private fun getScanType(): CZSaoMiao_Alg.ScanType {
        return CZSaoMiao_Alg.ScanType.NOTHING
    }

    private fun setBgCheck() {
        binding.colorLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.originalLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.blackLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.purifyLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_red_stroke))
        binding.colorTv.setTextColor(resources.getColor(R.color.white))
        binding.blackTv.setTextColor(resources.getColor(R.color.white))
        binding.purifyTv.setTextColor(resources.getColor(R.color.red_de4d4d))
        binding.originalTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun setBwCheck() {
        binding.colorLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.originalLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.purifyLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.blackLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_red_stroke))
        binding.colorTv.setTextColor(resources.getColor(R.color.white))
        binding.blackTv.setTextColor(resources.getColor(R.color.red_de4d4d))
        binding.purifyTv.setTextColor(resources.getColor(R.color.white))
        binding.originalTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun setOriginalCheck() {
        binding.blackLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.colorLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.purifyLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.originalLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_red_stroke))
        binding.colorTv.setTextColor(resources.getColor(R.color.white))
        binding.blackTv.setTextColor(resources.getColor(R.color.white))
        binding.purifyTv.setTextColor(resources.getColor(R.color.white))
        binding.originalTv.setTextColor(resources.getColor(R.color.red_de4d4d))
    }

    private fun setColorCheck() {
        binding.blackLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.colorLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_red_stroke))
        binding.purifyLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.originalLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.colorTv.setTextColor(resources.getColor(R.color.red_de4d4d))
        binding.blackTv.setTextColor(resources.getColor(R.color.white))
        binding.purifyTv.setTextColor(resources.getColor(R.color.white))
        binding.originalTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun registerEvent() {
        binding.cutRl.setOnClickListener(this)
        binding.rotateRl.setOnClickListener(this)
        binding.handRl.setOnClickListener(this)
        binding.normalBackBtn.setOnClickListener(this)
        binding.colorLl.setOnClickListener(this)
        binding.blackLl.setOnClickListener(this)
        binding.purifyLl.setOnClickListener(this)
        binding.originalLl.setOnClickListener(this)
    }


    /**
     * @des: 压缩并且保存小图
     * @params:
     * @return:
     */
    private fun makeSmallImg(isBase: Boolean): Boolean {
        if (FileUtils.createOrExistsDir(dirPath)) {
            val bitmap = ImageUtils.getBitmap(if (isBase) baseImagePath else bigImagePath,2048,2048)
            val scale = bitmap.width * 1.0 / bitmap.height
            val v = 300 * scale
            var smallBitmap: Bitmap? = null
            smallBitmap = ImageUtils.compressByScale(bitmap, v.toInt(), 300, true)
            val isSuccessSave = ImageUtils.save(smallBitmap, if (isBase) smallBasePath else smallImagePath, Bitmap.CompressFormat.JPEG, true)
            return isSuccessSave

        }
        return false
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.rotateRl -> if (canRotate!!.get()) rotateAnimAndOperate()
            R.id.colorLl -> if (docEntity!!.enhanceMode != 0) colorMode()
            R.id.blackLl -> if (docEntity!!.enhanceMode != 1) bwMode()
            R.id.purifyLl -> if (docEntity!!.enhanceMode != 2) bgMode()
            R.id.originalLl -> if (docEntity!!.enhanceMode != 3) originalMode()
            R.id.cutRl -> {
                if (canClick()){
                    val intent = Intent(this@FileEditActivity, FileCutActivity::class.java)
                    intent.putExtra("fileId", fileId)
                    intent.putExtra("baseUrl", baseImagePath);
                    intent.putExtra("url", bigImagePath)
                    ActivityUtils.startActivity(intent)
                }
            }
            R.id.handRl -> {
                if (canClick()){
                    // TODO 没有原图时手动调整按钮不可点

                    if (docEntity!!.fileType == 0) {
                        if (getSp().simpleGuideFirst){
                            val intent = Intent(this, AdjustEdgeSimpleActivity::class.java)
                            intent.putExtra("fileID", docEntity!!.fileID)
                            intent.putExtra("originalImagePath", docEntity!!.originalImagePath)
                            ActivityUtils.startActivity(intent)
                        }else{
                            val intent = Intent(this, AdjustGuideActivity::class.java)
                            intent.putExtra("fileID", docEntity!!.fileID)
                            intent.putExtra("isSimple",true)
                            intent.putExtra("originalImagePath", docEntity!!.originalImagePath)
                            ActivityUtils.startActivity(intent)
                        }

                    } else if (docEntity!!.fileType == 1) {
                        val intent = Intent(this, AdjustEdgeBookActivity::class.java)
                        intent.putExtra("fileID", docEntity!!.fileID)
                        intent.putExtra("originalImagePath", docEntity!!.originalImagePath)
                        ActivityUtils.startActivity(intent)
                    }
                }

            }
            else -> {
            }
        }
    }

    private fun rotateAnimAndOperate() {
        showProgressDialog(true)
        if (rotate == 360f) rotate = 0f
        if (colorRotate == 360f) colorRotate = 0f

        val animator = AnimatorSet()
        val scale = if (isColorScale && isColorHorizontal) binding.editBigImg.height * 1.0 / binding.editBigImg.width else binding.editBigImg.width * 1.0 / binding.editBigImg.height
        val smallScale = if (isScale && isHorizontal) binding.colorImg.height * 1.0 / binding.colorImg.width else binding.colorImg.width * 1.0 / binding.colorImg.height

        val fromScale = if (isColorScale == isColorHorizontal) 1f else scale.toFloat()
        val toScale = if (isColorScale == !isColorHorizontal) 1f else scale.toFloat()
        val fromSmallScale = if (isScale == isHorizontal) 1f else smallScale.toFloat()
        val toSmallScale = if (isScale == !isHorizontal) 1f else smallScale.toFloat()



        animator.playTogether(
                ObjectAnimator.ofFloat(binding.editBigImg, "rotation", colorRotate!!, colorRotate!! + 90f),
                ObjectAnimator.ofFloat(binding.editBigImg, "scaleX", fromScale, toScale),
                ObjectAnimator.ofFloat(binding.editBigImg, "scaleY", fromScale, toScale),

                ObjectAnimator.ofFloat(binding.colorImg, "rotation", rotate!!, rotate!! + 90f),
                ObjectAnimator.ofFloat(binding.colorImg, "scaleX", fromSmallScale, toSmallScale),
                ObjectAnimator.ofFloat(binding.colorImg, "scaleY", fromSmallScale, toSmallScale),

                ObjectAnimator.ofFloat(binding.blackImg, "rotation", rotate!!, rotate!! + 90f),
                ObjectAnimator.ofFloat(binding.blackImg, "scaleX", fromSmallScale, toSmallScale),
                ObjectAnimator.ofFloat(binding.blackImg, "scaleY", fromSmallScale, toSmallScale),

                ObjectAnimator.ofFloat(binding.purifyImg, "rotation", rotate!!, rotate!! + 90f),
                ObjectAnimator.ofFloat(binding.purifyImg, "scaleX", fromSmallScale, toSmallScale),
                ObjectAnimator.ofFloat(binding.purifyImg, "scaleY", fromSmallScale, toSmallScale),

                ObjectAnimator.ofFloat(binding.originalImg, "rotation", rotate!!, rotate!! + 90f),
                ObjectAnimator.ofFloat(binding.originalImg, "scaleX", fromSmallScale, toSmallScale),
                ObjectAnimator.ofFloat(binding.originalImg, "scaleY", fromSmallScale, toSmallScale)

        )
        animator.duration = 250
        animator.addListener(animListener)
        animator.start()
        isColorScale = !isColorScale
        isScale = !isScale
        rotate = rotate!! + 90f
        colorRotate = colorRotate!! + 90f

    }

    private val animListener = object : Animator.AnimatorListener {
        override fun onAnimationStart(animation: Animator) {
            canRotate!!.set(false)
            ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                override fun doInBackground(): Void? {
                    val bitmap = ImageUtils.getBitmap(bigImagePath,2048,2048)
                    val rotateBitmap = ImageUtils.rotate(bitmap, 90, bitmap.width * 1.0f / 2, bitmap.height * 1.0f / 2)
                    ImageUtils.save(rotateBitmap, bigImagePath, Bitmap.CompressFormat.JPEG)

                    val baseBitmap = ImageUtils.getBitmap(baseImagePath,2048,2048)
                    val rotateBaseBitmap = ImageUtils.rotate(baseBitmap, 90, baseBitmap.width * 1.0f / 2, baseBitmap.height * 1.0f / 2)
                    ImageUtils.save(rotateBaseBitmap, baseImagePath, Bitmap.CompressFormat.JPEG)
                    makeSmallImg(true)
                    makeSmallImg(false)

                    return null
                }

                override fun onSuccess(path: Void?) {
                    realm!!.executeTransaction {
                        docEntity!!.isDirty = 1
                        docEntity!!.uuid = UUID.randomUUID().toString()
                        docEntity!!.updateTime = getCurrentTime()
                    }
                    hideProgressDialog()
                    EventBus.getDefault().post(EditEvent(EventType.ROTATE))
                    canRotate!!.set(true)
                    startAutoSync()

                }

                override fun onFail(t: Throwable?) {
                    super.onFail(t)
                    LogUtils.e(t)
                }
            })
        }

        override fun onAnimationEnd(animation: Animator) {


        }

        override fun onAnimationCancel(animation: Animator) {}

        override fun onAnimationRepeat(animation: Animator) {}
    }

    private fun colorMode() {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = CZSaoMiao_Alg.ColorType.AUTO
                alg!!.miao_static_process(baseImagePath, bigImagePath, null, smallImagePath, args)
                return null
            }

            override fun onSuccess(path: Void?) {
                realm!!.executeTransaction {
                    docEntity!!.enhanceMode = 0
                    docEntity!!.isDirty = 1
                    docEntity!!.uuid = UUID.randomUUID().toString()
                    docEntity!!.updateTime = getCurrentTime()
                }

                hideProgressDialog()
                setColorCheck()
                setColorFlag()
                resetImage()
                Fresco.getImagePipeline().evictFromCache(Uri.parse("file://"+docEntity!!.processImagePath))
                binding.editBigImg.setImageURI(Uri.parse("file://"+docEntity!!.processImagePath))
                EventBus.getDefault().post(EditEvent(EventType.COLOR))
                startAutoSync()

            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                hideProgressDialog()
                LogUtils.e(t)
            }
        })
    }

    /**
     * 重置大图的flag
     *
     * @param: []
     * @return: []
     */

    private fun setColorFlag() {
        val bitmap = ImageUtils.getBitmap(bigImagePath,2048,2048)
        isColorHorizontal = bitmap.width > bitmap.height
        isColorScale = bitmap.width > bitmap.height
    }

    /**
     * 重置小图的flag
     *
     * @param: []
     * @return: []
     */
    private fun setPreviewFlag() {
        val bitmap = ImageUtils.getBitmap(bigImagePath,2048,2048)
        isHorizontal = bitmap.width > bitmap.height
        isScale = bitmap.width > bitmap.height
    }

    private fun resetImage() {
        val scale = if (isColorScale && isColorHorizontal) binding.editBigImg.height * 1.0 / binding.editBigImg.width else binding.editBigImg.width * 1.0 / binding.editBigImg.height
        val toScale = if (isColorScale == isColorHorizontal) 1f else scale.toFloat()
        binding.editBigImg.scaleX = toScale
        binding.editBigImg.scaleY = toScale
        binding.editBigImg.rotation = 0f
        colorRotate = 0f
    }

    private fun resetPreviewImage() {
        val scale = if (isScale && isHorizontal) binding.colorImg.height * 1.0 / binding.colorImg.width else binding.colorImg.width * 1.0 / binding.colorImg.height
        val toScale = if (isScale == isHorizontal) 1f else scale.toFloat()
        binding.blackImg.scaleX = toScale
        binding.blackImg.scaleY = toScale
        binding.blackImg.rotation = 0f

        binding.colorImg.scaleX = toScale
        binding.colorImg.scaleY = toScale
        binding.colorImg.rotation = 0f

        binding.purifyImg.scaleX = toScale
        binding.purifyImg.scaleY = toScale
        binding.purifyImg.rotation = 0f

        binding.originalImg.scaleX = toScale
        binding.originalImg.scaleY = toScale
        binding.originalImg.rotation = 0f
        rotate = 0f
    }

    private fun bgMode() {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = CZSaoMiao_Alg.ColorType.BG_WHITEN
                alg!!.miao_static_process(baseImagePath, bigImagePath, null, smallImagePath, args)

                return null
            }

            override fun onSuccess(path: Void?) {
                hideProgressDialog()

                realm!!.executeTransaction {
                    docEntity!!.enhanceMode = 2
                    docEntity!!.isDirty = 1
                    docEntity!!.uuid = UUID.randomUUID().toString()
                    docEntity!!.updateTime = getCurrentTime()

                }
                setBgCheck()
                setColorFlag()
                resetImage()
                Fresco.getImagePipeline().evictFromCache(Uri.parse("file://"+docEntity!!.processImagePath))
                binding.editBigImg.setImageURI(Uri.parse("file://"+docEntity!!.processImagePath))
                EventBus.getDefault().post(EditEvent(EventType.COLOR))
                startAutoSync()

            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                hideProgressDialog()

                LogUtils.e(t)
            }
        })
    }


    private fun bwMode() {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = CZSaoMiao_Alg.ColorType.BW
                alg!!.miao_static_process(baseImagePath, bigImagePath, null, smallImagePath, args)

                return null
            }

            override fun onSuccess(path: Void?) {
                hideProgressDialog()
                realm!!.executeTransaction {
                    docEntity!!.enhanceMode = 1
                    docEntity!!.isDirty = 1
                    docEntity!!.uuid = UUID.randomUUID().toString()
                    docEntity!!.updateTime = getCurrentTime()
                }
                setBwCheck()
                setColorFlag()
                resetImage()
                Fresco.getImagePipeline().evictFromCache(Uri.parse("file://"+docEntity!!.processImagePath))
                binding.editBigImg.setImageURI(Uri.parse("file://"+docEntity!!.processImagePath))
                EventBus.getDefault().post(EditEvent(EventType.COLOR))
                startAutoSync()

            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                LogUtils.e(t)
            }
        })
    }


    private fun originalMode() {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = CZSaoMiao_Alg.ColorType.NO_TRANS
                alg!!.miao_static_process(baseImagePath, bigImagePath, null, smallImagePath, args)
                return null
            }

            override fun onSuccess(path: Void?) {
                hideProgressDialog()
                realm!!.executeTransaction {
                    docEntity!!.enhanceMode = 3
                    docEntity!!.isDirty = 1
                    docEntity!!.uuid = UUID.randomUUID().toString()
                    docEntity!!.updateTime = getCurrentTime()
                }
                setOriginalCheck()
                setColorFlag()
                resetImage()
                Fresco.getImagePipeline().evictFromCache(Uri.parse("file://"+docEntity!!.processImagePath))
                binding.editBigImg.setImageURI(Uri.parse("file://"+docEntity!!.processImagePath))
                EventBus.getDefault().post(EditEvent(EventType.COLOR))
                startAutoSync()
            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                hideProgressDialog()

                LogUtils.e(t)
            }
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        val realm = Realm.getDefaultInstance()
        when (event.eventType) {

            EventType.CUT, EventType.ADJUST ,EventType.EDGE_ADJUST_IN_CAMERA-> {
                LogUtils.e("edit")

                setColorFlag()
                resetImage()
                setPreviewFlag()
                resetPreviewImage()
                makeSmallColorPreview()
            }


            else -> {
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                CleanUtils.cleanCustomDir(cacheDir.path + Constants.TEMP)
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}