package com.czur.scanpro.ui.base

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.drawable.AnimationDrawable
import android.os.*
import android.util.Log
import android.view.KeyEvent
import android.view.View
import com.blankj.utilcode.util.*
import com.blankj.utilcode.util.FileUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityIndexBinding
import com.czur.scanpro.databinding.ActivityWelcomeAdBinding
import com.czur.scanpro.databinding.ActivityWelcomeBinding
import com.czur.scanpro.entity.model.ADModel
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.WelcomeADActivity.DisplayType.*
import com.czur.scanpro.ui.base.WelcomeADActivity.PositionOfTheCloseButton.*
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.PrivacyPopup
import com.czur.scanpro.ui.download.HttpDownloader
import com.czur.scanpro.utils.*
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import com.umeng.commonsdk.UMConfigure
import io.realm.Realm
import io.realm.Sort
import kotlinx.coroutines.delay
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class WelcomeADActivity : BaseActivity(), View.OnClickListener {
    private lateinit var binding: ActivityWelcomeAdBinding


    private var userPreferences: UserPreferences? = null
    private lateinit var realm: Realm
    var commonPopup: PrivacyPopup? = null
    lateinit var nowAdModel: ADModel
    lateinit var nowAdDataBean: ADModel.DataBean
    var nowAdFilePath = ""
    private lateinit var nowAdFileTypeEnum: DisplayType //显示方式 1静态图片 2视频 3轮播图片
    var hasIntentWebView = false
    var currentPosition = 0
    var advertisingThatCanBePlayedList = arrayListOf<ADModel.DataBean>()
    var countDownTime = -1
    var AD_DOCUMENT_PATH = ""
    var countDownTimer: CountDownTimer? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.transparent)
        setTranslucentBar(this, true);
        setContentView(R.layout.activity_welcome_ad)
//        BarUtils.setStatusBarLightMode(this, true)
        binding = ActivityWelcomeAdBinding.inflate(layoutInflater)
        setContentView(binding.root)
        launch {
            LogUtils.i("WelcomeActivity.onCreate()11")
            delay(1500)
            LogUtils.i("WelcomeActivity.onCreate()22")
            initComponent()
        }

        LogUtils.i("WelcomeActivity.onCreate()")
    }

    fun isToday(timestamp: Long): Boolean {
        val currentTime = System.currentTimeMillis()
        val format = SimpleDateFormat("yyyy-MM-dd")
        val dateStr = format.format(Date(currentTime))
        val currentDate = format.format(Date(timestamp))
        return dateStr == currentDate
    }


    private fun initAD() {
        val random = (0 until advertisingThatCanBePlayedList.size).shuffled().last()
        nowAdDataBean = advertisingThatCanBePlayedList[random]
        nowAdFileTypeEnum = DisplayType.create(nowAdDataBean.displayType)
        nowAdFilePath =
            AD_DOCUMENT_PATH + FileUtils.getFileName(nowAdDataBean.imagesVo.verticalImages[0].imageUrl)
        countDownTime = nowAdDataBean.seconds + 1;//倒计时次数

        binding.hasAdRl.visibility = View.VISIBLE
        binding.noAdRl.visibility = View.GONE

        binding.skipBtnRightTopRl.setOnClickListener(this)
        binding.skipBtnRightDownRl.setOnClickListener(this)

        binding.adVv.setOnPreparedListener {

            it.setVolume(0f, 0f)
            it.setOnSeekCompleteListener {
                binding.adVv.start()
            }
        }

        when (PositionOfTheCloseButton.create(nowAdDataBean.closeButton)) {
            RIGHT_UP -> {
                binding.skipBtnRightTopRl.visibility = View.VISIBLE
                binding.skipBtnRightDownRl.visibility = View.GONE
            }
            RIGHT_DOWN -> {
                binding.skipBtnRightTopRl.visibility = View.GONE
                binding.skipBtnRightDownRl.visibility = View.VISIBLE
            }
        }
        //显示方式 1静态图片 2视频 3轮播图片
        when (nowAdFileTypeEnum) {
            IMAGE -> {
                Glide.with(this)
                    .asBitmap()
                    .load(nowAdFilePath)
                    .diskCacheStrategy(DiskCacheStrategy.NONE)
                    .into(binding.adIv);
                binding.adIv.visibility = View.VISIBLE
                binding.adVv.visibility = View.GONE
            }
            VIDEO -> {
                val videoFile = File(nowAdFilePath)
                binding.adVv.setVideoPath(videoFile.path)
                binding.adVv.start()


                binding.adIv.visibility = View.GONE
                binding.adVv.visibility = View.VISIBLE
            }
            CAROUSEL_CHART -> {

            }
        }

        val anim = binding.adClickIntentIv.background as AnimationDrawable
        anim.start()

        binding.countdownDownTv.text = ToDBC(countDownTime.toString())
        binding.countdownTopTv.text = ToDBC(countDownTime.toString())
        binding.countdownUpSkipTv.text = ToDBC(getString(R.string.ad_skip))
        binding.countdownDownSkipTv.text = ToDBC(getString(R.string.ad_skip))

        countDownTimer = object : CountDownTimer((countDownTime * 1000).toLong(), 1000) {
            override fun onTick(millisUntilFinished: Long) {
                Log.d("song","onTickonTickonTickonTick")
                countDownTime--
                if (countDownTime < 0) {
                    countDownTime = 0
                }
                binding.countdownDownTv.text = ToDBC(countDownTime.toString())
                binding.countdownTopTv.text = ToDBC(countDownTime.toString())
            }

            override fun onFinish() {
                if (!hasIntentWebView) {
                    delayIntentToIndex()
                }
            }
        }.start()

        binding.clickIntentTv.setOnClickListener {

            when (nowAdDataBean.jumpWay) {
                0 -> {
                    hasIntentWebView = true
                    intentToIndexActivity()
                    Intent(this, WebViewActivity::class.java).apply {
                        putExtra("title", nowAdDataBean.name)
                        putExtra("url", nowAdDataBean.targetUrl)
                        startActivity(this)
                    }
                    this.finish()
                }
                1 -> {//跳转各种商城
                    hasIntentWebView = true
                    requestRecordUrl()
                    intentToIndexActivity()
                    try {
                        when (nowAdDataBean.platform) {
                            "JD" -> {
                                openJD(this, nowAdDataBean.productId)
                            }
                            "Taobao" -> {
                                openTaobao(this, nowAdDataBean.productId)
                            }
                            "Tmall" -> {
                                openTianMao(this, nowAdDataBean.productId)
                            }
                            "Youpin" -> {//小米有品
                                openXiaoMiYouPin(this, nowAdDataBean.targetUrl)
                            }
                            "Pdd" -> {//拼多多
                                openPdd(this, nowAdDataBean.productId)
                            }
                            "offical" -> {//官网
                                openBrowser(this, nowAdDataBean.targetUrl)
                            }
                        }
                    } catch (e: Exception) {
                        LogUtils.i("没有安装对应项,跳转网页:${nowAdDataBean.targetUrl}")
                        openBrowser(this, nowAdDataBean.targetUrl)
                    } finally {
                        this.finish()
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (currentPosition != 0) {
            var i = nowAdDataBean.seconds - (countDownTime - 1)
            if (i < 0) {
                i = 0
            }
            binding.adVv!!.seekTo(i * 1000)
        }

        if (countDownTime == 0) {
            intentToIndexActivity()
        }
    }

    override fun onPause() {
        super.onPause()
//        adVv.pause()
        currentPosition = binding.adVv!!.currentPosition
    }

    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        val hasAd = checkCachedAdvertisements()
        if (!userPreferences!!.isFirstPrivacyPolicy && hasAd) {
            init()
            initAD()
        } else {
            binding.hasAdRl.visibility = View.GONE
            binding.noAdRl.visibility = View.VISIBLE

            if (userPreferences!!.isFirstPrivacyPolicy) {
                if (commonPopup == null || commonPopup?.isShowing == false) {
                    showPrivacyDialog()
                }
            } else {
                LogUtils.e("xxx", MemoryUtils.getAvailMemory(this))
                val tz = TimeZone.getDefault()
                val s =
                    "TimeZone   " + tz.getDisplayName(
                        false,
                        TimeZone.SHORT
                    ) + " Timezon id :: " + tz.id
                LogUtils.e(s)
                init()
                delayIntentToIndex()
            }
        }

        LogUtils.i("WelcomeActivity.initComponent()")
    }

    // 检查是否有缓存的广告,和未下载完成的
    private fun checkCachedAdvertisements(): Boolean {
        AD_DOCUMENT_PATH = getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.getAbsolutePath()
            .toString() + Constants.SD_PATH + Constants.AD_PATH
        val jsonPath = AD_DOCUMENT_PATH + "adJson.txt"

        advertisingThatCanBePlayedList = arrayListOf<ADModel.DataBean>()//可以播放的广告列表
        if (File(jsonPath).exists()) {
            val readTxtFile = HttpDownloader.readTxtFile(jsonPath)
            nowAdModel = Gson().fromJson(readTxtFile, ADModel::class.java)
            if (nowAdModel.data == null || nowAdModel.data.size == 0) {
                return false
            }

            for (data in nowAdModel.data) {
                if (data.beginTime.toLong() < System.currentTimeMillis()
                    && System.currentTimeMillis() < data.endTime.toLong()
                ) {//在播放时间内
                    var fileExists = true
                    //判断本地文件是否齐全
                    if (data.imagesVo.verticalImages != null && data.imagesVo.verticalImages.size > 0) {
                        for (image in data.imagesVo.verticalImages) {
                            val filePath = AD_DOCUMENT_PATH + FileUtils.getFileName(image.imageUrl)
                            if (!File(filePath).exists()) {
                                //文件不齐全
                                fileExists = false
                            }
                        }
                    }
                    if (fileExists && data.displayType != 3) { // 暂时不支持轮播图形式
                        advertisingThatCanBePlayedList.add(data)
                    }
                }
            }
        } else {
            return false
        }

        // 检查播放次数是否达到3次
        val adPlayRecord = userPreferences!!.adPlayRecord.toString()
        val adPlayRecordSplit = adPlayRecord.split("_")

        if (adPlayRecordSplit.size == 2) {
//
            if (System.currentTimeMillis() - adPlayRecordSplit[0].toLong() <= 60 * 60 * 6 * 1000) {//是今天的话,看看保存的时间和现在是否间隔6小时
                //小于6小时,不播放
                return false
            } else {
                if (!isToday(adPlayRecordSplit[0].toLong())) {//不是今天日期,继续
                    userPreferences!!.adPlayRecord = System.currentTimeMillis().toString() + "_1"
                } else if (adPlayRecordSplit[1].toInt() < 3) {
                    userPreferences!!.adPlayRecord = System.currentTimeMillis()
                        .toString() + "_" + (adPlayRecordSplit[1].toInt() + 1).toString()
                } else if (adPlayRecordSplit[1].toInt() >= 3) {// 今天日期并且播放达到3次,return false
                    return false
                } else {//其他未知情况,不播放
                    return false
                }
            }
        } else {
            userPreferences!!.adPlayRecord = System.currentTimeMillis().toString() + "_1"
        }

        return advertisingThatCanBePlayedList.size > 0
    }

    private fun delayIntentToIndex() {
        launch {
            delay(500)
            <EMAIL>()
            intentToIndexActivity()
        }
    }

    //首次安装紧调用一次
    private fun showPrivacyDialog() {
//        val builder = PrivacyPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        val builder = PrivacyPopup.Builder(this, CloudCommonPopupConstants.COMMON_TWO_BUTTON)
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            userPreferences!!.setIsFirstPrivacyPolicy(false)
            dialog.dismiss()
            initializeUtils.initAll(application)
            LogUtils.e("xxx", MemoryUtils.getAvailMemory(this))
            val tz = TimeZone.getDefault()
            val s =
                "TimeZone   " + tz.getDisplayName(
                    false,
                    TimeZone.SHORT
                ) + " Timezon id :: " + tz.id
            LogUtils.e(s)
            init()
            delayIntentToIndex()
        })
        builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, _ ->
            dialog.dismiss()
            ActivityUtils.finishAllActivities()
        })
        commonPopup = builder.create()
        commonPopup?.show()
    }

    private fun init() {

        UMConfigure.init(
            this,
            "5cf0ed994ca3575330000ba2",
            getChannel(this),
            UMConfigure.DEVICE_TYPE_PHONE,
            ""
        )
        realm = Realm.getDefaultInstance()
//        userPreferences!!.sdPath = Environment.getExternalStorageDirectory().toString() + Constants.SD_PATH

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
            getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.Q
        ) {
            userPreferences!!.sdPath =
                getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS).toString() + Constants.SD_PATH
        } else {
            userPreferences!!.sdPath =
                Environment.getExternalStorageDirectory().toString() + Constants.SD_PATH
        }
        var imei = userPreferences?.imei
        var udid = userPreferences?.udid
        val channel = userPreferences?.channel
        val endpoint = userPreferences?.endpoint
        if (Validator.isEmpty(imei)) {
            imei = UUID.randomUUID().toString()
            userPreferences!!.imei = imei
        }
        if (Validator.isEmpty(udid)) {
            udid = UUID.randomUUID().toString()
            userPreferences!!.udid = udid
        }
        if (Validator.isEmpty(channel)) {
            userPreferences!!.channel = Constants.CHANNEL
        }
        if (Validator.isEmpty(endpoint)) {
            userPreferences!!.endpoint = Constants.ENDPOINT
        }
        createTag(realm)
        resetTemp(realm)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                ResourceUtils.copyFileFromAssets(
                    "template_corner.jpg",
                    <EMAIL> + File.separator + "template_corner.jpg"
                )
                val tessPath =
                    <EMAIL> + File.separator + "tessdata"
                if (FileUtils.createOrExistsDir(tessPath)) {
                    ResourceUtils.copyFileFromAssets(
                        "eng.traineddata",
                        tessPath + File.separator + "eng.traineddata"
                    )
                    ResourceUtils.copyFileFromAssets(
                        "osd.traineddata",
                        tessPath + File.separator + "osd.traineddata"
                    )

                }

                val pdfPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.PDF_PATH
                val orExistsPdfPath = FileUtils.createOrExistsDir(pdfPath)
                val apkPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath.toString() + Constants.SD_PATH + Constants.APK_PATH
                val orExistsApkPath = FileUtils.createOrExistsDir(apkPath)
                val ocrPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.OCR_PATH
                val orExistsOcrPath = FileUtils.createOrExistsDir(ocrPath)

                val handwritingPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.HANDWRITING_PATH
                val orExistsHandwritingPath = FileUtils.createOrExistsDir(handwritingPath)
                FileUtils.createOrExistsDir(cacheDir.path + Constants.TEMP)
                CleanUtils.cleanCustomDir(cacheDir.path + Constants.TEMP)
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })
        startSyncNow()

    }


    /**
     * @des: 返回时设置temp为0
     * @params:
     * @return:
     */
    private fun resetTemp(realm: Realm) {
        realm.executeTransaction(Realm.Transaction {
            val docEntities = realm.where(DocEntity::class.java)
                .equalTo("isTemp", 1.toInt())
                .findAll().sort("takePhotoTime", Sort.ASCENDING)

            for (docEntity in docEntities) {
                docEntity.isTemp = 0
                docEntity.takePhotoTime = Constants.TAKE_PHOTO_INIT_TIME
            }

            for (docEntity in realm.where(DocEntity::class.java).equalTo("isDelete", 0.toInt())
                .equalTo("isNameTemp", 1.toInt()).findAll()) {
                docEntity.isNameTemp = 0
            }
            for (categoryEntity in realm.where(CategoryEntity::class.java)
                .equalTo("isDelete", 0.toInt()).equalTo("isNameTemp", 1.toInt()).findAll()) {
                categoryEntity.isNameTemp = 0
            }
        })

    }

    private fun registerEvent() {

    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.skipBtnRightTopRl, R.id.skipBtnRightDownRl -> {
                intentToIndexActivity()
                finish()
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            //不执行父类点击事件
            true
        } else {
            super.onKeyDown(keyCode, event)
        }

    }


    override fun onDestroy() {
        super.onDestroy()
        if (binding.adVv != null) {
            binding.adVv.suspend()
        }
        if (this::realm.isInitialized)
            realm.close()
    }

    fun getChannel(context: Context): String? {
        try {
            val appInfo = context.packageManager
                .getApplicationInfo(
                    context.packageName,
                    PackageManager.GET_META_DATA
                )
            return appInfo.metaData.getString("UMENG_CHANNEL")
        } catch (e: Exception) {
        }

        return "getChannelException"
    }

    //每次点击外部跳转都调用
    fun requestRecordUrl() {
        val url = nowAdDataBean.countUrl
        if (url.isEmpty()) {
            return
        }
        val okHttpClient = MiaoHttpManager.getInstance().client
        val request = Request.Builder()
            .header("udid", userPreferences!!.getIMEI())
            .header("App-Key", Constants.SCAN_PRO)
            .header("U-ID", userPreferences!!.getUserId())
            .header("T-ID", userPreferences!!.getToken())
            .header("Channel", userPreferences!!.getChannel())
            .header("phoneModel", Build.MODEL)
            .get()
            .url(url)
            .build()
        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                LogUtils.e("requestRecordUrl" + e)
            }

            @Throws(IOException::class)
            override fun onResponse(call: Call, response: Response) {
                //此方法运行在子线程中，不能在此方法中进行UI操作。
                val result = response.body?.string()

                LogUtils.e("requestRecordUrl:$result")
            }
        })
    }

    //解决数字字母汉子混合使用时,排版不齐的问题
    fun ToDBC(input: String): String {
        val c = input.toCharArray()
        for (i in c.indices) {
            if (c[i].code == 12288) {
                c[i] = 32.toChar()
                continue
            }
            if (c[i].code > 65280 && c[i].code < 65375) c[i] = (c[i].code - 65248).toChar()
        }
        return String(c)
    }


    fun intentToIndexActivity() {
        countDownTimer?.cancel()
        if (isActive) {
            val intent = Intent(this@WelcomeADActivity, IndexActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            ActivityUtils.startActivity(intent)
        }

    }

    enum class DisplayType(val type: Int) {
        IMAGE(1),
        VIDEO(2),
        CAROUSEL_CHART(3);

        companion object {
            fun create(type: Int): DisplayType {
                return values().find {
                    it.type == type
                } ?: IMAGE
            }
        }
    }

    enum class PositionOfTheCloseButton(val type: Int) {
        RIGHT_UP(1),
        RIGHT_DOWN(2);

        companion object {
            fun create(type: Int): PositionOfTheCloseButton {
                return values().find {
                    it.type == type
                } ?: RIGHT_UP
            }
        }
    }
}

