package com.czur.scanpro.ui.user

import android.os.Bundle
import android.os.StrictMode
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.scanpro.R
import com.czur.scanpro.adapter.InviteDetailAdapter
import com.czur.scanpro.databinding.ActivityInviteResultBinding
import com.czur.scanpro.entity.model.pay.SaoMiaoPrice
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.google.gson.Gson
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Request
import okhttp3.Response
import java.io.IOException
import java.util.*


/**
 * Created by shaojun
 */
class InviteResultActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityInviteResultBinding by lazy{
        ActivityInviteResultBinding.inflate(layoutInflater)
    }

    private var userPreferences: UserPreferences? = null
    private var days: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val policy = StrictMode.ThreadPolicy.Builder().permitAll().build()
        StrictMode.setThreadPolicy(policy)
        setStatusBarColor(com.czur.scanpro.R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(com.czur.scanpro.R.layout.activity_invite_result)
        setContentView(binding.root)
        initComponent()
        registerEvent()
        getVipDetail()
    }

    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        days = intent.getStringExtra("days")
        binding.recyclerview.setHasFixedSize(true)
        binding.recyclerview.layoutManager =
            LinearLayoutManager(this)
    }

    private fun registerEvent() {
        binding.registerTopBar.normalBackBtn.setOnClickListener(this)
        binding.confirmBtn.setOnClickListener(this)
    }


    private fun getVipDetail() {
        showProgressDialog()
        val checkRequest = Request.Builder().url(getString(R.string.price_url)).get().build()
        val checkCall = MiaoHttpManager.getInstance().client.newCall(checkRequest)
        checkCall.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    hideProgressDialog()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val saoMiaoPrice = Gson().fromJson(response.body?.string(), SaoMiaoPrice::class.java)
                runOnUiThread {
                    if (saoMiaoPrice != null) {
                        val list = ArrayList(saoMiaoPrice.invitationCodeResult.vipFunction)
                        binding.recyclerview.adapter = InviteDetailAdapter(this@InviteResultActivity, list)
                        binding.vipDaysTv.text = String.format("%s" + saoMiaoPrice.invitationCodeResult.daysSuffix, days)
                        binding.inviteTitle.text = saoMiaoPrice.invitationCodeResult.title
                        binding.confirmBtn.text = saoMiaoPrice.invitationCodeResult.buttonText
                        binding.inviteResultPrompt.text = saoMiaoPrice.invitationCodeResult.promptText
                    }
                    hideProgressDialog()
                }
            }
        })
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> ActivityUtils.finishToActivity(UserActivity::class.java, false)
            R.id.confirmBtn -> {
                ActivityUtils.startActivity(UserInviteActivity::class.java)
            }
            else -> {
            }
        }
    }


}