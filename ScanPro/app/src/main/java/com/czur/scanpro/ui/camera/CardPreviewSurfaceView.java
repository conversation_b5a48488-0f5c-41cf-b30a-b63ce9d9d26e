package com.czur.scanpro.ui.camera;

import android.content.Context;
import android.graphics.Rect;
import android.hardware.Camera;
import android.os.Handler;
import androidx.annotation.IntDef;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.SurfaceView;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;


/**
 * SurfaceView to show LenxCameraPreview2 feed
 */
public class CardPreviewSurfaceView extends SurfaceView {

    public static final int FOCUS_WHITE = 0;
    public static final int FOCUS_GREEN = 1;
    public static final int FOCUS_RED = 2;
    private float x;
    private float y;
    private float x1;
    private float y1;

    @IntDef({FOCUS_WHITE, FOCUS_GREEN, FOCUS_RED})
    public @interface FocusColor {
    }

    private CardCameraPreview camPreview;
    private boolean isTouchFocus = false;
    private CardFocusView focusView;
    private boolean isDraw = false;
    private Handler handler = new Handler();
    private Rect touchRect;

    public CardPreviewSurfaceView(Context context) {
        this(context, null);
    }

    public CardPreviewSurfaceView(Context context, AttributeSet attrs) {
        super(context, attrs);
//        getHolder().addCallback(cameraPreview);
//        getHolder().setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), MeasureSpec.getSize(heightMeasureSpec));
    }

    private OnSlideListener onSlideListener;

    interface OnSlideListener {
        void onSlide(boolean isRight);
    }

    public void setOnSlideListener(OnSlideListener onSlideListener) {
        this.onSlideListener = onSlideListener;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isTouchFocus) {
            return false;
        }
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                x1 = event.getX();
                y1 = event.getY();

                break;
            case MotionEvent.ACTION_UP:
                //当手指离开的时候
                float x2 = event.getX();
                float y2 = event.getY();
                LogUtils.d("aaa", x2, y1, x2 - x1);
                if (x1 - x2 > 100 && Math.abs(x1 - x2) > Math.abs(y1 - y2)) {

                    if (onSlideListener != null) {
                        onSlideListener.onSlide(false);
                    }
                } else if (x2 - x1 > 100 && Math.abs(x1 - x2) > Math.abs(y1 - y2)) {

                    if (onSlideListener != null) {
                        onSlideListener.onSlide(true);
                    }
                } else {

                    float chang = ScreenUtils.getScreenDensity() * 20;
                    touchRect = new Rect((int) (x2 - chang), (int) (y2 - chang), (int) (x2 + chang), (int) (y2 + chang));
                    Rect targetFocusRect = new Rect(
                            touchRect.left * 2000 / this.getWidth() - 1000,
                            touchRect.top * 2000 / this.getHeight() - 1000,
                            touchRect.right * 2000 / this.getWidth() - 1000,
                            touchRect.bottom * 2000 / this.getHeight() - 1000);
                    if (isDraw) {
                        focusView.show(touchRect, 0xEEFFFFFF);
                    }
                    camPreview.doTouchFocus(targetFocusRect, focusView, touchRect);
                }
                break;
            default:
                break;
        }

        return true;
    }


    public void hideFocusView() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                focusView.hide();
            }
        }, 500);
    }

    private class CustomAutoFocusCallback implements Camera.AutoFocusCallback {

        private Rect rect;

        public CustomAutoFocusCallback(Rect rect) {
            this.rect = rect;
        }

        @Override
        public void onAutoFocus(boolean success, Camera camera) {
            if (success) {
                focusView.show(rect, 0xFF00FF00);
                camera.cancelAutoFocus();
            } else {
                focusView.show(rect, 0x7300FF00);
            }
            hideFocusView();
        }
    }


    /**
     * set CameraPreview instance for touch focus.
     *
     * @param camPreview - CameraPreview
     */
    public void setListener(CardCameraPreview camPreview) {
        this.camPreview = camPreview;
        isTouchFocus = true;
    }

    /**
     * set DrawingView instance for touch focus indication.
     *
     * @param dView - DrawingView
     */
    public void setFocusView(CardFocusView dView) {
        focusView = dView;
        isDraw = true;
    }

}