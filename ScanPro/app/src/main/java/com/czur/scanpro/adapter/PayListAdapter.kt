package com.czur.scanpro.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.czur.scanpro.R
import io.realm.Realm


/**
 * Created by Yz on 2019/1/5.
 * Email：<EMAIL>
 */
class PayListAdapter
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<String>?) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    val realm = Realm.getDefaultInstance()

    companion object {
        private const val ITEM_TYPE_NORMAL = 0
    }


    fun refreshData(vipsCons: List<String>) {
        this.datas = vipsCons
        notifyDataSetChanged()

    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_pay_gird, parent, false)
        return VipDifferenceHolder(view)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        if (holder is VipDifferenceHolder) {
            holder.mItem = datas!![position]

            holder.itemTv.text = holder.mItem!!


        }

    }

    override fun getItemViewType(position: Int): Int = ITEM_TYPE_NORMAL

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int {
        return datas!!.size
    }


    private inner class VipDifferenceHolder internal constructor(val mView: View) : RecyclerView.ViewHolder(mView) {
        internal var mItem: String? = null
        internal var itemTv: TextView


        init {

            itemTv = mView.findViewById<View>(R.id.item_tv) as TextView

        }


    }


}
