package com.czur.scanpro.ui.album;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.scanpro.BuildConfig;
import com.czur.scanpro.R;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.entity.model.RegisterModel;
import com.czur.scanpro.event.EventType;
import com.czur.scanpro.event.UserInfoEvent;
import com.czur.scanpro.network.HttpManager;
import com.czur.scanpro.network.core.MiaoHttpEntity;
import com.czur.scanpro.network.core.MiaoHttpManager;
import com.czur.scanpro.network.core.SyncHttpTask;
import com.czur.scanpro.preferences.UserPreferences;
import com.czur.scanpro.ui.account.LoginActivity;
import com.czur.scanpro.ui.base.BaseActivity;
import com.czur.scanpro.ui.user.UserActivity;
import com.czur.scanpro.utils.BitmapUtils;
import com.czur.scanpro.utils.MD5Utils;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import cn.forward.androids.utils.ImageUtils;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


/**
 * Created by Yz on 2018/3/15
 * Email：<EMAIL>
 */

public class ImageCropActivity extends BaseActivity implements View.OnClickListener, CropImageView.OnBitmapSaveCompleteListener {

    private CropImageView mCropImageView;
    private Bitmap mBitmap;
    private boolean mIsSaveRectangle;
    private int mOutputX;
    private int mOutputY;
    private ArrayList<ImageItem> mImageItems;
    private ImagePicker imagePicker;
    private String path;
    private UserPreferences userPreferences;
    private Handler handler = new Handler();
    private TextView okBtn;
    private ImageView backBtn;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black);
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_image_crop);
        initComponent();
        registerEvent();

        //获取需要的参数
        mOutputX = 1000;
        mOutputY = 1000;
        mIsSaveRectangle = false;
        mImageItems = new ArrayList<>();
        String imagePath = path;

        mCropImageView.setFocusStyle(CropImageView.Style.CIRCLE);
        mCropImageView.setFocusWidth(SizeUtils.dp2px(320));
        mCropImageView.setFocusHeight(SizeUtils.dp2px(320));
        mCropImageView.setBorderColor(getResources().getColor(R.color.white));
        mCropImageView.setBorderWidth(SizeUtils.dp2px(1));

        //缩放图片
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(imagePath, options);
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        options.inSampleSize = calculateInSampleSize(options, displayMetrics.widthPixels, displayMetrics.heightPixels);
        options.inJustDecodeBounds = false;
        mBitmap = BitmapFactory.decodeFile(imagePath, options);
        //设置默认旋转角度
        mCropImageView.setImageBitmap(mCropImageView.rotate(mBitmap, BitmapUtils.getBitmapDegree(imagePath)));

    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        imagePicker = ImagePicker.getInstance();
        path = getIntent().getStringExtra("path");
        TextView desTv = (TextView) findViewById(R.id.tv_des);
        desTv.setText(getString(R.string.move_and_scale));
        backBtn = (ImageView) findViewById(R.id.btn_back);
        okBtn = (TextView) findViewById(R.id.btn_ok);
        mCropImageView = (CropImageView) findViewById(R.id.cv_crop_image);

    }

    private void registerEvent() {
        backBtn.setOnClickListener(this);
        okBtn.setOnClickListener(this);
        mCropImageView.setOnBitmapSaveCompleteListener(this);
    }

    public int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        int width = options.outWidth;
        int height = options.outHeight;
        int inSampleSize = 1;
        if (height > reqHeight || width > reqWidth) {
            if (width > height) {
                inSampleSize = width / reqWidth;
            } else {
                inSampleSize = height / reqHeight;
            }
        }
        return inSampleSize;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_back) {
            setResult(RESULT_CANCELED);
            finish();
        } else if (id == R.id.btn_ok) {
            showProgressDialog(true);
            mCropImageView.saveBitmapToFile(imagePicker.getCropCacheFolder(this), mOutputX, mOutputY, mIsSaveRectangle);
        }
    }

    @Override
    public void onBitmapSaveSuccess(final File file) {
        LogUtils.i("裁剪成功:" + file.getName());
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                upload(file.getName(), file.getAbsolutePath());
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

//        //裁剪后替换掉返回数据的内容，但是不要改变全局中的选中数据
//        ImageItem imageItem = new ImageItem();
//        imageItem.path = file.getAbsolutePath();
//        mImageItems.add(imageItem);
//        LogUtils.e(new Gson().toJson(mImageItems));
//        Intent intent = new Intent();
//        intent.putExtra(ImagePicker.EXTRA_RESULT_ITEMS, mImageItems);
//        setResult(ImagePicker.RESULT_CODE_ITEMS, intent);   //单选不需要裁剪，返回数据
    }

    @Override
    public void onBitmapSaveError(File file) {

    }

    /**
     * @des: 上传
     * @params:
     * @return:
     */

    private void upload(final String fileName, String path) {
        Log.e("xxx", fileName + "/////" + path);
        try {

            Response response = requestUpload(fileName, path);
            LogUtils.i("-- 上传头像 start --");
            String jsonString = response.body().string();
            LogUtils.i(jsonString);
            LogUtils.i("-- 上传头像 end --");
            final JSONObject jsonObject = new JSONObject(jsonString);
            // 请求成功
            if (response.isSuccessful()) {
                String code = jsonObject.getString(SyncHttpTask.DATA_CODE);
                // 上传成功
                if (code.equals(MiaoHttpManager.STATUS_SUCCESS + Constants.EMPTY)) {
                    afterUpload(jsonObject, fileName);
                }
                // token过期
                else if (code.equals(MiaoHttpManager.STATUS_TIMEOUT + Constants.EMPTY)) {
                    boolean isThirdParty = userPreferences.isThirdParty();
                    String thirdPartyOpenid = userPreferences.getThirdPartyOpenid();
                    final String thirdPartyPlatName = userPreferences.getThirdPartyPlatName();
                    String servicePlatName = userPreferences.getServicePlatName();
                    String thirdPartyToken = userPreferences.getThirdPartyToken();
                    if (isThirdParty) {
                        thirdPartyTimeout(fileName, path, thirdPartyOpenid, thirdPartyPlatName, servicePlatName, thirdPartyToken);
                    } else {
                        normalTokenTimeout(fileName, path);
                    }
                }
                // 其他code失败
                else {
                    FailedToast();
                }
            }
            // 请求失败
            else {
                FailedToast();
            }
        } catch (IOException | JSONException e) {
            LogUtils.e(e);
            FailedToast();
        } catch (Exception e) {
            LogUtils.e(e);
        }
        handler.post(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
            }
        });
    }

    private Response requestUpload(String fileName, String path) throws IOException {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("u_id", userPreferences.getUserId())
                .addFormDataPart("file", fileName, RequestBody.create(MediaType.parse("image/png"), new File(path)))
                .build();
        Request request = new Request.Builder()
                .header("udid", userPreferences.getIMEI())
                .header("App-Key", Constants.SCAN_PRO)
                .header("U-ID", userPreferences.getUserId())
                .header("T-ID", userPreferences.getToken())
                .header("Channel", userPreferences.getChannel())
                .url(BuildConfig.PHASE.getPassportServiceUrl() + getString(R.string.upload_url))
                .post(requestBody)
                .build();
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();
        return okHttpClient.newCall(request).execute();
    }

    private void thirdPartyTimeout(String fileName, String path, String thirdPartyOpenid, String thirdPartyPlatName, String servicePlatName, String thirdPartyToken) throws IOException, JSONException {
        LogUtils.i("第三方上传头像token过期");
        LogUtils.i("token: " + userPreferences.getToken());
        MiaoHttpEntity<RegisterModel> thirdPartyLoginModel = HttpManager.getInstance().requestPassport().thirdPartyLoginSync(userPreferences.getChannel(), userPreferences.getIMEI(), Constants.SCAN_PRO,
                servicePlatName, thirdPartyToken, thirdPartyOpenid, RegisterModel.class);
        // 重新登录成功
        if (thirdPartyLoginModel.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
            LogUtils.i("第三方上传头像重新登录成功");
            LogUtils.i("新的token: " + thirdPartyLoginModel.getBody().getToken());
            userPreferences.setToken(thirdPartyLoginModel.getBody().getToken());
            Response responseAgain = requestUpload(fileName, path);
            JSONObject jsonObjectAgain = new JSONObject(responseAgain.body().string());
            LogUtils.i(jsonObjectAgain.toString());
            if (responseAgain.isSuccessful() && jsonObjectAgain.getString(SyncHttpTask.DATA_CODE).equals(MiaoHttpManager.STATUS_SUCCESS + Constants.EMPTY)) {
                afterUpload(jsonObjectAgain, fileName);
            } else {
                FailedToast();
            }
        }
        // 重新登录账号或者密码不正确
        else if (thirdPartyLoginModel.getCode() == MiaoHttpManager.STATUS_THIRD_PARTY_TIME_OUT) {
            userPreferences.setIsThirdParty(false);
            userPreferences.setIsUserLogin(false);
            showMessage(String.format(getString(R.string.third_party_token_timeout_login_again), thirdPartyPlatName));
            Intent intent = new Intent(this, LoginActivity.class);
            intent.putExtra("platName", thirdPartyPlatName);
            intent.putExtra("isThirdPartyToken", true);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        }
        // 其他情况
        else {
            FailedToast();
        }
    }


    /**
     * @des:普通登录token过期
     * @params:
     * @return:
     */

    private void normalTokenTimeout(String fileName, String path) throws IOException, JSONException {
        LogUtils.i("上传头像token过期");
        LogUtils.i("token: " + userPreferences.getToken());
        MiaoHttpEntity<RegisterModel> loginModel = HttpManager.getInstance().requestPassport().loginSync(Constants.SCAN_PRO, userPreferences.getIMEI(),
                userPreferences.getChannel(), userPreferences.getLoginUserName(), MD5Utils.md5(userPreferences.getLoginPassword()), RegisterModel.class);
        // 重新登录成功
        if (loginModel.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
            LogUtils.i("上传头像重新登录成功");
            LogUtils.i("新的token: " + loginModel.getBody().getToken());
            userPreferences.setToken(loginModel.getBody().getToken());
            Response responseAgain = requestUpload(fileName, path);
            JSONObject jsonObjectAgain = new JSONObject(responseAgain.body().string());
            LogUtils.i(jsonObjectAgain.toString());
            if (responseAgain.isSuccessful() && jsonObjectAgain.getString(SyncHttpTask.DATA_CODE).equals(MiaoHttpManager.STATUS_SUCCESS + Constants.EMPTY)) {
                afterUpload(jsonObjectAgain, fileName);
            } else {
                FailedToast();
            }
        }
        // 重新登录账号或者密码不正确
        else if (loginModel.getCode() == MiaoHttpManager.STATUS_INVALID_USER_OR_PASSWORD) {
            userPreferences.setIsUserLogin(false);
            logout();
            showMessage(R.string.token_timeout_login_again);
            Intent intent = new Intent(this, LoginActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        }
        // 其他情况
        else {
            FailedToast();
        }
    }


    private void afterUpload(final JSONObject jsonObject, final String fileName) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
                try {
                    String photo = jsonObject.getJSONObject(SyncHttpTask.DATA_BODY).getString("photo");
                    LogUtils.i(photo);
                    userPreferences.setUserPhoto(photo);
                    LogUtils.i(userPreferences.getUserPhoto());
                    EventBus.getDefault().post(new UserInfoEvent(EventType.EDIT_USER_IMAGE));
                    ActivityUtils.finishToActivity(UserActivity.class, false);
                } catch (JSONException e) {
                    LogUtils.e(e);
                }
            }
        });
    }

    private void FailedToast() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mCropImageView.setOnBitmapSaveCompleteListener(null);
        if (null != mBitmap && !mBitmap.isRecycled()) {
            mBitmap.recycle();
            mBitmap = null;
        }
    }
}
