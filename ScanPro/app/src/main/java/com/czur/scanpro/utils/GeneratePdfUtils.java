package com.czur.scanpro.utils;

import android.app.Activity;
import android.graphics.Bitmap;
import android.util.Log;

import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.LogUtils;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.preferences.UserPreferences;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfWriter;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Created by Yz on 2018/4/8.
 * Email：<EMAIL>
 */
public class GeneratePdfUtils {
    private String path;
    private List<String> paths;
    private String pdfName;
    private Activity activity;
    private long beginTime;

    public GeneratePdfUtils(Activity activity, String path, List<String> paths, String pdfName) {
        this.activity = activity;
        this.path = path;
        this.paths = paths;
        this.pdfName = pdfName;

    }

    public void createPdf() {
        UserPreferences userPreferences = UserPreferences.getInstance(activity);
        int type = userPreferences.getPdfType();
        int isHorizontal = userPreferences.getPdfIsHorizontal();
        int pdfQuality = userPreferences.getPdfQuality();

        long totalTime = 0;
        String filePath = "";
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (onGeneratePdfListener != null) {
                    onGeneratePdfListener.onStart();
                }
            }
        });

        try {
            beginTime = System.currentTimeMillis();
            Document document;
            float width = 0;
            float height = 0;
            Rectangle fitRect;
            float temp = 0;

            if (type == 1) {
                fitRect = isHorizontal == 0 ? PageSize.A3 : PageSize.A3.rotate();
                width = isHorizontal == 0 ? PageSize.A3.getWidth() : PageSize.A3.getHeight();
                height = isHorizontal == 0 ? PageSize.A3.getHeight() : PageSize.A3.getWidth();
            } else if (type == 2) {
                fitRect = isHorizontal == 0 ? PageSize.A4 : PageSize.A4.rotate();
                width = isHorizontal == 0 ? PageSize.A4.getWidth() : PageSize.A4.getHeight();
                height = isHorizontal == 0 ? PageSize.A4.getHeight() : PageSize.A4.getWidth();
            } else if (type == 3) {
                fitRect = isHorizontal == 0 ? PageSize.A5 : PageSize.A5.rotate();
                width = isHorizontal == 0 ? PageSize.A5.getWidth() : PageSize.A5.getHeight();
                height = isHorizontal == 0 ? PageSize.A5.getHeight() : PageSize.A5.getWidth();
            } else if (type == 7) {
                fitRect = isHorizontal == 0 ? PageSize.B5 : PageSize.B5.rotate();
                width = isHorizontal == 0 ? PageSize.B5.getWidth() : PageSize.B5.getHeight();
                height = isHorizontal == 0 ? PageSize.B5.getHeight() : PageSize.B5.getWidth();
            } else {
                Bitmap bitmap = ImageUtils.getBitmap(paths.get(0), 2048, 2048);
                fitRect = new Rectangle(bitmap.getWidth(), bitmap.getHeight());
            }
            document = new Document(fitRect);
            if (FileUtils.createOrExistsDir(path)) {
                filePath = path + pdfName + Constants.PDF;
                LogUtils.i(filePath);
            }
            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(filePath));
            Background event = new Background();
            writer.setPageEvent(event);
            document.open();


            LogUtils.e(type, isHorizontal, pdfQuality);
            LogUtils.i("页面尺寸：(" + width + "," + height + ")");
            for (int i = 0; i < paths.size(); i++) {

                Image img = Image.getInstance(paths.get(i));
                if (type == 0) {

                    fitRect = new Rectangle(img.getScaledWidth(), img.getScaledHeight());
                    document.setPageSize(fitRect);
                    document.newPage();
                    img.setAbsolutePosition(0, 0);
                } else {
                    document.newPage();
                    img.scaleToFit(fitRect);
                    img.setAbsolutePosition((width - img.getScaledWidth()) / 2, (height - img.getScaledHeight()) / 2);
                }
                LogUtils.i("图片尺寸：(" + img.getScaledWidth() + "," + img.getScaledHeight() + ")");
                img.setCompressionLevel(pdfQuality == 1 ? 9 : 0);
                LogUtils.i("压缩等级：(" + img.getCompressionLevel() + ")");
                document.add(img);
                final long l = System.currentTimeMillis() - beginTime;
                totalTime += l;
                final int finalI = i;
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (onGeneratePdfListener != null) {
                            onGeneratePdfListener.onGenerate(finalI + 1, l);
                        }
                    }
                });
            }
            document.close();
            final long finalTotalTime = totalTime;
            final String finalFilePath = filePath;
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (onGeneratePdfListener != null) {
                        onGeneratePdfListener.onFinish(finalTotalTime, finalFilePath, pdfName);
                    }
                }
            });

        } catch (IOException e) {
            e.printStackTrace();
            Log.i("xxx", "IOException error：" + e);
        } catch (DocumentException e) {
            e.printStackTrace();
            Log.i("xxx", "DocumentException error：" + e);
        }
    }

    private class Background extends PdfPageEventHelper {
        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            //color
            PdfContentByte canvas = writer.getDirectContentUnder();
            Rectangle rect = document.getPageSize();
            canvas.setColorFill(BaseColor.WHITE);
            canvas.rectangle(rect.getLeft(), rect.getBottom(), rect.getWidth(), rect.getHeight());
            canvas.fill();

            //border
            PdfContentByte canvasBorder = writer.getDirectContent();
            Rectangle rectBorder = document.getPageSize();
            rectBorder.setBorder(Rectangle.BOX);
            rectBorder.setBorderWidth(0);
            rectBorder.setBorderColor(BaseColor.WHITE);
            rectBorder.setUseVariableBorders(true);
            canvasBorder.rectangle(rectBorder);
        }
    }

    private OnGeneratePdfListener onGeneratePdfListener;

    public void setOnGeneratePdfListener(OnGeneratePdfListener onGeneratePdfListener) {
        this.onGeneratePdfListener = onGeneratePdfListener;
    }

    public interface OnGeneratePdfListener {
        void onStart();

        void onGenerate(int progress, long time);

        void onFinish(long totalTime, String pdfPath, String pdfName);
    }
}
