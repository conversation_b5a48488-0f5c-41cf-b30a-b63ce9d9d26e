package com.czur.scanpro.ui.component.popup;

import static com.blankj.utilcode.util.ActivityUtils.startActivity;
import static com.czur.scanpro.common.Constants.PRAVATE_INFO_LIST;
import static com.czur.scanpro.common.Constants.PRIVACY_AGREEMENT;
import static com.czur.scanpro.common.Constants.PRIVACY_TERMS;
import static com.czur.scanpro.common.Constants.THIRD_SHARE_INFO_LIST;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.ui.base.WebViewActivity;
import com.czur.scanpro.ui.base.WelcomeActivity;
import com.czur.scanpro.utils.validator.StringUtils;

import org.jetbrains.annotations.NotNull;

import java.util.Calendar;

import kotlin.jvm.internal.Intrinsics;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class PrivacyPopup extends Dialog {

    private static final String USER_PRIVACY_USER = "userPrivacyUser";//用户协议
    private static final String USER_PRIVACY_PRIVACY = "userPrivacyPrivacy";//隐私政策
    private static final String USER_PRIVACY_INFO = "userPrivacyInfo";
    private static final String USER_PRIVACY_SHARE = "userPrivacyShare";

    public static final float DIMMED_OPACITY = 0.2f;

    public PrivacyPopup(Context context) {
        super(context);
    }

    public PrivacyPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;
        private boolean isSuccess = false;
        private boolean isError = false;
        private boolean isFirstFinish = true;
        private String title;
        private String message;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setOnNegativeListener(OnClickListener onNegativeListener) {
            this.onNegativeListener = onNegativeListener;
            return this;
        }

        public PrivacyPopup create() {
            DisplayMetrics dm = new DisplayMetrics();
            WindowManager m = ActivityUtils.getTopActivity().getWindowManager();
            m.getDefaultDisplay().getMetrics(dm);
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final PrivacyPopup dialog = new PrivacyPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(false);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            params.width = (int) (dm.widthPixels * 0.74);
            params.height = (int) (dm.heightPixels * 0.6);
            dialog.getWindow().setAttributes(params);
            BarUtils.setStatusBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode( dialog.getWindow(), true);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final PrivacyPopup dialog) {

            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_privacy_common, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);

            dialog.addContentView(layout, params);

            TextView title = (TextView) layout.findViewById(R.id.title);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.add_tag_back_btn);
            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);
            TextView negativeBtn = (TextView) layout.findViewById(R.id.negative_button);
            FrameLayout webContainer = (FrameLayout) layout.findViewById(R.id.web_frame);
            RelativeLayout  reloadWebviewRl= (RelativeLayout) layout.findViewById(R.id.reload_webview_rl);
            TextView   reloadBtn = (TextView) layout.findViewById(R.id.reload_btn);
            if (NetworkUtils.isConnected()) {
                reloadWebviewRl.setVisibility(View.GONE);
            } else {
                reloadWebviewRl.setVisibility(View.VISIBLE);
            }
            backBtn.setVisibility(View.GONE);

            TextView textView = new TextView(context);
            String userPrivacyExplain = context.getString(R.string.user_first_in_privacy_explain);
            String userPrivacyUser = context.getString(R.string.privacy_terms );
            String userPrivacyPrivacy = context.getString(R.string.privacy_policy1);
            String userPrivacyInfo = context.getString(R.string.private_checklist);
            String userPrivacyShare = context.getString(R.string.thrid_sharelist);
            SpannableStringBuilder spannableBuilder = new SpannableStringBuilder((CharSequence) userPrivacyExplain);
            setTextViewColorAndClick(USER_PRIVACY_USER, spannableBuilder, userPrivacyExplain.indexOf(userPrivacyUser), userPrivacyExplain.indexOf(userPrivacyUser) + userPrivacyUser.length());
            setTextViewColorAndClick(USER_PRIVACY_PRIVACY, spannableBuilder, userPrivacyExplain.indexOf(userPrivacyPrivacy), userPrivacyExplain.indexOf(userPrivacyPrivacy) + userPrivacyPrivacy.length());
            setTextViewColorAndClick(USER_PRIVACY_INFO, spannableBuilder, userPrivacyExplain.indexOf(userPrivacyInfo), userPrivacyExplain.indexOf(userPrivacyInfo) + userPrivacyInfo.length());
            setTextViewColorAndClick(USER_PRIVACY_SHARE, spannableBuilder, userPrivacyExplain.indexOf(userPrivacyShare), userPrivacyExplain.indexOf(userPrivacyShare) + userPrivacyShare.length());

            textView.setPadding(60,20,60,20);
            textView.setTextSize(12f);
            textView.setMovementMethod(LinkMovementMethod.getInstance());
            textView.setText((CharSequence) spannableBuilder);
            textView.setHighlightColor(Color.parseColor("#00000000"));
            webContainer.addView(textView);


//            WebView webView = new WebView(context);
//            WebSettings settings = webView.getSettings();
//            settings.setDomStorageEnabled(true);
//            //解决一些图片加载问题
//            settings.setJavaScriptEnabled(true);
//            settings.setBlockNetworkImage(false);
//            webView.setWebChromeClient(new WebChromeClient() {
//
//                @Override
//                public void onProgressChanged(WebView view, int progress) {
//                    //当进度走到100的时候做自己的操作，我这边是弹出dialog
//
//                }
//            });
//            webContainer.addView(webView);
//            webView.setWebViewClient(new WebViewClient() {
//
//
//                //处理网页加载失败时
//                @Override
//                public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
//                    super.onReceivedError(view, request, error);
//                    isError = true;
//                    isSuccess = false;
//                    reloadWebviewRl.setVisibility(View.VISIBLE);
//                    webContainer.setVisibility(View.GONE);
//                    LogUtils.i("above 6.0 error callback", error);
//                }
//
//                @Override
//                public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
//                    super.onReceivedError(view, errorCode, description, failingUrl);
//
//                    isError = true;
//                    isSuccess = false;
//                    reloadWebviewRl.setVisibility(View.VISIBLE);
//                    webContainer.setVisibility(View.GONE);
//                    LogUtils.i("below 6.0 error callback", errorCode);
//                }
//
//                @Override
//                public void onPageFinished(WebView view, String url) {
//                    LogUtils.i("WebView load finish");
//
//                    if (!isFirstFinish) {
//                        return;
//                    }
//                    isFirstFinish = false;
//
//                    LogUtils.i(isError);
//                    if (!isError) {
//                        isSuccess = true;
//                        //回调成功后的相关操作
//                        reloadWebviewRl.setVisibility(View.GONE);
//                        webContainer.setVisibility(View.VISIBLE);
//                    } else {
//                        isError = false;
//                        reloadWebviewRl.setVisibility(View.VISIBLE);
//                        webContainer.setVisibility(View.GONE);
//                    }
//                }
//
//                @Override
//                public boolean shouldOverrideUrlLoading(WebView view, String url) {
//                    Log.d("webview", "url: " + url);
//                    if(url.startsWith("mailto:")){
//                        url = url.replaceFirst("mailto:", "");
//                        url = url.trim();
//                        Intent i = new Intent(Intent.ACTION_SEND);
//                        i.setType("plain/text").putExtra(Intent.EXTRA_EMAIL, new String[]{url});
//                        startActivity(i);
//                        return true;
//                    }
//                    view.loadUrl(url);
//                    return true;
//                }
//            });
//            webView.loadUrl(PRIVACY_AGREEMENT);
//            reloadBtn.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    LogUtils.e("xxx");
//                    isFirstFinish = true;
//                    webView.reload();
//                }
//            });
            if (contentsView == null) {

                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }
            } else {
                title.setVisibility(View.GONE);
            }
//            if (constants.getPositiveBtn() > 0) {
//                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
//            } else {
//                positiveBtn.setVisibility(View.GONE);
//            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }
            if (negativeBtn != null) {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                negativeBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                        ActivityUtils.finishActivity(WelcomeActivity.class);
                    }
                });
            }

            if (onNegativeListener != null) {
                backBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, constants.getNegativeBtn());
                    }
                });
            } else {
                backBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }


            return layout;
        }



        private boolean isChinese(char c) {
            return c >= 0x4E00 && c <= 0x9FA5;// 根据字节码判断
        }

        private void setTextViewColorAndClick(String type, SpannableStringBuilder spannableBuilder, int clickTextPositionStart, int clickTextPositionEnd) {

            ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#FF2c86e4"));
            spannableBuilder.setSpan(colorSpan, clickTextPositionStart, clickTextPositionEnd, 33);
            final String pkgName = "com.czur.cloud";
            ClickableSpan clickableSpanTwo = (ClickableSpan) (new ClickableSpan() {
                public void onClick(@NotNull View view) {
                    switch (type) {
                        case USER_PRIVACY_USER:
                            Intent intent = new Intent(context, WebViewActivity.class);
                            intent.putExtra("title", context.getString(R.string.privacy_terms));
                            long cTime = Calendar.getInstance().getTimeInMillis();
                            intent.putExtra("url", PRIVACY_TERMS);
                            ActivityUtils.startActivity(intent);
                            break;
                        case USER_PRIVACY_PRIVACY:
                            Intent intent1 = new Intent(context, WebViewActivity.class);
                            intent1.putExtra("title", context.getString(R.string.privacy_policy1));
                            long cTime1 = Calendar.getInstance().getTimeInMillis();
//                            intent1.putExtra("url", CZURConstants.PRIVACY_AGREEMENT2 + cTime1 + "");
                            intent1.putExtra("url", PRIVACY_AGREEMENT);
                            ActivityUtils.startActivity(intent1);
                            break;
                        case USER_PRIVACY_INFO:
                            Intent intent2 = new Intent(context, WebViewActivity.class);
                            intent2.putExtra("title", context.getString(R.string.private_checklist));
                            long cTime2 = Calendar.getInstance().getTimeInMillis();
                            intent2.putExtra("url", PRAVATE_INFO_LIST+ "?timeStep=" + cTime2 + "");
                            ActivityUtils.startActivity(intent2);

                            break;
                        case USER_PRIVACY_SHARE:
                            Intent intent3 = new Intent(context, WebViewActivity.class);
                            intent3.putExtra("title", context.getString(R.string.thrid_sharelist));
                            long cTime3 = Calendar.getInstance().getTimeInMillis();
                            intent3.putExtra("url", THIRD_SHARE_INFO_LIST+ "?timeStep=" + cTime3 + "");
                            ActivityUtils.startActivity(intent3);
                            break;
                    }
                }

                public void updateDrawState(@NotNull TextPaint paint) {
                    Intrinsics.checkNotNullParameter(paint, "paint");
                    paint.setColor(Color.parseColor("#3072F6"));
                    paint.setUnderlineText(false);
                }
            });
            spannableBuilder.setSpan(clickableSpanTwo, clickTextPositionStart, clickTextPositionEnd, 33);
        }
    }




}
