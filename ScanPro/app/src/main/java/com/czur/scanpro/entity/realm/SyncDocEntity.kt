package com.czur.scanpro.entity.realm

import io.realm.RealmObject
import io.realm.annotations.RealmClass

/**
 * Created by Yz on 2019/1/4.
 * Email：<EMAIL>
 */
@RealmClass
open class SyncDocEntity : RealmObject() {

    var fileID: String? = null
    var categoryID: String? = null
    var categoryName: String? = null
    var tagId: String? = null//标签Id
    var tagName: String? = null//标签名
    var bucket: String? = null
    var uuid: String? = null

    //0:优化,1: 黑白,2:底色净化,3:原图
    var enhanceMode: Int = 0
    var fileType: Int = 0
    var originalImagePath: String? = null
    var processImagePath: String? = null
    var processSmallImagePath: String? = null
    var baseImagePath: String? = null
    var baseSmallImagePath: String? = null


    var isDelete: Int = 0
    var isNewAdd: Int = 0//是否是本地新创建
    var createTime: String? = null//创建时间
    var updateTime: String? = null//更新时间
    var userID: String? = null
    var fileSize: String? = null// 文件size

    //同步表单独字段
    var hasUploadBaseImage: Boolean= false
    var syncTime: String? = null//更新时间
    var isDirty: Int = 0


    var contentImagePath: String? = null
    var contentSmallImagePath: String? = null
    var ocrResult: String? = null// OCR结果, (JSON) (文字识别, 云识别)
    var ocrHandResult: String? = null// OCR结果, (JSON) (手写识别)

}
