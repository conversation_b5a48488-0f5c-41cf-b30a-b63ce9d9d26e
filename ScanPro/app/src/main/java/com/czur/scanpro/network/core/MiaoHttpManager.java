package com.czur.scanpro.network.core;

import android.app.Application;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.scanpro.BuildConfig;
import com.czur.scanpro.R;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.network.NetCacheInterceptor;
import com.czur.scanpro.network.ResponseCacheInterceptor;
import com.czur.scanpro.preferences.UserPreferences;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.lang.reflect.Type;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.CertificateFactory;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;

import okhttp3.Cache;
import okhttp3.OkHttpClient;

public class MiaoHttpManager {

    public static final int STATUS_FAIL = 1001;
    public static final int STATUS_TIMEOUT = 1002;
    public static final int STATUS_NOT_MANAGER = 1003;
    public static final int STATUS_SUCCESS = 1000;
    public static final int STATUS_INVALID_USER_OR_PASSWORD = 1004;
    public static final int STATUS_USER_EXISTS = 1011;
    public static final int STATUS_INVALID_CODE = 1014;
    public static final int STATUS_INVALID_MOBILE = 1007;
    public static final int STATUS_INVALID_EMAIL = 1008;
    public static final int STATUS_5_TIME_IN_ONE_DAY = 1009;
    public static final int STATUS_CODE_1_MIN = 1010;
    public static final int STATUS_COND_1_MAIL = 1013;
    public static final int STATUS_FlATTEN_ERROR = 1015;
    public static final int STATUS_ERROR = 1017;
    public static final int STATUS_NOT_USER = 1018;
    public static final int STATUS_TIME_OUT = 1019;
    public static final int STATUS_5_MIN_4_TIME = 1023;
    public static final int STATUS_DEVICE_NOT_EXIST = 1024;
    public static final int STATUS_DEVICE_NOT_SAME_AREA = 1025;
    public static final int STATUS_DEVICE_NOT_BIND = 1026;
    public static final int STATUS_NETWORK_FAIL = 1028;
    public static final int STATUS_EMAIL_BIND_OTHER_USER = 1035;
    public static final int STATUS_MOBILE_BIND_OTHER_USER = 1034;
    public static final int STATUS_CODE_EXPIRED = 1033;
    public static final int STATUS_OLD_PWD_FAIL = 1031;
    public static final int STATUS_PDF_RETURN_CODE = 1039;
    public static final int STATUS_HAS_BINDED_THIRD_PARTY_BY_MOBILE = 1046;
    public static final int STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_MOBILE = 1047;
    public static final int STATUS_HAS_BINDED_THIRD_PARTY_BY_EMAIL = 1048;
    public static final int STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_EMAIL = 1049;
    public static final int STATUS_INVALID_THIRD_PARTY = 1050;

    public static final int STATUS_THIRD_PARTY_TIME_OUT = 1058;


    private static final int METHOD_NONE = 0;
    private static final int METHOD_GET = 1;
    private static final int METHOD_POST = 2;

    private Application application;
    private ExecutorService executor = Executors.newFixedThreadPool(5);

    public OkHttpClient getClient() {
        return client;
    }

    private OkHttpClient client, clientNoRetry, clientWeixin, clientWeibo;
    private boolean isDebug;
    public String urlServer, urlPassport, adUrlPassport;
    private Handler handler = new Handler(Looper.getMainLooper());

    private MiaoHttpManager() {
    }

    public static MiaoHttpManager getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        static final MiaoHttpManager instance = new MiaoHttpManager();
    }

    public void init(Application application) {
        this.application = application;
        // 缓存目录
        File file = new File(application.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS).getAbsolutePath().toString(), "cache");
        // 缓存大小
        int cacheSize = 10 * 1024 * 1024;
        client = new OkHttpClient.Builder()
                .cache(new Cache(file, cacheSize)) // 配置缓存
                .build();
        LogUtils.d(BuildConfig.PHASE.getServiceUrl(), BuildConfig.PHASE.getPassportServiceUrl());
        urlServer = BuildConfig.PHASE.getServiceUrl();
        urlPassport = BuildConfig.PHASE.getPassportServiceUrl();
        adUrlPassport = BuildConfig.PHASE.getAdPassportServiceUrl();
        client = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .cache(new Cache(file, cacheSize))
                .addInterceptor(new NetCacheInterceptor())
                .addNetworkInterceptor(new ResponseCacheInterceptor())
                // Jason 2022-06-02
//                .addInterceptor(new LoggingInterceptor())
                .build();
        clientNoRetry = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .retryOnConnectionFailure(false)
                .cache(new Cache(file, cacheSize))
                .addInterceptor(new NetCacheInterceptor())
                .addNetworkInterceptor(new ResponseCacheInterceptor())
                .build();
//        clientWeixin = new OkHttpClient.Builder().sslSocketFactory(getCertificates("weixin.cer")).connectTimeout(5, TimeUnit.SECONDS)
//                .readTimeout(5, TimeUnit.SECONDS).retryOnConnectionFailure(false).build();
//        clientWeibo = new OkHttpClient.Builder().sslSocketFactory(getCertificates("weibo.cer")).connectTimeout(5, TimeUnit.SECONDS)
//                .readTimeout(5, TimeUnit.SECONDS).retryOnConnectionFailure(false).build();
        clientWeixin = new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.SECONDS)
                .retryOnConnectionFailure(false)
                .build();
        clientWeibo = new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.SECONDS)
                .retryOnConnectionFailure(false)
                .build();
    }

    private SSLSocketFactory getCertificates(String cer) {
        try {
            InputStream certificate = application.getAssets().open(cer);
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null);
            String certificateAlias = Integer.toString(0);
            keyStore.setCertificateEntry(certificateAlias, certificateFactory.generateCertificate(certificate));
            try {
                if (certificate != null) {
                    certificate.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            SSLContext sslContext = SSLContext.getInstance("TLS");
            TrustManagerFactory trustManagerFactory =
                    TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init(keyStore);
            sslContext.init(null, trustManagerFactory.getTrustManagers(), new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public <T> T create(Class<T> service, String endpoint, boolean isNotPassport) {
        return (T) Proxy.newProxyInstance(service.getClassLoader(), new Class<?>[]{service}, new HttpHandler<T>(endpoint, isNotPassport));
    }

    private class HttpHandler<T> implements InvocationHandler {

        private String endpoint;
        private boolean isNotPassport;

        HttpHandler(String endpoint, boolean isNotPassport) {
            super();
            this.endpoint = endpoint;
            this.isNotPassport = isNotPassport;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(this, args);
            }

            if (args == null || args.length == 0) {
                throw new Exception("方法参数为空或者规则不正确!");
            }

            Annotation[][] paramAnnotationArr = null;
            MiaoHttpMethod miaoHttpMethod;
//            MiaoHttpMethod miaoHttpMethod = methodCache.get(method.getName());
//            if (miaoHttpMethod == null) {
            miaoHttpMethod = new MiaoHttpMethod();
            miaoHttpMethod.setIsAsync(args[args.length - 1] instanceof Callback);
            miaoHttpMethod.setNoParamCount(miaoHttpMethod.isAsync() ? 2 : 1);
            paramAnnotationArr = method.getParameterAnnotations();
            for (int i = 0; i < paramAnnotationArr.length - miaoHttpMethod.getNoParamCount(); i++) {
                Annotation tempAnnotation = paramAnnotationArr[i][0];
                if (tempAnnotation instanceof MiaoHttpParam) {
                    miaoHttpMethod.getParams().put(((MiaoHttpParam) tempAnnotation).value(), i + "");
                } else if (tempAnnotation instanceof MiaoHttpHeader) {
                    miaoHttpMethod.getHeaders().put(((MiaoHttpHeader) tempAnnotation).value(), i + "");
                } else if (tempAnnotation instanceof MiaoHttpPath) {
                    miaoHttpMethod.getPaths().put(((MiaoHttpPath) tempAnnotation).value(), i + "");
                }
            }
            Annotation[] annotations = method.getAnnotations();
            int witchMethod = METHOD_NONE;
            Class<?> clazz;
            MiaoHttpGet miaoGet = null;
            MiaoHttpPost miaoPost = null;
            boolean isRetry = true;
            for (Annotation annotation : annotations) {
                clazz = annotation.annotationType();
                if (clazz == MiaoHttpGet.class) {
                    witchMethod = METHOD_GET;
                    miaoGet = (MiaoHttpGet) annotation;
                } else if (clazz == MiaoHttpPost.class) {
                    witchMethod = METHOD_POST;
                    miaoPost = (MiaoHttpPost) annotation;
                } else if (clazz == MiaoRetry.class) {
                    isRetry = ((MiaoRetry) annotation).value();
                }
            }
            if (witchMethod == METHOD_NONE) {
                throw new Exception("方法上面说好的注解呢?MiaoGet或者MiaoPost什么的?");
            }

            switch (witchMethod) {
                case METHOD_GET:
                    miaoHttpMethod.setMethod(MiaoHttpMethod.Method.Get);
                    miaoHttpMethod.setUrl(endpoint + miaoGet.value());
                    break;
                case METHOD_POST:
                    miaoHttpMethod.setMethod(MiaoHttpMethod.Method.Post);
                    miaoHttpMethod.setUrl(endpoint + miaoPost.value());
                    break;
            }

//                methodCache.put(method.getName(), miaoHttpMethod);
//            }

//            if (paramAnnotationArr == null) {
//                paramAnnotationArr = method.getParameterAnnotations();
//            }

            HashMap<String, String> postParam = null;
            ArrayList<String> logs = new ArrayList<>();
            logs.add("接口url:");
            logs.add(miaoHttpMethod.getUrl());
            logs.add("接口方法名称:");
            logs.add(method.getName());
            logs.add("接口参数:");
            String url = "";
            if (MiaoHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                boolean isFirstParam = true;
                for (Map.Entry<String, String> entry : miaoHttpMethod.getParams().entrySet()) {
                    String value = (String) args[Integer.parseInt(entry.getValue())];
                    if (value == null) {
                        value = "";
                    }
                    logs.add(entry.getKey() + " --> " + value);
                    url += (isFirstParam ? "?" : "&") + entry.getKey() + "=" + value;
                    isFirstParam = false;
                }
            } else {
                postParam = new HashMap<>();
                for (Map.Entry<String, String> entry : miaoHttpMethod.getParams().entrySet()) {
                    String value = (String) args[Integer.parseInt(entry.getValue())];
                    if (value == null) {
                        value = "";
                    }
                    logs.add(entry.getKey() + " --> " + value);
                    postParam.put(entry.getKey(), value);
                }
            }
            String baseUrl = miaoHttpMethod.getUrl();
            for (Map.Entry<String, String> entry : miaoHttpMethod.getPaths().entrySet()) {
                String value = (String) args[Integer.parseInt(entry.getValue())];
                if (value == null) {
                    value = "";
                }
                baseUrl = baseUrl.replace("{" + entry.getKey() + "}", value);
            }
            miaoHttpMethod.setUrl(baseUrl + url);
            if (miaoHttpMethod.getHeaders().size() != 0) {
                for (Map.Entry<String, String> entry : miaoHttpMethod.getHeaders().entrySet()) {
                    String value = (String) args[Integer.parseInt(entry.getValue())];
                    if (value == null) {
                        value = "";
                    }
                    logs.add("header: " + entry.getKey() + " --> " + value);
                    miaoHttpMethod.getHeaders().put(entry.getKey(), value);
                }
            }
            if (isNotPassport) {
                UserPreferences userPreferences = UserPreferences.getInstance(application);
                logs.add("固定Header:");
                miaoHttpMethod.getHeaders().put("udid", userPreferences.getIMEI());
                logs.add("udid --> " + userPreferences.getIMEI());
                miaoHttpMethod.getHeaders().put("App-Key", Constants.SCAN_PRO);
                logs.add("App-Key --> " + Constants.SCAN_PRO);
                if (userPreferences.getUser() != null) {
                    miaoHttpMethod.getHeaders().put("T-ID", userPreferences.getToken());
                    logs.add("T-ID --> " + userPreferences.getToken());
                    miaoHttpMethod.getHeaders().put("U-ID", userPreferences.getUserId());
                    logs.add("U-ID --> " + userPreferences.getUserId());
                }
            }
            miaoHttpMethod.getHeaders().put("App-Bundle", Constants.APP_BUNDLE);
            logs.add("App-Bundle --> " + Constants.APP_BUNDLE);

            LogUtils.i("MiaoHttpManager" , logs);

            if (miaoHttpMethod.isAsync()) {
                final Callback<T> callback = (Callback<T>) args[args.length - 1];
                if (!com.blankj.utilcode.util.NetworkUtils.isConnected()) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.showShort(R.string.no_connection_network);
                            if (callback instanceof CallbackNetwork) {
                                ((CallbackNetwork) callback).onNoNetwork();
                            }
                        }
                    });
               //     return null;
                }
                Type type = (Type) args[args.length - 2];
                callback.onStart();
                if (MiaoHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    executor.execute(new AsyncHttpTask<>(application, miaoHttpMethod.getUrl(), type, callback, miaoHttpMethod.getHeaders(), logs, isRetry));
                } else {
                    executor.execute(new AsyncHttpTask<>(application, miaoHttpMethod.getUrl(), type, postParam, callback, miaoHttpMethod.getHeaders(), logs, isRetry));
                }
            } else {
                if (!com.blankj.utilcode.util.NetworkUtils.isConnected()) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtils.showShort(R.string.no_connection_network);

                        }
                    });
                    throw new NoNetworkException();
                }
                Type type = (Type) args[args.length - 1];
                if (MiaoHttpMethod.Method.Get.equals(miaoHttpMethod.getMethod())) {
                    return SyncHttpTask.getInstance().syncGet(application, miaoHttpMethod.getUrl(), type, miaoHttpMethod.getHeaders(), logs, isRetry);
                } else {
                    return SyncHttpTask.getInstance().syncPost(application, miaoHttpMethod.getUrl(), type, postParam, miaoHttpMethod.getHeaders(), logs, isRetry);
                }
            }
            return null;
        }
    }

    public interface Callback<T> {
        void onStart();

        void onResponse(MiaoHttpEntity<T> entity);

        void onFailure(MiaoHttpEntity<T> entity);

        void onError(Exception e);
    }

    public interface CallbackNetwork<T> extends Callback<T> {
        void onNoNetwork();
    }

    public OkHttpClient getHttpClient() {
        return client;
    }

    OkHttpClient getHttpClientNoRetry() {
        return clientNoRetry;
    }

    OkHttpClient getHttpClientWeixin() {
        return clientWeixin;
    }

    OkHttpClient getHttpClientWeibo() {
        return clientWeibo;
    }
}
