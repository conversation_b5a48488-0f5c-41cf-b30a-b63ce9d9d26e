package com.czur.scanpro.ui.album;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.ui.base.BaseActivity;
import com.czur.scanpro.utils.PermissionUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class SelectAlbumPhotoActivity2 extends BaseActivity implements View.OnClickListener, ImageDataSource.OnImagesLoadedListener {
    public static final int REQUEST_PERMISSION_STORAGE = 0x01;
    private TextView noBackTopBarTitle;
    private RelativeLayout noBackTopBarCancel;
    private List<ImageFolder> mImageFolders;
    private AlbumFolderAdapter2 albumFolderAdapter;
    private RecyclerView albumFolderList;
    private ImagePicker imagePicker;
    private boolean isFirstLoad = true;
    private boolean isImport = false;
    private String tagId;
    private String tagName;
    private String categoryID;
    private String categoryName;
    private int type = 0;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_select_album_photo2);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        isImport = getIntent().getBooleanExtra("isImport", false);
        tagId = getIntent().getStringExtra("tagId");
        tagName = getIntent().getStringExtra("tagName");
        categoryID = getIntent().getStringExtra("categoryID");
        categoryName = getIntent().getStringExtra("categoryName");
        type = getIntent().getIntExtra("type", 0);
        albumFolderList = (RecyclerView) findViewById(R.id.album_folder_list);
        noBackTopBarTitle = (TextView) findViewById(R.id.no_back_top_bar_title);
        noBackTopBarCancel = (RelativeLayout) findViewById(R.id.no_back_top_bar_cancel);
        imagePicker = ImagePicker.getInstance();
        noBackTopBarTitle.setText(R.string.album);
        initAlbumList();
        getPermission();


    }

    /**
     * @des:获取权限
     * @params:
     * @return:
     */
    private void getPermission123() {
        if (checkPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            showProgressDialog(false);
            new ImageDataSource(this, null, this);
        } else {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, REQUEST_PERMISSION_STORAGE);
        }
    }
    private void getPermission() {
        if (PermissionUtils.isGranted(PermissionUtil.getStoragePermission())) {
            showProgressDialog(false);
            new ImageDataSource(this, null, this);
        } else {
            ActivityCompat.requestPermissions(this, PermissionUtil.getStoragePermission(), REQUEST_PERMISSION_STORAGE);
        }
    }

    @Override
    public void onImagesLoaded(List<ImageFolder> imageFolders) {

        this.mImageFolders = imageFolders;
        imagePicker.setImageFolders(mImageFolders);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
                if (albumFolderAdapter != null){
                    albumFolderAdapter.refreshData(mImageFolders);
                }
            }
        });
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initAlbumList() {
        mImageFolders = new ArrayList<>();
        albumFolderAdapter = new AlbumFolderAdapter2(this, mImageFolders);
        albumFolderAdapter.setOnItemClickListener(onItemClickListener);
        albumFolderList.setAdapter(albumFolderAdapter);
        albumFolderList.setHasFixedSize(true);
        albumFolderList.setLayoutManager(new LinearLayoutManager(this));
    }


    private void registerEvent() {
        noBackTopBarCancel.setOnClickListener(this);
    }


    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.no_back_top_bar_cancel) {
            ActivityUtils.finishActivity(this);
        }
    }


    private AlbumFolderAdapter2.OnItemClickListener onItemClickListener = new AlbumFolderAdapter2.OnItemClickListener() {
        @Override
        public void OnItemClick(int position, ImageFolder imageFolder) {
            imagePicker.setCurrentImageFolderPosition(position);
            try {
                if (null != imageFolder) {
                    Intent intent = new Intent(SelectAlbumPhotoActivity2.this, ImageGridActivity2.class);
                    Bundle mBundle = new Bundle();
                    mBundle.putSerializable("imageFolder", imageFolder);
                    intent.putExtra("isImport", isImport);
                    intent.putExtra("type", type);
                    if (type == 2) {
                        intent.putExtra("tagName", tagName);
                        intent.putExtra("tagId", tagId);
                    } else if (type == 1) {
                        intent.putExtra("categoryID", categoryID);
                        intent.putExtra("categoryName", categoryName);
                    }
                    intent.putExtras(mBundle);
                    startActivity(intent);
                }
            } catch (Exception e) {
                showMessage(R.string.crop_image_bad_image);
                e.printStackTrace();
            }
        }

    };


    /**
     * @des:检查权限
     * @params:
     * @return:
     */


    public boolean checkPermission(@NonNull String permission) {
        return ActivityCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSION_STORAGE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showProgressDialog("SelectAlbumPhotoActivity2");
                new ImageDataSource(this, null, this);
            } else {
                showMessage(R.string.can_not_open_album);
            }
        }
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        ImagePicker.getInstance().restoreInstanceState(savedInstanceState);
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        ImagePicker.getInstance().saveInstanceState(outState);
    }
}
