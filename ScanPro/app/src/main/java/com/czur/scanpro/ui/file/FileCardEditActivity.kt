package com.czur.scanpro.ui.file

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.alg.Args
import com.czur.scanpro.alg.CZSaoMiao_Alg
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityCardEditBinding
import com.czur.scanpro.databinding.ActivityDocumentsSettingBinding
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.CardFrontEvent
import com.czur.scanpro.event.EditEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.facebook.drawee.backends.pipeline.Fresco
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean


class FileCardEditActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityCardEditBinding by lazy{
        ActivityCardEditBinding.inflate(layoutInflater)
    }

    private var fileId: String? = null
    private var isCard: Boolean = false
    private var cardType: Int? = null

    private var realm: Realm? = null
    private var alg: CZSaoMiao_Alg? = null
    private var userPreferences: UserPreferences? = null
    private var dirPath: String? = null
    private var tempPath: String? = null

    private var bigImagePath: String? = null
    private var smallImagePath: String? = null
    private var baseImagePath: String? = null
    private var smallBasePath: String? = null

    private var docEntity: DocEntity? = null
    private var rotate: Float? = 0f
    private var colorRotate: Float? = 0f

    private var isHorizontal: Boolean = true
    private var isScale: Boolean = true
    private var isColorHorizontal: Boolean = true
    private var isColorScale: Boolean = true
    private var isCut: Boolean = false

    private var isColor: Boolean = false

    private var blackPath: String? = null
    private var bgPath: String? = null
    private var colorPath: String? = null
    private var canRotate: AtomicBoolean? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        canRotate = AtomicBoolean(true)
        binding.editTopBar.z = 1000f
        alg = CZSaoMiao_Alg.getInstance(this)
        fileId = intent.getStringExtra("fileId")
        isCard = intent.getBooleanExtra("isCard", false);
        cardType = intent.getIntExtra("cardType", 0)
        EventBus.getDefault().register(this)
        userPreferences = UserPreferences.getInstance(this)
        tempPath = cacheDir.path + Constants.TEMP
        dirPath = <EMAIL> + File.separator + userPreferences!!.userId + File.separator
        setImage()

    }


    private fun setImage() {
        realm = Realm.getDefaultInstance()
        docEntity = realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("fileID", fileId).findFirst()
        blackPath = tempPath + UUID.randomUUID() + Constants.SMALL_JPG
        colorPath = tempPath + UUID.randomUUID() + Constants.SMALL_JPG

        baseImagePath = docEntity!!.baseImagePath
        smallBasePath = docEntity!!.baseSmallImagePath

        bigImagePath = docEntity!!.processImagePath
        smallImagePath = docEntity!!.processSmallImagePath

        val bitmap = ImageUtils.getBitmap(bigImagePath, 2048, 2048)
        isScale = bitmap.width > bitmap.height
        isHorizontal = bitmap.width > bitmap.height

        isColorHorizontal = bitmap.width > bitmap.height
        isColorScale = bitmap.width > bitmap.height

        when (docEntity!!.enhanceMode) {
            0 -> {
                setColorCheck()
            }
            1 -> {
                setBwCheck()
            }
        }
//        resetImage()
        makeSmallColorPreview()
    }

    private fun makeSmallColorPreview() {
        val uri = Uri.parse("file://$bigImagePath")
        Fresco.getImagePipeline().evictFromCache(uri)
        binding.editBigImg.setImageURI(uri)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = getColorType()
                alg!!.miao_static_process(smallBasePath, colorPath, null, null, args)


                val args2 = Args()
                args2.scanType = getScanType()
                args2.colorType = getColorGrayType()
                alg!!.miao_static_process(smallBasePath, blackPath, null, null, args2)
                return null
            }

            override fun onSuccess(path: Void?) {
                val blackUri = Uri.parse("file://$blackPath")
                Fresco.getImagePipeline().evictFromCache(uri)
                binding.blackImg.setImageURI(blackUri, null)
                val colorUri = Uri.parse("file://$colorPath")
                Fresco.getImagePipeline().evictFromCache(uri)
                binding.colorImg.setImageURI(colorUri, null)
            }
        })
    }

    private fun getScanType(): CZSaoMiao_Alg.ScanType {
        return CZSaoMiao_Alg.ScanType.NOTHING
    }

    private fun getColorType(): CZSaoMiao_Alg.ColorType {
        return when (cardType) {
            2 -> CZSaoMiao_Alg.ColorType.IDCARD_DEFAULT
            3 -> CZSaoMiao_Alg.ColorType.ENTERPRISE_DOC_DEFAULT
            else -> CZSaoMiao_Alg.ColorType.DRIVER_LICENSE_DEFAULT
        }
    }

    private fun getColorGrayType(): CZSaoMiao_Alg.ColorType {
        return when (cardType) {
            2 -> CZSaoMiao_Alg.ColorType.IDCARD_GRAY
            3 -> CZSaoMiao_Alg.ColorType.ENTERPRISE_DOC_GRAY
            else -> CZSaoMiao_Alg.ColorType.DRIVER_LICENSE_GRAY
        }
    }

    private fun setBwCheck() {
        binding.colorLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.blackLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_red_stroke))
    }


    private fun setColorCheck() {
        binding.blackLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_black24_stroke))
        binding.colorLl.setBackgroundDrawable(resources.getDrawable(R.drawable.btn_rec_6_bg_with_red_stroke))

    }

    private fun registerEvent() {
       binding.cutRl.setOnClickListener(this)
       binding.rotateRl.setOnClickListener(this)
       binding.normalBackBtn.setOnClickListener(this)
       binding.colorLl.setOnClickListener(this)
       binding.blackLl.setOnClickListener(this)
    }


    /**
     * @des: 压缩并且保存小图
     * @params:
     * @return:
     */
    private fun makeSmallImg(isBase: Boolean): Boolean {
        if (FileUtils.createOrExistsDir(dirPath)) {
            val bitmap = ImageUtils.getBitmap(if (isBase) baseImagePath else bigImagePath, 2048, 2048)
            val scale = bitmap.width * 1.0 / bitmap.height
            val v = 300 * scale
            var smallBitmap: Bitmap? = null
            smallBitmap = ImageUtils.compressByScale(bitmap, v.toInt(), 300, true)
            val isSuccessSave = ImageUtils.save(smallBitmap, if (isBase) smallBasePath else smallImagePath, Bitmap.CompressFormat.JPEG, true)
            return isSuccessSave

        }
        return false
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.rotateRl -> if (canRotate!!.get()) rotateAnimAndOperate()
            R.id.colorLl -> if (docEntity!!.enhanceMode != 0) colorMode()
            R.id.blackLl -> if (docEntity!!.enhanceMode != 1) bwMode()
            R.id.cutRl -> {
                val intent = Intent(this@FileCardEditActivity, FileCutActivity::class.java)
                intent.putExtra("fileId", fileId)
                intent.putExtra("baseUrl", baseImagePath);
                intent.putExtra("url", bigImagePath)
                ActivityUtils.startActivity(intent)
            }

            else -> {
            }
        }
    }

    private fun rotateAnimAndOperate() {
        showProgressDialog(true)

        if (rotate == 360f) rotate = 0f
        if (colorRotate == 360f) colorRotate = 0f

        val animator = AnimatorSet()
        val scale = if (isColorScale && isColorHorizontal) binding.editBigImg.height * 1.0 / binding.editBigImg.width else binding.editBigImg.width * 1.0 / binding.editBigImg.height
        val smallScale = if (isScale && isHorizontal) binding.colorImg.height * 1.0 / binding.colorImg.width else binding.colorImg.width * 1.0 / binding.colorImg.height

        val fromScale = if (isColorScale == isColorHorizontal) 1f else scale.toFloat()
        val toScale = if (isColorScale == !isColorHorizontal) 1f else scale.toFloat()
        val fromSmallScale = if (isScale == isHorizontal) 1f else smallScale.toFloat()
        val toSmallScale = if (isScale == !isHorizontal) 1f else smallScale.toFloat()



        animator.playTogether(
                ObjectAnimator.ofFloat(binding.editBigImg, "rotation", colorRotate!!, colorRotate!! + 90f),
                ObjectAnimator.ofFloat(binding.editBigImg, "scaleX", fromScale, toScale),
                ObjectAnimator.ofFloat(binding.editBigImg, "scaleY", fromScale, toScale),

                ObjectAnimator.ofFloat(binding.colorImg, "rotation", rotate!!, rotate!! + 90f),
                ObjectAnimator.ofFloat(binding.colorImg, "scaleX", fromSmallScale, toSmallScale),
                ObjectAnimator.ofFloat(binding.colorImg, "scaleY", fromSmallScale, toSmallScale),

                ObjectAnimator.ofFloat(binding.blackImg, "rotation", rotate!!, rotate!! + 90f),
                ObjectAnimator.ofFloat(binding.blackImg, "scaleX", fromSmallScale, toSmallScale),
                ObjectAnimator.ofFloat(binding.blackImg, "scaleY", fromSmallScale, toSmallScale)


        )
        animator.duration = 250
        animator.addListener(animListener)
        animator.start()
        isColorScale = !isColorScale
        isScale = !isScale
        rotate = rotate!! + 90f
        colorRotate = colorRotate!! + 90f

    }

    private val animListener = object : Animator.AnimatorListener {
        override fun onAnimationStart(animation: Animator) {
            canRotate!!.set(false)
            ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                override fun doInBackground(): Void? {
                    val bitmap = ImageUtils.getBitmap(bigImagePath, 2048, 2048)
                    val rotateBitmap = ImageUtils.rotate(bitmap, 90, bitmap.width * 1.0f / 2, bitmap.height * 1.0f / 2)
                    ImageUtils.save(rotateBitmap, bigImagePath, Bitmap.CompressFormat.JPEG)

                    val baseBitmap = ImageUtils.getBitmap(baseImagePath, 2048, 2048)
                    val rotateBaseBitmap = ImageUtils.rotate(baseBitmap, 90, baseBitmap.width * 1.0f / 2, baseBitmap.height * 1.0f / 2)
                    ImageUtils.save(rotateBaseBitmap, baseImagePath, Bitmap.CompressFormat.JPEG)
                    makeSmallImg(true)
                    makeSmallImg(false)

                    return null
                }

                override fun onSuccess(path: Void?) {
                    realm!!.executeTransaction {
                        docEntity!!.isDirty = 1
                        docEntity!!.uuid = UUID.randomUUID().toString()
                        docEntity!!.updateTime = getCurrentTime()
                    }
                    hideProgressDialog()
                    EventBus.getDefault().post(EditEvent(EventType.ROTATE))
                    canRotate!!.set(true)

                }

                override fun onFail(t: Throwable?) {
                    super.onFail(t)
                    LogUtils.e(t)
                }
            })
        }

        override fun onAnimationEnd(animation: Animator) {


        }

        override fun onAnimationCancel(animation: Animator) {}

        override fun onAnimationRepeat(animation: Animator) {}
    }

    private fun colorMode() {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = getColorType()
                alg!!.miao_static_process(baseImagePath, bigImagePath, null, smallImagePath, args)
                return null
            }

            override fun onSuccess(path: Void?) {
                realm!!.executeTransaction {
                    docEntity!!.enhanceMode = 0
                    docEntity!!.isDirty = 1
                    docEntity!!.uuid = UUID.randomUUID().toString()
                    docEntity!!.updateTime = getCurrentTime()
                }
                makeSmallImg(false)
                hideProgressDialog()
                setColorCheck()
                setColorFlag()
                resetImage()
                val uri = Uri.parse("file://" + docEntity!!.processImagePath)
                Fresco.getImagePipeline().evictFromCache(uri)
                binding.editBigImg.setImageURI(uri)
                EventBus.getDefault().post(EditEvent(EventType.COLOR))

            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                LogUtils.e(t)
            }
        })
    }

    /**
     * 重置大图的flag
     *
     * @param: []
     * @return: []
     */

    private fun setColorFlag() {
        val bitmap = ImageUtils.getBitmap(bigImagePath, 2048, 2048)
        isColorHorizontal = bitmap.width > bitmap.height
        isColorScale = bitmap.width > bitmap.height
    }

    /**
     * 重置小图的flag
     *
     * @param: []
     * @return: []
     */
    private fun setPreviewFlag() {
        val bitmap = ImageUtils.getBitmap(bigImagePath, 2048, 2048)
        isHorizontal = bitmap.width > bitmap.height
        isScale = bitmap.width > bitmap.height
    }

    private fun resetImage() {
        val scale = if (isColorScale && isColorHorizontal) binding.editBigImg.height * 1.0 /binding.editBigImg.width else binding.editBigImg.width * 1.0 / binding.editBigImg.height
        val toScale = if (isColorScale == isColorHorizontal) 1f else scale.toFloat()
        binding.editBigImg.scaleX = toScale
        binding.editBigImg.scaleY = toScale
        binding.editBigImg.rotation = 0f
        colorRotate = 0f
    }

    private fun resetPreviewImage() {
        val scale = if (isScale && isHorizontal) binding.colorImg.height * 1.0 / binding.colorImg.width else binding.colorImg.width * 1.0 / binding.colorImg.height
        val toScale = if (isScale == isHorizontal) 1f else scale.toFloat()
        binding.blackImg.scaleX = toScale
        binding.blackImg.scaleY = toScale
        binding.blackImg.rotation = 0f

        binding.colorImg.scaleX = toScale
        binding.colorImg.scaleY = toScale
        binding.colorImg.rotation = 0f

        rotate = 0f
    }


    private fun bwMode() {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val args = Args()
                args.scanType = getScanType()
                args.colorType = getColorGrayType()
                alg!!.miao_static_process(baseImagePath, bigImagePath, null, smallImagePath, args)

                return null
            }

            override fun onSuccess(path: Void?) {
                realm!!.executeTransaction {
                    docEntity!!.enhanceMode = 1
                    docEntity!!.isDirty = 1
                    docEntity!!.uuid = UUID.randomUUID().toString()
                    docEntity!!.updateTime = getCurrentTime()
                }
                makeSmallImg(false)
                hideProgressDialog()
                setBwCheck()
                setColorFlag()
                resetImage()

                val uri = Uri.parse("file://" + docEntity!!.processImagePath)
                Fresco.getImagePipeline().evictFromCache(uri)
                binding.editBigImg.setImageURI(uri, null)
                EventBus.getDefault().post(EditEvent(EventType.COLOR))
            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                LogUtils.e(t)
            }
        })
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        val realm = Realm.getDefaultInstance()
        when (event.eventType) {

            EventType.CUT -> {
                LogUtils.e("cut")
                isCut = true
                setColorFlag()
                resetImage()
                setPreviewFlag()
                resetPreviewImage()
                blackPath = tempPath + UUID.randomUUID() + Constants.SMALL_JPG
                colorPath = tempPath + UUID.randomUUID() + Constants.SMALL_JPG
                makeSmallColorPreview()
            }

            else -> {
            }
        }
    }

    override fun onDestroy() {
//        if (isCard) {
//        }
        EventBus.getDefault().post(CardFrontEvent(EventType.CARD_IS_FRONT, rotate!!, isCut))

        super.onDestroy()
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                CleanUtils.cleanCustomDir(cacheDir.path + Constants.TEMP)
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}