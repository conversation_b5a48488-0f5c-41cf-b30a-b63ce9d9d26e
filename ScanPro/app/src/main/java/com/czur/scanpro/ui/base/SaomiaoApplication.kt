package com.czur.scanpro.ui.base

import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import androidx.multidex.BuildConfig
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.Utils
import com.czur.scanpro.alg.CZSaoMiao_Alg
import com.czur.scanpro.common.Constants
import com.czur.scanpro.common.OSSInstance
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.utils.sharesdk.ShareSDKUtils
import com.facebook.common.memory.MemoryTrimType
import com.facebook.common.memory.NoOpMemoryTrimmableRegistry
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.imagepipeline.core.ImagePipelineConfig
import com.facebook.imagepipeline.core.ImagePipelineFactory
import com.facebook.imagepipeline.nativecode.ImagePipelineNativeLoader
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.umeng.commonsdk.UMConfigure
import io.realm.Realm
import io.realm.RealmConfiguration
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


/**
 * Created by Yz on 2018/12/27.
 * Email：<EMAIL>
 */
class SaomiaoApplication : MultiDexApplication() {

    private var wechatPayApi: IWXAPI? = null
    private val CPU_COUNT = Runtime.getRuntime().availableProcessors()
    private val CORE_POOL_SIZE = 2.coerceAtLeast((CPU_COUNT - 1).coerceAtMost(4))
    private lateinit var executorService: ExecutorService;
    private var userPreferences: UserPreferences? = null


    override fun onCreate() {
        super.onCreate()

        LogUtils.i("===SaomiaoApplication===")

        userPreferences = UserPreferences.getInstance(this)

        if (!userPreferences!!.isFirstPrivacyPolicy) {

//            CZSaoMiao_Alg.getInstance(this).init_tess(this.filesDir.absolutePath + File.separator + "tessdata")
            executorService = Executors.newFixedThreadPool(CORE_POOL_SIZE)
            //初始化工具类
            Utils.getApp()
            Utils.init(this)
            //初始化网络请求
            MiaoHttpManager.getInstance().init(this)
            //初始化图片加载库
            initFresco()
            //初始化数据库
            initRealm()

            executorService.execute {
                //初始化shareSdk
//                MobSDK.init(this)
                // 初始化shareSdk
//                Log.i("Jason", "SaomiaoApplication.ShareSDKUtils.init")
                ShareSDKUtils.init(this.applicationContext)

                //提前加载so库
                CZSaoMiao_Alg.getInstance(this)
            }
            executorService.execute {
                //初始化Log
                initLog()
            }
            executorService.execute {
                //初始化微信支付
                initWechatPay()
            }

            executorService.execute {
                //初始化统计
                initBugly()
                initOSSInstance()
                initUMeng()
            }
        }
    }

    fun getChannel(context: Context): String? {
        try {
            val appInfo = context.packageManager
                .getApplicationInfo(
                    context.packageName,
                    PackageManager.GET_META_DATA
                )
            return appInfo.metaData.getString("UMENG_CHANNEL")
        } catch (e: Exception) {
        }

        return "getChannelException"
    }

    private fun initUMeng() {
        LogUtils.e(getChannel(this))
        UMConfigure.preInit(this, "5cf0ed994ca3575330000ba2", getChannel(this))
        UMConfigure.init(
            this,
            "5cf0ed994ca3575330000ba2",
            getChannel(this),
            UMConfigure.DEVICE_TYPE_PHONE,
            ""
        )
    }

    private fun initOSSInstance() {
        OSSInstance.instance.init(this)
    }

    private fun initBugly() {
        CrashReport.initCrashReport(this, "e4740051ed", false)
        CrashReport.setIsDevelopmentDevice(this, BuildConfig.DEBUG);
    }

    private fun initWechatPay() {
        wechatPayApi = WXAPIFactory.createWXAPI(this, null)
    }

    fun wechatPayApi(): IWXAPI {
        if (wechatPayApi == null) {
            wechatPayApi = WXAPIFactory.createWXAPI(this, null)
            wechatPayApi!!.registerApp("wx1e22b31e63df0112")
            return wechatPayApi!!
        } else {
            return wechatPayApi!!
        }
    }

    private fun initFresco() {
        //解决oom和so库加载失败
        val memoryTrimmableRegistry = NoOpMemoryTrimmableRegistry.getInstance()
        memoryTrimmableRegistry.registerMemoryTrimmable { trimType ->
            val suggestedTrimRatio = trimType.suggestedTrimRatio;
            if (MemoryTrimType.OnCloseToDalvikHeapLimit.suggestedTrimRatio == suggestedTrimRatio
                || MemoryTrimType.OnSystemLowMemoryWhileAppInBackground.suggestedTrimRatio == suggestedTrimRatio
                || MemoryTrimType.OnSystemLowMemoryWhileAppInForeground.suggestedTrimRatio == suggestedTrimRatio
            ) {
                //清空内存缓存
                ImagePipelineFactory.getInstance().imagePipeline.clearMemoryCaches()
            }
        };
        val builder = ImagePipelineConfig.newBuilder(this)
        var imagePipelineConfig = builder.setDownsampleEnabled(true)
            .setResizeAndRotateEnabledForNetwork(true)
            .setBitmapsConfig(Bitmap.Config.RGB_565)
            .setMemoryTrimmableRegistry(memoryTrimmableRegistry)
            .build()
        Fresco.initialize(this, imagePipelineConfig)
        try {
            ImagePipelineNativeLoader.load()
        } catch (error: UnsatisfiedLinkError) {
            Fresco.shutDown()
            builder.experiment().setNativeCodeDisabled(true)
            imagePipelineConfig = builder.build()
            Fresco.initialize(this, imagePipelineConfig)
            error.printStackTrace()
        }
    }


    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    private fun initRealm() {
        Realm.init(this)
        val config = RealmConfiguration.Builder()
            //文件名
            .name("saomiao.realm")
            //版本号
            .schemaVersion(1)
            .compactOnLaunch()
            .migration(MyMigration())
            .allowWritesOnUiThread(true)
            .allowQueriesOnUiThread(true)
            .build()
        Realm.setDefaultConfiguration(config)

    }


    /**
     * 初始化全局Log配置
     */
    private fun initLog() {
        val strDir: String = Constants.SAOMIAO_SD_PATH.toString()
        LogUtils.getConfig()
            // 设置 log 总开关，包括输出到控制台和文件，默认开
            .setLogSwitch(true)//TODO logUtils总开关
            // 设置是否输出到控制台开关，默认开
            .setConsoleSwitch(true)
            // 设置 log 文件开关
            .setLog2FileSwitch(Constants.IS_DEBUG_FLAG)
            // 设置 log 文件存储目录
            .setDir(strDir)
            .setGlobalTag(LOG_TAG)
            // 设置 log 头信息开关，默认为开
            .setLogHeadSwitch(true)
            // 输出日志是否带边框开关，默认开
            .setBorderSwitch(false)
            // log 的控制台过滤器，和 logcat 过滤器同理，默认 Verbose
            .setConsoleFilter(LogUtils.V)
            // log 文件过滤器，和 logcat 过滤器同理，默认 Verbose
            .setFileFilter(LogUtils.V)
            .stackDeep = 1
        // 日志保留天数
        LogUtils.getConfig().saveDays = 7

        LogUtils.i("SaomiaoApplication.initLog.strDir=" + LogUtils.getConfig().dir)
    }

    companion object {
        private var application: SaomiaoApplication? = null
        val LOG_TAG = "Saomiaoyyy"

        /**
         * 获取context
         *
         * @return
         */
        @Synchronized
        open fun getApplication(): Context? {
            if (application == null) {
                application = SaomiaoApplication()
            }
            return application
        }

    }

}
