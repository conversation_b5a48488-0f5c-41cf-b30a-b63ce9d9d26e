package com.czur.scanpro.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;


public class MoreBottomDialogPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;


    public MoreBottomDialogPopup(Context context) {
        super(context);
    }

    public MoreBottomDialogPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder implements View.OnClickListener {
        private Context mContext;
        private RelativeLayout saveBtn;
        private RelativeLayout deleteBtn;
        private RelativeLayout cancelBtn;

        public Builder(Context mContext,  OnBottomSheetClickListener onBottomSheetClickListener) {
            this.mContext = mContext;
            this.onBottomSheetClickListener = onBottomSheetClickListener;
        }

        /**
         * 点击事件接口
         **/
        public interface OnBottomSheetClickListener {
            /**
             * @param viewId
             */
            void onClick(int viewId);
        }

        private OnBottomSheetClickListener onBottomSheetClickListener;

        private void setOnBottomSheetClickListener(OnBottomSheetClickListener onBottomSheetClickListener) {
            this.onBottomSheetClickListener = onBottomSheetClickListener;
        }

        @Override
        public void onClick(View v) {
            int id = v.getId();
            if (id == R.id.save_btn) {
                if (onBottomSheetClickListener != null) {
                    onBottomSheetClickListener.onClick(R.id.save_btn);
                }
            } else if (id == R.id.deleteBtn) {
                if (onBottomSheetClickListener != null) {
                    onBottomSheetClickListener.onClick(R.id.deleteBtn);
                }
            } else if (id == R.id.cancelBtn) {
                if (onBottomSheetClickListener != null) {
                    onBottomSheetClickListener.onClick(R.id.cancelBtn);
                }
            }
        }

        public MoreBottomDialogPopup create() {
            LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final MoreBottomDialogPopup dialog = new MoreBottomDialogPopup(mContext, R.style.SocialAccountDialogStyle);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            params.gravity = Gravity.BOTTOM;
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = SizeUtils.dp2px(160);
            dialog.getWindow().setAttributes(params);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final MoreBottomDialogPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            BarUtils.setStatusBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode( dialog.getWindow(), true);
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            View layout = inflater.inflate(R.layout.dialog_more_sheet, null, false);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            dialog.addContentView(layout, params);
            saveBtn = (RelativeLayout)  layout.findViewById(R.id.save_btn);
            deleteBtn = (RelativeLayout) layout. findViewById(R.id.deleteBtn);
            cancelBtn = (RelativeLayout)  layout.findViewById(R.id.cancelBtn);
            saveBtn.setOnClickListener(this);
            deleteBtn.setOnClickListener(this);
            cancelBtn.setOnClickListener(this);
            return layout;
        }
    }
}
