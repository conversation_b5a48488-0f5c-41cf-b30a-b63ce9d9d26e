package com.czur.scanpro.ui.sync;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSS;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.model.GetObjectRequest;
import com.alibaba.sdk.android.oss.model.GetObjectResult;
import com.alibaba.sdk.android.oss.model.ObjectMetadata;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.scanpro.BuildConfig;
import com.czur.scanpro.R;
import com.czur.scanpro.alg.Args;
import com.czur.scanpro.alg.CZSaoMiao_Alg;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.common.OSSInstance;
import com.czur.scanpro.entity.model.BaseModel;
import com.czur.scanpro.entity.model.FileSizeModel;
import com.czur.scanpro.entity.model.SyncEntity;
import com.czur.scanpro.entity.model.UserAllInfoModel;
import com.czur.scanpro.entity.realm.CategoryEntity;
import com.czur.scanpro.entity.realm.DocEntity;
import com.czur.scanpro.entity.realm.DownloadEntity;
import com.czur.scanpro.entity.realm.SyncCategoryEntity;
import com.czur.scanpro.entity.realm.SyncDocEntity;
import com.czur.scanpro.entity.realm.SyncTagEntity;
import com.czur.scanpro.entity.realm.TagEntity;
import com.czur.scanpro.event.BaseEvent;
import com.czur.scanpro.event.CategoryOrDocsChangedEvent;
import com.czur.scanpro.event.EventType;
import com.czur.scanpro.event.NoticeServiceEnoughStopEvent;
import com.czur.scanpro.event.NoticeServiceIsStopEvent;
import com.czur.scanpro.event.ResetTimeCountEvent;
import com.czur.scanpro.event.StopSyncTimeCountEvent;
import com.czur.scanpro.event.SyncFinishEvent;
import com.czur.scanpro.event.SynchronizingEvent;
import com.czur.scanpro.network.HttpManager;
import com.czur.scanpro.network.core.MiaoHttpEntity;
import com.czur.scanpro.network.core.MiaoHttpManager;
import com.czur.scanpro.preferences.UserPreferences;
import com.czur.scanpro.utils.BitmapUtils;
import com.czur.scanpro.utils.validator.Validator;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

import io.realm.Realm;
import io.realm.RealmResults;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class SyncService extends Service {
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private List<SyncDocEntity> syncPageList;
    private List<SyncCategoryEntity> syncCategoryList;
    private List<SyncTagEntity> syncTagList;
    private String syncDir;
    private long beginTime;
    private String dirPath;
    private Realm realm;
    private ThreadUtils.SimpleTask<Void> syncTask;
    private SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        initNotification();
        initComponent();
        syncTask = new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() throws Throwable {
                realm = Realm.getDefaultInstance();
                try {
                    syncTask();
                } finally {
                    realm.close();
                }
                return null;
            }

            @Override
            public void onSuccess(@Nullable Void result) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    stopForeground(true);
                }
                ServiceUtils.stopService(SyncService.class);
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                syncStop(System.currentTimeMillis());
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    stopForeground(true);
                }
                ServiceUtils.stopService(SyncService.class);
            }
        };
        initThreadToSync();
    }

    /**
     * Activity中一启动Service之后，就会调用 onStartCommand()方法
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_STICKY;
    }

    private void initComponent() {
        syncCategoryList = new ArrayList<>();
        syncPageList = new ArrayList<>();
        syncTagList = new ArrayList<>();
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        syncDir = getFilesDir() + File.separator + Constants.SYNC_PATH;
        dirPath = getFilesDir() + File.separator + userPreferences.getUserId() + File.separator;
    }

    /**
     * 初始化线程线程池
     */
    private void initThreadToSync() {
        ThreadUtils.executeByCpu(syncTask);
    }

    /**
     * 同步流程
     */
    private void syncTask() {
        LogUtils.e("开始同步");
        EventBus.getDefault().postSticky(new SynchronizingEvent(EventType.IS_SYNCHRONIZING));
        EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));
        LogUtils.i("step1 获取服务器时间");
        String serverTime = getServerTime();
        beginTime = System.currentTimeMillis();
        if (Validator.isEmpty(serverTime)) {
            LogUtils.i("获取服务器时间失败");
            syncStop(beginTime);
            return;
        }

        LogUtils.i("step2 根据服务器时间去获得数据");
        SyncEntity serverSyncEntity;
        if (userPreferences.getSyncTime() == 0) {
            LogUtils.i("获得所有数据", "所有数据");
            serverSyncEntity = getFileByTime("");
        } else {
            String lastSyncTime = formatter.format(new Date(userPreferences.getSyncTime()));
            LogUtils.i("根据上次保存时间获得数据", lastSyncTime);
            serverSyncEntity = getFileByTime(lastSyncTime);
        }

        if (Validator.isEmpty(serverSyncEntity)) {
            syncStop(beginTime);
            return;
        }

        LogUtils.i("step3 根据书和页两个List去构建下载List");
        List<SyncEntity.ScanProCategoryBean> categoryList = serverSyncEntity.getAppFileGroupList();
        List<SyncEntity.ScanProDocBean> docList = serverSyncEntity.getAppScanFileList();
        List<SyncEntity.ScanProTagBean> tagsList = serverSyncEntity.getAppFileTagList();
        List<DownloadEntity> downloadList = makeDownLoadList(docList);

        LogUtils.i("step4 合并下载文件");
        mergeDownloadFile(downloadList);

        LogUtils.i("step5 需要同步的数据添加至同步表中, 数据表中设置为不需要更新");
        copyToSyncTable(serverTime);

        LogUtils.i("step6 需要同步的数据拷贝到data/file/sync 文件夹下");
        copyToSyncDir();

        LogUtils.i("step7 检查云存储剩余空间是否足够");
        getUserInfo();
        boolean isNotEnough = checkCloudIsEnough();
        if (isNotEnough) {
            EventBus.getDefault().postSticky(new NoticeServiceEnoughStopEvent(EventType.SYNC_SPACE_IS_NOT_ENOUGH));
            syncStop(beginTime);
            return;
        }

        LogUtils.i("step8 下载并且处理文件");
        HashSet<String> fileNoExistSet = downloadAndProcessFile();
        boolean isAllDownload = isAllDownloadAndMakeSmallImg();
        LogUtils.e("同步：下载完成");

        LogUtils.i("step9 检查并更新需要同步表数据");
        checkAndUpdateSyncData(categoryList, tagsList);

        LogUtils.i("step10 上传文件并且删除同步文件夹的文件");
        checkIsUploadedAndDeleteFilesInSyncDir();
        boolean isAllUpload = isAllPagesUpload();

        if (isAllDownload && isAllUpload) {
            SyncEntity syncEntity = transFormRealmListToSyncList(syncCategoryList, syncPageList, syncTagList);
            LogUtils.i("step11 上传同步表中的数据并且删除同步表");
            LogUtils.e(new Gson().toJson(syncEntity));
            boolean isPush = syncDataToServer(syncEntity);
            if (!isPush) {
                syncFinish(beginTime);
                return;
            }
            try {
                long saveTime = formatter.parse(serverTime).getTime();
                userPreferences.setSyncTime(saveTime);
                LogUtils.i("保存同步时间", serverTime, saveTime);
            } catch (ParseException e) {
                LogUtils.e(e);
                syncFinish(beginTime);
                return;
            }
        } else {
            syncFinish(beginTime);
            return;
        }

        LogUtils.e("上传完成");
        LogUtils.i("step12 合并数据前与推送数据对比检查");
        checkMergeData(categoryList, docList, tagsList, fileNoExistSet);
        clearSyncTables();
        LogUtils.i("step13 合并books,pages,tags数据");
        boolean hasDeleteCategorys = mergeCategory(categoryList);
        boolean hasDeletePages = mergeDocs(docList);
        boolean hasDeleteTags = mergeTags(tagsList);

        LogUtils.i(hasDeleteCategorys, hasDeletePages, hasDeleteTags);
        if (hasDeleteCategorys || hasDeletePages || hasDeleteTags) {
            EventBus.getDefault().postSticky(new CategoryOrDocsChangedEvent(EventType.CATEGORY_OR_DOCS_CHANGED));
        }

        LogUtils.e("同步完成");
        if (hasUpdateFiles()) {
            LogUtils.i("同步过程中有数据更新，重新计时");
            if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService.class)) {
                EventBus.getDefault().post(new ResetTimeCountEvent(EventType.RESET_TIME_COUNT));
            } else {
                Intent intent = new Intent(SyncService.this, AutoSyncTimeCountService.class);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    startForegroundService(intent);
                } else {
                    startService(intent);
                }
            }
            syncFinish(beginTime);
        } else {
            syncFinish(beginTime);
        }


    }

    /**
     * 清空同步表
     */
    private void clearSyncTables() {
        realm.beginTransaction();
        realm.where(SyncCategoryEntity.class).findAll().deleteAllFromRealm();
        realm.where(SyncDocEntity.class).findAll().deleteAllFromRealm();
        realm.where(SyncTagEntity.class).findAll().deleteAllFromRealm();
        realm.commitTransaction();
    }


    /**
     * 是否有更新的页或书
     */
    private boolean hasUpdateFiles() {
        RealmResults<DocEntity> docEntities = realm.where(DocEntity.class)
                .notEqualTo("isDirty", 0)
                .equalTo("isTemp", 0)
                .findAll();
        RealmResults<CategoryEntity> categoryEntities = realm.where(CategoryEntity.class)
                .equalTo("isDirty", 1)
                .findAll();

        RealmResults<TagEntity> tagEntities = realm.where(TagEntity.class)
                .equalTo("isDirty", 1)
                .findAll();
        LogUtils.i("hasUpdateFiles", docEntities.size() > 0 || categoryEntities.size() > 0 || tagEntities.size() > 0);
        return docEntities.size() > 0 || categoryEntities.size() > 0 || tagEntities.size() > 0;
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        if (event.getEventType() == EventType.STOP_SYNC) {
            stopSync(System.currentTimeMillis());
            LogUtils.i("SERVIECE 收到 EventBus needStop为true");
        }
    }

    /**
     * step1 获取服务器时间
     */
    private String getServerTime() {
        if (NetworkUtils.isConnected()) {
            MiaoHttpEntity<String> serverTimeEntity = httpManager.request().getServerTime(
                    userPreferences.getUserId(), String.class);
            if (serverTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                String serverTime = serverTimeEntity.getBody();
                LogUtils.i("server time", serverTime);
                return serverTime;
            } else {
                LogUtils.i("server time", "null");
                return null;
            }
        } else {
            return null;
        }

    }

    /**
     * step2 根据服务器时间去获得数据
     */
    private SyncEntity getFileByTime(String serverTime) {
        LogUtils.i(userPreferences.getUserId(), serverTime, userPreferences.getEndpoint(), userPreferences.getChannel());
        MiaoHttpEntity<SyncEntity> fileByTimeEntity = httpManager.request().getFileByTime(
                userPreferences.getUserId(), serverTime, SyncEntity.class);

        if (fileByTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
            LogUtils.i("server file", new Gson().toJson(fileByTimeEntity.getBody()));
            return fileByTimeEntity.getBody();
        } else {
            return null;
        }
    }

    /**
     * step3 根据书和页两个List去构建下载List
     */
    private List<DownloadEntity> makeDownLoadList(List<SyncEntity.ScanProDocBean> docList) {
        List<DownloadEntity> downloadEntities = new ArrayList<>();
        for (SyncEntity.ScanProDocBean docBean : docList) {
            DocEntity docEntity = realm.where(DocEntity.class).equalTo("fileID", docBean.getFileId()).findFirst();

            //  本地数据已经发生更改已本地数据为主，增量数据丢弃继续遍历
            if (docEntity != null && docEntity.isDirty() != 0) {
                continue;
            }
            //  增量数据为已删除数据无需下载文件，增量数据丢弃继续遍历
            if (docBean.isDelete()) {
                continue;
            }
            //  文件移动、标签更改等非文件操作的数据无需下载文件，增量数据丢弃继续遍历
            if (docEntity != null && Objects.equals(docEntity.getUuid(), docBean.getUuid())) {
                continue;
            }

            realm.beginTransaction();
            DownloadEntity downloadEntity = realm.createObject(DownloadEntity.class);
            LogUtils.i("需要下载", docBean.getFileId(), docBean.getUuid(), docBean, docBean.getFileType(), docBean.getEnhanceMode());
            downloadEntity.setFileID(docBean.getFileId());
            downloadEntity.setEnhanceMode(docBean.getEnhanceMode());
            downloadEntity.setFileType(docBean.getFileType());
            downloadEntity.setUuid(docBean.getUuid());
            realm.insertOrUpdate(downloadEntity);
            downloadEntities.add(downloadEntity);
            realm.commitTransaction();
        }


        return downloadEntities;
    }

    /**
     * step4 合并下载文件
     */
    private void mergeDownloadFile(List<DownloadEntity> downloadList) {
        if (Validator.isNotEmpty(downloadList)) {
            for (DownloadEntity downloadEntity : downloadList) {
                String fileId = downloadEntity.getFileID();
                String uuid = downloadEntity.getUuid();
                int fileType = downloadEntity.getFileType();
                int enhanceMode = downloadEntity.getEnhanceMode();
                DownloadEntity queryDownloadEntity = realm.where(DownloadEntity.class).equalTo("fileID", fileId).findFirst();
                if (Validator.isNotEmpty(queryDownloadEntity)) {
                    realm.beginTransaction();
                    queryDownloadEntity.deleteFromRealm();
                    realm.commitTransaction();
                }
                realm.beginTransaction();
                DownloadEntity newDownloadEntity = realm.createObject(DownloadEntity.class);
                newDownloadEntity.setFileID(fileId);
                newDownloadEntity.setUuid(uuid);
                newDownloadEntity.setFileType(fileType);
                newDownloadEntity.setEnhanceMode(enhanceMode);
                newDownloadEntity.setHasDownloadImage(false);
                newDownloadEntity.setHasMakeSmallImage(false);
                realm.commitTransaction();
            }
        }

    }

    /**
     * 需要同步的数据添加至同步表中, 数据表中设置为不需要更新
     */
    private void copyToSyncTable(String serverTime) {
        long firstTime = System.currentTimeMillis();
        String curDate = formatter.format(new Date(firstTime));

        //Categorys 移动并把数据表的数据isDirty 置为0
        RealmResults<CategoryEntity> localCategoryEntities = realm.where(CategoryEntity.class)
                .equalTo("isDirty", 1)
                .findAll();

        realm.beginTransaction();
        for (CategoryEntity localCategoryEntity : localCategoryEntities) {
            SyncCategoryEntity syncCategoryEntity = realm.createObject(SyncCategoryEntity.class);
            syncCategoryEntity.setCategoryID(localCategoryEntity.getCategoryID());
            syncCategoryEntity.setCategoryName(localCategoryEntity.getCategoryName());
            syncCategoryEntity.setCreateTime(localCategoryEntity.getCreateTime());
            syncCategoryEntity.setUserID(userPreferences.getUserId());
            syncCategoryEntity.setUpdateTime(localCategoryEntity.getUpdateTime());
            syncCategoryEntity.setDirty(localCategoryEntity.isDirty());
            syncCategoryEntity.setDelete(localCategoryEntity.isDelete());
            syncCategoryEntity.setSyncTime(serverTime);
            realm.insertOrUpdate(syncCategoryEntity);
            localCategoryEntity.setDirty(0);
        }
        realm.commitTransaction();
        RealmResults<SyncCategoryEntity> syncCategorysEntities = realm.where(SyncCategoryEntity.class).findAll();
        syncCategoryList = realm.copyFromRealm(syncCategorysEntities);

        //Pages 移动并把数据表的数据isDirty 置为0
        RealmResults<DocEntity> localPagesEntities = realm.where(DocEntity.class)
                .equalTo("isTemp", 0)
                .notEqualTo("isDirty", 0)
                .findAll();
        LogUtils.e(localPagesEntities.size() + "xxxxxxxxxxxxx");

        realm.beginTransaction();
        for (DocEntity localDocsEntity : localPagesEntities) {

            SyncDocEntity notUpdateDocEntity = realm.where(SyncDocEntity.class).equalTo("fileID", localDocsEntity.getFileID()).findFirst();
            if (notUpdateDocEntity != null) {
                notUpdateDocEntity.setFileID(localDocsEntity.getFileID());
                notUpdateDocEntity.setCategoryID(localDocsEntity.getCategoryID());
                notUpdateDocEntity.setCategoryName(localDocsEntity.getCategoryName());
                notUpdateDocEntity.setTagId(localDocsEntity.getTagId());
                notUpdateDocEntity.setTagName(localDocsEntity.getTagName());
                notUpdateDocEntity.setBucket(localDocsEntity.getBucket());
                notUpdateDocEntity.setUuid(localDocsEntity.getUuid());
                notUpdateDocEntity.setDirty(localDocsEntity.isDirty() == 1 ? 1 : 2);

                notUpdateDocEntity.setEnhanceMode(localDocsEntity.getEnhanceMode());
                notUpdateDocEntity.setFileType(localDocsEntity.getFileType());
                notUpdateDocEntity.setUserID(userPreferences.getUserId());
                if (localDocsEntity.isNewAdd() == 1) {
                    notUpdateDocEntity.setNewAdd(1);
                } else if (localDocsEntity.isNewAdd() == 0 && notUpdateDocEntity.isNewAdd() == 1) {
                    notUpdateDocEntity.setNewAdd(1);
                } else {
                    notUpdateDocEntity.setNewAdd(0);
                }
                notUpdateDocEntity.setBaseImagePath(localDocsEntity.getBaseImagePath());
                notUpdateDocEntity.setBaseSmallImagePath(localDocsEntity.getBaseSmallImagePath());
                notUpdateDocEntity.setOriginalImagePath(localDocsEntity.getOriginalImagePath());
                notUpdateDocEntity.setProcessImagePath(localDocsEntity.getProcessImagePath());
                notUpdateDocEntity.setProcessSmallImagePath(localDocsEntity.getProcessSmallImagePath());

                notUpdateDocEntity.setNewAdd(localDocsEntity.isNewAdd());
                notUpdateDocEntity.setDelete(localDocsEntity.isDelete());
                notUpdateDocEntity.setCreateTime(formatDate(localDocsEntity.getCreateTime()));
                notUpdateDocEntity.setUpdateTime(formatDate(localDocsEntity.getUpdateTime()));
                notUpdateDocEntity.setSyncTime(serverTime);
                notUpdateDocEntity.setFileSize(localDocsEntity.getFileSize());
                if (localDocsEntity.isDelete() == 0){
                    Log.d("chucunkongjian", "获取图片大小告诉服务器1 "+localDocsEntity.getFileSize());
                }
            } else {
                SyncDocEntity syncDocEntity = realm.createObject(SyncDocEntity.class);
                syncDocEntity.setFileID(localDocsEntity.getFileID());
                syncDocEntity.setCategoryID(localDocsEntity.getCategoryID());
                syncDocEntity.setCategoryName(localDocsEntity.getCategoryName());
                syncDocEntity.setTagId(localDocsEntity.getTagId());
                syncDocEntity.setTagName(localDocsEntity.getTagName());
                syncDocEntity.setDirty(localDocsEntity.isDirty());
                syncDocEntity.setNewAdd(localDocsEntity.isNewAdd());
                syncDocEntity.setBaseImagePath(localDocsEntity.getBaseImagePath());
                syncDocEntity.setBaseSmallImagePath(localDocsEntity.getBaseSmallImagePath());
                syncDocEntity.setOriginalImagePath(localDocsEntity.getOriginalImagePath());
                syncDocEntity.setProcessImagePath(localDocsEntity.getProcessImagePath());
                syncDocEntity.setProcessSmallImagePath(localDocsEntity.getProcessSmallImagePath());

                syncDocEntity.setEnhanceMode(localDocsEntity.getEnhanceMode());
                syncDocEntity.setFileType(localDocsEntity.getFileType());
                syncDocEntity.setUserID(userPreferences.getUserId());
                syncDocEntity.setBucket(localDocsEntity.getBucket());
                syncDocEntity.setCreateTime(formatDate(localDocsEntity.getCreateTime()));
                syncDocEntity.setDelete(localDocsEntity.isDelete());
                syncDocEntity.setUpdateTime(formatDate(localDocsEntity.getUpdateTime()));
                syncDocEntity.setUuid(localDocsEntity.getUuid());
                syncDocEntity.setSyncTime(serverTime);
                if (localDocsEntity.isDelete() == 0){
                    Log.d("chucunkongjian", "获取图片大小告诉服务器2 "+localDocsEntity.getFileSize());
                }
                syncDocEntity.setFileSize(localDocsEntity.getFileSize());
                realm.insertOrUpdate(syncDocEntity);
            }
            localDocsEntity.setDirty(0);
            if (localDocsEntity.isNewAdd() == 1) {
                localDocsEntity.setNewAdd(0);
            }
        }
        realm.commitTransaction();

        RealmResults<SyncDocEntity> syncPageEntities = realm.where(SyncDocEntity.class).findAll();
        syncPageList = realm.copyFromRealm(syncPageEntities);


        //Tags 移动并把数据表的数据isDirty 置为0
        RealmResults<TagEntity> localTagEntities = realm.where(TagEntity.class)
                .equalTo("isDirty", 1)
                .findAll();
        realm.beginTransaction();
        for (TagEntity localTagEntity : localTagEntities) {
            SyncTagEntity syncTagEntity = realm.createObject(SyncTagEntity.class);
            syncTagEntity.setTagId(localTagEntity.getTagId());
            syncTagEntity.setTagName(localTagEntity.getTagName());
            syncTagEntity.setCreateTime(formatDate(localTagEntity.getCreateTime()));
            syncTagEntity.setUpdateTime(formatDate(localTagEntity.getUpdateTime()));
            syncTagEntity.setDirty(localTagEntity.isDirty());
            syncTagEntity.setDelete(localTagEntity.isDelete());
            syncTagEntity.setSyncTime(serverTime);
            realm.insertOrUpdate(syncTagEntity);
            localTagEntity.setDirty(0);
        }
        realm.commitTransaction();
        RealmResults<SyncTagEntity> syncTagEntities = realm.where(SyncTagEntity.class).findAll();
        syncTagList = realm.copyFromRealm(syncTagEntities);

    }

    /**
     * step6 需要同步的数据拷贝到data/file/sync 文件夹下
     */
    private void copyToSyncDir() {
        if (FileUtils.createOrExistsDir(syncDir)) {
            for (SyncDocEntity syncDocEntity : syncPageList) {
                if (syncDocEntity.isDelete() == 1 || syncDocEntity.isDirty() == 2) {
                    continue;
                }
                String syncImgPath = syncDir + syncDocEntity.getFileID() + Constants.BASE_JPG;
                String syncOriginalImgPath = syncDir + syncDocEntity.getFileID() + Constants.PHOTO_JPG;

                if(userPreferences.isPushOriPhoto()){
                    //最长边压缩至800，图片质量 70%;保存到同步路径
                    BitmapUtils.getimage(syncDocEntity.getOriginalImagePath(),syncOriginalImgPath);
                }

                FileUtils.copy(syncDocEntity.getBaseImagePath(), syncImgPath, new FileUtils.OnReplaceListener() {
                    @Override
                    public boolean onReplace(File srcFile, File destFile) {
                        LogUtils.i("同步文件夹中图片被替换");
                        return true;
                    }
                });

            }
        }
    }

    /**
     * step8 检查云存储剩余空间是否足够
     */
    private boolean checkCloudIsEnough() {
        float newAddFileSize = 0;
        float deleteFileSize = 0;
        float changeFileSize = 0;
        float totalFileSize = 0;
        //1.本次新增文件大小总和
        RealmResults<SyncDocEntity> addDocEntities = realm.where(SyncDocEntity.class).equalTo("isDirty", 1).equalTo("isNewAdd", 1).findAll();
        if (addDocEntities.size() > 0) {
            for (SyncDocEntity addDocEntity : addDocEntities) {
                String syncImgPath = syncDir + addDocEntity.getFileID() + Constants.BASE_JPG;
                if (FileUtils.isFileExists(syncImgPath)) {
                    LogUtils.e("ccc", FileUtils.getFileLength(syncImgPath));
                    newAddFileSize += FileUtils.getFileLength(syncImgPath);
                }

            }
        }


        //2.本次删除文件大小总和

        RealmResults<SyncDocEntity> deletePageEntities = realm.where(SyncDocEntity.class).equalTo("isDirty", 1).equalTo("isDelete", 1).findAll();
        if (deletePageEntities.size() > 0) {
            FileSizeModel.BodyBean bodyBean = new FileSizeModel.BodyBean();
            List<FileSizeModel.BodyBean.KeyListBean> keyList = new ArrayList<>();
            FileSizeModel.BodyBean.KeyListBean keyListBean = new FileSizeModel.BodyBean.KeyListBean();
            for (SyncDocEntity deleteDocEntity : deletePageEntities) {
                keyListBean.setFileSize(deleteDocEntity.getFileSize() + "");
                keyListBean.setOssKey(deleteDocEntity.getFileID());
                keyList.add(keyListBean);
            }
            bodyBean.setKeyList(keyList);
            FileSizeModel.BodyBean deleteBean = getFileSize(bodyBean);
            if (deleteBean != null && deleteBean.getKeyList() != null) {
                if (Validator.isNotEmpty(deleteBean.getKeyList())) {
                    LogUtils.e(new Gson().toJson(deleteBean.getKeyList()));
                    for (FileSizeModel.BodyBean.KeyListBean deleteKeyBean : deleteBean.getKeyList()) {
                        if (Validator.isNotEmpty(deleteKeyBean.getFileSize()) && !deleteKeyBean.getFileSize().equals("null")) {
                            deleteFileSize -= Float.parseFloat(deleteKeyBean.getFileSize());
                        }
                    }

                }
            }

        }


        //3.本次修改文件大小总和

        RealmResults<SyncDocEntity> changePageEntities = realm.where(SyncDocEntity.class).equalTo("isDirty", 1).notEqualTo("isNewAdd", 1).equalTo("isDelete", 0).findAll();
        if (changePageEntities.size() > 0) {
            FileSizeModel.BodyBean bodyBean = new FileSizeModel.BodyBean();
            List<FileSizeModel.BodyBean.KeyListBean> keyList = new ArrayList<>();
            FileSizeModel.BodyBean.KeyListBean keyListBean = new FileSizeModel.BodyBean.KeyListBean();
            for (SyncDocEntity changeDocEntity : changePageEntities) {
                keyListBean.setFileSize(changeDocEntity.getFileSize() + "");
                keyListBean.setOssKey(changeDocEntity.getFileID());
                keyList.add(keyListBean);
            }
            bodyBean.setKeyList(keyList);
            FileSizeModel.BodyBean changeBean = getFileSize(bodyBean);
            for (FileSizeModel.BodyBean.KeyListBean changeKeyBean : changeBean.getKeyList()) {
                for (FileSizeModel.BodyBean.KeyListBean listBean : keyList) {
                    if (listBean.getOssKey().equals(changeKeyBean.getOssKey())) {
                        LogUtils.e("ccc", Float.parseFloat(listBean.getFileSize()), Float.parseFloat(changeKeyBean.getFileSize()));
                        changeFileSize += (Float.parseFloat(listBean.getFileSize()) - Float.parseFloat(changeKeyBean.getFileSize()));
                    }
                }
            }

        }

        totalFileSize = newAddFileSize + deleteFileSize + changeFileSize;
//        LogUtils.e("ccc", totalFileSize, userPreferences.getUsagesLimit() - userPreferences.getUsages());

        return totalFileSize > userPreferences.getUsagesLimit() - userPreferences.getUsages();
    }

    /**
     * step8 下载并且处理文件
     */
    private HashSet<String> downloadAndProcessFile() {
        RealmResults<DownloadEntity> downloadEntities = realm.where(DownloadEntity.class).findAll();
        HashSet<String> fileNoExistSet = new HashSet<>();
        for (DownloadEntity downloadEntity : downloadEntities) {
            // 检查文件是否在OSS上存在
            OSS oss = OSSInstance.Companion.getInstance().oss();
            try {
                if (oss == null) {
                    return fileNoExistSet;
                }
                boolean hasFileExist = oss.doesObjectExist(Constants.BUCKET,
                        userPreferences.getUserId() + File.separator + downloadEntity.getFileID() + "_" + downloadEntity.getUuid() + Constants.BASE_JPG);
                if (!hasFileExist) {
                    fileNoExistSet.add(downloadEntity.getFileID());
                    realm.beginTransaction();
                    downloadEntity.deleteFromRealm();
                    realm.commitTransaction();
                    continue;
                }
            } catch (ClientException | ServiceException e) {
                LogUtils.e(e);

                return fileNoExistSet;
            }

            if (!downloadEntity.isHasDownloadImage()) {
                boolean isDownloadImgSuccess = downloadFile(downloadEntity.getFileID(), downloadEntity.getUuid());
                LogUtils.i("isDownloadImgSuccess", isDownloadImgSuccess);
                if (isDownloadImgSuccess && downloadEntity.isValid()) {
                    realm.beginTransaction();
                    downloadEntity.setHasDownloadImage(true);
                    realm.commitTransaction();
                }
            }

            if (!downloadEntity.isHasMakeSmallImage() && downloadEntity.isHasDownloadImage()) {
                boolean isMakeSmallImgSuccess = makeColorAndSmallImg(downloadEntity);
                LogUtils.i("makeColorAndSmallImg", isMakeSmallImgSuccess);
                if (isMakeSmallImgSuccess && downloadEntity.isValid()) {
                    realm.beginTransaction();
                    downloadEntity.setHasMakeSmallImage(true);
                    realm.commitTransaction();
                }
            }

            if (downloadEntity.isHasDownloadImage() && downloadEntity.isHasMakeSmallImage()) {
                realm.beginTransaction();
                downloadEntity.deleteFromRealm();
                realm.commitTransaction();
            }

        }

        return fileNoExistSet;
    }

    /**
     * 下载文件
     */
    private boolean downloadFile(String fileIDUUid, String Uuid) {

        boolean isSuccess = false;
        if (!FileUtils.createOrExistsDir(dirPath)) {
            return false;
        }
        OSS ossClient = OSSInstance.Companion.getInstance().oss();
        if (ossClient == null) {
            return false;
        }

        String imgKey = userPreferences.getUserId() + File.separator + fileIDUUid + "_" + Uuid + Constants.BASE_JPG;
//构造下载文件请求
        GetObjectRequest get = new GetObjectRequest(Constants.BUCKET, imgKey);
//设置下载进度回调
//        get.setProgressListener(new OSSProgressCallback<GetObjectRequest>() {
//            @Override
//            public void onProgress(GetObjectRequest request, long currentSize, long totalSize) {
//                Log.d("xxx", "download_progress: " + currentSize + "  total_size: " + totalSize);
//
//            }
//        });
        try {
            // 同步执行下载请求，返回结果
            GetObjectResult getResult = ossClient.getObject(get);
//            LogUtils.d("Content-Length", "" + getResult.getContentLength());
            // 获取文件输入流
            InputStream inputStream = getResult.getObjectContent();
            byte[] buffer = new byte[2048];
            int len;
            String sourcePath = dirPath + fileIDUUid + Constants.BASE_JPG;
            FileOutputStream downloadImg = new FileOutputStream(sourcePath);
            while ((len = inputStream.read(buffer)) != -1) {
                // 处理下载的数据，比如图片展示或者写入文件等
                downloadImg.write(buffer, 0, len);
                downloadImg.flush();
            }
            downloadImg.close();
            inputStream.close();
            LogUtils.i("download success");
            isSuccess = true;

            // 下载后可以查看文件元信息
            ObjectMetadata metadata = getResult.getMetadata();
            Log.d("ContentType", metadata.getContentType());
        } catch (ClientException e) {
            LogUtils.i("download defeat" + e);
            // 本地异常如网络异常等
            e.printStackTrace();
        } catch (ServiceException e) {
            LogUtils.i("download defeat" + e);
            // 服务异常
            Log.e("RequestId", e.getRequestId());
            Log.e("ErrorCode", e.getErrorCode());
            Log.e("HostId", e.getHostId());
            Log.e("RawMessage", e.getRawMessage());
        } catch (IOException e) {
            LogUtils.i("download defeat" + e);
            e.printStackTrace();
        } catch (Exception e) {
            LogUtils.e("unknown exception" + e);
            return false;
        }
        return isSuccess;
    }


    /**
     * 压缩并且保存小图
     */
    private boolean makeColorAndSmallImg(DownloadEntity downloadEntity) {
        String fileIDUUid = downloadEntity.getFileID();
        if (FileUtils.createOrExistsDir(dirPath)) {
            String basePath = dirPath + fileIDUUid + Constants.BASE_JPG;
            String smallBasePath = dirPath + fileIDUUid + Constants.BASE_SMALL_JPG;
            String colorPath = dirPath + fileIDUUid + Constants.JPG;
            String smallColorPath = dirPath + fileIDUUid + Constants.SMALL_JPG;
            Args args = new Args();
            args.setScanType(CZSaoMiao_Alg.ScanType.NOTHING);
            args.setColorType(getColorType(downloadEntity));
            LogUtils.e(args.getColorType(), downloadEntity.getEnhanceMode(), downloadEntity.getFileType());
            CZSaoMiao_Alg.getInstance(this).miao_static_process(basePath, colorPath, smallBasePath, smallColorPath, args);
            return true;
        }
        return false;
    }

    private CZSaoMiao_Alg.ColorType getColorType(DownloadEntity downloadEntity) {
        switch (downloadEntity.getEnhanceMode()) {
            case 0:
                if (downloadEntity.getFileType() == 2) {
                    return CZSaoMiao_Alg.ColorType.IDCARD_DEFAULT;
                } else if (downloadEntity.getFileType() == 3) {
                    return CZSaoMiao_Alg.ColorType.ENTERPRISE_DOC_DEFAULT;
                } else {
                    return CZSaoMiao_Alg.ColorType.AUTO;
                }
            case 1:
                if (downloadEntity.getFileType() == 2) {
                    return CZSaoMiao_Alg.ColorType.IDCARD_GRAY;
                } else if (downloadEntity.getFileType() == 3) {
                    return CZSaoMiao_Alg.ColorType.ENTERPRISE_DOC_GRAY;
                } else {
                    return CZSaoMiao_Alg.ColorType.BW;
                }
            case 2:
                return CZSaoMiao_Alg.ColorType.BG_WHITEN;
            case 3:
                return CZSaoMiao_Alg.ColorType.NO_TRANS;
            default:
                return CZSaoMiao_Alg.ColorType.NO_TRANS;
        }
    }

    /**
     * 是否所有的图片下载并且压缩小图成功
     */
    private boolean isAllDownloadAndMakeSmallImg() {
        RealmResults<DownloadEntity> downloadEntities = realm.where(DownloadEntity.class).findAll();
        return !Validator.isNotEmpty(downloadEntities);
    }

    /**
     * step9 检查并更新需要同步表数据
     */
    private void checkAndUpdateSyncData(List<SyncEntity.ScanProCategoryBean> categoryList, List<SyncEntity.ScanProTagBean> tagsList) {
        realm.beginTransaction();
        for (SyncDocEntity syncDocEntity : syncPageList) {
            boolean isExist = false;
            for (SyncEntity.ScanProCategoryBean notesBean : categoryList) {
                if (notesBean.getGroupId().equals(syncDocEntity.getCategoryID())) {
                    if (notesBean.getIsDelete()) {
                        isExist = true;
                    }
                }
            }
            if ((syncDocEntity.isDelete() == 0) && isExist) {
                syncDocEntity.setDelete(1);
            }

            boolean isTagExist = false;
            for (SyncEntity.ScanProTagBean tagsBean : tagsList) {
                if (tagsBean.getTagId().equals(syncDocEntity.getTagId())) {
                    if (tagsBean.getIsDelete()) {
                        isTagExist = true;
                    }
                }
            }
            if (isTagExist) {
                syncDocEntity.setTagName("");
                syncDocEntity.setTagId("");
            }

        }
        realm.commitTransaction();

    }


    /**
     * step10 检查是否上传文件 或者已删除  并且删除同步文件夹的文件
     */
    private void checkIsUploadedAndDeleteFilesInSyncDir() {
        RealmResults<SyncDocEntity> syncDocEntities = realm.where(SyncDocEntity.class).findAll();
        for (SyncDocEntity syncDocEntity : syncDocEntities) {
            if (!syncDocEntity.getHasUploadBaseImage()) {
                if (syncDocEntity.isDelete() == 1 || syncDocEntity.isDirty() == 2) {
                    setUploadFlagAndDeleteFile(syncDocEntity, false);
                } else {
                    boolean uploadResult = uploadFiles(syncDocEntity.getFileID(), syncDocEntity.getUuid());
                    if (uploadResult) {
                        setUploadFlagAndDeleteFile(syncDocEntity, true);
                    }
                }
            }
        }
    }

    private void setUploadFlagAndDeleteFile(SyncDocEntity syncDocEntity, boolean needDeleteFile) {
        if (syncDocEntity.isValid()) {
            realm.beginTransaction();
            syncDocEntity.setHasUploadBaseImage(true);
            realm.commitTransaction();
            if (needDeleteFile) {
                String syncImgPath = syncDir  + syncDocEntity.getFileID() + Constants.BASE_JPG;
                String syncImgPath2 = syncDir  + syncDocEntity.getFileID() + Constants.PHOTO_JPG;
                FileUtils.delete(syncImgPath);
                FileUtils.delete(syncImgPath2);
            }
        }

    }

    /**
     * 上传文件
     */
    private boolean uploadFiles(String fileIDUUid, String Uuid) {
        boolean isUploadSuccess = false;
        String sourcePath = syncDir + fileIDUUid + Constants.BASE_JPG;
        String ossPath = userPreferences.getUserId() + File.separator + fileIDUUid + "_" + Uuid + Constants.BASE_JPG;

        String OriginalsourcePath = syncDir + fileIDUUid + Constants.PHOTO_JPG;
        String OriginalossPath = userPreferences.getUserId() + File.separator + fileIDUUid + "_" + Uuid + Constants.PHOTO_JPG;
        LogUtils.i("upload", sourcePath, ossPath);
        OSS ossClient = OSSInstance.Companion.getInstance().oss();
        if (ossClient == null) {
            return false;
        }
        if(userPreferences.isPushOriPhoto()){
            PutObjectRequest put = new PutObjectRequest(Constants.BUCKET, OriginalossPath, OriginalsourcePath);
            try {
                PutObjectResult putResult = ossClient.putObject(put);
                isUploadSuccess = true;
                LogUtils.i("PutObject: UploadSuccess", "ETag :" + putResult.getETag(), "RequestId: " + putResult.getRequestId());
            } catch (ClientException e) {
                // 本地异常如网络异常等
                e.printStackTrace();
            } catch (ServiceException e) {
                // 服务异常
                Log.e("RequestId", e.getRequestId());
                Log.e("ErrorCode", e.getErrorCode());
                Log.e("HostId", e.getHostId());
                Log.e("RawMessage", e.getRawMessage());
            }
        }
        // 构造上传请求
        PutObjectRequest put = new PutObjectRequest(Constants.BUCKET, ossPath, sourcePath);

        try {
            PutObjectResult putResult = ossClient.putObject(put);
            isUploadSuccess = true;
            LogUtils.i("PutObject: UploadSuccess", "ETag :" + putResult.getETag(), "RequestId: " + putResult.getRequestId());
        } catch (ClientException e) {
            // 本地异常如网络异常等
            e.printStackTrace();
        } catch (ServiceException e) {
            // 服务异常
            Log.e("RequestId", e.getRequestId());
            Log.e("ErrorCode", e.getErrorCode());
            Log.e("HostId", e.getHostId());
            Log.e("RawMessage", e.getRawMessage());
        }
        return isUploadSuccess;
    }

    /**
     * 是否所有的图片下载并且压缩小图成功
     */
    private boolean isAllPagesUpload() {
        boolean isAllUpload = true;
        RealmResults<SyncDocEntity> syncPageEntities = realm.where(SyncDocEntity.class).findAll();
        for (SyncDocEntity syncDocEntity : syncPageEntities) {
            if (!syncDocEntity.getHasUploadBaseImage()) {
                LogUtils.d(syncDocEntity.getCategoryID());
                isAllUpload = false;
            }
        }

        return isAllUpload;

    }

    /**
     * stepX 上传同步表中的数据
     */
    private boolean syncDataToServer(SyncEntity syncEntity) {
        String syncJson = new Gson().toJson(syncEntity);
        LogUtils.json("同步数据", syncEntity);
        try {
            RequestBody requestBody = RequestBody.create(JSON, syncJson);
            Request request = new Request.Builder()
                    .header("udid", userPreferences.getIMEI())
                    .header("App-Key", Constants.SCAN_PRO)
                    .header("T-ID", userPreferences.getToken())
                    .header("U-ID", userPreferences.getUserId())
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.PHASE.getServiceUrl() + Constants.SYNC_URL)
                    .post(requestBody)
                    .build();
            LogUtils.d("header", request.headers());
            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();
            String responseString = response.body().string();
            LogUtils.d("同步请求返回", responseString);
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    LogUtils.i("上传数据库至服务器 成功", new Gson().toJson(syncEntity));
                    return true;
                } else {
                    LogUtils.i("上传数据库至服务器 失败", new Gson().toJson(syncEntity));
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            LogUtils.e(e);
            return false;
        }
    }

    /**
     * 把同步表的数据转换成上传给服务器的List
     */
    private SyncEntity transFormRealmListToSyncList(List<SyncCategoryEntity> syncCategoryList, List<SyncDocEntity> syncPageList, List<SyncTagEntity> syncTagList) {
        List<SyncEntity.ScanProCategoryBean> categoryBeen = new ArrayList<>();
        List<SyncEntity.ScanProDocBean> docsBeen = new ArrayList<>();
        List<SyncEntity.ScanProTagBean> tagsBeen = new ArrayList<>();

        for (SyncCategoryEntity syncCategoryEntity : syncCategoryList) {
            SyncEntity.ScanProCategoryBean categoryBean = new SyncEntity.ScanProCategoryBean();
            categoryBean.setGroupId(syncCategoryEntity.getCategoryID());
            if (Validator.isNotEmpty(userPreferences.getUserId())) {
                categoryBean.setUserId(Integer.parseInt(userPreferences.getUserId()));
            }
            categoryBean.setGroupName(syncCategoryEntity.getCategoryName());
            categoryBean.setIsDelete(syncCategoryEntity.isDelete() == 1);
            categoryBean.setCreateTime(formatDate(syncCategoryEntity.getCreateTime()));
            categoryBean.setUpdateTime(formatDate(syncCategoryEntity.getUpdateTime()));
            categoryBean.setSynchronousTime(syncCategoryEntity.getSyncTime());
            categoryBeen.add(categoryBean);
        }
        for (SyncDocEntity syncDocEntity : syncPageList) {
            SyncEntity.ScanProDocBean docBean = new SyncEntity.ScanProDocBean();
            docBean.setFileId(syncDocEntity.getFileID());
            docBean.setEnhanceMode(syncDocEntity.getEnhanceMode());
            docBean.setFileType(syncDocEntity.getFileType());
            if (Validator.isNotEmpty(userPreferences.getUserId())) {
                docBean.setUserId(Integer.parseInt(userPreferences.getUserId()));
            }
            docBean.setTagId(syncDocEntity.getTagId());
            docBean.setTagName(syncDocEntity.getTagName());
            docBean.setGroupClientId(syncDocEntity.getCategoryID());
            docBean.setGroupName(syncDocEntity.getCategoryName());
            docBean.setUuid(syncDocEntity.getUuid());
            docBean.setOssBucket(Constants.BUCKET);
            docBean.setDelete(syncDocEntity.isDelete() == 1);

            docBean.setCreateTime(formatDate(syncDocEntity.getCreateTime()));
            docBean.setUpdateTime(formatDate(syncDocEntity.getUpdateTime()));
            docBean.setSynchronousTime(syncDocEntity.getSyncTime());
            if (Validator.isNotEmpty(syncDocEntity.getFileSize())) {
                if (syncDocEntity.isDelete() == 0){
                    Log.d("chucunkongjian", "获取图片大小告诉服务器3 "+syncDocEntity.getFileSize());
                }
                docBean.setFileSize(Integer.parseInt(syncDocEntity.getFileSize()));
            }
            docsBeen.add(docBean);
        }

        for (SyncTagEntity syncTagEntity : syncTagList) {
            SyncEntity.ScanProTagBean tagsBean = new SyncEntity.ScanProTagBean();
            tagsBean.setTagId(syncTagEntity.getTagId());
            tagsBean.setTagName(syncTagEntity.getTagName());
            tagsBean.setUserId(Integer.parseInt(userPreferences.getUserId()));
            tagsBean.setCreateTime(formatDate(syncTagEntity.getCreateTime()));
            tagsBean.setUpdateTime(formatDate(syncTagEntity.getUpdateTime()));
            tagsBean.setIsDelete(syncTagEntity.isDelete() == 1);
            tagsBean.setSynchronousTime(syncTagEntity.getSyncTime());
            tagsBeen.add(tagsBean);
        }
        SyncEntity syncEntity = new SyncEntity();
        if (categoryBeen.size() > 0) {
            syncEntity.setAppFileGroupList(categoryBeen);
        }
        if (docsBeen.size() > 0) {
            syncEntity.setAppScanFileList(docsBeen);
        }
        if (tagsBeen.size() > 0) {
            syncEntity.setAppFileTagList(tagsBeen);
        }
        return syncEntity;
    }

    private String formatDate(String date) {
        if (!TextUtils.isEmpty(date) && !date.contains(".")) {
            return date + ".000";
        } else {
            return date;
        }

    }

    private void checkMergeData(List<SyncEntity.ScanProCategoryBean> categoryList, List<SyncEntity.ScanProDocBean> docList,
                                List<SyncEntity.ScanProTagBean> tagsList, HashSet<String> fileNoExistSet) {
        // 检查文件
        ArrayList<SyncEntity.ScanProDocBean> needRemoveDocList = new ArrayList<>();
        for (SyncEntity.ScanProDocBean doc : docList) {
            SyncDocEntity queryResult = realm.where(SyncDocEntity.class).equalTo("fileID", doc.getFileId()).findFirst();
            // 推数据中存在当前文件 || 当前文件OSS中不存在，从增量数据中删除
            if (Validator.isNotEmpty(queryResult) || fileNoExistSet.contains(doc.getFileId())) {
                needRemoveDocList.add(doc);
            }
        }
        docList.removeAll(needRemoveDocList);
        needRemoveDocList = new ArrayList<>();

        // 检查文档
        ArrayList<SyncEntity.ScanProCategoryBean> needRemoveCategoryList = new ArrayList<>();
        for (SyncEntity.ScanProCategoryBean category : categoryList) {
            SyncCategoryEntity queryResult = realm.where(SyncCategoryEntity.class).equalTo("categoryID", category.getGroupId()).findFirst();
            // 推数据中存在当前文档，从增量数据中删除
            if (Validator.isNotEmpty(queryResult)) {
                needRemoveCategoryList.add(category);

                // 如果删除的文档为“已删除”状态，检查所有增量数据中的文件，如果有同GroupID的，从增量数据中删除
                if (queryResult.isDelete() == 1) {
                    for (SyncEntity.ScanProDocBean doc : docList) {
                        if (doc.getGroupClientId().equals(queryResult.getCategoryID())) {
                            needRemoveDocList.add(doc);
                        }
                    }
                }
            }
        }
        categoryList.removeAll(needRemoveCategoryList);
        docList.removeAll(needRemoveDocList);

        // 检查标签
        ArrayList<SyncEntity.ScanProTagBean> needRemoveTagList = new ArrayList<>();
        for (SyncEntity.ScanProTagBean tag : tagsList) {
            SyncTagEntity queryResult = realm.where(SyncTagEntity.class).equalTo("tagId", tag.getTagId()).findFirst();
            // 推数据中存在当前标签，从增量数据中删除
            if (Validator.isNotEmpty(queryResult)) {
                needRemoveTagList.add(tag);

                // 如果删除的标签为“已删除”状态，检查所有增量数据中的文件，如果TagID相同，修改增量数据文件为无标签装
                if (queryResult.isDelete() == 1) {
                    for (SyncEntity.ScanProDocBean doc : docList) {
                        if (doc.getTagId().equals(queryResult.getTagId())) {
                            realm.beginTransaction();
                            doc.setTagId("");
                            doc.setTagName("");
                            realm.commitTransaction();
                        }
                    }
                }
            }
        }
        tagsList.removeAll(needRemoveTagList);

    }

    /**
     * step12 合并books数据
     */
    private boolean mergeCategory(List<SyncEntity.ScanProCategoryBean> categoryList) {
        boolean needNotifyDelete = false;
        if (Validator.isNotEmpty(categoryList)) {
            for (SyncEntity.ScanProCategoryBean categoryBean : categoryList) {
                CategoryEntity categoryEntity = realm.where(CategoryEntity.class)
                        .equalTo("categoryID", categoryBean.getGroupId())
                        .findFirst();
                if (Validator.isNotEmpty(categoryEntity)) {
                    if (categoryEntity.isDirty() == 0) {
                        realm.beginTransaction();
                        categoryEntity.setCategoryName(categoryBean.getGroupName());
                        categoryEntity.setDelete(categoryBean.getIsDelete() ? 1 : 0);
                        categoryEntity.setUpdateTime(categoryBean.getUpdateTime());
                        realm.commitTransaction();
                        if (categoryBean.getIsDelete()) {
                            needNotifyDelete = true;
                            realm.beginTransaction();
                            RealmResults<DocEntity> docEntities = realm.where(DocEntity.class)
                                    .equalTo("categoryID", categoryBean.getGroupId())
                                    .findAll();
                            for (DocEntity docEntity : docEntities) {
                                docEntity.setDirty(0);
                                docEntity.setDelete(1);
                            }
                            realm.commitTransaction();
                        }
                    }
                } else {
                    realm.beginTransaction();
                    CategoryEntity newCategoryEntity = realm.createObject(CategoryEntity.class, categoryBean.getGroupId());
                    newCategoryEntity.setCategoryName(categoryBean.getGroupName());
                    newCategoryEntity.setDirty(0);
                    newCategoryEntity.setUserID(categoryBean.getUserId() + "");
                    newCategoryEntity.setDelete(categoryBean.getIsDelete() ? 1 : 0);
                    newCategoryEntity.setUpdateTime(categoryBean.getUpdateTime());
                    newCategoryEntity.setCreateTime(categoryBean.getCreateTime());
                    realm.commitTransaction();
                }

            }

        }
        return needNotifyDelete;
    }

    /**
     * step12 合并pages数据
     */
    private boolean mergeDocs(List<SyncEntity.ScanProDocBean> docList) {
        boolean needNotifyDelete = false;

        if (Validator.isNotEmpty(docList)) {
            LogUtils.e("pageList not empty 需要新建或者更新页");
            for (SyncEntity.ScanProDocBean docBean : docList) {
                DocEntity docEntity = realm.where(DocEntity.class)
                        .equalTo("fileID", docBean.getFileId())
                        .findFirst();

                if (Validator.isNotEmpty(docEntity)) {
                    LogUtils.e("更新页", docEntity.isDirty());
                    if (docEntity.isDirty() == 0) {
                        realm.beginTransaction();
                        createOrUpdateRealmPage(docEntity, docBean, realm);
                        if (docBean.isDelete()) {
                            needNotifyDelete = true;
                        }
                        realm.commitTransaction();
                    }
                } else {
                    LogUtils.e("新建页");
                    realm.beginTransaction();
                    DocEntity newDocEntity = realm.createObject(DocEntity.class, docBean.getFileId());
                    createOrUpdateRealmPage(newDocEntity, docBean, realm);
                    realm.commitTransaction();
                }
            }

        }
        return needNotifyDelete;
    }

    /**
     * step12 合并tags数据
     */
    private boolean mergeTags(List<SyncEntity.ScanProTagBean> tagsList) {
        boolean needNotifyDelete = false;
        if (Validator.isNotEmpty(tagsList)) {
            for (SyncEntity.ScanProTagBean tagsBean : tagsList) {
                TagEntity tagEntity = realm.where(TagEntity.class)
                        .equalTo("tagId", tagsBean.getTagId())
                        .findFirst();
                if (Validator.isNotEmpty(tagEntity)) {
                    if (tagEntity.isDirty() == 0) {
                        realm.beginTransaction();
                        tagEntity.setTagName(tagsBean.getTagName());
                        tagEntity.setUpdateTime(formatDate(tagsBean.getUpdateTime()));
                        tagEntity.setDelete(tagsBean.getIsDelete() ? 1 : 0);
                        realm.commitTransaction();
                        if (tagsBean.getIsDelete()) {
                            needNotifyDelete = true;
                            realm.beginTransaction();
                            RealmResults<DocEntity> docEntities = realm.where(DocEntity.class)
                                    .equalTo("tagId", tagsBean.getTagId())
                                    .findAll();
                            for (DocEntity docEntity : docEntities) {
                                docEntity.setTagName("");
                                docEntity.setTagId("");
                            }
                            realm.commitTransaction();
                        }
                    }
                } else {
                    realm.beginTransaction();
                    TagEntity newTagEntity = realm.createObject(TagEntity.class, tagsBean.getTagId());
                    newTagEntity.setTagName(tagsBean.getTagName());
                    newTagEntity.setDirty(0);
                    newTagEntity.setUserID(tagsBean.getUserId() + "");
                    newTagEntity.setDelete(tagsBean.getIsDelete() ? 1 : 0);
                    newTagEntity.setUpdateTime(formatDate(tagsBean.getUpdateTime()));
                    newTagEntity.setCreateTime(formatDate(tagsBean.getCreateTime()));
                    realm.commitTransaction();
                }
            }
        }
        return needNotifyDelete;
    }


    /**
     * 新建或者更新数据库书页
     */
    private void createOrUpdateRealmPage(DocEntity docEntity, SyncEntity.ScanProDocBean docBean, Realm realm) {
        docEntity.setCategoryID(docBean.getGroupClientId());
        docEntity.setCategoryName(docBean.getGroupName());
        docEntity.setTagId(docBean.getTagId());
        docEntity.setTagName(docBean.getTagName());
        docEntity.setBucket(docBean.getOssBucket());
        docEntity.setUuid(docBean.getUuid());

        docEntity.setEnhanceMode(docBean.getEnhanceMode());
        docEntity.setFileType(docBean.getFileType());
        docEntity.setBaseImagePath(dirPath + docBean.getFileId() + Constants.BASE_JPG);
        docEntity.setBaseSmallImagePath(dirPath + docBean.getFileId() + Constants.BASE_SMALL_JPG);
        docEntity.setProcessImagePath(dirPath + docBean.getFileId() + Constants.JPG);
        docEntity.setProcessSmallImagePath(dirPath + docBean.getFileId() + Constants.SMALL_JPG);

        docEntity.setDelete(docBean.isDelete() ? 1 : 0);
        docEntity.setDirty(0);
        docEntity.setCreateTime(formatDate(docBean.getCreateTime()));
        //脏数据兼容
        docEntity.setUpdateTime(formatDate(docBean.getUpdateTime()));
        docEntity.setUserID(docBean.getUserId() + "");

        docEntity.setTemp(0);
        docEntity.setFileSize(docBean.getFileSize() + "");
        docEntity.setTakePhotoTime(Constants.TAKE_PHOTO_INIT_TIME);


    }


    /**
     * 获取FileSize信息
     */
    private FileSizeModel.BodyBean getFileSize(FileSizeModel.BodyBean fileSizeModel) {
        String request = new Gson().toJson(fileSizeModel);
        try {
            RequestBody requestBody = RequestBody.create(JSON, request);
            Request httpRequest = new Request.Builder()
                    .header("udid", userPreferences.getIMEI())
                    .header("App-Key", Constants.SCAN_PRO)
                    .header("T-ID", userPreferences.getToken())
                    .header("U-ID", userPreferences.getUserId())
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.PHASE.getServiceUrl() + Constants.FILE_SIZE_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(httpRequest).execute();
            String responseString = response.body().string();
            FileSizeModel baseModel = new Gson().fromJson(responseString, FileSizeModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    LogUtils.i("获取文件大小 成功", new Gson().toJson(baseModel.getBody()));
                    return baseModel.getBody();
                } else {
                    LogUtils.i("获取文件大小 失败");
                    return null;
                }
            } else {
                LogUtils.i("获取文件大小 失败");
                return null;
            }
        } catch (Exception e) {
            LogUtils.e(e);
            return null;
        }
    }

    /**
     * 获取用户信息
     */
    private void getUserInfo() {
        try {
            MiaoHttpEntity<UserAllInfoModel> userInfo = httpManager.request().userAllInfoSync(
                    userPreferences.getUserId(), UserAllInfoModel.class);
            if (userInfo.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.setUsages(userInfo.getBody().getUsages());
                userPreferences.setUsagesLimit(userInfo.getBody().getUsagesLimit());
            } else {
                syncStop(beginTime);
            }
        } catch (Exception e) {
            LogUtils.e(e);
            syncStop(beginTime);
            e.printStackTrace();
        }
    }

    /**
     * 结束线程池并且停止service 发送同步结束EventBus
     */
    private void syncFinish(long beginTime) {
        stopSync(beginTime);
        EventBus.getDefault().postSticky(new SyncFinishEvent(EventType.SYNC_IS_FINISH));
        EventBus.getDefault().post(new SyncFinishEvent(EventType.SYNC_FINISH_FRESH));
        EventBus.getDefault().post(new SyncFinishEvent(EventType.SYNC_ANIM_FINISH));
    }

    /**
     * 结束线程池并且停止service 发送中断Eventbus
     */
    private void syncStop(long beginTime) {
        stopSync(beginTime);
        EventBus.getDefault().postSticky(new NoticeServiceIsStopEvent(EventType.SYNC_IS_STOP));
    }

    /**
     * 停止Service
     */
    private void stopSync(long beginTime) {
        ThreadUtils.cancel(syncTask);
        EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));
        long runTime = System.currentTimeMillis() - beginTime;
        if (runTime <= 2000) {
            try {
                Thread.sleep(2000 - runTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }


        if (ServiceUtils.isServiceRunning(SyncService.class)) {
            LogUtils.i("service  stopped  by self");
            stopSelf();
            ServiceUtils.stopService(SyncService.class);
        }
    }

    private void initNotification() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            // 提高进程优先级 ，就会在通知栏中出现自己的应用，如果不想提高优先级，可以把这个注释
            // 参数1：id 参数2：通知
            String channelId = "com.czur.cloud";
            String channelName = "Channel CZUR";
            NotificationChannel notificationChannel = null;

            notificationChannel = new NotificationChannel(channelId,
                    channelName, NotificationManager.IMPORTANCE_HIGH);
            notificationChannel.setShowBadge(true);
            notificationChannel.enableVibration(false);
            notificationChannel.setSound(null, null);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            Notification notification = new NotificationCompat.Builder(this)
                    .setChannelId(channelId)
                    .setContentTitle(getString(R.string.sync_title))
                    .setWhen(System.currentTimeMillis())
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .build();
            int messageId = 1;
            startForeground(messageId, notification);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            stopForeground(true);
        }
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }


    public void showMessage(int resId) {
        ToastUtils.showShort(resId);
    }

    public void showMessage(String text) {
        ToastUtils.showShort(text);
    }
}

