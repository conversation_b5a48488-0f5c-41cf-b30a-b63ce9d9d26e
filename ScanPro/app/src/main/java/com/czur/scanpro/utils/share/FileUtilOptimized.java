package com.czur.scanpro.utils.share;

import android.annotation.SuppressLint;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import androidx.core.content.FileProvider;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Android 16优化版本的文件工具类
 * 完全去除反射，使用FileProvider和MediaStore API
 * Created by Yz on 2018/4/26
 * Email：<EMAIL>
 */
public class FileUtilOptimized {

    private static final String TAG = "Share";

    /**
     * 获取文件URI - Android 16优化版本
     *
     * @param context          上下文
     * @param shareContentType 分享内容类型 {@link ShareContentType}
     * @param file             文件
     * @return Uri
     */
    public static Uri getFileUri(Context context, @ShareContentType String shareContentType, File file) {
        if (context == null) {
            Log.e(TAG, "getFileUri current activity is null.");
            return null;
        }

        if (file == null || !file.exists()) {
            Log.e(TAG, "getFileUri file is null or not exists.");
            return null;
        }

        Uri uri = null;

        // Android 7.0+ 优先使用FileProvider
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uri = getFileProviderUri(context, file);
        }

        // 如果FileProvider失败，使用MediaStore
        if (uri == null) {
            if (TextUtils.isEmpty(shareContentType)) {
                shareContentType = ShareContentType.FILE;
            }

            switch (shareContentType) {
                case ShareContentType.IMAGE:
                    uri = getImageContentUri(context, file);
                    break;
                case ShareContentType.VIDEO:
                    uri = getVideoContentUri(context, file);
                    break;
                case ShareContentType.AUDIO:
                    uri = getAudioContentUri(context, file);
                    break;
                case ShareContentType.FILE:
                    uri = getFileContentUri(context, file);
                    break;
                default:
                    break;
            }
        }

        // 最后的备用方案：直接使用file://（仅限Android 6.0及以下）
        if (uri == null && Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            uri = Uri.fromFile(file);
        }

        return uri;
    }

    /**
     * 获取多个文件的URI
     *
     * @param context 上下文
     * @param files   文件列表
     * @return URI列表
     */
    public static ArrayList<Uri> getFileUris(Context context, List<File> files) {
        if (context == null || files == null || files.isEmpty()) {
            return new ArrayList<>();
        }

        ArrayList<Uri> uris = new ArrayList<>();
        
        // Android 7.0+ 优先使用FileProvider
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uris = getFileProviderUris(context, files);
        }
        
        // 如果FileProvider失败，使用MediaStore
        if (uris.isEmpty()) {
            uris = getFileContentUris(context, files);
        }

        return uris;
    }

    /**
     * 分享图片 - 原始方法保持兼容性
     */
    public static void originalShareImage(Context context, ArrayList<File> files) {
        ArrayList<Uri> imageUris = getFileUris(context, files);
        if (imageUris.isEmpty()) {
            Log.e(TAG, "No valid URIs found for sharing");
            return;
        }

        Intent share_intent = new Intent();
        share_intent.setAction(Intent.ACTION_SEND_MULTIPLE);
        share_intent.setType("image/*");
        share_intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
        share_intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, imageUris);
        context.startActivity(Intent.createChooser(share_intent, "Share"));
    }

    /**
     * 获取FileProvider URI - Android 16优化版本
     *
     * @param context 上下文
     * @param file    文件
     * @return FileProvider URI
     */
    private static Uri getFileProviderUri(Context context, File file) {
        try {
            String authority = context.getPackageName() + ".fileprovider";
            return FileProvider.getUriForFile(context, authority, file);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get FileProvider URI: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取多个FileProvider URI
     *
     * @param context 上下文
     * @param files   文件列表
     * @return FileProvider URI列表
     */
    private static ArrayList<Uri> getFileProviderUris(Context context, List<File> files) {
        ArrayList<Uri> uris = new ArrayList<>();
        String authority = context.getPackageName() + ".fileprovider";
        
        for (File file : files) {
            try {
                Uri uri = FileProvider.getUriForFile(context, authority, file);
                uris.add(uri);
            } catch (Exception e) {
                Log.e(TAG, "Failed to get FileProvider URI for: " + file.getName());
            }
        }
        return uris;
    }

    /**
     * 通过MediaStore获取文件URI
     *
     * @param context 上下文
     * @param file    文件
     * @return MediaStore URI
     */
    private static Uri getFileContentUri(Context context, File file) {
        String volumeName = "external";
        String filePath = file.getAbsolutePath();
        String[] projection = new String[]{MediaStore.Files.FileColumns._ID};
        Uri uri = null;

        Cursor cursor = context.getContentResolver().query(
            MediaStore.Files.getContentUri(volumeName), 
            projection,
            MediaStore.Files.FileColumns.DATA + "=?", 
            new String[]{filePath}, 
            null);
            
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    int columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID);
                    long id = cursor.getLong(columnIndex);
                    uri = MediaStore.Files.getContentUri(volumeName, id);
                }
            } finally {
                cursor.close();
            }
        }

        return uri;
    }

    /**
     * 获取多个文件的MediaStore URI
     *
     * @param context 上下文
     * @param files   文件列表
     * @return MediaStore URI列表
     */
    private static ArrayList<Uri> getFileContentUris(Context context, List<File> files) {
        ArrayList<Uri> uris = new ArrayList<>();
        
        for (File file : files) {
            Uri uri = getFileContentUri(context, file);
            if (uri != null) {
                uris.add(uri);
            }
        }
        
        return uris;
    }

    /**
     * 获取图片的MediaStore URI
     *
     * @param context   上下文
     * @param imageFile 图片文件
     * @return 图片URI
     */
    private static Uri getImageContentUri(Context context, File imageFile) {
        String filePath = imageFile.getAbsolutePath();
        Cursor cursor = context.getContentResolver().query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            new String[]{MediaStore.Images.Media._ID}, 
            MediaStore.Images.Media.DATA + "=?",
            new String[]{filePath}, 
            null);
            
        Uri uri = null;

        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    int columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID);
                    long id = cursor.getLong(columnIndex);
                    uri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id);
                }
            } finally {
                cursor.close();
            }
        }

        if (uri == null) {
            ContentValues values = new ContentValues();
            values.put(MediaStore.Images.Media.DATA, filePath);
            uri = context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
        }

        return uri;
    }

    /**
     * 获取视频的MediaStore URI
     *
     * @param context   上下文
     * @param videoFile 视频文件
     * @return 视频URI
     */
    private static Uri getVideoContentUri(Context context, File videoFile) {
        String filePath = videoFile.getAbsolutePath();
        Cursor cursor = context.getContentResolver().query(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            new String[]{MediaStore.Video.Media._ID}, 
            MediaStore.Video.Media.DATA + "=?",
            new String[]{filePath}, 
            null);

        Uri uri = null;
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    int columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media._ID);
                    long id = cursor.getLong(columnIndex);
                    uri = ContentUris.withAppendedId(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, id);
                }
            } finally {
                cursor.close();
            }
        }

        if (uri == null) {
            ContentValues values = new ContentValues();
            values.put(MediaStore.Video.Media.DATA, filePath);
            uri = context.getContentResolver().insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, values);
        }

        return uri;
    }

    /**
     * 获取音频的MediaStore URI
     *
     * @param context   上下文
     * @param audioFile 音频文件
     * @return 音频URI
     */
    private static Uri getAudioContentUri(Context context, File audioFile) {
        String filePath = audioFile.getAbsolutePath();
        Cursor cursor = context.getContentResolver().query(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
            new String[]{MediaStore.Audio.Media._ID}, 
            MediaStore.Audio.Media.DATA + "=?",
            new String[]{filePath}, 
            null);
            
        Uri uri = null;
        if (cursor != null) {
            try {
                if (cursor.moveToFirst()) {
                    int columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media._ID);
                    long id = cursor.getLong(columnIndex);
                    uri = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id);
                }
            } finally {
                cursor.close();
            }
        }

        if (uri == null) {
            ContentValues values = new ContentValues();
            values.put(MediaStore.Audio.Media.DATA, filePath);
            uri = context.getContentResolver().insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, values);
        }

        return uri;
    }
}
