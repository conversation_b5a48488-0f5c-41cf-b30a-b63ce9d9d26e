package com.czur.scanpro.entity.model;

public class UserAllInfoModel {
    private int id;
    private String name;
    private String mobile;
    private String email;
    private String photo;
    private String photoOssKey;
    private boolean isVip;
    private String invitationCode;
    private boolean isInvitate;
    private String vipStartOn;
    private String vipEndOn;
    private String svipEndOn;
    private int funcDefId;
    private String appleExpireDate;
    private String userType;
    private int totalOcr;
    private int totalPdf;
    private int totalHandwriting;
    private int totalCloudocr;
    private long usagesLimit;
    private int remainingOcr;
    private int remainingPdf;
    private int remainingHandwriting;
    private int remainingHighCertificate;
    private int totalCertificate;
    private int remainingHandwritingNormal;
    private int remainingCloudocrVip;
    private int remainingCloudocrNormal;
    private long usages;
    private int remainingVip;
    private int invitationCount;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getPhotoOssKey() {
        return photoOssKey;
    }

    public void setPhotoOssKey(String photoOssKey) {
        this.photoOssKey = photoOssKey;
    }

    public boolean isIsVip() {
        return isVip;
    }

    public void setIsVip(boolean isVip) {
        this.isVip = isVip;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public boolean isIsInvitate() {
        return isInvitate;
    }

    public void setIsInvitate(boolean isInvitate) {
        this.isInvitate = isInvitate;
    }

    public String getVipStartOn() {
        return vipStartOn;
    }

    public void setVipStartOn(String vipStartOn) {
        this.vipStartOn = vipStartOn;
    }

    public String getVipEndOn() {
        return vipEndOn;
    }

    public void setVipEndOn(String vipEndOn) {
        this.vipEndOn = vipEndOn;
    }

    public int getFuncDefId() {
        return funcDefId;
    }

    public void setFuncDefId(int funcDefId) {
        this.funcDefId = funcDefId;
    }

    public String getAppleExpireDate() {
        return appleExpireDate;
    }

    public void setAppleExpireDate(String appleExpireDate) {
        this.appleExpireDate = appleExpireDate;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public int getTotalOcr() {
        return totalOcr;
    }

    public void setTotalOcr(int totalOcr) {
        this.totalOcr = totalOcr;
    }

    public int getTotalPdf() {
        return totalPdf;
    }

    public void setTotalPdf(int totalPdf) {
        this.totalPdf = totalPdf;
    }

    public int getTotalHandwriting() {
        return totalHandwriting;
    }

    public void setTotalHandwriting(int totalHandwriting) {
        this.totalHandwriting = totalHandwriting;
    }

    public int getTotalCloudocr() {
        return totalCloudocr;
    }

    public void setTotalCloudocr(int totalCloudocr) {
        this.totalCloudocr = totalCloudocr;
    }

    public long getUsagesLimit() {
        return usagesLimit;
    }

    public void setUsagesLimit(long usagesLimit) {
        this.usagesLimit = usagesLimit;
    }

    public int getRemainingOcr() {
        return remainingOcr;
    }

    public void setRemainingOcr(int remainingOcr) {
        this.remainingOcr = remainingOcr;
    }

    public int getRemainingPdf() {
        return remainingPdf;
    }

    public void setRemainingPdf(int remainingPdf) {
        this.remainingPdf = remainingPdf;
    }

    public int getRemainingHandwriting() {
        return remainingHandwriting;
    }

    public void setRemainingHandwriting(int remainingHandwriting) {
        this.remainingHandwriting = remainingHandwriting;
    }

    public int getRemainingCertificate() {
        return remainingHighCertificate;
    }

    public void setRemainingCertificate(int remainingCertificate) {
        this.remainingHighCertificate = remainingCertificate;
    }

    public int getTotalCertificate() {
        return totalCertificate;
    }

    public void setTotalCertificate(int totalCertificate) {
        this.totalCertificate = totalCertificate;
    }

    public int getRemainingHandwritingNormal() {
        return remainingHandwritingNormal;
    }

    public void setRemainingHandwritingNormal(int remainingHandwritingNormal) {
        this.remainingHandwritingNormal = remainingHandwritingNormal;
    }

    public int getRemainingCloudocrVip() {
        return remainingCloudocrVip;
    }

    public void setRemainingCloudocrVip(int remainingCloudocrVip) {
        this.remainingCloudocrVip = remainingCloudocrVip;
    }

    public int getRemainingCloudocrNormal() {
        return remainingCloudocrNormal;
    }

    public void setRemainingCloudocrNormal(int remainingCloudocrNormal) {
        this.remainingCloudocrNormal = remainingCloudocrNormal;
    }

    public long getUsages() {
        return usages;
    }

    public void setUsages(long usages) {
        this.usages = usages;
    }

    public int getRemainingVip() {
        return remainingVip;
    }

    public void setRemainingVip(int remainingVip) {
        this.remainingVip = remainingVip;
    }

    public int getInvitationCount() {
        return invitationCount;
    }

    public void setInvitationCount(int invitationCount) {
        this.invitationCount = invitationCount;
    }

    public String getSvipEndOn() {
        return svipEndOn;
    }

    public void setSvipEndOn(String svipEndOn) {
        this.svipEndOn = svipEndOn;
    }
}
