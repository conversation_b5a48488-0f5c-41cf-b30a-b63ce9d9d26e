package com.czur.scanpro.entity.model;

import java.util.List;

/**
 * Created by Yz on 2018/3/30.
 * Email：<EMAIL>
 */

public class HandwritingEntity {

    /**
     * code : 0
     * message : OK
     * data : {"session_id":"10000011386698236","items":[{"itemcoord":{"x":37,"y":22,"width":221,"height":40},"itemstring":"只要有百分之一的希些","coords":[],"words":[{"character":"只","confidence":0.9995492100715636},{"character":"要","confidence":0.9999183416366576},{"character":"有","confidence":0.9975007176399232},{"character":"百","confidence":0.9886131286621094},{"character":"分","confidence":0.8933222889900208},{"character":"之","confidence":0.9992995262145996},{"character":"一","confidence":0.8288528323173523},{"character":"的","confidence":0.999326467514038},{"character":"希","confidence":0.959851086139679},{"character":"些","confidence":0.3065926730632782}],"candword":[]},{"itemcoord":{"x":37,"y":59,"width":254,"height":37},"itemstring":"涿会付头百分动+九的多力t","coords":[],"words":[{"character":"涿","confidence":0.330208957195282},{"character":"会","confidence":0.9987956285476683},{"character":"付","confidence":0.9981868863105774},{"character":"头","confidence":0.5886265635490417},{"character":"百","confidence":0.9990888833999634},{"character":"分","confidence":0.9986497759819032},{"character":"动","confidence":0.6296136379241943},{"character":"+","confidence":0.865349292755127},{"character":"九","confidence":0.9954916834831238},{"character":"的","confidence":0.9976463913917542},{"character":"多","confidence":0.505245566368103},{"character":"力","confidence":0.6335962414741516},{"character":"t","confidence":0.3001934289932251}],"candword":[]},{"itemcoord":{"x":32,"y":88,"width":227,"height":38},"itemstring":"让它变或百分百的现实","coords":[],"words":[{"character":"让","confidence":0.97903972864151},{"character":"它","confidence":0.9997988343238832},{"character":"变","confidence":0.9956278800964355},{"character":"或","confidence":0.5307473540306091},{"character":"百","confidence":0.9948757290840148},{"character":"分","confidence":0.9949339032173156},{"character":"百","confidence":0.9808925986289978},{"character":"的","confidence":0.8506680130958557},{"character":"现","confidence":0.9439561367034912},{"character":"实","confidence":0.997634768486023}],"candword":[]},{"itemcoord":{"x":57,"y":130,"width":33,"height":18},"itemstring":"an","coords":[],"words":[{"character":"a","confidence":0.3443509638309479},{"character":"n","confidence":0.6972714066505432}],"candword":[]}]}
     */

    private int code;
    private String message;
    private String data;

    private DataBean databean;


//    private DataBean data;

    /**
     * TextDetections : [{"DetectedText":"500m","Confidence":93,"Polygon":[{"X":154,"Y":68},{"X":259,"Y":61},{"X":262,"Y":110},{"X":157,"Y":117}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":1}}","WordPolygon":[]},{"DetectedText":"D","Confidence":99,"Polygon":[{"X":709,"Y":88},{"X":739,"Y":88},{"X":739,"Y":122},{"X":709,"Y":122}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":2}}","WordPolygon":[]},{"DetectedText":"500以下不可柔，提示空间不足","Confidence":93,"Polygon":[{"X":92,"Y":124},{"X":581,"Y":124},{"X":581,"Y":190},{"X":92,"Y":190}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":3}}","WordPolygon":[]},{"DetectedText":"50以上可认。寻过惯→500认1","Confidence":84,"Polygon":[{"X":89,"Y":227},{"X":753,"Y":215},{"X":755,"Y":286},{"X":91,"Y":298}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":4}}","WordPolygon":[]},{"DetectedText":"5m","Confidence":83,"Polygon":[{"X":754,"Y":232},{"X":876,"Y":232},{"X":876,"Y":293},{"X":754,"Y":293}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":4}}","WordPolygon":[]},{"DetectedText":"IG,800M提示？有吗","Confidence":94,"Polygon":[{"X":111,"Y":366},{"X":509,"Y":359},{"X":510,"Y":436},{"X":112,"Y":444}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":4}}","WordPolygon":[]},{"DetectedText":"25.17","Confidence":95,"Polygon":[{"X":333,"Y":557},{"X":424,"Y":557},{"X":424,"Y":606},{"X":333,"Y":606}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":5}}","WordPolygon":[]},{"DetectedText":"24.67","Confidence":99,"Polygon":[{"X":315,"Y":612},{"X":432,"Y":605},{"X":435,"Y":656},{"X":318,"Y":663}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":6}}","WordPolygon":[]},{"DetectedText":"9.50","Confidence":96,"Polygon":[{"X":327,"Y":665},{"X":437,"Y":665},{"X":437,"Y":735},{"X":327,"Y":735}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":7}}","WordPolygon":[]},{"DetectedText":"72.","Confidence":99,"Polygon":[{"X":387,"Y":720},{"X":451,"Y":717},{"X":454,"Y":764},{"X":390,"Y":767}],"AdvancedInfo":"{\"Parag\":{\"ParagNo\":7}}","WordPolygon":[]}]
     * Angel : 0.99
     * RequestId : 6b667882-e140-47d9-9a9c-b63f48317659
     */


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public DataBean getDatabean() {
        return databean;
    }

    public void setDatabean(DataBean databean) {
        this.databean = databean;
    }

    public static class DataBean {
        //{
        //	"TextDetections": [{
        //		"DetectedText": "500m",
        //		"Confidence": 93,
        //		"Polygon": [{
        //			"X": 154,
        //			"Y": 68
        //		}, {
        //			"X": 259,
        //			"Y": 61
        //		}, {
        //			"X": 262,
        //			"Y": 110
        //		}, {
        //			"X": 157,
        //			"Y": 117
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":1}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "D",
        //		"Confidence": 99,
        //		"Polygon": [{
        //			"X": 709,
        //			"Y": 88
        //		}, {
        //			"X": 739,
        //			"Y": 88
        //		}, {
        //			"X": 739,
        //			"Y": 122
        //		}, {
        //			"X": 709,
        //			"Y": 122
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":2}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "500以下不可柔，提示空间不足",
        //		"Confidence": 93,
        //		"Polygon": [{
        //			"X": 92,
        //			"Y": 124
        //		}, {
        //			"X": 581,
        //			"Y": 124
        //		}, {
        //			"X": 581,
        //			"Y": 190
        //		}, {
        //			"X": 92,
        //			"Y": 190
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":3}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "50以上可认。寻过惯→500认1",
        //		"Confidence": 84,
        //		"Polygon": [{
        //			"X": 89,
        //			"Y": 227
        //		}, {
        //			"X": 753,
        //			"Y": 215
        //		}, {
        //			"X": 755,
        //			"Y": 286
        //		}, {
        //			"X": 91,
        //			"Y": 298
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":4}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "5m",
        //		"Confidence": 83,
        //		"Polygon": [{
        //			"X": 754,
        //			"Y": 232
        //		}, {
        //			"X": 876,
        //			"Y": 232
        //		}, {
        //			"X": 876,
        //			"Y": 293
        //		}, {
        //			"X": 754,
        //			"Y": 293
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":4}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "IG,800M提示？有吗",
        //		"Confidence": 94,
        //		"Polygon": [{
        //			"X": 111,
        //			"Y": 366
        //		}, {
        //			"X": 509,
        //			"Y": 359
        //		}, {
        //			"X": 510,
        //			"Y": 436
        //		}, {
        //			"X": 112,
        //			"Y": 444
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":4}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "25.17",
        //		"Confidence": 95,
        //		"Polygon": [{
        //			"X": 333,
        //			"Y": 557
        //		}, {
        //			"X": 424,
        //			"Y": 557
        //		}, {
        //			"X": 424,
        //			"Y": 606
        //		}, {
        //			"X": 333,
        //			"Y": 606
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":5}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "24.67",
        //		"Confidence": 99,
        //		"Polygon": [{
        //			"X": 315,
        //			"Y": 612
        //		}, {
        //			"X": 432,
        //			"Y": 605
        //		}, {
        //			"X": 435,
        //			"Y": 656
        //		}, {
        //			"X": 318,
        //			"Y": 663
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":6}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "9.50",
        //		"Confidence": 96,
        //		"Polygon": [{
        //			"X": 327,
        //			"Y": 665
        //		}, {
        //			"X": 437,
        //			"Y": 665
        //		}, {
        //			"X": 437,
        //			"Y": 735
        //		}, {
        //			"X": 327,
        //			"Y": 735
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":7}}",
        //		"WordPolygon": []
        //	}, {
        //		"DetectedText": "72.",
        //		"Confidence": 99,
        //		"Polygon": [{
        //			"X": 387,
        //			"Y": 720
        //		}, {
        //			"X": 451,
        //			"Y": 717
        //		}, {
        //			"X": 454,
        //			"Y": 764
        //		}, {
        //			"X": 390,
        //			"Y": 767
        //		}],
        //		"AdvancedInfo": "{\"Parag\":{\"ParagNo\":7}}",
        //		"WordPolygon": []
        //	}],
        //	"Angel": 0.99,
        //	"RequestId": "6b667882-e140-47d9-9a9c-b63f48317659"
        //}
        private double Angel;
        private String RequestId;
        private List<TextDetectionsBean> TextDetections;


        public double getAngel() {
            return Angel;
        }

        public void setAngel(double angel) {
            Angel = angel;
        }

        public String getRequestId() {
            return RequestId;
        }

        public void setRequestId(String requestId) {
            RequestId = requestId;
        }

        public List<TextDetectionsBean> getTextDetections() {
            return TextDetections;
        }

        public void setTextDetections(List<TextDetectionsBean> textDetections) {
            TextDetections = textDetections;
        }
    }

    public static class TextDetectionsBean {
        /**
         * DetectedText : 500m
         * Confidence : 93
         * Polygon : [{"X":154,"Y":68},{"X":259,"Y":61},{"X":262,"Y":110},{"X":157,"Y":117}]
         * AdvancedInfo : {"Parag":{"ParagNo":1}}
         * WordPolygon : []
         */

        private String DetectedText;
        private int Confidence;
        private String AdvancedInfo;
        private List<PolygonBean> Polygon;
        private List<?> WordPolygon;

        public String getDetectedText() {
            return DetectedText;
        }

        public void setDetectedText(String DetectedText) {
            this.DetectedText = DetectedText;
        }

        public int getConfidence() {
            return Confidence;
        }

        public void setConfidence(int Confidence) {
            this.Confidence = Confidence;
        }

        public String getAdvancedInfo() {
            return AdvancedInfo;
        }

        public void setAdvancedInfo(String AdvancedInfo) {
            this.AdvancedInfo = AdvancedInfo;
        }

        public List<PolygonBean> getPolygon() {
            return Polygon;
        }

        public void setPolygon(List<PolygonBean> Polygon) {
            this.Polygon = Polygon;
        }

        public List<?> getWordPolygon() {
            return WordPolygon;
        }

        public void setWordPolygon(List<?> WordPolygon) {
            this.WordPolygon = WordPolygon;
        }

        public static class PolygonBean {
            /**
             * X : 154
             * Y : 68
             */

            private int X;
            private int Y;

            public int getX() {
                return X;
            }

            public void setX(int X) {
                this.X = X;
            }

            public int getY() {
                return Y;
            }

            public void setY(int Y) {
                this.Y = Y;
            }
        }
    }
}
