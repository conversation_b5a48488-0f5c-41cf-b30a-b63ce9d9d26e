package com.czur.scanpro.alg;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.Log;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;


public class CZSaoMiao_Alg {

    static {
        System.loadLibrary("cz_saomiao_dev");
    }

    private static CZSaoMiao_Alg instance;
    private TFModelUtils tfModelUtils;

    public static CZSaoMiao_Alg getInstance(Context context) {
        if (instance == null) {
            instance = new CZSaoMiao_Alg();
            try {
                InputStream is = context.getAssets().open("template_corner.jpg");
                byte[] idcard_bytes = Utils.input2byte(is);
                int dataLen = idcard_bytes.length;
                instance.jni_init(idcard_bytes, dataLen);

                //instance.tfModelUtils = new TFModelUtils(context.getAssets());
            } catch (IOException e) {
                Log.e("CZSaoMiao_Alg init", e.getMessage());
            }

        }
        return instance;
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
//        finalize_tess();

    }


    public enum ScanType {
        NOTHING(0),
        SINGLE(1),
        BOOK(2),
        IDCARD(3),
        ENTERPRISE_DOC(4),
        DriverLicense(5),

        SINGLE_STATIC(6), //导入走此接口
        ;

        private int value;

        ScanType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public enum ColorType {
        NO_TRANS(0),//不做处理
        COLOR(1),// 彩色模式，仅做提亮
        GRAY(2),//灰度模式
        BW(3),// 黑白模式
        AUTO(4),// 自动模式
        BG_WHITEN(5),// 底色净白
        ENTERPRISE_DOC_DEFAULT(6),//企业证件（营业执照默认模式）
        ENTERPRISE_DOC_GRAY(7), //企业证件灰度
        IDCARD_DEFAULT(8),//身份证默认颜色模式
        IDCARD_GRAY(9),// 身份证灰度
        DRIVER_LICENSE_DEFAULT(10),//驾驶证默认
        DRIVER_LICENSE_GRAY(11)//驾驶证灰度

        ;

        private int value;

        ColorType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }


    /**
     * @param nv21:     nv21 frame buffer
     * @param w         : frame width
     * @param h:        frame height
     * @param scanType: scan type
     * @brief 用于预览时实时检测
     */
    public Args miao_detect(
            byte[] nv21,
            int w, int h,
            ScanType scanType) {
        Args args = realtime_detect_native(nv21, w, h, scanType.getValue());
        args.on_extend_ui_points();
        return args;
    }

    private native Args realtime_detect_native(byte[] nv21, int w, int h, int type);


    /**
     * @param imgData
     * @param dataLen
     * @param cutted_save_path
     * @param colored_save_path
     * @param args
     * @return
     */
    public Args miao_single_pipeline(
            byte[] imgData,//拍照数据
            int dataLen,// 数据长度
            String base_save_path,//base图保存路径  拍照图旋转90度保存
            String cutted_save_path,// 裁剪图保存路径
            String colored_save_path,//颜色模式图保存路径
            String cutted_save_path_small,
            String colored_save_path_small,
            String cornel_template_path,// 身份证圆角留白模版路径
            boolean isFrontIDCard, //是否为身份证正面
            Args args) {
        ScanType scanType = args.getScanType();
        ColorType colorType = args.getColorType();
        return miao_single_pipeline(
                imgData,
                dataLen,
                base_save_path,
                cutted_save_path,
                colored_save_path,
                cutted_save_path_small,
                colored_save_path_small,
                cornel_template_path,
                isFrontIDCard,
                scanType.getValue(),
                colorType.getValue(),
                args.getAngleX(),
                args.getAngleY(),
                true
        );

    }

    private native Args miao_single_pipeline(
            byte[] imgData,//相机图像数据
            int dataLen,//宽
            String base_save_path,//base图保存路径  拍照图旋转90度保存
            String cutted_save_path,//裁剪保存路径
            String colored_save_path,//颜色模式保存路径
            String cutted_save_path_small,
            String colored_save_path_small,

            String cornel_template_path,// 身份证圆角留白模版路径
            boolean isFrontIDCard,//是否为身份证正面

            int stype, //扫描类型
            int ctype, //颜色模式类型
            float angleX,//陀螺仪角度
            float angleY,//陀螺仪角度
            boolean doCallback//是否进行回调
    );


    private Callbacks callbacks;

    public void setCallbacks(Callbacks callbacks) {
        this.callbacks = callbacks;
    }

    /**
     * Call  by Native
     *
     * @param isSucess
     * @param cuttedImg
     * @param savedPath
     */
    private void on_cut_finished(boolean isSucess,
                                 Bitmap cuttedImg,
                                 String savedPath) {
        if (callbacks != null) {
            callbacks.on_cut_finished(isSucess, cuttedImg, savedPath);
        }
    }// descriptor: (ZLandroid/graphics/Bitmap;Ljava/lang/String;)V


//    /**
//     * Call  by Native
//     * @param isSucess
//     * @param dstImg
//     * @param savedPath
//     */
//
//    public  void on_color_trans_finished(boolean isSucess,
//                                         Bitmap  dstImg,
//                                         String  savedPath)
//    {
//        callbacks.on_color_trans_finished(isSucess,dstImg,savedPath);
//
//    }


    /**
     * Call  by Native
     */
    public void on_color_trans_finished_with_keypoints(boolean isSucess,
                                                       Bitmap dstImg,
                                                       String savedPath,
                                                       float leftTopX,
                                                       float leftTopY,
                                                       float rightTopX,
                                                       float rightTopY,
                                                       float leftDownX,
                                                       float leftDownY,
                                                       float rightDownX,
                                                       float rightDownY) {
        if (callbacks != null) {
            callbacks.on_color_trans_finished_with_keypoints(isSucess, dstImg, savedPath,
                    leftTopX,
                    leftTopY,
                    rightTopX,
                    rightTopY,
                    leftDownX,
                    leftDownY,
                    rightDownX,
                    rightDownY);
        }

    }

    /**
     * Call  by Native
     */

    public void on_notify_angle(int angle) {
        if (callbacks != null) {
            callbacks.on_notify_angle(angle);
        }

    }


    /**
     * @param inpath：  输入图像路径
     * @param outpath: 输出图保存路径
     * @param args:    See Args
     *                 如果isOnlyGetKeyPtss为true 仅获取几何关键点，内部不会进行裁剪和颜色模式
     *                 scanType: 扫描类型
     *                 colorType: 颜色模式类型
     * @brief 静态处理
     */
    public Args miao_static_process(
            String inpath,
            String outpath,
            String base_small_save_path,//小图保存路径
            String colored_small_save_path,//小图保存路径
            Args args) {
        ScanType scanType = args.getScanType();
        ColorType colorType = args.getColorType();
        if (base_small_save_path == null || base_small_save_path.length() == 0)
            base_small_save_path = "CZUR";
        if (colored_small_save_path == null || colored_small_save_path.length() == 0)
            colored_small_save_path = "CZUR";


        boolean isOnlyGetKeyPtss = args.isOnlyGetKeyPoints();
        return static_process_native(inpath, outpath,
                base_small_save_path,
                colored_small_save_path,
                scanType.getValue(),
                colorType.getValue(),
                isOnlyGetKeyPtss,
                args.getAngleX(),
                args.getAngleY());

    }

    private native Args static_process_native(String inpath,
                                              String outpath,
                                              String base_small_save_path,
                                              String clored_small_save_path,
                                              int stype,
                                              int ctype,
                                              boolean isOnlyGetKeyPtss,
                                              float angleX,
                                              float angleY);


    /**
     * @param inpath：  输入图像路径
     * @param outpath: 输出图保存路径
     * @param args:    输入的手动调整相关的参数
     *                 scanType 时必须字段
     *                 四个关键点是必须字段
     *                 当为书本时，上下2组点是必须字段
     * @return 0: sucess  -1: failed TODO: more error code
     * @brief 手动调整模式后处理  此方法已经不再支持Book模式
     */
    @Deprecated
    public int manual_post_process(
            String inpath,
            String outpath,
            String colored_save_path,
            String base_small_save_path,
            String colored_small_save_path,
            Args args) {
        if (colored_save_path == null || colored_save_path.length() == 0)
            colored_save_path = "CZUR";
        if (base_small_save_path == null || base_small_save_path.length() == 0)
            base_small_save_path = "CZUR";
        if (colored_small_save_path == null || colored_small_save_path.length() == 0)
            colored_small_save_path = "CZUR";

        ScanType scanType = args.getScanType();
        ColorType colorType = args.getColorType();
        return manual_process_native(inpath, outpath,
                colored_save_path,
                base_small_save_path,
                colored_small_save_path,
                scanType.getValue(),
                colorType.getValue(),
                args);

    }

    private native int manual_process_native(String inpath,
                                             String outpath,
                                             String colored_save_path,
                                             String base_small_save_path,
                                             String colored_small_save_path,
                                             int stype, int ctype, Args args);


    private native void jni_init(byte[] idcard_bytes, int idcard_data_len);


    /**
     * @param p 待旋转点
     * @param h 预览图像高度
     * @return
     */
    public CZPoint transpose_key_points(CZPoint p, int h) {
        return new CZPoint(h - p.getY(), p.getX());

    }


    /////////////
    // 手动处理优化版本
    /////////////

    /**
     * @param memMB 允许内存估计
     * @return 获得内容bitmap  和四个关键点
     * @brief 单页手动调整时先进此方法， 获得内容bitmap,  args.getContentImg()
     * 以及四个关键点
     * 同时算法内部缓存住吸附时要用的直线以及content image
     */
    public Args before_single_manual_process(String inpath, int memMB) {
        Args ret = before_single_manual_process_native(inpath, memMB);
        /// please modify it log to buggly
        if (null == ret.getContentImg()) {
            Log.e("single_manual_process", ret.getInfos());
        }
        return ret;

    }

    private native Args before_single_manual_process_native(String inpath, int memMB);

    /**
     * @param args          里面需要包含当前的四个关键点
     * @param currentPointX 当前手指移动到的点X坐标
     * @param currentPointY 当前手指移动到的点Y坐标
     * @param whichPoint    针对哪一个点进行吸附 上(0:上， 1：右， 2：下， 3：左）
     * @return Args  如果Args.hasSorption() 则将四个关键点取出进行更新
     * @brief 单页吸附时调用此方法
     */
    public Args on_single_sorption(
            Args args,
            int currentPointX,
            int currentPointY,
            int whichPoint) {
        return on_single_sorption_native(args, currentPointX, currentPointY, whichPoint);

    }

    private native Args on_single_sorption_native(
            Args args,
            int currentPointX,
            int currentPointY,
            int whichPoint);


    /**
     * @param args     里面需要设置四个关键点以便用于裁剪
     * @param savePath 裁剪后保存到的路径
     * @return Args  Args.getContentImg 可以获得处理后的图
     * @brief 单页手动处理完时调用此方法， 裁剪以及进行颜色模式
     */
    public Args on_single_manual_finished(Args args,
                                          String savePath,
                                          String colored_save_path,
                                          String base_small_save_path,
                                          String colored_small_save_path
    ) {
        if (colored_save_path == null || colored_save_path.length() == 0)
            colored_save_path = "CZUR";
        if (base_small_save_path == null || base_small_save_path.length() == 0)
            base_small_save_path = "CZUR";
        if (colored_small_save_path == null || colored_small_save_path.length() == 0)
            colored_small_save_path = "CZUR";

        ColorType colorType = args.getColorType();
        return on_single_manual_finished_native(args,
                savePath,
                colored_save_path,
                base_small_save_path,
                colored_small_save_path,
                colorType.getValue());

    }

    private native Args on_single_manual_finished_native(Args args,
                                                         String savePath,
                                                         String colored_save_path,
                                                         String base_small_save_path,
                                                         String colored_small_save_path,
                                                         int cType);


    //// BOOK SORPTION /////

    /**
     * @param memMB 估计允许的内存
     * @return 获得内容bitmap  和四个关键点, 以及曲线点
     * @brief 书本手动调整时先进此方法， 获得内容bitmap,  args.getContentImg()
     * 以及四个关键点(左右直线)
     */
    public Args before_book_manual_process(String inpath, int memMB) {
        return before_book_manual_process_native(inpath, memMB);

    }

    private native Args before_book_manual_process_native(String inpath, int memMB);


    public CZPoint on_book_sorption_pts(
            int currentPointX,
            int currentPointY,
            boolean isVetical,//是否为调整垂直点（左右线点为垂直）
            boolean isUpPoint,//是否为上面曲线的控制点
            int whichPoint//如果为上下曲线， 那么是哪一个点 0-5 取值
    ) {
        return on_book_sorption_native(currentPointX, currentPointY, isVetical, isUpPoint, whichPoint);
    }

    private native CZPoint on_book_sorption_native(
            int currentPointX,
            int currentPointY,
            boolean isVetical,//是否为调整垂直点（左右线点为垂直）
            boolean isUpPoint,//是否为上面曲线的控制点
            int whichPoint//如果为上下曲线， 那么是哪一个点 0-5 取值

    );

    /**
     * @param args 需要传入四个关键点（左右线）以及上下控制点
     * @return
     * @brief 更新曲线
     */
    public Args on_update_curve(
            Args args) {
        return on_update_curve_native(args);

    }

    private native Args on_update_curve_native(Args args);


    /**
     * @param args 需要传入四个关键点（左右线）以及上下控制点,以及颜色模式
     * @return
     * @brief 调整完后后处理
     */
    public Args on_book_manual_finished(Args args,
                                        String savePath,
                                        String colored_save_path,
                                        String base_small_save_path,
                                        String colored_small_save_path
    ) {
        if (colored_save_path == null || colored_save_path.length() == 0)
            colored_save_path = "CZUR";
        if (base_small_save_path == null || base_small_save_path.length() == 0)
            base_small_save_path = "CZUR";
        if (colored_small_save_path == null || colored_small_save_path.length() == 0)
            colored_small_save_path = "CZUR";
        ColorType colorType = args.getColorType();
        return on_book_manual_finished_native(args,
                savePath,
                colored_save_path,
                base_small_save_path,
                colored_small_save_path,
                colorType.getValue());
    }

    private native Args on_book_manual_finished_native(Args args,
                                                       String savePath,
                                                       String colored_save_path,
                                                       String base_small_save_path,
                                                       String colored_small_save_path,
                                                       int cType);


    public List<Bitmap> cut_idcard_from_merged_img(String inpath) {
        return cut_idcard_from_merged_img_naitive(inpath);
    }

    private native List<Bitmap> cut_idcard_from_merged_img_naitive(String inpath);


    /**
     * @param img 512*512 size image
     * @return 0, 90, 180, 270
     * @brief 通过tensorflow mobile 模型获得文字方向
     */
    public int text_orientation_tf(Bitmap img) {
        return 0;
        //return  90*tfModelUtils.run(img);
    }

    /**
     * @brief call by native
     */
    private void on_save_sucess() {
        if (callbacks != null) {
            callbacks.on_save_success();
        }
    }

    public native void save_picture(byte[] imgData,//相机图像数据
                                    int dataLen,//宽
                                    String save_path);


    /////// ON START CALL //////

    /**
     * @param tessPath 必须是tessdata的绝对目录
     * @return True if success
     * @brief 初始化tesseract
     */
    public native boolean init_tess(String tessPath);

    private native void finalize_tess();

    /**
     * @param basePath
     * @param baseSmallPath
     * @param coloredPath
     * @param colorSmallPath
     * @param angle          0 90 180 270
     * @return
     */
    public native int rotate_images(String basePath,
                                    String baseSmallPath,
                                    String coloredPath,
                                    String colorSmallPath,
                                    int angle);

    public native void clear_cached_rotate_images();


    public native void clear_preview_cache();

    /**
     * @brief call by native
     */
    private void on_need_focus() {
        if (callbacks != null) {
            callbacks.on_need_focus();
        }
    }

    ;


    public native void set_sensitivity(float sens);


    /**
     * @param inPath   图像输入路径
     * @param outPath  保存路径
     * @param memoryMb 剩余运行使用的内存
     * @return 0: success,  -1: img path invalid, -2:  still out of memory
     * @brief resize dynamically by the memmory
     */
    public native int resize_dynamically(String inPath,
                                         String outPath,
                                         int memoryMb);


}
