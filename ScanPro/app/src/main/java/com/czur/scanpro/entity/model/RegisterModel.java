package com.czur.scanpro.entity.model;

public class RegisterModel {

    private String id;
    private String name;
    private String mobile;
    private String email;
    private String photo;
    private boolean isActive;
    private String token;
    /**
     * openId : **********
     * unionId : **********
     * platformId : 3
     * createOn : *************
     * bindOn : null
     * accountId : null
     * nickname : 邮编是侦探
     * imageUrl : https://tvax4.sinaimg.cn//crop.0.0.************/006FBdF2gy8g0oz2ej64wj30qw0qwq4x.jpg
     */

    private String openId;
    private String unionId;
    private int platformId;
    private long createOn;
    private Object bindOn;
    private Object accountId;
    private String nickname;
    private String imageUrl;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
            this.token = token;
        }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public long getCreateOn() {
        return createOn;
    }

    public void setCreateOn(long createOn) {
        this.createOn = createOn;
    }

    public Object getBindOn() {
        return bindOn;
    }

    public void setBindOn(Object bindOn) {
        this.bindOn = bindOn;
    }

    public Object getAccountId() {
        return accountId;
    }

    public void setAccountId(Object accountId) {
        this.accountId = accountId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
}
