package com.czur.scanpro.ui.account

import android.content.DialogInterface
import android.content.Intent
import android.content.res.ColorStateList
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.CleanUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.SizeUtils
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ThreadUtils
import com.blankj.utilcode.util.Utils
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityRegisterBinding
import com.czur.scanpro.entity.model.ChannelModel
import com.czur.scanpro.entity.model.RegisterModel
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.DownloadEntity
import com.czur.scanpro.entity.realm.OcrModeEntity
import com.czur.scanpro.entity.realm.PdfEntity
import com.czur.scanpro.entity.realm.SyncCategoryEntity
import com.czur.scanpro.entity.realm.SyncDocEntity
import com.czur.scanpro.entity.realm.SyncTagEntity
import com.czur.scanpro.entity.realm.TagEntity
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.LoginEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.base.WebViewActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.file.FilePreviewActivity
import com.czur.scanpro.ui.home.FileActivity
import com.czur.scanpro.ui.user.UserActivity
import com.czur.scanpro.utils.MD5Utils
import com.czur.scanpro.utils.validator.Validator
import com.facebook.drawee.backends.pipeline.Fresco
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import java.io.File


/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class RegisterActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityRegisterBinding by lazy{
        ActivityRegisterBinding.inflate(layoutInflater)
    }

    private lateinit var userPreferences: UserPreferences
    private lateinit var httpManager: HttpManager
    private lateinit var realm: Realm

    private var timeCount: TimeCount? = null
    private var codeHasContent = false
    private var userId: String? = null
    private var channel: String? = null

    //0：默认，1：图片浏览
    private var type: Int = 0
    private var isUserPrivacyChecked = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_register)
        setContentView(binding.root)

        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        binding.registerTopBar.normalTitle.text = getString(R.string.register_text)
        userPreferences = UserPreferences.getInstance(this)
        httpManager = HttpManager.getInstance()
        realm = Realm.getDefaultInstance()
        type = intent.getIntExtra("type", 0)
        userId = userPreferences.userId
        channel = userPreferences.channel

    }


    private fun registerEvent() {
        binding.registerTopBar.backBtn.setOnClickListener(this)
        binding.hadAccountLoginBtn.setOnClickListener(this)
        binding.registerBtn.setOnClickListener(this)
        binding.getCodeBtn.setOnClickListener(this)

        binding.registerAccountEdt.addTextChangedListener(accountTextWatcher)
        binding.registerPasswordEdt.addTextChangedListener(pswTextWatcher)
        binding.registerCodeEdt.addTextChangedListener(codeTextWatcher)

        binding.userPrivacyImg.setOnClickListener(this)
        binding.userTermsTv.setOnClickListener(this)
        binding.userPrivacyTv.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.user_privacy_img -> {
                isUserPrivacyChecked = !isUserPrivacyChecked
                if (isUserPrivacyChecked) {
                    binding.userPrivacyImg.setImageResource(R.mipmap.sitting_select)
                    binding.userPrivacyImg.imageTintList =
                        ColorStateList.valueOf(resources.getColor(R.color.identifying_code))
                } else {
                    binding.userPrivacyImg.setImageResource(R.mipmap.sitting_no_select)
                    binding.userPrivacyImg.imageTintList =
                        ColorStateList.valueOf(resources.getColor(R.color.gray_99))
                }
                checkRegisterButtonToClick()
            }

            R.id.user_privacy_tv -> {
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_policy1))
                intent.putExtra("url", Constants.PRIVACY_AGREEMENT)
                ActivityUtils.startActivity(intent)
            }
            R.id.user_terms_tv -> {
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_terms))
                intent.putExtra("url", Constants.PRIVACY_TERMS)
                ActivityUtils.startActivity(intent)
            }

            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.had_account_login_btn -> {
                val intent = Intent(this@RegisterActivity, LoginActivity::class.java)
                ActivityUtils.startActivity(intent)
            }
            R.id.getCodeBtn -> {
                validatorAccountAndPassword()
            }
            R.id.register_btn -> {
                mobileRegister()
            }
            else -> {
            }
        }
    }

    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private fun timeCountBegin() {
        timeCount = TimeCount(60000, 1000)
        timeCount?.start()
    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    internal inner class TimeCount(millisInFuture: Long, countDownInterval: Long) : CountDownTimer(millisInFuture, countDownInterval) {

        override fun onFinish() {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.red_de4d4d)).build()
            binding.getCodeBtn.background = drawable

            binding.getCodeBtn.setText(R.string.gain)
            binding.getCodeBtn.isClickable = true
            binding.getCodeBtn.isSelected = true


        }

        override fun onTick(millisUntilFinished: Long) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e1)).build()
            binding.getCodeBtn.background = drawable
            binding.getCodeBtn.isClickable = false
            binding.getCodeBtn.text = (millisUntilFinished / 1000).toString() + " s"
            binding.getCodeBtn.isSelected = false

        }

    }

    /**
     * @des: 验证手机号或者邮箱 和密码
     * @params:[]
     * @return:void
     */
    private fun validatorAccountAndPassword() {
        val accountStr = binding.registerAccountEdt.text.toString()
        if (Validator.isEmpty(accountStr)) {
            showMessage(R.string.user_input_phone)
        } else {
            if (RegexUtils.isMobileExact(accountStr)) {
                getMobileCode(accountStr)
            } else {
                showMessage(R.string.toast_mobile_format_wrong)
            }
        }
    }

    /**
     * @des: 手机注册前置判断
     * @params:
     * @return:
     */
    private fun mobileRegister() {
        val mobile = binding.registerAccountEdt.text.toString()
        val pwd = binding.registerPasswordEdt.text.toString()
        val mobileCode = binding.registerCodeEdt.text.toString()
        if (mobile.isEmpty()) {
            showMessage(R.string.login_alert_phone_empty)
        } else if (!RegexUtils.isMobileExact(mobile)) {
            showMessage(R.string.toast_mobile_format_wrong)
        } else if (pwd.isEmpty()) {
            showMessage(R.string.login_alert_password_empty)
        } else if (pwd.length <= 5) {
            showMessage(R.string.login_alert_pwd_length)
        } else if (mobileCode.isEmpty()) {
            showMessage(R.string.login_alert_mail_code)
        } else if (mobileCode.length <= 5) {
            showMessage(R.string.edit_text_code_length)
        } else {
            KeyboardUtils.hideSoftInput(this)
            //如果Channel没有值 获取Channel
            if (TextUtils.isEmpty(channel)) {
                httpManager.request().channel(ChannelModel::class.java, object : MiaoHttpManager.Callback<ChannelModel> {
                    override fun onStart() {
                        showProgressDialog(false)
                    }

                    override fun onResponse(entity: MiaoHttpEntity<ChannelModel>) {
                        hideProgressDialog()
                        LogUtils.iTag("CHANNEL", Gson().toJson(entity))
                        userPreferences.channel = entity.body.channel
                        userPreferences.endpoint = entity.body.endPoint
                        mobileRegisterRequest(mobile, pwd, mobileCode, false)
                    }

                    override fun onFailure(entity: MiaoHttpEntity<ChannelModel>) {
                        hideProgressDialog()
                        showMessage(R.string.request_failed_alert)
                    }

                    override fun onError(e: Exception) {
                        hideProgressDialog()
                        showMessage(R.string.request_failed_alert)
                    }
                })

            } else {
                LogUtils.i("has Channel")
                mobileRegisterRequest(mobile, pwd, mobileCode, true)
            }
        }
    }

    /**
     * @des: 手机注册
     * @params:[mail, pwd, code]
     * @return:void
     */
    private fun mobileRegisterRequest(mobile: String, pwd: String, code: String, hasChannel: Boolean) {
        val password = MD5Utils.md5(pwd)
        HttpManager.getInstance().requestPassport().mobileRegister(Constants.SCAN_PRO, userPreferences.imei, userPreferences.channel, mobile, password, code, RegisterModel::class.java, object : MiaoHttpManager.Callback<RegisterModel> {
            override fun onStart() {
                if (hasChannel) {
                    showProgressDialog(false)
                }
            }

            override fun onResponse(entity: MiaoHttpEntity<RegisterModel>) {
                confirmToClearLastUserData(entity, mobile, pwd)
            }

            override fun onFailure(entity: MiaoHttpEntity<RegisterModel>) {
                hideProgressDialog()
                when {
                    MiaoHttpManager.STATUS_USER_EXISTS == entity.code -> {
                        showMessage(R.string.toast_user_existing)
                    }
                    MiaoHttpManager.STATUS_INVALID_CODE == entity.code -> {
                        showMessage(R.string.toast_code_error)
                    }
                    entity.code == MiaoHttpManager.STATUS_INVALID_MOBILE -> {
                        showMessage(R.string.invalid_mobile)
                    }
                    else -> {
                        showMessage(R.string.request_failed_alert)
                    }
                }
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    private fun confirmToClearLastUserData(entity: MiaoHttpEntity<RegisterModel>, loginUsername: String, loginPassword: String) {
        val currentUserId = entity.body.id
        LogUtils.i(currentUserId, userId)
        if (!StringUtils.equals(userPreferences.lastUserId, currentUserId) && Validator.isNotEmpty(userPreferences.lastUserId)) {
            val builder = ScanProCommonPopup.Builder(this@RegisterActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
            builder.setTitle(resources.getString(R.string.prompt))
            val title = String.format(getString(R.string.confirm_to_clear_account), userPreferences.userName)
            builder.setMessage(title)
            builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
                dialog.dismiss()
                clearLastUserDataAndSetCurrentData(entity, loginUsername, loginPassword)
                ActivityUtils.finishActivity(this@RegisterActivity)
            })
            builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, _ ->
                dialog.dismiss()
                ActivityUtils.finishActivity(this@RegisterActivity)
            })
            val commonPopup = builder.create()
            if (isActive) {
                commonPopup.show()
            }

        } else {
            setCurrentUserData(entity, loginUsername, loginPassword)
            ActivityUtils.finishActivity(this@RegisterActivity)
        }

    }


    /**
     * @des: 存储当前用户信息到SP
     * @params:
     * @return:
     */
    private fun setCurrentUserData(entity: MiaoHttpEntity<RegisterModel>, loginUsername: String, loginPassword: String) {
        userPreferences.user = entity.body
        userPreferences.lastUserId = userPreferences.userId
        userPreferences.setIsUserLogin(true)
        userPreferences.loginUserName = loginUsername
        userPreferences.loginPassword = loginPassword
        LogUtils.i(MD5Utils.md5(loginPassword))
        LogUtils.i(Gson().toJson(userPreferences.user))
        transformCategory(realm)
        showProgressDialog(false)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                transformRealCategory()
                return null
            }

            override fun onSuccess(result: Void?) {
                hideProgressDialog()
                showLoginSuccessAndGoIndex()
            }
        })
    }

    /**
     * @des: 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     * @params:
     * @return:
     */

    private fun clearLastUserDataAndSetCurrentData(entity: MiaoHttpEntity<RegisterModel>, loginUsername: String, loginPassword: String) {

        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                LogUtils.i("clean last user file and sp")
                val filePath = filesDir.toString() + File.separator + userPreferences.lastUserId
                FileUtils.delete(File(filePath))
                runOnUiThread {
                    //清空sp
                    userPreferences.resetUser()
                    //清空数据库
                    realm.executeTransaction {
                        realm.where(DocEntity::class.java).notEqualTo("userID", Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm.where(CategoryEntity::class.java).notEqualTo("userID", Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm.where(TagEntity::class.java).notEqualTo("userID", Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm.where(PdfEntity::class.java).findAll().deleteAllFromRealm()
                        realm.where(OcrModeEntity::class.java).findAll().deleteAllFromRealm()
                        realm.where(SyncTagEntity::class.java).findAll().deleteAllFromRealm()
                        realm.where(SyncDocEntity::class.java).findAll().deleteAllFromRealm()
                        realm.where(SyncCategoryEntity::class.java).findAll().deleteAllFromRealm()
                        realm.where(DownloadEntity::class.java).findAll().deleteAllFromRealm()
                    }
                    Fresco.getImagePipeline().clearCaches()
                    CleanUtils.cleanCustomDir(Utils.getApp().filesDir.toString() + File.separator + Constants.PDF_PATH)
                    setCurrentUserData(entity, loginUsername, loginPassword)
                }

                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })

    }


    /**
     * @des: 显示登录成功并且跳转到主页
     * @params:
     * @return:
     */
    private fun showLoginSuccessAndGoIndex() {
        showMessage(R.string.register_success)
        requestUserInfo()
        EventBus.getDefault().post(LoginEvent(EventType.REGISTER_SUCCESS))

        when (type) {
            0 -> {
                if (ActivityUtils.isActivityExistsInStack(UserActivity::class.java)) {
                    ActivityUtils.finishToActivity(UserActivity::class.java, false)
                } else {
                    val intent = Intent(this@RegisterActivity, UserActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    ActivityUtils.startActivity(intent)
                }
            }
            1 -> {
                ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
            }
            2 -> {
                ActivityUtils.finishToActivity(IndexActivity::class.java, false)
            }
            3 -> {
                ActivityUtils.finishToActivity(FileActivity::class.java, false)
            }
        }

    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private fun getMobileCode(accountStr: String) {
        httpManager.requestPassport().mobileCode(accountStr, String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {}

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                LogUtils.d(Gson().toJson(entity))
                timeCountBegin()
                showMessage(R.string.toast_code_send)
            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                when (entity.code) {
                    MiaoHttpManager.STATUS_CODE_1_MIN -> {
                        showMessage(R.string.toast_code_1_min)
                    }
                    MiaoHttpManager.STATUS_5_MIN_4_TIME -> {
                        showMessage(R.string.toast_5_min_4_time)
                    }
                    MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY -> {
                        showMessage(R.string.toast_5_time_in_one_day)
                    }
                    else -> {
                        showMessage(R.string.request_failed_alert)
                    }
                }

            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }


    override fun onDestroy() {
        super.onDestroy()
        timeCount?.cancel()
    }

    private val codeTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            codeHasContent = s.isNotEmpty()
            checkRegisterButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            codeHasContent = s.isNotEmpty()
            checkRegisterButtonToClick()
        }
    }
    private val pswTextWatcher = object : TextWatcher {
        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听

            checkRegisterButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            checkRegisterButtonToClick()
        }
    }

    private val accountTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            judgingRegister(s)

        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            judgingRegister(s)
        }
    }


    private fun judgingRegister(s: CharSequence) {
        checkRegisterButtonToClick()
    }

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkRegisterButtonToClick() {
        val accountIsNotEmpty = Validator.isNotEmpty(binding.registerAccountEdt.text.toString())
        val passwordIsNotEmpty = Validator.isNotEmpty(binding.registerPasswordEdt.text.toString())
        if (accountIsNotEmpty && passwordIsNotEmpty && codeHasContent
            && isUserPrivacyChecked) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
            binding.registerBtn.background = drawable
            binding.registerBtn.isSelected = true
            binding.registerBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
            binding.registerBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
            binding.registerBtn.background = drawable
            binding.registerBtn.isSelected = false
            binding.registerBtn.setTextColor(resources.getColor(R.color.white))
            binding.registerBtn.isClickable = false
        }
    }
}
