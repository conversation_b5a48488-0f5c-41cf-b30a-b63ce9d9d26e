package com.czur.scanpro.common;

import com.blankj.utilcode.util.Utils;

public class Constants {
    //会员服务协议
    public static final String VIP_AGREEMENT = "https://passport.czur.cc/sm_vip_privacy.html";

    // 隐私政策
//    public static final String PRIVACY_AGREEMENT = "https://passport.czur.cc/privacy_part.html";
    public static final String PRIVACY_AGREEMENT = "https://privacy.czur.cc/privacy";
    // 用户协议
    public static final String PRIVACY_TERMS = "https://privacy.czur.cc/terms";
    // 个人信息收集清单
    public static final String PRAVATE_INFO_LIST = "https://passport.czur.cc/checklist.html";
    //第三方共享信息清单
    public static final String THIRD_SHARE_INFO_LIST = "https://passport.czur.cc/3dsharelist.html";

    public static final String ID = "id";
    public static final String EMPTY = "";
    public static final String SCAN_PRO = "scan_pro_android";
    public static final String APP_BUNDLE = "com.czur.scanpro";
    public static final String BUCKET = "changer-saomiao";
    public static final String CHANNEL = "CN";
    public static final String ENDPOINT = "oss-cn-beijing.aliyuncs.com";
    public static final String QQ = "腾讯QQ";
    public static final String WECHAT = "微信";
    public static final String WEIBO = "微博";
    public static final String PDF_EVENT = "PDFTimes";
    public static final String BUSINESS_LICENSE_EVENT = "BusinessLicenseOCRTimes";
    public static final String USE_CODE = "userCode";
    public static final String METHOD = "method";
    public static final String CODE = "code";
    public static final String WECHAT_SHARE = "WeChat";
    public static final String MOMENT_SHARE = "WeChatMoment";

    public static final String JPG = ".jpg";
    public static final String SMALL_JPG = ".small.jpg";
    public static final String ORIGINAL_JPG = ".original.jpg";
    public static final String PHOTO_JPG = ".photo.jpg";
    public static final String COMPRESS_JPG = ".compress.jpg";
    public static final String BASE_JPG = ".base.jpg";
    public static final String BASE_SMALL_JPG = ".baseSmall.jpg";


    public static final String PDF = ".pdf";
    public static final String ID_CARD_PATH = "template_corner.jpg";
    public static final String NO_USER = "NoUser";
    public static final String SCAN_PRO_CN = "扫描";
    public static final String FEEDBACK_URL = "czur_scanner/v1/uploadFailureFiles.do";
    public static final String PDF_PATH = "pdf/";
    public static final String APK_PATH = "apk/";
    public static final String APK_FULL_PATH = "/Download/ScanPro/apk/";
    public static final String FILEPROVIEDER = "com.czur.scanpro.fileProvider";
    public static final String TEMP = "/temp/";
    public static final String SD_PATH = "/ScanPro/";
    public static final String OCR_PATH = "ocr/";
    public static final String AD_PATH = "Advertising/";
    public static final String HANDWRITING_PATH = "handwriting/";
    public static final String BUSINESS_LICENSE_PATH = "businessLicense/";
    public static final String SYNC_PATH = "sync/";
    public static final String TAKE_PHOTO_INIT_TIME = "1994-06-07 08:08:08.000";
    public static final String CATEGORY_DEFAULT_NAME = "文档";
    public static final String DEFAULT_TIME1 = "1995-03-24 08:08:08";
    public static final String DEFAULT_TIME2 = "1995-03-24 09:09:09";
    public static final String DEFAULT_TIME3 = "1995-03-24 10:10:10";
    public static final String DEFAULT_TIME4 = "1995-03-24 11:11:11";
    public static final String DEFAULT_TIME5 = "1995-03-24 12:12:12";

    public static final String SYNC_URL = "v3/saomiao/synchronous";
    public static final String FILE_SIZE_URL = "v3/saomiao/getFileSize";
    public static final String SHARE_URL = "v3/saomiao/createShareFileLink";

    public static final String OSS_SERVER_PATH = "/Books/";

    public static final String REQUEST_STARTUP_ADVERTISEMENT = "app/store/ad/list";

    //手写识别API
    public static final long HANDWRITING_APP_ID = 1256326589;
    public static final long HANDWRITING_EXPIRED = 2592000;
    public static final String HANDWRITING_BUCKET_NAME = "tencentyun";
    public static final String HANDWRITING_HOST = "ocr.tencentcloudapi.com";
    public static final String BAIDU_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
    public static final String BAIDU_OCR_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting";
    public static final String BAIDU_CLIENT_ID = "3XLxTYOCoS1O5klE1GYMayUS";
    public static final String BAIDU_GRANT_TYPE = "client_credentials";
    public static final String BAIDU_CLIENT_SECRET = "qgalyzTM2cPDoqskGfvcy0V0q2wDrvAf";

//    public static final String HANDWRITING_URL = "http://recognition.image.myqcloud.com/ocr/handwriting";
//    test-ai.czur.com/api/czur/ocr/handwrite
//    POST application/json
//    base64：图片base64
    public static final String HANDWRITING_URL = "https://test-ai.czur.com/api/czur/ocr/handwrite";
    public static final String HANDWRITING_SECRET_ID = "AKIDPtDQo9MPTM4e4JvLDuX5Wr5EII9xD9c8";
    public static final String HANDWRITING_SECRET_KEY = "gCfzEMJ5gIoAORIyYxCHBtQl9UffUDmA";

    public static final String BUSINESS_LICENSE_ID = "AKIDDDnvoSkNglaAh3G3xd5KNBl6CZjfkoZ4";
    public static final String BUSINESS_LICENSE_KEY = "QZfW8UvTpxqfGcjbQ1OVQnvpXCesreT4";

//    public static final String BUSINESS_LICENSE_URL = " https://recognition.image.myqcloud.com/ocr/bizlicense";
    public static final String BUSINESS_LICENSE_URL = " https://test-ai.czur.com/api/czur/ocr/bizLicense";


    public static final String HANDWRITING_V3_URL = "http://ocr.tencentcloudapi.com ";
    public static final int WIDTH_2400W = 1200;
    public static final int HEIGHT_2400W = 900;
    public static final int WIDTH_2400W1 = 1200;
    public static final int HEIGHT_2400W1 = 900;

    //机型
    public static final String GOOGLE_Pixel_2 = "googlePixel 2";
    public static final String GOOGLE_Nexus_5 = "googleNexus 5";
    public static final String GOOGLE_Nexus_6 = "googleNexus 6";

    // TODO 注意：20210712需要在发布的时候关闭日志功能！！！
    public static boolean IS_DEBUG_FLAG = true;
//    public static boolean IS_DEBUG_FLAG = AppUtils.isAppDebug();

    public static final String SAOMIAO_SD_PATH = Utils.getApp().getExternalFilesDir("") + SD_PATH + "log/";

}
