package com.czur.scanpro.entity.model;

import java.util.List;

public class BaiduWordModel {

    /**
     * log_id : 2166812690126386802
     * words_result_num : 24
     * words_result : [{"location":{"width":348,"top":0,"left":5,"height":60},"words":"仅务器的莫他之"},{"location":{"width":551,"top":43,"left":2,"height":100},"words":"B收到Nmt请求后调用PE"},{"location":{"width":299,"top":70,"left":534,"height":52},"words":"eelennedoncet"},{"location":{"width":473,"top":116,"left":0,"height":81},"words":"创键个包含SOM的Hm合"},{"location":{"width":834,"top":182,"left":0,"height":76},"words":"叫Hq您会创建成功会调用SdpOtnerc哑"},{"location":{"width":801,"top":244,"left":29,"height":91},"words":"onCrasouasa响应徽,在B谢过"},{"location":{"width":578,"top":325,"left":11,"height":58},"words":"eweton.sSotloadDescilionC"},{"location":{"width":269,"top":313,"left":565,"height":76},"words":"方法将SDP"},{"location":{"width":793,"top":377,"left":16,"height":80},"words":"帕己的PerCouacio对象同时将dt"},{"location":{"width":834,"top":440,"left":0,"height":99},"words":"徐给服另器肢器将咖笨妆恰几"},{"location":{"width":494,"top":524,"left":0,"height":91},"words":"收到0信令忘调用"},{"location":{"width":217,"top":543,"left":483,"height":55},"words":"pertenodon"},{"location":{"width":122,"top":551,"left":709,"height":23},"words":"soKool"},{"location":{"width":834,"top":588,"left":0,"height":103},"words":"忌将B宽殊的SOPRF守KFutmtm"},{"location":{"width":699,"top":675,"left":0,"height":91},"words":"引用Porlonedtmn.cretehnswerFil"},{"location":{"width":169,"top":662,"left":665,"height":79},"words":"法剞建"},{"location":{"width":148,"top":794,"left":175,"height":43},"words":"anseH"},{"location":{"width":426,"top":728,"left":353,"height":100},"words":"令创成功后同样"},{"location":{"width":251,"top":828,"left":196,"height":101},"words":"当听中的,"},{"location":{"width":194,"top":848,"left":522,"height":44},"words":"etewosS"},{"location":{"width":125,"top":895,"left":82,"height":44},"words":"serler"},{"location":{"width":509,"top":915,"left":0,"height":137},"words":"g处A同样通生CpH"},{"location":{"width":312,"top":905,"left":520,"height":76},"words":"ennadion.SotLoc"},{"location":{"width":819,"top":979,"left":0,"height":127},"words":"将SO常O的OPaloOmn对"}]
     */

    private long log_id;
    private int words_result_num;
    private List<WordsResultBean> words_result;
    /**
     * error_code : 17
     * error_msg : Open api daily request limit reached
     */

    private int error_code;
    private String error_msg;

    public long getLog_id() {
        return log_id;
    }

    public void setLog_id(long log_id) {
        this.log_id = log_id;
    }

    public int getWords_result_num() {
        return words_result_num;
    }

    public void setWords_result_num(int words_result_num) {
        this.words_result_num = words_result_num;
    }

    public List<WordsResultBean> getWords_result() {
        return words_result;
    }

    public void setWords_result(List<WordsResultBean> words_result) {
        this.words_result = words_result;
    }

    public int getError_code() {
        return error_code;
    }

    public void setError_code(int error_code) {
        this.error_code = error_code;
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }

    public static class WordsResultBean {
        /**
         * location : {"width":348,"top":0,"left":5,"height":60}
         * words : 仅务器的莫他之
         */

        private LocationBean location;
        private String words;

        public LocationBean getLocation() {
            return location;
        }

        public void setLocation(LocationBean location) {
            this.location = location;
        }

        public String getWords() {
            return words;
        }

        public void setWords(String words) {
            this.words = words;
        }

        public static class LocationBean {
            /**
             * width : 348
             * top : 0
             * left : 5
             * height : 60
             */

            private int width;
            private int top;
            private int left;
            private int height;

            public int getWidth() {
                return width;
            }

            public void setWidth(int width) {
                this.width = width;
            }

            public int getTop() {
                return top;
            }

            public void setTop(int top) {
                this.top = top;
            }

            public int getLeft() {
                return left;
            }

            public void setLeft(int left) {
                this.left = left;
            }

            public int getHeight() {
                return height;
            }

            public void setHeight(int height) {
                this.height = height;
            }
        }
    }
}
