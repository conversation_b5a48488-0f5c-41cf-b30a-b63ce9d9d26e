package com.czur.scanpro.ui.user

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.StopServiceEvent
import com.czur.scanpro.event.StopSyncTimeCountEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.MediumBoldTextView
import com.czur.scanpro.ui.component.NoHintEditText
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.sync.AutoSyncTimeCountService
import com.czur.scanpro.ui.sync.SyncService
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator
import io.realm.Realm
import org.greenrobot.eventbus.EventBus


/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class UserRemoveAccountTwoActivity : BaseActivity(), View.OnClickListener {
    private var currentTime: Long = 0

    private lateinit var userPreferences: UserPreferences
    private lateinit var httpManager: HttpManager
    private lateinit var realm: Realm

    private var timeCount: TimeCount? = null
    private var codeHasContent = false
    private var userId: String? = null
    private var channel: String? = null

    //0：默认，1：图片浏览
    private var type: Int = 0

    private lateinit var normalTitle: TextView
    private lateinit var backBtn: ImageView
    private lateinit var cancel_account_btn: MediumBoldTextView
    private lateinit var getCodeBtn: MediumBoldTextView
    private lateinit var register_account_edt: NoHintEditText
    private lateinit var register_code_edt: NoHintEditText
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_remove_account_two)
        initView()
        initComponent()
        registerEvent()
    }

    private fun initView() {
        normalTitle = findViewById(R.id.normalTitle)
        backBtn = findViewById(R.id.backBtn)
        cancel_account_btn = findViewById(R.id.cancel_account_btn)
        getCodeBtn = findViewById(R.id.getCodeBtn)
        register_account_edt = findViewById(R.id.register_account_edt)
        register_code_edt = findViewById(R.id.register_code_edt)

    }

    private fun initComponent() {
        normalTitle.text = getString(R.string.user_remove_account)
        userPreferences = UserPreferences.getInstance(this)
        httpManager = HttpManager.getInstance()
        realm = Realm.getDefaultInstance()
        type = intent.getIntExtra("type", 0)
        userId = userPreferences.userId
        channel = userPreferences.channel

    }


    private fun registerEvent() {
        backBtn.setOnClickListener(this)
        cancel_account_btn.setOnClickListener(this)
        getCodeBtn.setOnClickListener(this)

        register_account_edt.addTextChangedListener(accountTextWatcher)
//        register_password_edt.addTextChangedListener(pswTextWatcher)
        register_code_edt.addTextChangedListener(codeTextWatcher)

    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }

            R.id.getCodeBtn -> {
                validatorAccountAndPassword()
            }

            R.id.cancel_account_btn -> {
                mobileRegister()
            }

            else -> {
            }
        }
    }

    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private fun timeCountBegin() {
        timeCount = TimeCount(60000, 1000)
        timeCount?.start()
    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    internal inner class TimeCount(millisInFuture: Long, countDownInterval: Long) :
        CountDownTimer(millisInFuture, countDownInterval) {

        override fun onFinish() {
            val drawable =
                DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat())
                    .setSolidColor(resources.getColor(R.color.red_de4d4d)).build()
            getCodeBtn.background = drawable

            getCodeBtn.setText(R.string.gain)
            getCodeBtn.isClickable = true
            getCodeBtn.isSelected = true


        }

        override fun onTick(millisUntilFinished: Long) {
            val drawable =
                DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat())
                    .setSolidColor(resources.getColor(R.color.gray_e1)).build()
            getCodeBtn.background = drawable
            getCodeBtn.isClickable = false
            getCodeBtn.text = (millisUntilFinished / 1000).toString() + " s"
            getCodeBtn.isSelected = false

        }

    }

    /**
     * @des: 验证手机号或者邮箱 和密码
     * @params:[]
     * @return:void
     */
    private fun validatorAccountAndPassword() {
        val accountStr = register_account_edt.text.toString()
        if (Validator.isEmpty(accountStr)) {
            showMessage(R.string.user_input_phone)
        } else if (accountStr != userPreferences.userMobile) {
            showMessage(R.string.user_input_current_account_info)
        } else {
            if (RegexUtils.isMobileExact(accountStr)) {
                getMobileCode(accountStr)
            } else {
                showMessage(R.string.toast_mobile_format_wrong)
            }
        }
    }

    /**
     * @des: 手机注册前置判断
     * @params:
     * @return:
     */
    private fun mobileRegister() {
        val mobile = register_account_edt.text.toString()
//        val pwd = register_password_edt.text.toString()
        val mobileCode = register_code_edt.text.toString()
        if (mobile.isEmpty()) {
            showMessage(R.string.login_alert_phone_empty)
        } else if (!RegexUtils.isMobileExact(mobile)) {
            showMessage(R.string.toast_mobile_format_wrong)
        } else if (mobileCode.isEmpty()) {
            showMessage(R.string.login_alert_mail_code)
        } else if (mobileCode.length <= 5) {
            showMessage(R.string.edit_text_code_length)
        } else if (mobile != userPreferences.userMobile) {
            showMessage(R.string.user_input_current_account_info)
        } else {
            ScanProCommonPopup.Builder(this, CloudCommonPopupConstants.CANCEL_ACCOUNT_BUTTON)
                .setTitle(getString(R.string.prompt))
                .setMessage(getString(R.string.user_remove_account_alert))
                .setOnPositiveListener { dialog, _ ->
                    confirmIdentifyCode()
//                    cancel_account_btn?.isSelected = false
//                    cancel_account_btn?.isClickable = false
//                    cancel_account_btn?.isEnabled = false
                    dialog.dismiss()
                }
                .setOnNegativeListener { dialog, _ ->
                    dialog.dismiss()
                }
                .create()
                .show()
        }
    }

    /**
     * @des: 校验 验证码并注销
     * @params:
     * @return:
     */
    private fun confirmIdentifyCode() {
        httpManager.requestPassport().cancellationSubmit(
            Constants.SCAN_PRO, userPreferences.imei, userPreferences.channel,
            userPreferences.userId,
            register_code_edt?.text.toString(),
            String::class.java,
            object : MiaoHttpManager.Callback<String?> {
                override fun onStart() {
                }

                override fun onError(e: java.lang.Exception) {
                    failedDelay(R.string.request_failed_alert)
                }

                override fun onResponse(entity: MiaoHttpEntity<String?>?) {
                    successDelay()
                }

                override fun onFailure(entity: MiaoHttpEntity<String?>?) {
                    when (entity?.code) {
                        MiaoHttpManager.STATUS_INVALID_CODE -> {
                            failedDelay(R.string.user_remove_account_code_fail)
                        }

                        MiaoHttpManager.STATUS_NOT_USER -> {
                            failedDelay(R.string.toast_user_no_exist)
                        }

                        MiaoHttpManager.STATUS_FAIL -> {
                            failedDelay(R.string.toast_internal_error)
                        }

                        else -> {
                            failedDelay(R.string.request_failed_alert)
                        }
                    }
                }
            })
    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private fun getMobileCode(accountStr: String) {
        httpManager.requestPassport()
            .mobileCode(accountStr, String::class.java, object : MiaoHttpManager.Callback<String> {
                override fun onStart() {}

                override fun onResponse(entity: MiaoHttpEntity<String>) {
                    LogUtils.d(Gson().toJson(entity))
                    timeCountBegin()
                    showMessage(R.string.toast_code_send)
                }

                override fun onFailure(entity: MiaoHttpEntity<String>) {
                    when (entity.code) {
                        MiaoHttpManager.STATUS_CODE_1_MIN -> {
                            showMessage(R.string.toast_code_1_min)
                        }

                        MiaoHttpManager.STATUS_5_MIN_4_TIME -> {
                            showMessage(R.string.toast_5_min_4_time)
                        }

                        MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY -> {
                            showMessage(R.string.toast_5_time_in_one_day)
                        }

                        else -> {
                            showMessage(R.string.request_failed_alert)
                        }
                    }

                }

                override fun onError(e: Exception) {
                    showMessage(R.string.request_failed_alert)
                }
            })
    }


    override fun onDestroy() {
        super.onDestroy()
        timeCount?.cancel()
    }

    private val codeTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            codeHasContent = s.isNotEmpty()
            checkRegisterButtonToClick()
        }

        override fun beforeTextChanged(
            s: CharSequence, start: Int, count: Int,
            after: Int
        ) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            codeHasContent = s.isNotEmpty()
            checkRegisterButtonToClick()
        }
    }

    private val accountTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            judgingRegister(s)

        }

        override fun beforeTextChanged(
            s: CharSequence, start: Int, count: Int,
            after: Int
        ) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            judgingRegister(s)
        }
    }

    private fun judgingRegister(s: CharSequence) {
        checkRegisterButtonToClick()
    }

    /**
     * @des: 检查注销按钮是否可以点击
     * @params:
     * @return:
     */
    private fun checkRegisterButtonToClick() {
        val accountIsNotEmpty = Validator.isNotEmpty(register_account_edt.text.toString())
        if (accountIsNotEmpty && codeHasContent) {
            val drawable =
                DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat())
                    .setSolidColor(resources.getColor(R.color.black_24)).build()
            cancel_account_btn.background = drawable
            cancel_account_btn.isSelected = true
            cancel_account_btn.setTextColor(resources.getColor(R.color.red_de4d4d))
            cancel_account_btn.isClickable = true
        } else {
            val drawable =
                DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat())
                    .setSolidColor(resources.getColor(R.color.gray_e4)).build()
            cancel_account_btn.background = drawable
            cancel_account_btn.isSelected = false
            cancel_account_btn.setTextColor(resources.getColor(R.color.white))
            cancel_account_btn.isClickable = false
        }
    }


    private fun failedDelay(failedText: Int) {
        Thread {
            try {
                val sleepTime: Long = if (System.currentTimeMillis() - currentTime < 1000) {
                    1000 - (System.currentTimeMillis() - currentTime)
                } else {
                    1
                }
                LogUtils.i("failed sleep time: $sleepTime")
                Thread.sleep(sleepTime)
                runOnUiThread {
                    showMessage(failedText)
                }
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }.start()
    }


    private fun successDelay() {
        Thread {
            try {
                val sleepTime: Long = if (System.currentTimeMillis() - currentTime < 1000) {
                    1000 - (System.currentTimeMillis() - currentTime)
                } else {
                    1
                }
                LogUtils.i("success sleep time: $sleepTime")
                Thread.sleep(sleepTime)
                runOnUiThread { confirmSuccess() }
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }.start()
    }


    private fun confirmSuccess() {
        EventBus.getDefault().post(StopServiceEvent(EventType.STOP_SYNC))
        EventBus.getDefault().post(StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT))
        if (ServiceUtils.isServiceRunning(SyncService::class.java)) {
            ServiceUtils.stopService(SyncService::class.java)
        }
        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
            ServiceUtils.stopService(AutoSyncTimeCountService::class.java)
        }
        logout()

        showMessage(R.string.user_remove_account_success)

//        ActivityUtils.finishAllActivities()
//        val intent = Intent(Utils.getApp(), LoginActivity::class.java)
//        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
//        ActivityUtils.startActivity(intent)
        if (ActivityUtils.isActivityExistsInStack(UserActivity::class.java)) {
            ActivityUtils.finishToActivity(UserActivity::class.java, false)
        } else {
            val intent = Intent(this, UserActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            ActivityUtils.startActivity(intent)
        }
    }
}
