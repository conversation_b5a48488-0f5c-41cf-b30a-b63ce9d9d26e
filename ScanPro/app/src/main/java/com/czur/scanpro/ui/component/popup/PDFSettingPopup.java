package com.czur.scanpro.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.preferences.UserPreferences;
import com.noober.background.drawable.DrawableCreator;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class PDFSettingPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public PDFSettingPopup(Context context) {
        super(context);
    }

    public PDFSettingPopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String message;
        private String title;
        private View contentsView;
        private int type = 0;
        private int quality = 1;
        private int isHorizontal = 0;
        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(DialogInterface.OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setOnPdfClickListener(OnPdfClickListener onPdfClickListener) {
            this.onPdfClickListener = onPdfClickListener;
            return this;
        }

        public Builder setOnNegativeListener(DialogInterface.OnClickListener onNegativeListener) {
            this.onNegativeListener = onNegativeListener;
            return this;
        }

        public Builder setOnDismissListener(OnDismissListener onDismissListener) {
            this.onDismissListener = onDismissListener;
            return this;
        }

        /**
         * 点击事件接口
         **/
        public interface OnPdfClickListener {
            /**
             * @param type
             * @param quality
             * @param isHorizontal
             */
            void onClick(int type, int quality, int isHorizontal, DialogInterface dialog);
        }

        private OnPdfClickListener onPdfClickListener;

        private void setOnPdfClickListener1(OnPdfClickListener onPdfClickListener) {
            this.onPdfClickListener = onPdfClickListener;

        }

        public PDFSettingPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final PDFSettingPopup dialog = new PDFSettingPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            BarUtils.setStatusBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode( dialog.getWindow(), true);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final PDFSettingPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.6f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.pdf_setting_popup, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);


            final RelativeLayout createPdfTab = (RelativeLayout) layout.findViewById(R.id.create_pdf_tab);
            final TextView createPdfTabTv = (TextView) layout.findViewById(R.id.create_pdf_tab_tv);
            final View createPdfTabLine = (View) layout.findViewById(R.id.create_pdf_tab_line);
            final RelativeLayout pdfSettingTab = (RelativeLayout) layout.findViewById(R.id.pdf_setting_tab);
            final TextView pdfSettingTabTv = (TextView) layout.findViewById(R.id.pdf_setting_tab_tv);
            final View pdfSettingTabLine = (View) layout.findViewById(R.id.pdf_setting_tab_line);
            final RelativeLayout createPdfRl = (RelativeLayout) layout.findViewById(R.id.create_pdf_rl);
            final EditText createPdfEdt = (EditText) layout.findViewById(R.id.create_pdf_edt);
            final LinearLayout pdfSettingRl = (LinearLayout) layout.findViewById(R.id.pdf_setting_rl);
            final LinearLayout autoLl = (LinearLayout) layout.findViewById(R.id.auto_ll);
            final ImageView autoRight = (ImageView) layout.findViewById(R.id.auto_right);
            final LinearLayout A3Ll = (LinearLayout) layout.findViewById(R.id.A3_ll);
            final ImageView A3Right = (ImageView) layout.findViewById(R.id.A3_right);
            final LinearLayout A4Ll = (LinearLayout) layout.findViewById(R.id.A4_ll);
            final ImageView A4Right = (ImageView) layout.findViewById(R.id.A4_right);
            final LinearLayout A5Ll = (LinearLayout) layout.findViewById(R.id.A5_ll);
            final ImageView A5Right = (ImageView) layout.findViewById(R.id.A5_right);
            final LinearLayout B5Ll = (LinearLayout) layout.findViewById(R.id.B5_ll);
            final ImageView B5Right = (ImageView) layout.findViewById(R.id.B5_right);
            final LinearLayout horizontalLl = (LinearLayout) layout.findViewById(R.id.horizontal_ll);
            final ImageView horizontalRight = (ImageView) layout.findViewById(R.id.horizontal_right);
            final LinearLayout verticalLl = (LinearLayout) layout.findViewById(R.id.vertical_ll);
            final ImageView verticalRight = (ImageView) layout.findViewById(R.id.vertical_right);
            final LinearLayout standardLl = (LinearLayout) layout.findViewById(R.id.standard_ll);
            final ImageView standardRight = (ImageView) layout.findViewById(R.id.standard_right);
            final LinearLayout bestLl = (LinearLayout) layout.findViewById(R.id.best_ll);
            final ImageView bestRight = (ImageView) layout.findViewById(R.id.best_right);



            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);
            ImageView negativeBtn = (ImageView) layout.findViewById(R.id.close_btn);
            final UserPreferences userPreferences = UserPreferences.getInstance(context);
            if (userPreferences.getPdfType() == 0) {
                setTypeAuto(autoRight, A3Right, A4Right, A5Right, B5Right);
            } else if (userPreferences.getPdfType() == 1) {
                setTypeA3(autoRight, A3Right, A4Right, A5Right, B5Right);
            } else if (userPreferences.getPdfType() == 2) {

                setTypeA4(autoRight, A3Right, A4Right, A5Right, B5Right);

            } else if (userPreferences.getPdfType() == 3) {
                setTypeA5(autoRight, A3Right, A4Right, A5Right, B5Right);
            } else if (userPreferences.getPdfType() == 7) {
                setTypeB5(autoRight, A3Right, A4Right, A5Right, B5Right);
            }

            if (userPreferences.getPdfIsHorizontal() == 0) {
                verticalRight.setVisibility(View.VISIBLE);
                horizontalRight.setVisibility(View.GONE);
            } else {
                verticalRight.setVisibility(View.GONE);
                horizontalRight.setVisibility(View.VISIBLE);
            }

            if (userPreferences.getPdfQuality() == 1) {
                standardRight.setVisibility(View.VISIBLE);
                bestRight.setVisibility(View.GONE);
            } else {
                standardRight.setVisibility(View.GONE);
                bestRight.setVisibility(View.VISIBLE);
            }


            createPdfEdt.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                }

                @Override
                public void onTextChanged(CharSequence s, int i, int i1, int i2) {

                    checkToClick(s.toString().length() <= 0, positiveBtn);
                }

                @Override
                public void afterTextChanged(Editable s) {
                    checkToClick(s.toString().length() <= 0, positiveBtn);
                }
            });
            createPdfTab.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    createPdfTabTv.setTextColor(context.getResources().getColor(R.color.red_de4d4d));
                    createPdfTabLine.setVisibility(View.VISIBLE);
                    pdfSettingTabTv.setTextColor(context.getResources().getColor(R.color.black_24));
                    pdfSettingTabLine.setVisibility(View.GONE);
                    createPdfRl.setVisibility(View.VISIBLE);
                    pdfSettingRl.setVisibility(View.GONE);
                }
            });
            pdfSettingTab.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    createPdfTabTv.setTextColor(context.getResources().getColor(R.color.black_24));
                    createPdfTabLine.setVisibility(View.GONE);
                    pdfSettingTabTv.setTextColor(context.getResources().getColor(R.color.red_de4d4d));
                    pdfSettingTabLine.setVisibility(View.VISIBLE);
                    createPdfRl.setVisibility(View.GONE);
                    pdfSettingRl.setVisibility(View.VISIBLE);
                }
            });
            autoLl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    type = 0;
                    userPreferences.setPdfType(0);
                    setTypeAuto(autoRight, A3Right, A4Right, A5Right, B5Right);
                }
            });
            A3Ll.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    type = 1;
                    userPreferences.setPdfType(1);
                    setTypeA3(autoRight, A3Right, A4Right, A5Right, B5Right);
                }
            });
            A4Ll.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    type = 2;
                    userPreferences.setPdfType(2);
                    setTypeA4(autoRight, A3Right, A4Right, A5Right, B5Right);
                }
            });
            A5Ll.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    type = 3;
                    userPreferences.setPdfType(3);
                    setTypeA5(autoRight, A3Right, A4Right, A5Right, B5Right);
                }
            });
            B5Ll.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    type = 7;
                    userPreferences.setPdfType(7);
                    setTypeB5(autoRight, A3Right, A4Right, A5Right, B5Right);
                }
            });

            standardLl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    quality = 1;
                    userPreferences.setPdfQuality(1);
                    standardRight.setVisibility(View.VISIBLE);
                    bestRight.setVisibility(View.GONE);
                }
            });
            bestLl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    quality = 2;
                    userPreferences.setPdfQuality(2);
                    standardRight.setVisibility(View.GONE);
                    bestRight.setVisibility(View.VISIBLE);
                }
            });
            verticalLl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    isHorizontal = 0;
                    userPreferences.setPdfIsHorizontal(0);
                    verticalRight.setVisibility(View.VISIBLE);
                    horizontalRight.setVisibility(View.GONE);
                }
            });
            horizontalLl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    isHorizontal = 1;
                    userPreferences.setPdfIsHorizontal(1);
                    verticalRight.setVisibility(View.GONE);
                    horizontalRight.setVisibility(View.VISIBLE);
                }
            });

            if (constants.getPositiveBtn() > 0) {
//                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
            } else {
                positiveBtn.setVisibility(View.GONE);
            }

            positiveBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onPdfClickListener != null) {
                        onPdfClickListener.onClick(type, quality, isHorizontal, dialog);
                    }
                }
            });

            negativeBtn.setOnClickListener(new android.view.View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });


            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener);
            }

            return layout;
        }

        private void checkToClick(boolean isEmpty, TextView positiveBtn) {
            if (!isEmpty) {
                Drawable drawable = new DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f)).setSolidColor(context.getResources().getColor(R.color.black_24)).build();
                positiveBtn.setBackground(drawable);
                positiveBtn.setSelected(true);
                positiveBtn.setTextColor(context.getResources().getColor(R.color.white));
                positiveBtn.setClickable(true);
            } else {
                Drawable drawable = new DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f)).setSolidColor(context.getResources().getColor(R.color.gray_e4)).build();
                positiveBtn.setBackground(drawable);
                positiveBtn.setSelected(false);
                positiveBtn.setTextColor(context.getResources().getColor(R.color.white));
                positiveBtn.setClickable(false);
            }
        }
        private  void setTypeAuto(ImageView autoRight, ImageView a3Right, ImageView a4Right, ImageView a5Right, ImageView b5Right) {
            autoRight.setVisibility(View.VISIBLE);
            a3Right.setVisibility(View.GONE);
            a4Right.setVisibility(View.GONE);
            a5Right.setVisibility(View.GONE);
            b5Right.setVisibility(View.GONE);
        }
        private void setTypeB5(ImageView autoRight, ImageView a3Right, ImageView a4Right, ImageView a5Right, ImageView b5Right) {
            autoRight.setVisibility(View.GONE);
            a3Right.setVisibility(View.GONE);
            a4Right.setVisibility(View.GONE);
            a5Right.setVisibility(View.GONE);
            b5Right.setVisibility(View.VISIBLE);
        }

        private void setTypeA5(ImageView autoRight, ImageView a3Right, ImageView a4Right, ImageView a5Right, ImageView b5Right) {
            autoRight.setVisibility(View.GONE);
            a3Right.setVisibility(View.GONE);
            a4Right.setVisibility(View.GONE);
            a5Right.setVisibility(View.VISIBLE);
            b5Right.setVisibility(View.GONE);
        }

        private void setTypeA4(ImageView autoRight, ImageView a3Right, ImageView a4Right, ImageView a5Right, ImageView b5Right) {
            autoRight.setVisibility(View.GONE);
            a3Right.setVisibility(View.GONE);
            a4Right.setVisibility(View.VISIBLE);
            a5Right.setVisibility(View.GONE);
            b5Right.setVisibility(View.GONE);
        }

        private void setTypeA3(ImageView autoRight, ImageView a3Right, ImageView a4Right, ImageView a5Right, ImageView b5Right) {
            autoRight.setVisibility(View.GONE);
            a3Right.setVisibility(View.VISIBLE);
            a4Right.setVisibility(View.GONE);
            a5Right.setVisibility(View.GONE);
            b5Right.setVisibility(View.GONE);
        }
    }
}
