package com.czur.scanpro.adapter

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.SizeUtils
import com.blankj.utilcode.util.UriUtils
import com.czur.scanpro.R
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.fresco.cache.CustomImageRequest
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.drawee.view.SimpleDraweeView
import com.facebook.imagepipeline.request.ImageRequestBuilder
import java.util.*
import kotlin.collections.ArrayList


/**
 * Created by Yz on 2019/1/5.
 * Email：<EMAIL>
 */
class MainFileAdapter
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<DocEntity>?, private var isCategory: Boolean?) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var isCheckedMap: LinkedHashMap<String, Boolean>? = LinkedHashMap()
    private var isSelected: Boolean = false

   // var isAnim: Boolean = false
    var count: Int = 0
    val list = ArrayList<String>()


    companion object {
        private val ITEM_TYPE_DOC = 0
        private val ITEM_TYPE_FOOTER = 1
    }


    fun refreshData(docs: List<DocEntity>) {
        this.datas = docs
        notifyDataSetChanged()
    }

    fun refreshData(books: List<DocEntity>, isSelectItem: Boolean, isCheckedMap: LinkedHashMap<String, Boolean>?) {
        this.isSelected = isSelectItem
        this.datas = books
        this.isCheckedMap = isCheckedMap
        this.list.clear()
        val iter = isCheckedMap!!.entries.iterator()
        while (iter.hasNext()) {
            val entry = iter.next()
            list.add(entry.key)
        }
        notifyDataSetChanged()
    }


    fun refreshData(datas: List<DocEntity>, isAnim: Boolean, count: Int) {
        this.datas = datas
      //  this.isAnim = isAnim
        this.count = count
        notifyDataSetChanged()
    }

    fun refreshData(isSelected: Boolean) {
        this.isSelected = isSelected
        notifyDataSetChanged()
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == ITEM_TYPE_DOC) {
            val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_file, parent, false)
            return DocHolder(view)
        } else {
            val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_file, parent, false)
            return FooterHolder(view)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is DocHolder) {
            holder.mItem = datas!![position]
            // 计算item的长宽
            val itemLayoutParams = holder.fileInnerItem.layoutParams
            itemLayoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(64f)) / 3
            itemLayoutParams.height = itemLayoutParams.width
            holder.fileInnerItem.layoutParams = itemLayoutParams
//            if (isCategory!!) {
//                if (isAnim && (position >= datas!!.size - count)) {
//                    addItemAnim(holder, position, isCategory)
//                }
//            } else {
//                if (isAnim && (position <= count - 1)) {
//                    addItemAnim(holder, position, isCategory)
//                }
//            }

            if (isSelected) {
                holder.tvIndex.isEnabled = true
                holder.tvIndex.tag = holder.mItem!!.fileID
                holder.tvIndex.text = (position + 1).toString()
            } else {
                holder.tvIndex.isEnabled = false
                holder.tvIndex.text = ""
            }

            holder.itemView.setOnClickListener {
                if (isSelected) {
                    if (!holder.tvIndex.isEnabled) {
                        if (!isCheckedMap!!.containsKey(holder.tvIndex.tag)) {
                            //选中时添加
                            isCheckedMap!![holder.mItem!!.fileID!!] = true
                        }
                    } else {
                        if (isCheckedMap!!.containsKey(holder.tvIndex.tag)) {
                            //没选中时移除
                            isCheckedMap!!.remove(holder.mItem!!.fileID)
                        }
                    }
                    this.list.clear()
                    val iter = isCheckedMap!!.entries.iterator()
                    while (iter.hasNext()) {
                        val entry = iter.next()
                        list.add(entry.key)
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener!!.onItemCheck(position, holder.mItem!!, isCheckedMap!!, datas!!.size)
                    }
                    notifyDataSetChanged()
                } else {
                    if (onItemClickListener != null) {
                        onItemClickListener!!.onDocEntityClick(holder.mItem!!, position, holder.tvIndex)
                    }
                }
            }

            if (isCheckedMap != null) {
                holder.tvIndex.isEnabled = isCheckedMap!!.containsKey(holder.mItem!!.fileID)
                if (holder.tvIndex.isEnabled) {
                    for (index in 0 until list.size) {
                        if (list[index] == holder.mItem!!.fileID!!) {
                            holder.tvIndex.text = (index + 1).toString()
                            holder.bgImg.visibility = View.VISIBLE
                        }
                    }
                } else {
                    holder.tvIndex.isEnabled = false
                    holder.tvIndex.text = ""
                    holder.bgImg.visibility = View.GONE
                }
            } else {
                holder.tvIndex.isEnabled = false
                holder.tvIndex.text = ""
                holder.bgImg.visibility = View.GONE
            }

            holder.fileImage.setImageURI("")
            val controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(CustomImageRequest(ImageRequestBuilder.newBuilderWithSource(UriUtils.file2Uri(FileUtils.getFileByPath(holder.mItem!!.processSmallImagePath)))))
                    .setOldController(holder.fileImage.controller)
                    .build()
            holder.fileImage.controller = controller


        }

    }

    /**
     * @des: 条目添加动画
     * @params:
     * @return:
     */

    private fun addItemAnim(mHolder: DocHolder, position: Int, isCategory: Boolean?) {
        val animatorSet = AnimatorSet()//组合动画
        val scaleX = ObjectAnimator.ofFloat(mHolder.fileInnerItem, "scaleX", 0.1f, 1.2f, 0.9f, 1.0f)
        val scaleY = ObjectAnimator.ofFloat(mHolder.fileInnerItem, "scaleY", 0.1f, 1.2f, 0.9f, 1.0f)
        animatorSet.duration = 400
        animatorSet.interpolator = DecelerateInterpolator()

        animatorSet.play(scaleX).with(scaleY)//两个动画同时开始
        animatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)

            }

            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
            }
        })

        val animatorSet1 = AnimatorSet()//组合动画
        val scaleX1 = ObjectAnimator.ofFloat(mHolder.fileInnerItem, "scaleX", 0f, 0f)
        val scaleY1 = ObjectAnimator.ofFloat(mHolder.fileInnerItem, "scaleY", 0f, 0f)
        animatorSet1.duration = 500
        animatorSet1.interpolator = AccelerateInterpolator()
        animatorSet1.play(scaleX1).with(scaleY1)//两个动画同时开始
        animatorSet1.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                animatorSet.start()
            }

            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
            }
        })
        animatorSet1.start()
//        if (isCategory!!) {
//            if (position == datas!!.size - 1) {
//                isAnim = false
//            }
//        } else {
//            if (position == count - 1) {
//                isAnim = false
//            }
//        }

    }

    override fun getItemViewType(position: Int): Int = ITEM_TYPE_DOC

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int {
        return datas!!.size
    }


    private inner class DocHolder internal constructor(val mView: View) : RecyclerView.ViewHolder(mView) {
        internal var mItem: DocEntity? = null
        internal var fileImage: SimpleDraweeView = mView.findViewById<View>(R.id.file_image) as SimpleDraweeView
        internal var fileInnerItem: RelativeLayout = mView.findViewById<View>(R.id.file_inner_item) as RelativeLayout
        internal var bgImg: SimpleDraweeView = mView.findViewById<View>(R.id.bg_img) as SimpleDraweeView
        internal var tvIndex: TextView = mView.findViewById(R.id.tv_index) as TextView
    }


    private inner class FooterHolder(val mView: View) : RecyclerView.ViewHolder(mView)

    private var onItemClickListener: OnItemClickListener? = null

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    interface OnItemClickListener {
        fun onDocEntityClick(DocEntity: DocEntity, position: Int, tvIndex: TextView)
    }

    private var onItemLongClickListener: OnItemLongClickListener? = null

    fun setOnItemLongClickListener(onItemLongClickListener: OnItemLongClickListener) {
        this.onItemLongClickListener = onItemLongClickListener
    }

    interface OnItemLongClickListener {
        fun onDocEntityLongClick(position: Int, DocEntity: DocEntity, isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int)
    }


    private var onItemCheckListener: OnItemCheckListener? = null

    fun setOnItemCheckListener(onItemCheckListener: OnItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener
    }

    interface OnItemCheckListener {
        fun onItemCheck(position: Int, DocEntity: DocEntity, isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int)

    }

}
