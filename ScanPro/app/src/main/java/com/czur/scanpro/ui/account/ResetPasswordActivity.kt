package com.czur.scanpro.ui.account

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityRegisterBinding
import com.czur.scanpro.databinding.ActivityResetPasswordBinding
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.MD5Utils
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator

/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class ResetPasswordActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityResetPasswordBinding by lazy{
        ActivityResetPasswordBinding.inflate(layoutInflater)
    }

    private val userPreferences = UserPreferences.getInstance(this)
    private var accountContent = false
    private var account: String? = null
    private var resetCode: String? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this,true)
        setContentView(R.layout.activity_reset_password)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        account = intent.getStringExtra("account")
        resetCode = intent.getStringExtra("resetCode")

        binding.firstPswEdt.addTextChangedListener(firstPswTextWatcher)
    }


    private fun registerEvent() {
        binding.registerTopBar.backBtn.setOnClickListener(this)
        binding.confirmBtn.setOnClickListener(this)
    }


    /**
     * @des: 确认新密码
     * @params:[]
     * @return:void
     */
    private fun confirmPassword() {
        val firstPassword = binding.firstPswEdt.text.toString()
            if (firstPassword.length <= 5) {
                showMessage(R.string.login_alert_pwd_length)
            } else {
                LogUtils.e(resetCode)
                KeyboardUtils.hideSoftInput(this)
                HttpManager.getInstance().requestPassport().againPwd(Constants.SCAN_PRO, userPreferences.imei, userPreferences.channel,
                        resetCode, MD5Utils.md5(firstPassword), String::class.java, object : MiaoHttpManager.Callback<String>{
                    override fun onStart() {
                        showProgressDialog(false)
                    }

                    override fun onResponse(entity: MiaoHttpEntity<String>) {
                        hideProgressDialog()
                        LogUtils.iTag("set new password success", Gson().toJson(entity))
                        showMessage(R.string.toast_pwd_success)
//                        EventBus.getDefault().post(LoginEvent(EventType.RESET_PASSWORD))
                        //跳转到ResetPasswordActivity
                      ActivityUtils.finishToActivity(LoginActivity::class.java,false)
                    }

                    override fun onFailure(entity: MiaoHttpEntity<String>) {
                        hideProgressDialog()

                        if (entity.code === MiaoHttpManager.STATUS_TIME_OUT) {
                            showMessage(R.string.toast_operate_time_out)
                        } else {
                            showMessage(R.string.request_failed_alert)
                        }
                    }

                    override  fun onError(e: Exception) {
                        hideProgressDialog()
                        showMessage(R.string.request_failed_alert)
                    }
                })
            }


    }


    private val firstPswTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            if (s.length > 0) {
                accountContent = true
            } else {
                accountContent = false
            }

            checkRegisterButtonToClick()

        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            if (s.length > 0) {
                accountContent = true
            } else {
                accountContent = false
            }

            checkRegisterButtonToClick()
        }
    }


    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkRegisterButtonToClick() {

        val accountIsNotEmpty = Validator.isNotEmpty(binding.firstPswEdt.text.toString())

        if (accountIsNotEmpty && accountContent) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
            binding.confirmBtn.background = drawable
            binding.confirmBtn.isSelected = true
            binding.confirmBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
            binding.confirmBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
            binding.confirmBtn.background = drawable
            binding.confirmBtn.isSelected = false
            binding.confirmBtn.setTextColor(resources.getColor(R.color.white))
            binding.confirmBtn.isClickable = false
        }
    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }

            R.id.confirmBtn -> {
                confirmPassword()
            }
            else -> {
            }
        }
    }


}
