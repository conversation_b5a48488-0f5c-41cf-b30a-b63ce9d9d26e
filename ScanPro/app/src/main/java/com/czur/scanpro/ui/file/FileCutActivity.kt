package com.czur.scanpro.ui.file

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowInsets
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.databinding.ActivityCardEditBinding
import com.czur.scanpro.databinding.ActivityEditCutBinding
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.EditEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.util.*

/**
 * Created by Yz on 2019/4/9.
 * Email：<EMAIL>
 */
class FileCutActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityEditCutBinding by lazy{
        ActivityEditCutBinding.inflate(layoutInflater)
    }
    
    private var fileId: String? = null
    private var docEntity: DocEntity? = null
    private var realm: Realm? = null

    private var isRun = false
    private var x: Int = 0
    private var y: Int = 0
    private var width: Int = 0
    private var height: Int = 0
    private var originalBitmap: Bitmap? = null
    private val rotated: Int = 0
    private var position: Int = 0
    private var userPreferences: UserPreferences? = null
    private var url: String? = null
    private var baseUrl: String? = null

    private var bigBitmap: Bitmap? = null

    private var dirPath: String? = null

    private var bigImagePath: String? = null
    private var smallImagePath: String? = null
    private var baseImagePath: String? = null
    private var smallBasePath: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(binding.root)
        fileId = intent.getStringExtra("fileId")
        userPreferences = UserPreferences.getInstance(this)
        realm = Realm.getDefaultInstance()
        url = intent.getStringExtra("url")
        baseUrl = intent.getStringExtra("baseUrl")

        binding.cancelBtn.setOnClickListener(this)
        binding.commitBtn.setOnClickListener(this)
        binding.cropImageView.setImageBitmap(ImageUtils.getBitmap(url,2048,2048), ImageUtils.getBitmap(baseUrl,2048,2048))

        docEntity = realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("fileID", fileId).findFirst()
        dirPath = <EMAIL> + File.separator + userPreferences!!.userId + File.separator
        baseImagePath = docEntity!!.baseImagePath
        smallBasePath = docEntity!!.baseSmallImagePath

        bigImagePath = docEntity!!.processImagePath
        smallImagePath = docEntity!!.processSmallImagePath


        binding.root.setOnApplyWindowInsetsListener(object: View.OnApplyWindowInsetsListener {
            override fun onApplyWindowInsets(p0: View, p1: WindowInsets): WindowInsets {
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
                    binding.root.setPadding(binding.cutTopBar.getPaddingLeft()
                        ,binding.root.getPaddingTop(),binding.root.getPaddingRight()
                        ,p1.getSystemWindowInsets().bottom);
                }
                return p1;
            }
        });
    }


    override fun onClick(v: View) {
        when (v.id) {
            R.id.cancelBtn -> {
                ActivityUtils.finishActivity(this)
            }

            R.id.commitBtn -> {
                cropImage()
            }

        }
    }

    private fun cropImage() {
        isRun = false
        val cropped = binding.cropImageView.croppedImage

        val baseCropped = binding.cropImageView.baseCroppedImage
        val points = binding.cropImageView.cropPoints
//        val originalWidth = originalBitmap!!.width
//        val originalHeight = originalBitmap!!.height
        when (rotated) {
            0 -> {
                x = points[0].toInt()
                y = points[1].toInt()
                width = cropped.width
                height = cropped.height
            }
            90 -> {
                x = points[2].toInt()
                y = points[3].toInt()
                width = cropped.height
                height = cropped.width
            }
            180 -> {
                x = points[4].toInt()
                y = points[5].toInt()
                width = cropped.width
                height = cropped.height
            }
            270 -> {
                x = points[6].toInt()
                y = points[7].toInt()
                width = cropped.height
                height = cropped.width
            }
            360 -> {
                x = points[0].toInt()
                y = points[1].toInt()
                width = cropped.width
                height = cropped.height
            }
            else -> {
            }
        }
        LogUtils.i(cropped.width, cropped.height)
        val imagePath = docEntity!!.processImagePath
        val imageBasePath = docEntity!!.baseImagePath
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                ImageUtils.save(cropped, imagePath, Bitmap.CompressFormat.JPEG)
                ImageUtils.save(baseCropped, imageBasePath, Bitmap.CompressFormat.JPEG)
                makeSmallImg(false)
                makeSmallImg(true)

                return null
            }

            override fun onSuccess(path: Void?) {
                hideProgressDialog()
                EventBus.getDefault().post(EditEvent(EventType.CUT))
                ActivityUtils.finishActivity(this@FileCutActivity)
                realm!!.executeTransaction {
                    docEntity!!.isDirty = 1
                    docEntity!!.uuid = UUID.randomUUID().toString()
                    docEntity!!.updateTime = getCurrentTime()
                }
                startAutoSync()

            }

            override fun onFail(t: Throwable?) {
                super.onFail(t)
                LogUtils.e(t)
            }
        })


    }

    /**
     * @des: 压缩并且保存小图
     * @params:
     * @return:
     */
    private fun makeSmallImg(isBase: Boolean): Boolean {
        if (FileUtils.createOrExistsDir(dirPath)) {
            val bitmap = ImageUtils.getBitmap(if (isBase) baseImagePath else bigImagePath,2048,2048)
            val scale = bitmap.width * 1.0 / bitmap.height
            val v = 300 * scale
            var smallBitmap: Bitmap? = null
            smallBitmap = ImageUtils.compressByScale(bitmap, v.toInt(), 300, true)
            val isSuccessSave = ImageUtils.save(smallBitmap, if (isBase) smallBasePath else smallImagePath, Bitmap.CompressFormat.JPEG, true)
            return isSuccessSave

        }
        return false
    }

    fun recycleBitmap() {
        if (null != bigBitmap) {
            //释放bitmap
            if (bigBitmap!!.isRecycled) {
                bigBitmap!!.recycle()
                bigBitmap = null
            }
        }
    }

    private fun getBigBitmap(src: Bitmap?): Bitmap? {
        if (src == null) {
            LogUtils.i("src is null")
            return src
        }
        var bitmap: Bitmap? = Bitmap.createBitmap(800, 800, Bitmap.Config.ARGB_8888)
        bitmap!!.eraseColor(Color.parseColor("#FFFFFF"))
        try {
            val cv = Canvas(bitmap)
            cv.drawBitmap(src, 0f, 0f, null)
            cv.save()
            cv.restore()
        } catch (e: Exception) {
            bitmap = null
            e.stackTrace
        }

        return bitmap
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        super.onActivityResult(requestCode, resultCode, data)

    }


    override fun onBackPressed() {
        if (!isRun) {
            super.onBackPressed()
        }
    }


}