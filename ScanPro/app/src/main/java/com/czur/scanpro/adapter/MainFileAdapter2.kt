package com.czur.scanpro.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.SizeUtils
import com.blankj.utilcode.util.UriUtils
import com.czur.scanpro.R
import com.czur.scanpro.entity.model.BeanDoEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.fresco.cache.CustomImageRequest
import com.czur.scanpro.ui.component.MediumBoldTextView
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.drawee.view.SimpleDraweeView
import com.facebook.imagepipeline.request.ImageRequestBuilder
import com.truizlop.sectionedrecyclerview.SectionedRecyclerViewAdapter
import java.util.*
import kotlin.collections.ArrayList


/**
 *
 */
class MainFileAdapter2
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<BeanDoEntity>?, private var isCategory: Boolean?) : SectionedRecyclerViewAdapter<MainFileAdapter2.MyHeaderViewHolder, MainFileAdapter2.MyItemViewHolder, RecyclerView.ViewHolder>() {
    private var isCheckedMap: LinkedHashMap<String, Boolean>? = LinkedHashMap()
    private var isSelected: Boolean = false

    // var isAnim: Boolean = false
    var count: Int = 0
    val list = ArrayList<String>()
    var lastTime = "0"
    private var sectionCount: Int = 0
    private var totalsize = 0

    companion object {
        private val ITEM_TYPE_DOC = 0
        private val ITEM_TYPE_FOOTER = 1
    }


    fun refreshData(docs: List<BeanDoEntity>) {
        this.datas = docs
        notifyDataSetChanged()
    }

    fun refreshData(books: List<BeanDoEntity>, isSelectItem: Boolean, isCheckedMap: LinkedHashMap<String, Boolean>?) {
        this.isSelected = isSelectItem
        this.datas = books
        this.isCheckedMap = isCheckedMap
        this.list.clear()
        val iter = isCheckedMap!!.entries.iterator()
        while (iter.hasNext()) {
            val entry = iter.next()
            list.add(entry.key)
        }
        notifyDataSetChanged()
    }


    fun refreshData(datas: List<BeanDoEntity>, isAnim: Boolean, count: Int) {
        this.datas = datas
//      //  this.isAnim = isAnim
        this.count = count
        notifyDataSetChanged()
    }

    fun refreshData(isSelected: Boolean) {
        this.isSelected = isSelected
        notifyDataSetChanged()
    }


    private var onItemClickListener: OnItemClickListener? = null

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    interface OnItemClickListener {
        fun onDocEntityClick(DocEntity: DocEntity, section: Int, position: Int, tvIndex: TextView)
    }

    private var onItemLongClickListener: OnItemLongClickListener? = null

    fun setOnItemLongClickListener(onItemLongClickListener: OnItemLongClickListener) {
        this.onItemLongClickListener = onItemLongClickListener
    }

    interface OnItemLongClickListener {
        fun onDocEntityLongClick(position: Int, DocEntity: DocEntity, isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int)
    }


    private var onItemCheckListener: OnItemCheckListener? = null

    fun setOnItemCheckListener(onItemCheckListener: OnItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener
    }

    interface OnItemCheckListener {
        fun onItemCheck(section: Int, position: Int, DocEntity: DocEntity, isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int)

    }

    private fun getAllCount(): Int{
        totalsize = 0
        for (i in 0 until  datas!!.size){
            totalsize =totalsize + datas!!.get(i).listDoc.size
        }
        return totalsize
    }

    /**
     * header的个数
     * @return
     */
    override fun getSectionCount(): Int {
        return datas!!.size
    }

    /**
     * 每个header或者footer中包含具体的内容个数
     * @return
     */
    override fun getItemCountForSection(section: Int): Int {
        return datas!!.get(section).listDoc!!.size
    }

    override fun hasFooterInSection(section: Int): Boolean {
        return false
    }

    override fun onCreateSectionHeaderViewHolder(parent: ViewGroup?, viewType: Int): MyHeaderViewHolder {
        val itemView = LayoutInflater.from(parent?.context)
                .inflate(R.layout.item_rv_header, parent, false)
        return MyHeaderViewHolder(itemView)
    }

    /**
     * HeaderViewHolder
     */
    inner class MyHeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        internal var tv_header_title = itemView.findViewById<View>(R.id.item_header_title) as TextView
    }


    override fun onCreateSectionFooterViewHolder(parent: ViewGroup?, viewType: Int): RecyclerView.ViewHolder? {
        return null
    }


    override fun onCreateItemViewHolder(parent: ViewGroup?, viewType: Int): MyItemViewHolder {

        val itemView = LayoutInflater.from(parent?.context)
                .inflate(R.layout.item_file_time, parent, false)
        return MyItemViewHolder(itemView)
    }

    /**
     * ItemViewHolder
     */
    inner class MyItemViewHolder constructor(itemView: View) : RecyclerView.ViewHolder(itemView) {
        internal var mItem: DocEntity? = null
        internal var fileImage: SimpleDraweeView = itemView.findViewById<View>(R.id.file_image2) as SimpleDraweeView
        internal var fileInnerItem: RelativeLayout = itemView.findViewById<View>(R.id.file_inner_item2) as RelativeLayout
        internal var bgImg: SimpleDraweeView = itemView.findViewById<View>(R.id.bg_img2) as SimpleDraweeView
        internal var tvIndex: TextView = itemView.findViewById(R.id.tv_index2) as TextView

    }

    override fun onBindSectionHeaderViewHolder(holder: MyHeaderViewHolder?, section: Int) {
        var header = datas!!.get(section).header
        val c = Calendar.getInstance()
        val year = c[Calendar.YEAR].toString()
        if (year == header.substring(0, 4)) {
            holder!!.tv_header_title.setText(header.substring(4).toInt().toString() + "月")
        } else {
            holder!!.tv_header_title.setText(header.substring(0, 4) + "年" + header.substring(4).toInt() + "月")
        }


    }

    override fun onBindSectionFooterViewHolder(holder: RecyclerView.ViewHolder?, section: Int) {
    }

    override fun onBindItemViewHolder(holder: MainFileAdapter2.MyItemViewHolder?, section: Int, position: Int) {
        var headerDocEntity = datas!!.get(section)
        if (headerDocEntity == null) return
        if (holder is MyItemViewHolder) {
            holder.mItem = headerDocEntity.listDoc?.get(position)


            // 计算item的长宽
            val itemLayoutParams = holder.fileInnerItem.layoutParams
            itemLayoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(64f)) / 3
            itemLayoutParams.height = itemLayoutParams.width
            holder.fileInnerItem.layoutParams = itemLayoutParams

            if (isSelected) {
                holder.tvIndex.isEnabled = true
                holder.tvIndex.tag = holder.mItem!!.fileID
                holder.tvIndex.text = (position + 1).toString()
            } else {
                holder.tvIndex.isEnabled = false
                holder.tvIndex.text = ""
            }

            holder.itemView.setOnClickListener {
                if (isSelected) {
                    if (!holder.tvIndex.isEnabled) {
                        if (!isCheckedMap!!.containsKey(holder.tvIndex.tag)) {
                            //选中时添加
                            isCheckedMap!![holder.mItem!!.fileID!!] = true
                        }
                    } else {
                        if (isCheckedMap!!.containsKey(holder.tvIndex.tag)) {
                            //没选中时移除
                            isCheckedMap!!.remove(holder.mItem!!.fileID)
                        }
                    }
                    this.list.clear()
                    val iter = isCheckedMap!!.entries.iterator()
                    while (iter.hasNext()) {
                        val entry = iter.next()
                        list.add(entry.key)
                    }
                    if (onItemCheckListener != null) {
                        getAllCount()
                        onItemCheckListener!!.onItemCheck(section, position, holder.mItem!!, isCheckedMap!!, totalsize)
                    }
                    notifyDataSetChanged()
                } else {
                    if (onItemClickListener != null) {
                        onItemClickListener!!.onDocEntityClick(holder.mItem!!, section, position, holder.tvIndex)
                    }
                }
            }

            if (isCheckedMap != null) {
                holder.tvIndex.isEnabled = isCheckedMap!!.containsKey(holder.mItem!!.fileID)
                if (holder.tvIndex.isEnabled) {
                    for (index in 0 until list.size) {
                        if (list[index] == holder.mItem!!.fileID!!) {
                            holder.tvIndex.text = (index + 1).toString()
                            holder.bgImg.visibility = View.VISIBLE
                        }
                    }
                } else {
                    holder.tvIndex.isEnabled = false
                    holder.tvIndex.text = ""
                    holder.bgImg.visibility = View.GONE
                }
            } else {
                holder.tvIndex.isEnabled = false
                holder.tvIndex.text = ""
                holder.bgImg.visibility = View.GONE
            }

            holder.fileImage.setImageURI("")
            val controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(CustomImageRequest(ImageRequestBuilder.newBuilderWithSource(UriUtils.file2Uri(FileUtils.getFileByPath(holder.mItem!!.processSmallImagePath)))))
                    .setOldController(holder.fileImage.controller)
                    .build()
            holder.fileImage.controller = controller


        }
    }


}
