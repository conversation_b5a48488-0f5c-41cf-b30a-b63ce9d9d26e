package com.czur.scanpro.adapter

import android.content.Context
import android.graphics.Typeface
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.TagEntity
import com.czur.scanpro.preferences.UserPreferences
import io.realm.Realm


/**
 * Created by Yz on 2019/1/5.
 * Email：<EMAIL>
 */
class MainTagAdapter
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<TagEntity>?, private var isSelected: Boolean) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    val realm = Realm.getDefaultInstance()

    companion object {
        private val ITEM_TYPE_TAG = 0
        private val ITEM_TYPE_ADD = 1
    }


    fun refreshData(Tags: List<TagEntity>, isSelected: Boolean) {
        this.isSelected = isSelected
        this.datas = Tags
        notifyDataSetChanged()

    }

    fun refreshData(isSelected: Boolean) {
        this.isSelected = isSelected
        notifyDataSetChanged()

    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == ITEM_TYPE_TAG) {
            val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_tag_normal, parent, false)
            return TagHolder(view)
        } else {
            val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_tag_add, parent, false)
            return AddHolder(view)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        if (holder is TagHolder) {
            holder.mItem = datas!![position]

            if (isSelected) {
                holder.tagItemName?.typeface = Typeface.DEFAULT
                holder.tagInnerItem?.setBackgroundResource(R.drawable.btn_rect_6_bg_gray_ec)
                if (holder.mItem?.isDefault == 0) {
                    holder.tagDeleteBtn?.visibility = View.VISIBLE
                    holder.tagItemName?.setTextColor(context!!.resources.getColor(R.color.black_24))
                    setNotDefaultNameText(holder)

                } else {
                    holder.tagDeleteBtn?.visibility = View.GONE
                    holder.tagItemName?.setTextColor(context!!.resources.getColor(R.color.gray_99))
                    setDefaultNameText(holder)

                }

            } else {

                if (holder.mItem?.isDefault == 1) {
                    setDefaultNameText(holder)
                    holder.tagInnerItem?.setBackgroundResource(R.drawable.btn_rect_6_bg_black_24)
                } else {
                    setNotDefaultNameText(holder)
                    holder.tagInnerItem?.setBackgroundResource(R.drawable.btn_rect_6_bg_red_d4)
                }

                holder.tagInnerItem?.isClickable = true
                holder.tagInnerItem?.isEnabled = true

                holder.tagItemName?.setTextColor(context!!.resources.getColor(R.color.white))
                holder.tagDeleteBtn?.visibility = View.GONE
            }


            holder.tagInnerItem!!.setOnClickListener {
                onItemClickListener!!.onTagClick(holder.mItem!!, position)
            }
            holder.tagDeleteBtn!!.setOnClickListener {
                onDeleteClickListener!!.onTagDeleteClick(holder.mItem, position)
            }
            holder.itemView.setOnLongClickListener(View.OnLongClickListener {
                if (onItemLongClickListener != null) {
                    onItemLongClickListener!!.onTagLongClick(holder.mItem, position)
                }
                return@OnLongClickListener false
            })


        } else if (holder is AddHolder) {
            holder.tagAddItem!!.setOnClickListener {
                onAddItemClickListener!!.onClick()
            }

        }

    }
    private fun getUserIdIsLogin(): String {
        return if (getSp().isValidUser) getSp().userId else Constants.NO_USER
    }
    private fun getSp(): UserPreferences {
        return UserPreferences.getInstance(context)
    }
    private fun setDefaultNameText(holder: TagHolder) {
        val results = realm.where(DocEntity::class.java)
                .equalTo("isDelete", 0.toInt())
                .equalTo("userID",getUserIdIsLogin())
                .equalTo("fileType", holder.mItem?.fileType)
                .findAll()
        holder.tagItemName?.text = holder.mItem?.tagName + String.format(context!!.getString(R.string.tag_counts), results.size.toString())
//        if (results.size > 0) {
//            holder.tagItemName?.text = holder.mItem?.tagName + String.format(context!!.getString(R.string.tag_counts), results.size.toString())
//        } else {
//            holder.tagItemName?.text = holder.mItem?.tagName
//        }
    }

    private fun setNotDefaultNameText(holder: TagHolder) {
        val results = realm.where(DocEntity::class.java)
                .equalTo("isDelete", 0.toInt())
                .equalTo("userID",getUserIdIsLogin())
                .equalTo("tagName", holder.mItem?.tagName)
                .findAll()
        holder.tagItemName?.text = holder.mItem?.tagName + String.format(context!!.getString(R.string.tag_counts), results.size.toString())

//        if (results.size > 0) {
//            holder.tagItemName?.text = holder.mItem?.tagName + String.format(context!!.getString(R.string.tag_counts), results.size.toString())
//        } else {
//            holder.tagItemName?.text = holder.mItem?.tagName
//        }
    }


    override fun getItemViewType(position: Int): Int = ITEM_TYPE_TAG


    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int = datas!!.size


    private inner class TagHolder internal constructor(val mView: View) : RecyclerView.ViewHolder(mView) {
        internal var mItem: TagEntity? = null
        internal var tagItem: RelativeLayout? = null
        internal var tagInnerItem: RelativeLayout? = null
        internal var tagItemName: TextView? = null
        internal var tagDeleteBtn: ImageView? = null


        init {

            tagInnerItem = mView.findViewById<View>(R.id.tag_inner_item) as RelativeLayout
            tagItem = mView.findViewById<View>(R.id.tag_item) as RelativeLayout
            tagDeleteBtn = mView.findViewById<View>(R.id.tag_delete_btn) as ImageView
            tagItemName = mView.findViewById<View>(R.id.tag_item_name) as TextView

        }


    }

    private var onItemClickListener: OnItemClickListener? = null
    private var onAddItemClickListener: OnAddItemClickListener? = null
    private var onDeleteClickListener: OnDeleteClickListener? = null


    private inner class AddHolder internal constructor(val mView: View) : RecyclerView.ViewHolder(mView) {
        internal var mItem: TagEntity? = null
        internal var tagAddItem: RelativeLayout? = null

        init {
            tagAddItem = mView.findViewById<View>(R.id.tag_add_item) as RelativeLayout
        }

    }

    interface OnItemClickListener {
        fun onTagClick(TagEntity: TagEntity, position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    interface OnDeleteClickListener {
        fun onTagDeleteClick(TagEntity: TagEntity?, position: Int)
    }

    fun setOnDeleteClickListener(onDeleteClickListener: OnDeleteClickListener) {
        this.onDeleteClickListener = onDeleteClickListener
    }


    fun setOnAddItemClickListener(onAddItemClickListener: OnAddItemClickListener) {
        this.onAddItemClickListener = onAddItemClickListener
    }

    interface OnAddItemClickListener {
        fun onClick()
    }

    private var onItemLongClickListener: OnItemLongClickListener? = null

    interface OnItemLongClickListener {
        fun onTagLongClick(TagEntity: TagEntity?, position: Int)
    }


}
