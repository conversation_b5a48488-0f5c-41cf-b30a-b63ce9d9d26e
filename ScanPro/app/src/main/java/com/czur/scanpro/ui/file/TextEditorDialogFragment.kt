package com.czur.scanpro.ui.file

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.Editable
import android.text.TextWatcher
import android.util.DisplayMetrics
import android.view.*
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.DialogFragment
import com.blankj.utilcode.util.ColorUtils
import com.czur.scanpro.R
import com.czur.scanpro.adapter.ColorPickerAdapter
import com.czur.scanpro.databinding.AddTextDialogBinding


/**
 * Created by s<PERSON><PERSON> on 7/13/2020.
 */
class TextEditorDialogFragment : DialogFragment() {
    private lateinit var  binding: AddTextDialogBinding



    private var mInputMethodManager: InputMethodManager? = null
    private var mColorCode = 0
    private var mTextEditor: TextEditor? = null

    interface TextEditor {
        fun onDone(inputText: String?, colorCode: Int)
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            val dm = DisplayMetrics()
            requireActivity().windowManager.defaultDisplay.getMetrics(dm)
            dialog!!.window?.setLayout(dm.widthPixels, dialog!!.window!!.attributes.height)
            dialog!!.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            //    dialog.window.setWindowAnimations(R.style.BottomAnimation)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        this.dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val window: Window? = this.dialog?.window
        window?.decorView?.setPadding(0, 0, 0, 0)
        val lp: WindowManager.LayoutParams? = window?.attributes
        lp?.width = WindowManager.LayoutParams.MATCH_PARENT
        lp?.height = WindowManager.LayoutParams.MATCH_PARENT
        window?.attributes = lp
        binding = AddTextDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mColorCode = arguments?.getInt(EXTRA_COLOR_CODE)!!
        if (mColorCode == 0) {
            mColorCode = ColorUtils.getColor(R.color.red_de4d4d)
        }


//        mInputMethodManager = requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        val addTextColorPickerRecyclerView: RecyclerView = view.findViewById(R.id.add_text_color_picker_recycler_view)
        val layoutManager = LinearLayoutManager(
            activity,
            LinearLayoutManager.HORIZONTAL,
            false
        )
        addTextColorPickerRecyclerView.layoutManager = layoutManager
        addTextColorPickerRecyclerView.setHasFixedSize(true)
        val colorPickerAdapter = ColorPickerAdapter(requireActivity(), mColorCode, false)
        colorPickerAdapter.setOnColorPickerClickListener(object : ColorPickerAdapter.OnColorPickerClickListener {
            override fun onColorPickerClickListener(colorCode: Int) {
                mColorCode = colorCode
                binding.addTextEditText.setTextColor(colorCode)
                binding.addTextTextView.setTextColor(colorCode)
            }
        })
        addTextColorPickerRecyclerView.adapter = colorPickerAdapter
        binding.addTextEditText.setText(arguments?.getString(EXTRA_INPUT_TEXT))
        binding.addTextTextView.text = arguments?.getString(EXTRA_INPUT_TEXT)
        binding.addTextTextView.post {
            if (binding.addTextTextView.lineCount > 1) {
                binding.addTextTextView.gravity = Gravity.START or Gravity.CENTER_VERTICAL
            } else {
                binding.addTextTextView.gravity = Gravity.CENTER
            }
        }
        binding.addTextEditText.setTextColor(mColorCode)
        binding.addTextTextView.setTextColor(mColorCode)
        binding.addTextEditText.requestFocus()
        binding.addTextEditText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                binding.addTextTextView.text = s.toString()
                binding.addTextTextView.post {
                    if (binding.addTextTextView.lineCount > 1) {
                        binding.addTextTextView.gravity = Gravity.START or Gravity.CENTER_VERTICAL
                    } else {
                        binding.addTextTextView.gravity = Gravity.CENTER
                    }
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
//        mInputMethodManager!!.showSoftInput(binding.addTextEditText, InputMethodManager.SHOW_IMPLICIT);

//        mInputMethodManager!!.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0)
        binding.rlDone.setOnClickListener(View.OnClickListener { view ->
            val imm: InputMethodManager =
                requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
//            imm.toggleSoftInput(InputMethodManager.SHOW_IMPLICIT, InputMethodManager.HIDE_NOT_ALWAYS)
            imm!!.hideSoftInputFromWindow(view.windowToken, 0)
            dismiss()
            val inputText = binding.addTextEditText.text.toString()
            mTextEditor?.onDone(inputText, mColorCode)
        })

        dialog?.getWindow()?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE);
    }

    fun setOnTextEditorListener(textEditor: TextEditor?) {
        mTextEditor = textEditor
    }

    fun requestFocus() {
        binding.addTextEditText.requestFocus()
        val imm: InputMethodManager =
            requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.toggleSoftInput(InputMethodManager.SHOW_IMPLICIT, InputMethodManager.HIDE_NOT_ALWAYS)
    }
    companion object {
        val TAG: String = TextEditorDialogFragment::class.java.simpleName
        const val EXTRA_INPUT_TEXT = "extra_input_text"
        const val EXTRA_COLOR_CODE = "extra_color_code"

        fun show(appCompatActivity: AppCompatActivity, inputText: String = "", @ColorInt colorCode: Int = ContextCompat.getColor(appCompatActivity, R.color.red_de4d4d)): TextEditorDialogFragment {
            val args = Bundle()
            args.putString(EXTRA_INPUT_TEXT, inputText)
            args.putInt(EXTRA_COLOR_CODE, colorCode)
            val fragment = TextEditorDialogFragment()
            fragment.arguments = args
            fragment.show(appCompatActivity.supportFragmentManager, TAG)
            return fragment
        }
    }
}