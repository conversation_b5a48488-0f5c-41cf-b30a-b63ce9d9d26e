package com.czur.scanpro.ui.component.popup;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import com.blankj.utilcode.util.BarUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.utils.validator.StringUtils;
import com.czur.scanpro.utils.validator.Validator;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class UpdatePopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public UpdatePopup(Context context) {
        super(context);
    }

    public UpdatePopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String title;
        private String message;

        private View contentsView;

        private OnClickListener positiveListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }



        public UpdatePopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final UpdatePopup dialog = new UpdatePopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            BarUtils.setStatusBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode( dialog.getWindow(), true);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final UpdatePopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_update, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);



            TextView messageTv = (TextView) layout.findViewById(R.id.message);
            messageTv.setMovementMethod(ScrollingMovementMethod.getInstance());
            TextView title = (TextView) layout.findViewById(R.id.title);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.add_tag_back_btn);
            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);

            if (contentsView == null) {

                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }
            } else {
                title.setVisibility(View.GONE);
            }
            if (constants.getPositiveBtn() > 0) {
                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
            } else {
                positiveBtn.setVisibility(View.GONE);
            }
            if (Validator.isNotEmpty(message)){
                messageTv.setText(message);
            }

            if (positiveListener != null) {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }
            backBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });

            return layout;
        }

        private void startAnim(View layout) {
            AnimatorSet animatorSet1 = new AnimatorSet();
            ObjectAnimator scaleX1 = ObjectAnimator.ofFloat(layout, "scaleX", 0.1f,0.1f);
            ObjectAnimator   scaleY1 = ObjectAnimator.ofFloat(layout, "scaleY", 0.1f,0.1f);
            animatorSet1.setDuration(200);
            animatorSet1.setInterpolator(new DecelerateInterpolator());
            animatorSet1.play(scaleX1).with(scaleY1);
            animatorSet1.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animator) {

                }

                @Override
                public void onAnimationEnd(Animator animator) {
                    AnimatorSet animatorSet = new AnimatorSet();
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(layout, "scaleX", 0.1f, 1.2f, 0.9f, 1.0f);
                    ObjectAnimator   scaleY = ObjectAnimator.ofFloat(layout, "scaleY", 0.1f, 1.2f, 0.9f, 1.0f);
                    animatorSet.setDuration(600);

                    animatorSet.setInterpolator(new DecelerateInterpolator());
                    animatorSet.play(scaleX).with(scaleY);
                    animatorSet.start();
                }

                @Override
                public void onAnimationCancel(Animator animator) {

                }

                @Override
                public void onAnimationRepeat(Animator animator) {

                }
            });
            animatorSet1.start();
        }

        private boolean isChinese(char c) {
            return c >= 0x4E00 && c <= 0x9FA5;// 根据字节码判断
        }
    }
}
