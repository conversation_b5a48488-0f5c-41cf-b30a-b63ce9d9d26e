package com.czur.scanpro.event;

public class CardFrontEvent extends BaseEvent {
    public float getRotate() {
        return rotate;
    }

    private float rotate;

    public boolean isCut() {
        return isCut;
    }

    private boolean isCut;

    public CardFrontEvent(EventType eventType, float rotate, boolean isCut) {
        super(eventType);
        this.rotate = rotate;
        this.isCut = isCut;
    }

    @Override
    public boolean match(Object obj) {
        return true;
    }
}
