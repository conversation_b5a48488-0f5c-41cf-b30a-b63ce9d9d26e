package com.czur.scanpro.ui.user

import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.ImageView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.scanpro.R
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.MediumBoldTextView

/**
 * 隐私协议页面
 */
class UserRemoveAccountActivity : BaseActivity(), View.OnClickListener {


    private lateinit var normalTitle: MediumBoldTextView
    private lateinit var backBtn: ImageView
    private lateinit var next_setp_btn: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)

        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.user_remove_account)
        initView()
        initComponent()
        normalTitle.text = getString(R.string.privacy_policy)
    }

    private fun initView() {
        normalTitle = findViewById(R.id.normalTitle)
        backBtn = findViewById(R.id.backBtn)
        next_setp_btn = findViewById(R.id.next_setp_btn)

    }

    private fun initComponent() {
        backBtn.setOnClickListener(this)
        next_setp_btn.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.backBtn -> ActivityUtils.finishActivity(this)
            R.id.next_setp_btn -> {
                ActivityUtils.startActivity(UserRemoveAccountTwoActivity::class.java)
            }

            else -> {
            }
        }
    }

}