package com.czur.scanpro.network;

import com.blankj.utilcode.util.NetworkUtils;

import java.io.IOException;

import okhttp3.CacheControl;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class NetCacheInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Request.Builder requestBuilder = request.newBuilder();
        if (!NetworkUtils.isConnected()) {
            requestBuilder.cacheControl(CacheControl.FORCE_CACHE); // 无网络直接使用缓存
        } else {
            requestBuilder.cacheControl(CacheControl.FORCE_NETWORK);
        }
        return chain.proceed(requestBuilder.build());
    }
}