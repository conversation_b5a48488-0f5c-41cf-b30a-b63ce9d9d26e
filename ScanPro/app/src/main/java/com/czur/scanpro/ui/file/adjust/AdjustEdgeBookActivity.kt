package com.czur.scanpro.ui.file.adjust


import android.graphics.PointF
import android.graphics.RectF
import androidx.constraintlayout.widget.ConstraintSet
import android.util.SizeF
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.R
import com.czur.scanpro.alg.*
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityAdjustEdgeBinding
import com.czur.scanpro.databinding.AddTextDialogBinding
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.CameraEvent
import com.czur.scanpro.event.EditEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.ui.component.adjustEdge.AdjustEdgeBookView
import com.czur.scanpro.utils.MemoryUtils
import com.czur.scanpro.utils.ktExtension.createBitmapSquareOutOfBounds
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.abs
import kotlin.math.min

class AdjustEdgeBookActivity : BaseAdjustEdgeActivity() {


    private var pointView: AdjustEdgeBookView? = null

    // witch point
    private var isCurve: Boolean = false
    private var isTop: Boolean = false
    private var isLeft: Boolean = false
    private var currentPointIndex: Int = -1
    private var isJustLandscapeMove: Boolean = false
    private var magnifierDifferenceSize = SizeF(0f, 0f)

    private var finalCategoryName: String? = null
    private var finalCategoryId: String? = null
    private var isCamera = false
    private var tagId: String? = null
    private var tagName: String? = null
    private var categoryID: String? = null
    private var categoryName: String? = null
    private var type = 0

    override fun getIntentInfo() {
        isCamera = intent.getBooleanExtra("isCamera", false)
        tagId = intent.getStringExtra("tagId")
        tagName = intent.getStringExtra("tagName")
        categoryID = intent.getStringExtra("categoryID")
        categoryName = intent.getStringExtra("categoryName")
        type = intent.getIntExtra("type", 0)
        fileID = intent.getStringExtra("fileID")

        finalCategoryId = intent.getStringExtra("finalCategoryId")
        finalCategoryName = intent.getStringExtra("finalCategoryName")

        originalImagePath = intent.getStringExtra("originalImagePath")

    }

    override fun touchDown() {
        checkTouchWitchPoint()
    }

    override fun touchMove(isInImage: Boolean) {
        if (currentPointIndex == -1) {
            return
        }
        // 曲线
        if (isCurve) {
            curvePointMove()
        }
        // 直线
        else {
            // 两端点
            if (currentPointIndex != 1) {
                leftRightPointMove()
            }
            // 中间点
            else {
                middlePointMove()
            }
        }
    }

    private fun curvePointMove() {
        val currentPoint = getCurrentPoint()
        currentPoint.run {
            x = touchPosition.x - differenceSize.width
            y = touchPosition.y - differenceSize.height
        }

        // 调整点吸附
        val sorptionPoint = CZSaoMiao_Alg.getInstance(this).on_book_sorption_pts(
                px2Image(touchPosition.x - differenceSize.width).toInt(), px2Image(touchPosition.y - differenceSize.height).toInt(),
                false, isTop, currentPointIndex)
        sorptionPoint.x = px2Screen(sorptionPoint.x)
        sorptionPoint.y = px2Screen(sorptionPoint.y)

        if (currentPoint.x != sorptionPoint.x || currentPoint.y != sorptionPoint.y) {
            currentPoint.run {
                x = sorptionPoint.x
                y = sorptionPoint.y
            }
//            differenceSize = SizeF(touchPosition.x - currentPoint.x, touchPosition.y - currentPoint.y)
        }

        if (currentPoint.x <= 0) {
            currentPoint.x = 0F
        }
        if (currentPoint.y <= 0) {
            currentPoint.y = 0F
        }
        if (currentPoint.x >= bitmap!!.width) {
            currentPoint.x = bitmap!!.width.toFloat()
        }
        if (currentPoint.y >= bitmap!!.height) {
            currentPoint.y = bitmap!!.height.toFloat()
        }
        showMagnifier(currentPoint)
        // 曲线拟合
        updateCurves()

        pointView!!.showPoints()


    }

    private fun getCurrentPoint(): PointF {
        return if (isCurve) {
            if (isTop) pointView!!.topPointsList[currentPointIndex] else pointView!!.bottomPointsList[currentPointIndex]
        } else {
            if (isLeft) pointView!!.leftPoints[currentPointIndex] else pointView!!.rightPoints[currentPointIndex]
        }
    }

    private fun updateCurves() {
        val inArgs = Args()
        inArgs.leftTopP = CZPoint(px2Image(pointView!!.leftPoints[0].x), px2Image(pointView!!.leftPoints[0].y))
        inArgs.leftDownP = CZPoint(px2Image(pointView!!.leftPoints[2].x), px2Image(pointView!!.leftPoints[2].y))
        inArgs.rightTopP = CZPoint(px2Image(pointView!!.rightPoints[0].x), px2Image(pointView!!.rightPoints[0].y))
        inArgs.rightDownP = CZPoint(px2Image(pointView!!.rightPoints[2].x), px2Image(pointView!!.rightPoints[2].y))
        inArgs.upPtss = run {
            val resultList = mutableListOf<CZPoint>()
            pointView!!.topPointsList.forEach {
                resultList.add(CZPoint(px2Image(it.x), px2Image(it.y)))
            }
            resultList
        }
        inArgs.downPtss = run {
            val resultList = mutableListOf<CZPoint>()
            pointView!!.bottomPointsList.forEach {
                resultList.add(CZPoint(px2Image(it.x), px2Image(it.y)))
            }
            resultList
        }
        val outArgs = CZSaoMiao_Alg.getInstance(this).on_update_curve(inArgs)
        makeTopBottomCurve(outArgs)
    }

    private fun makeTopBottomCurve(args: Args) {
        pointView!!.topCurveList.clear()
        args.upCurve.forEach {
            pointView!!.topCurveList.add(PointF(px2Screen(it.x), px2Screen(it.y)))
        }
        pointView!!.bottomCurveList.clear()
        args.downCurve.forEach {
            pointView!!.bottomCurveList.add(PointF(px2Screen(it.x), px2Screen(it.y)))
        }
    }

    private fun leftRightPointMove() {
        isJustLandscapeMove = true
        if (isLeft) {
            pointView!!.leftPoints[currentPointIndex].x = touchPosition.x - differenceSize.width
            if (pointView!!.leftPoints[currentPointIndex].x <= 0) {
                pointView!!.leftPoints[currentPointIndex].x = 0F
            }
            if (pointView!!.leftPoints[currentPointIndex].y <= 0) {
                pointView!!.leftPoints[currentPointIndex].y = 0F
            }
            if (pointView!!.leftPoints[currentPointIndex].x >= bitmap!!.width) {
                pointView!!.leftPoints[currentPointIndex].x = bitmap!!.width.toFloat()
            }
            if (pointView!!.leftPoints[currentPointIndex].y >= bitmap!!.height) {
                pointView!!.leftPoints[currentPointIndex].y = bitmap!!.height.toFloat()
            }
            showMagnifier(pointView!!.leftPoints[currentPointIndex])
        } else {
            pointView!!.rightPoints[currentPointIndex].x = touchPosition.x - differenceSize.width
            if (pointView!!.rightPoints[currentPointIndex].x <= 0) {
                pointView!!.rightPoints[currentPointIndex].x = 0F
            }
            if (pointView!!.rightPoints[currentPointIndex].y <= 0) {
                pointView!!.rightPoints[currentPointIndex].y = 0F
            }
            if (pointView!!.rightPoints[currentPointIndex].x >= bitmap!!.width) {
                pointView!!.rightPoints[currentPointIndex].x = bitmap!!.width.toFloat()
            }
            if (pointView!!.rightPoints[currentPointIndex].y >= bitmap!!.height) {
                pointView!!.rightPoints[currentPointIndex].y = bitmap!!.height.toFloat()
            }

            showMagnifier(pointView!!.rightPoints[currentPointIndex])
        }


        makeMiddlePoint()
        makePoints()

        // 曲线拟合
        updateCurves()

        pointView!!.showPoints()


    }

    private fun showMagnifier(point: PointF) {
        binding.magnifier.x = point.x + imagePosition.x - dp65
        binding.magnifier.y = point.y + imagePosition.y - dp180

        binding.hView.x = binding.magnifier.x + dp50
        binding.hView.y = binding.magnifier.y + dp64

        binding.vView.x = binding.magnifier.x + dp64
        binding.vView.y = binding.magnifier.y + dp50

        binding.magnifierGroup.visibility = View.VISIBLE
        makeMagnifierImage(point.x, point.y)
    }

    private fun middlePointMove() {
        val oryVal: Float = touchPosition.x - prevTouchPosition.x

        if (isLeft) {
            pointView!!.leftPoints[0].x = touchPosition.x - differenceSize.width + (pointView!!.leftPoints[0].x - pointView!!.leftPoints[1].x)
            pointView!!.leftPoints[2].x = touchPosition.x - differenceSize.width + (pointView!!.leftPoints[2].x - pointView!!.leftPoints[1].x)

            val leftTopSorptionPoint = CZSaoMiao_Alg.getInstance(this).on_book_sorption_pts(
                    px2Image(pointView!!.leftPoints[0].x).toInt(), px2Image(pointView!!.leftPoints[0].y).toInt(), true, false, 0)

            val leftBottomSorptionPoint = CZSaoMiao_Alg.getInstance(this).on_book_sorption_pts(
                    px2Image(pointView!!.leftPoints[2].x).toInt(), px2Image(pointView!!.leftPoints[2].y).toInt(), true, false, 0)

            pointView!!.leftPoints[0].x = px2Screen(leftTopSorptionPoint.x)
            pointView!!.leftPoints[2].x = px2Screen(leftBottomSorptionPoint.x)


            if (pointView!!.leftPoints[0].x <= 0F) {
                pointView!!.leftPoints[0].x = 0F
                pointView!!.leftPoints[2].x -= oryVal
            }
            if (pointView!!.leftPoints[2].x <= 0F) {
                pointView!!.leftPoints[2].x = 0F
                pointView!!.leftPoints[0].x -= oryVal
            }

        } else {
            pointView!!.rightPoints[0].x = touchPosition.x - differenceSize.width + (pointView!!.rightPoints[0].x - pointView!!.rightPoints[1].x)
            pointView!!.rightPoints[2].x = touchPosition.x - differenceSize.width + (pointView!!.rightPoints[2].x - pointView!!.rightPoints[1].x)

            val rightTopSorptionPoint = CZSaoMiao_Alg.getInstance(this).on_book_sorption_pts(
                    px2Image(pointView!!.rightPoints[0].x).toInt(), px2Image(pointView!!.rightPoints[0].y).toInt(), true, false, 0)

            val rightBottomSorptionPoint = CZSaoMiao_Alg.getInstance(this).on_book_sorption_pts(
                    px2Image(pointView!!.rightPoints[2].x).toInt(), px2Image(pointView!!.rightPoints[2].y).toInt(), true, false, 0)

            pointView!!.rightPoints[0].x = px2Screen(rightTopSorptionPoint.x)
            pointView!!.rightPoints[2].x = px2Screen(rightBottomSorptionPoint.x)

            if (pointView!!.rightPoints[0].x >= bitmap!!.width) {
                pointView!!.rightPoints[0].x = bitmap!!.width.toFloat()
                pointView!!.rightPoints[2].x += oryVal
            }
            if (pointView!!.rightPoints[2].x >= bitmap!!.width) {
                pointView!!.rightPoints[2].x = bitmap!!.width.toFloat()
                pointView!!.rightPoints[0].x += oryVal
            }
        }

        makeMiddlePoint()
        makePoints()

        // 曲线拟合
        updateCurves()

        pointView!!.showPoints()
    }

    override fun checkNeedResetPoints(): Boolean {
        return false
    }

    override fun touchUp() {
        magnifierHide()
    }

    private fun checkTouchWitchPoint() {
        var mixDistance: Float = -1f
        currentPointIndex = -1
        pointView!!.topPointsList.forEachIndexed { index, pointF ->
            val distance = pointDistanceMath(pointF)
            if (distance != -1f && (mixDistance == -1f || distance < mixDistance)) {
                mixDistance = distance
                isCurve = true
                isTop = true
                currentPointIndex = index
            }
        }
        pointView!!.bottomPointsList.forEachIndexed { index, pointF ->
            val distance = pointDistanceMath(pointF)
            if (distance != -1f && (mixDistance == -1f || distance < mixDistance)) {
                mixDistance = distance
                isCurve = true
                isTop = false
                currentPointIndex = index
            }
        }
        pointView!!.leftPoints.forEachIndexed { index, pointF ->
            val distance = pointDistanceMath(pointF)
            if (distance != -1f && (mixDistance == -1f || distance < mixDistance)) {
                mixDistance = distance
                isCurve = false
                isLeft = true
                currentPointIndex = index
            }
        }
        pointView!!.rightPoints.forEachIndexed { index, pointF ->
            val distance = pointDistanceMath(pointF)
            if (distance != -1f && (mixDistance == -1f || distance < mixDistance)) {
                mixDistance = distance
                isCurve = false
                isLeft = false
                currentPointIndex = index
            }
        }
        if (currentPointIndex != -1) {
            val point: PointF = if (isCurve) {
                if (isTop) pointView!!.topPointsList[currentPointIndex] else pointView!!.bottomPointsList[currentPointIndex]
            } else {
                if (isLeft) pointView!!.leftPoints[currentPointIndex] else pointView!!.rightPoints[currentPointIndex]
            }

            differenceSize = SizeF(touchPosition.x - point.x, touchPosition.y - point.y)
            magnifierDifferenceSize = SizeF(touchPosition.x - point.x, touchPosition.y - point.y)
        }
    }

    override fun makeMagnifierImage(x: Float, y: Float) {
        val currentPoint = getCurrentPoint()
        val magnifierBitmap = if (isJustLandscapeMove) {
            bitmap!!.createBitmapSquareOutOfBounds(x.toInt(), currentPoint.y.toInt(), magnifierWidthHeight)
        } else {
            bitmap!!.createBitmapSquareOutOfBounds(x.toInt(), y.toInt(), magnifierWidthHeight)
        }
        binding.magnifier.setImageBitmap(magnifierBitmap)
    }

    private fun pointDistanceMath(point: PointF): Float {
        val distanceX = abs(touchPosition.x - point.x)
        val distanceY = abs(touchPosition.y - point.y)
        return if (distanceX <= dp20 && distanceY <= dp20) distanceX + distanceY else -1f
    }


    override fun makePointView() {
        pointView = AdjustEdgeBookView(this)
        pointView!!.id = R.id.AdjustEdgeSimplePointView
        binding.adjustEdgeBody.addView(pointView)

        val constraintSet = ConstraintSet()
        constraintSet.run {
            connect(pointView!!.id, ConstraintSet.START, imageView!!.id, ConstraintSet.START)
            connect(pointView!!.id, ConstraintSet.TOP, imageView!!.id, ConstraintSet.TOP)
            connect(pointView!!.id, ConstraintSet.END, imageView!!.id, ConstraintSet.END)
            connect(pointView!!.id, ConstraintSet.BOTTOM, imageView!!.id, ConstraintSet.BOTTOM)
            constrainWidth(pointView!!.id, bitmap!!.width + dp20 * 2)
            constrainHeight(pointView!!.id, bitmap!!.height + dp20 * 2)
            applyTo(binding.adjustEdgeBody)
        }

        pointView!!.pointRadius = rectWidth / 2f

        makePointsFirst()
    }

    private fun makePointsFirst() {
        makePointsWithMiddle(
                PointF(px2Screen(algInit!!.leftTopP.x), px2Screen(algInit!!.leftTopP.y)),
                PointF(px2Screen(algInit!!.leftDownP.x), px2Screen(algInit!!.leftDownP.y)),
                PointF(px2Screen(algInit!!.rightTopP.x), px2Screen(algInit!!.rightTopP.y)),
                PointF(px2Screen(algInit!!.rightDownP.x), px2Screen(algInit!!.rightDownP.y)))
        makePoints()
        algInit!!.upPtss.forEach {
            pointView!!.topPointsList.add(PointF(px2Screen(it.x), px2Screen(it.y)))
        }
        algInit!!.downPtss.forEach {
            pointView!!.bottomPointsList.add(PointF(px2Screen(it.x), px2Screen(it.y)))
        }
        algInit!!.upCurve.forEach {
            pointView!!.topCurveList.add(PointF(px2Screen(it.x), px2Screen(it.y)))
        }
        algInit!!.downCurve.forEach {
            pointView!!.bottomCurveList.add(PointF(px2Screen(it.x), px2Screen(it.y)))
        }
        pointView!!.showPoints()
        pointEventView = pointView

    }

    private fun makeMiddlePoint() {
        if (isLeft) {
            pointView!!.leftPoints[1] = PointF(
                    abs(pointView!!.leftPoints[0].x - pointView!!.leftPoints[2].x) / 2f + min(pointView!!.leftPoints[0].x, pointView!!.leftPoints[2].x),
                    abs(pointView!!.leftPoints[0].y - pointView!!.leftPoints[2].y) / 2f + min(pointView!!.leftPoints[0].y, pointView!!.leftPoints[2].y))
        } else {
            pointView!!.rightPoints[1] = PointF(
                    abs(pointView!!.rightPoints[0].x - pointView!!.rightPoints[2].x) / 2f + min(pointView!!.rightPoints[0].x, pointView!!.rightPoints[2].x),
                    abs(pointView!!.rightPoints[0].y - pointView!!.rightPoints[2].y) / 2f + min(pointView!!.rightPoints[0].y, pointView!!.rightPoints[2].y))
        }
    }

    private fun makePointsWithMiddle(leftPoint1: PointF, leftPoint2: PointF, rightPoint1: PointF, rightPoint2: PointF) {
        val leftMiddlePoint = PointF(
                abs(leftPoint1.x - leftPoint2.x) / 2f + min(leftPoint1.x, leftPoint2.x),
                abs(leftPoint1.y - leftPoint2.y) / 2f + min(leftPoint1.y, leftPoint2.y))
        val leftTopPoint = PointF(
                abs(leftPoint1.x - leftMiddlePoint.x) / 2f + min(leftPoint1.x, leftMiddlePoint.x),
                abs(leftPoint1.y - leftMiddlePoint.y) / 2f + min(leftPoint1.y, leftMiddlePoint.y))
        val leftBottomPoint = PointF(
                abs(leftPoint2.x - leftMiddlePoint.x) / 2f + min(leftPoint2.x, leftMiddlePoint.x),
                abs(leftPoint2.y - leftMiddlePoint.y) / 2f + min(leftPoint2.y, leftMiddlePoint.y))
        pointView!!.leftPoints = mutableListOf(leftTopPoint, leftMiddlePoint, leftBottomPoint)

        val rightMiddlePoint = PointF(
                abs(rightPoint1.x - rightPoint2.x) / 2f + min(rightPoint1.x, rightPoint2.x),
                abs(rightPoint1.y - rightPoint2.y) / 2f + min(rightPoint1.y, rightPoint2.y))
        val rightTopPoint = PointF(
                abs(rightPoint1.x - rightMiddlePoint.x) / 2f + min(rightPoint1.x, rightMiddlePoint.x),
                abs(rightPoint1.y - rightMiddlePoint.y) / 2f + min(rightPoint1.y, rightMiddlePoint.y))
        val rightBottomPoint = PointF(
                abs(rightPoint2.x - rightMiddlePoint.x) / 2f + min(rightPoint2.x, rightMiddlePoint.x),
                abs(rightPoint2.y - rightMiddlePoint.y) / 2f + min(rightPoint2.y, rightMiddlePoint.y))
        pointView!!.rightPoints = mutableListOf(rightTopPoint, rightMiddlePoint, rightBottomPoint)
    }

    private fun makePoints() {
        makeRectList()
        makePointCrossLine()
    }

    private fun makeRectList() {
        pointView!!.leftRects.clear()
        pointView!!.rightRects.clear()
        pointView!!.leftPoints.forEach {
            val x = it.x - rectWidth / 2f
            val y = it.y - rectWidth / 2f
            pointView!!.leftRects.add(RectF(x, y, x + rectWidth, y + rectWidth))
        }
        pointView!!.rightPoints.forEach {
            val x = it.x - rectWidth / 2f
            val y = it.y - rectWidth / 2f
            pointView!!.rightRects.add(RectF(x, y, x + rectWidth, y + rectWidth))
        }
    }

    private fun makePointCrossLine() {
        pointView!!.leftPointCrossLine.clear()
        pointView!!.rightPointCrossLine.clear()
        pointView!!.leftPointCrossLine.run {
            add(Vec4f(
                    lineRectCrossPts(pointView!!.leftRects[0], pointView!!.leftPoints[0], pointView!!.leftPoints[1], CZOrientation.Bottom),
                    lineRectCrossPts(pointView!!.leftRects[1], pointView!!.leftPoints[0], pointView!!.leftPoints[1], CZOrientation.Top)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.leftRects[1], pointView!!.leftPoints[1], pointView!!.leftPoints[2], CZOrientation.Bottom),
                    lineRectCrossPts(pointView!!.leftRects[2], pointView!!.leftPoints[1], pointView!!.leftPoints[2], CZOrientation.Top)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.rightRects[0], pointView!!.rightPoints[0], pointView!!.rightPoints[1], CZOrientation.Bottom),
                    lineRectCrossPts(pointView!!.rightRects[1], pointView!!.rightPoints[0], pointView!!.rightPoints[1], CZOrientation.Top)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.rightRects[1], pointView!!.rightPoints[1], pointView!!.rightPoints[2], CZOrientation.Bottom),
                    lineRectCrossPts(pointView!!.rightRects[2], pointView!!.rightPoints[1], pointView!!.rightPoints[2], CZOrientation.Top)))
        }
    }

    override fun algInit(): Args {
        return CZSaoMiao_Alg.getInstance(this).before_book_manual_process(originalImagePath, MemoryUtils.getAvailMemory(this))
    }

    override fun backClick() {
        ActivityUtils.finishActivity(this)
    }

    private fun createDoc(realm: Realm) {

        realm.executeTransaction {
            if (FileUtils.createOrExistsDir(getFilesPath())) {
                LogUtils.e(originalImagePath, getFilesPath() + fileID + Constants.ORIGINAL_JPG)
                val docEntity = DocEntity()
                docEntity.fileID = fileID
                docEntity.bucket = Constants.BUCKET
                docEntity.uuid = UUID.randomUUID().toString()
                docEntity.enhanceMode = 0
                docEntity.isTemp = 1
                docEntity.userID = getUserIdIsLogin()
                when (type) {
                    0 -> {
                        docEntity.categoryID = finalCategoryId
                        docEntity.categoryName = finalCategoryName
                    }
                    1 -> {
                        docEntity.isNameTemp = 1
                        docEntity.categoryID = categoryID
                        docEntity.categoryName = categoryName
                    }
                    2 -> {
                        docEntity.tagId = tagId
                        docEntity.tagName = tagName
                        docEntity.categoryID = finalCategoryId
                        docEntity.categoryName = finalCategoryName
                    }
                }
                docEntity.isNewAdd = 1
                docEntity.bucket = Constants.SCAN_PRO
                docEntity.fileType = 1
                docEntity.isDirty = 1

                docEntity.fileSize = FileUtils.getFileLength(getFilesPath() + fileID + Constants.BASE_JPG).toString()
                docEntity.baseImagePath = getFilesPath() + fileID + Constants.BASE_JPG
                docEntity.baseSmallImagePath = getFilesPath() + fileID + Constants.BASE_SMALL_JPG
                docEntity.originalImagePath = getFilesPath() + fileID + Constants.ORIGINAL_JPG
                docEntity.processSmallImagePath = getFilesPath() + fileID + Constants.SMALL_JPG
                docEntity.processImagePath = getFilesPath() + fileID + Constants.JPG
                val curDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(System.currentTimeMillis()))
                docEntity.createTime = curDate
                docEntity.updateTime = curDate
                docEntity.takePhotoTime = curDate
                realm.copyToRealmOrUpdate(docEntity)

            }


        }

    }

    override fun finishClick() {
        showProgressDialog(true)

        Thread(Runnable {
            val args = Args()
            args.leftTopP = CZPoint(px2Image(pointView!!.leftPoints[0].x), px2Image(pointView!!.leftPoints[0].y))
            args.leftDownP = CZPoint(px2Image(pointView!!.leftPoints[2].x), px2Image(pointView!!.leftPoints[2].y))
            args.rightTopP = CZPoint(px2Image(pointView!!.rightPoints[0].x), px2Image(pointView!!.rightPoints[0].y))
            args.rightDownP = CZPoint(px2Image(pointView!!.rightPoints[2].x), px2Image(pointView!!.rightPoints[2].y))
            args.upPtss = mutableListOf<CZPoint>()
            pointView!!.topPointsList.forEach {
                args.upPtss.add(CZPoint(px2Image(it.x), px2Image(it.y)))
            }
            args.downPtss = mutableListOf<CZPoint>()
            pointView!!.bottomPointsList.forEach {
                args.downPtss.add(CZPoint(px2Image(it.x), px2Image(it.y)))
            }
            val realm = Realm.getDefaultInstance()
            if (isCamera) {
                createDoc(realm)
            }
            val doc = realm.where(DocEntity::class.java).equalTo("fileID", fileID).findFirst()
            val baseImagePath = doc!!.baseImagePath!!
            val baseSmallImagePath = doc.baseSmallImagePath!!
            val processImagePath = doc.processImagePath!!
            val processSmallImagePath = doc.processSmallImagePath!!
            args.colorType = if (isCamera) {
                CZSaoMiao_Alg.ColorType.AUTO
            } else {
                when (doc.enhanceMode) {
                    0 -> CZSaoMiao_Alg.ColorType.AUTO
                    1 -> CZSaoMiao_Alg.ColorType.BW
                    2 -> CZSaoMiao_Alg.ColorType.BG_WHITEN
                    3 -> CZSaoMiao_Alg.ColorType.NO_TRANS
                    else -> CZSaoMiao_Alg.ColorType.AUTO
                }
            }

            CZSaoMiao_Alg.getInstance(this).on_book_manual_finished(args, baseImagePath, processImagePath, baseSmallImagePath, processSmallImagePath)

            realm.executeTransaction {
                if (isCamera) {
                    doc.fileSize = FileUtils.getFileLength(getFilesPath() + fileID + Constants.BASE_JPG).toString()
                }
                doc.isDirty = 1
                doc.uuid = UUID.randomUUID().toString()
                doc.updateTime = getCurrentTime()
            }
            if (isCamera) {
                EventBus.getDefault().post(CameraEvent(EventType.EDGE_ADJUST_IN_CAMERA))
            } else {
                EventBus.getDefault().post(EditEvent(EventType.ADJUST))
            }
            handler.post {
                hideProgressDialog()
                finish()
            }
        }).start()

    }
}