package com.czur.scanpro.ui.account

import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.ColorStateList
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import cn.sharesdk.framework.Platform
import cn.sharesdk.framework.PlatformActionListener
import cn.sharesdk.framework.ShareSDK
import cn.sharesdk.sina.weibo.SinaWeibo
import cn.sharesdk.tencent.qq.QQ
import cn.sharesdk.wechat.friends.Wechat
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.CleanUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.SizeUtils
import com.blankj.utilcode.util.StringUtils
import com.blankj.utilcode.util.ThreadUtils
import com.blankj.utilcode.util.Utils
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityLoginBinding
import com.czur.scanpro.entity.model.RegisterModel
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.DownloadEntity
import com.czur.scanpro.entity.realm.OcrModeEntity
import com.czur.scanpro.entity.realm.PdfEntity
import com.czur.scanpro.entity.realm.SyncCategoryEntity
import com.czur.scanpro.entity.realm.SyncDocEntity
import com.czur.scanpro.entity.realm.SyncTagEntity
import com.czur.scanpro.entity.realm.TagEntity
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.LoginEvent
import com.czur.scanpro.event.TokenTimeOutEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.base.WebViewActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.component.popup.SocialAccountDialog
import com.czur.scanpro.ui.file.FilePreviewActivity
import com.czur.scanpro.ui.home.FileActivity
import com.czur.scanpro.ui.user.UserActivity
import com.czur.scanpro.utils.MD5Utils
import com.czur.scanpro.utils.validator.Validator
import com.facebook.drawee.backends.pipeline.Fresco
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File


/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class LoginActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityLoginBinding by lazy {
        ActivityLoginBinding.inflate(layoutInflater)
    }

    private var userPreferences: UserPreferences? = null
    private var handler: WeakHandler? = null
    private val socialAccountDialog: SocialAccountDialog? = null
    private var currentTimeMillis: Long = 0
    private var exitTime: Long = 0

    //0：默认，1：图片浏览,2:主页
    private var type: Int = 0
    private var realm: Realm? = null
    private var isUserPrivacyChecked = false
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(binding.root)
        initView()
        initComponent()
        registerEvent()
    }

    private fun initView() {
        val userPrivacyImg = findViewById<ImageView>(R.id.user_privacy_img)
        val userTermsTv = findViewById<TextView>(R.id.user_terms_tv)
        val userPrivacyPv = findViewById<TextView>(R.id.user_privacy_tv)
        userPrivacyImg.setOnClickListener(this)
        userTermsTv.setOnClickListener(this)
        userPrivacyPv.setOnClickListener(this)
    }

    private fun initComponent() {
        realm = Realm.getDefaultInstance()
        handler = WeakHandler()
        type = intent.getIntExtra("type", 0)
        userPreferences = UserPreferences.getInstance(this)
        EventBus.getDefault().register(this);
        setSoftInputVisibleListener()
    }

    private fun registerEvent() {
//        requestPermission()
        binding.loginUserNameEdt.addTextChangedListener(accountTextWatcher)
        binding.loginUserPasswordEdt.addTextChangedListener(pswTextWatcher)
        binding.loginBtn.setOnClickListener(this)
        binding.loginNewUserRegisterBtn.setOnClickListener(this)
        binding.loginForgetPasswordBtn.setOnClickListener(this)
        binding.socialAccountLoginBtn.setOnClickListener(this)
        binding.loginBackBtn.setOnClickListener(this)
        binding.weixinAccount.setOnClickListener(this)
        binding.qqAccount.setOnClickListener(this)
        binding.weiboAccount.setOnClickListener(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS -> {
                ActivityUtils.finishActivity(this)
            }

            EventType.THIRD_TOKEN_TIME_OUT_TO_LOGIN -> if (event is TokenTimeOutEvent) {
                val tokenTimeOutEvent = event as TokenTimeOutEvent
                val plat = ShareSDK.getPlatform(tokenTimeOutEvent.platName)
                authorize(plat)
            }

            else -> {
            }
        }
    }

    /**
     * @des: 确认删除用户信息
     * @params:
     * @return:
     */

    @SuppressLint("StringFormatInvalid")
    private fun confirmToClearLastUserData(
        entity: MiaoHttpEntity<RegisterModel>, isThirdParty: Boolean,
        openId: String?, token: String?,
        platformName: String?, finalPlatName: String?, refreshToken: String? = null
    ) {
        val currentUserId = entity.body.id
        if (!StringUtils.equals(
                userPreferences!!.lastUserId,
                currentUserId
            ) && Validator.isNotEmpty(userPreferences!!.lastUserId)
        ) {

            val builder = ScanProCommonPopup.Builder(
                this@LoginActivity,
                CloudCommonPopupConstants.COMMON_ONE_BUTTON
            )
            builder.setTitle(resources.getString(R.string.prompt))
            val title = String.format(
                getString(R.string.confirm_to_clear_account),
                userPreferences!!.userName
            )
            builder.setMessage(title)
            builder.setOnPositiveListener { dialog, _ ->
                dialog.dismiss()
                clearLastUserDataAndSetCurrentData(
                    entity,
                    isThirdParty,
                    openId,
                    token,
                    platformName,
                    finalPlatName,
                    refreshToken
                )
            }
            val commonPopup = builder.create()
            commonPopup.setCancelable(false)
            commonPopup.show()
        } else {
            setCurrentUserData(
                entity,
                isThirdParty,
                openId,
                token,
                platformName,
                finalPlatName,
                refreshToken
            )
        }

    }


    /**
     * @des: 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     * @params:
     * @return:
     */

    private fun clearLastUserDataAndSetCurrentData(
        entity: MiaoHttpEntity<RegisterModel>,
        isThirdParty: Boolean,
        openId: String?,
        token: String?,
        platformName: String?,
        finalPlatName: String?,
        refreshToken: String?
    ) {
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                LogUtils.i("clean last user file and sp")
                val filePath = filesDir.toString() + File.separator + userPreferences!!.lastUserId
                FileUtils.delete(File(filePath))
                runOnUiThread {
                    //清空sp
                    userPreferences!!.resetUser()
                    //清空数据库

                    realm!!.executeTransaction(Realm.Transaction {
                        realm!!.where(DocEntity::class.java).notEqualTo("userID", Constants.NO_USER)
                            .findAll().deleteAllFromRealm()
                        realm!!.where(CategoryEntity::class.java)
                            .notEqualTo("userID", Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm!!.where(TagEntity::class.java).notEqualTo("userID", Constants.NO_USER)
                            .findAll().deleteAllFromRealm()

                        realm!!.where(PdfEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(OcrModeEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncTagEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncDocEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncCategoryEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(DownloadEntity::class.java).findAll().deleteAllFromRealm()
                    })
                    Fresco.getImagePipeline().clearCaches()
                    CleanUtils.cleanCustomDir(Utils.getApp().filesDir.toString() + File.separator + Constants.PDF_PATH)
                    setCurrentUserData(
                        entity,
                        isThirdParty,
                        openId,
                        token,
                        platformName,
                        finalPlatName,
                        refreshToken
                    )
                }
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })

    }

    /**
     * @des: 显示登录成功并且跳转到主页
     * @params:
     * @return:
     */
    private fun showLoginSuccessAndGoIndex(isThirdParty: Boolean) {
        requestUserInfo()

        if (isThirdParty) {
            EventBus.getDefault().post(LoginEvent(EventType.THIRD_PARTY_LOGIN_IN))
        } else {
            EventBus.getDefault().post(LoginEvent(EventType.LOG_IN))
        }
        when (type) {
            0 -> {
                if (ActivityUtils.isActivityExistsInStack(UserActivity::class.java)) {
                    ActivityUtils.finishToActivity(UserActivity::class.java, false)
                } else {
                    val intent = Intent(this@LoginActivity, UserActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    ActivityUtils.startActivity(intent)
                }
            }

            1 -> {
                ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
            }

            2 -> {
                ActivityUtils.finishToActivity(IndexActivity::class.java, false)
            }

            3 -> {
                ActivityUtils.finishToActivity(FileActivity::class.java, false)
            }

            99 -> {
                ActivityUtils.finishActivity(this)
            }
        }

    }

    /**
     * @des: 存储当前用户信息到SP
     * @params:
     * @return:
     */
    private fun setCurrentUserData(
        entity: MiaoHttpEntity<RegisterModel>,
        isThirdParty: Boolean,
        openId: String?,
        token: String?,
        platformName: String?,
        finalPlatName: String?,
        refreshToken: String?
    ) {
        userPreferences!!.user = entity.body
        userPreferences!!.lastUserId = userPreferences!!.userId
        userPreferences!!.setIsUserLogin(true)
        LogUtils.i(Gson().toJson(userPreferences!!.user))
        if (isThirdParty) {
            userPreferences!!.setIsThirdParty(true)
            userPreferences!!.thirdPartyOpenid = openId
            userPreferences!!.thirdPartyToken = token
            userPreferences!!.thirdPartyPlatName = platformName
            userPreferences!!.servicePlatName = finalPlatName
            userPreferences!!.thirdPartyRefreshToken = refreshToken
            LogUtils.i(
                userPreferences!!.isThirdParty,
                userPreferences!!.thirdPartyOpenid,
                userPreferences!!.thirdPartyPlatName,
                userPreferences!!.thirdPartyToken,
                userPreferences!!.servicePlatName
            )
        } else {
            userPreferences!!.loginUserName = binding.loginUserNameEdt.text.toString()
            userPreferences!!.loginPassword = binding.loginUserPasswordEdt.text.toString()
        }
        transformCategory(realm!!)
        showProgressDialog(false)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                transformRealCategory()
                return null
            }

            override fun onSuccess(result: Void?) {
                hideProgressDialog()
                showLoginSuccessAndGoIndex(isThirdParty)
            }
        })

    }


    private var accountHasContent = false
    private var passwordHasContent = false
    private val accountTextWatcher = object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            accountHasContent = s!!.isNotEmpty()
            checkLoginButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            accountHasContent = s!!.isNotEmpty()
            checkLoginButtonToClick()
        }
    }
    private val pswTextWatcher = object : TextWatcher {
        override fun afterTextChanged(s: Editable?) {
            passwordHasContent = s!!.isNotEmpty()
            checkLoginButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            passwordHasContent = s!!.isNotEmpty()
            checkLoginButtonToClick()
        }
    }


    override fun onClick(v: View) {
        when (v.id) {
            R.id.login_btn -> {
                KeyboardUtils.hideSoftInput(this)
                userLogin()
            }

            R.id.login_new_user_register_btn -> {
                val intent = Intent(this@LoginActivity, RegisterActivity::class.java)
                intent.putExtra("type", type)
                ActivityUtils.startActivity(intent)
            }

            R.id.login_forget_password_btn -> {
                ActivityUtils.startActivity(ForgetPasswordActivity::class.java)
            }

            R.id.social_account_login_btn -> {
                socialAccountDialog?.show()
            }

            R.id.login_back_btn -> {
                ActivityUtils.finishActivity(this)
            }

            R.id.weixin_account -> {
                if (!isUserPrivacyChecked) {
                    showLongMessage(R.string.user_agree_privacy_toast_tips)
                    return
                }
                // 微信登录
                val wechat = ShareSDK.getPlatform(Wechat.NAME)
                authorize(wechat)
            }

            R.id.qq_account -> {
                if (!isUserPrivacyChecked) {
                    showLongMessage(R.string.user_agree_privacy_toast_tips)
                    return
                }
                // qq登录
                val qq = ShareSDK.getPlatform(QQ.NAME)
                authorize(qq)
            }

            R.id.weibo_account -> {
                if (!isUserPrivacyChecked) {
                    showLongMessage(R.string.user_agree_privacy_toast_tips)
                    return
                }
                // 微博登录
                val weibo = ShareSDK.getPlatform(SinaWeibo.NAME)
                authorize(weibo)
            }

            R.id.user_privacy_img -> {
                isUserPrivacyChecked = !isUserPrivacyChecked
                if (isUserPrivacyChecked) {
                    binding.userPrivacyImg.setImageResource(R.mipmap.sitting_select)
                    binding.userPrivacyImg.imageTintList =
                        ColorStateList.valueOf(resources.getColor(R.color.identifying_code))
                } else {
                    binding.userPrivacyImg.setImageResource(R.mipmap.sitting_no_select)
                    binding.userPrivacyImg.imageTintList =
                        ColorStateList.valueOf(resources.getColor(R.color.gray_99))
                }
                checkLoginButtonToClick()
            }

            R.id.user_privacy_tv -> {
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_policy1))
                intent.putExtra("url", Constants.PRIVACY_AGREEMENT)
                ActivityUtils.startActivity(intent)
            }

            R.id.user_terms_tv -> {
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_terms))
                intent.putExtra("url", Constants.PRIVACY_TERMS)
                ActivityUtils.startActivity(intent)
            }
        }
    }

    /**
     * @des: 登录前置校验
     * @params:[]
     * @return:void
     */
    private fun userLogin() {
        val mobileMail = binding.loginUserNameEdt.text.toString()
        val pwd = binding.loginUserPasswordEdt.text.toString()
        if (mobileMail.isEmpty()) {
            showLongMessage(R.string.account_empty)
        } else if (pwd.isEmpty()) {
            showLongMessage(R.string.login_alert_password_empty)

        } else if (pwd.length <= 5) {
            showLongMessage(R.string.login_alert_pwd_length)

        } else if (!RegexUtils.isMobileExact(mobileMail) && !RegexUtils.isEmail(mobileMail)) {
            showLongMessage(R.string.toast_format_wrong)

        } else {

            loginRequest(mobileMail, pwd, true)

        }

    }

    /**
     * @des: 登录
     * @params:[mobileMail, pwd]
     * @return:void
     */

    private fun loginRequest(mobileMail: String, pwd: String, hasChannel: Boolean) {

        val password = MD5Utils.md5(pwd)
        HttpManager.getInstance().requestPassport()
            .login(
            Constants.SCAN_PRO,
            userPreferences!!.imei,
            userPreferences!!.channel,
            mobileMail,
            password,
            RegisterModel::class.java,
            object : MiaoHttpManager.Callback<RegisterModel> {
                override fun onStart() {
                    if (hasChannel) {
                        showProgressDialog(false)
                    }

                }

                override fun onResponse(entity: MiaoHttpEntity<RegisterModel>) {
                    hideProgressDialog()
                    confirmToClearLastUserData(entity, false, null, null, null, null)

                }

                override fun onFailure(entity: MiaoHttpEntity<RegisterModel>) {
                    hideProgressDialog()
                    if (entity.code == MiaoHttpManager.STATUS_INVALID_USER_OR_PASSWORD) {
                        showMessage(R.string.toast_error)
                    } else {
                        showMessage(R.string.request_failed_alert)
                    }
                }

                override fun onError(e: Exception) {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)

                }
            })
    }

    /**
     * @des: 检查登录按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkLoginButtonToClick() {
        if (accountHasContent && passwordHasContent && isUserPrivacyChecked) {
            val drawable =
                DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36F).toFloat())
                    .setSolidColor(resources.getColor(R.color.black_24)).build()
            binding.loginBtn.background = drawable
            binding.loginBtnImg.setImageResource(R.mipmap.login_icon_selected)
            binding.loginBtnTv.setTextColor(resources.getColor(R.color.red_de4d4d))
            binding.loginBtn.isClickable = true
        } else {
            val drawable =
                DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36F).toFloat())
                    .setSolidColor(resources.getColor(R.color.gray_e6)).build()
            binding.loginBtn.background = drawable
            binding.loginBtnImg.setImageResource(R.mipmap.login_icon_unselected)
            binding.loginBtnTv.setTextColor(resources.getColor(R.color.gray_f9))
            binding.loginBtn.isClickable = false
        }
    }

    private fun setSoftInputVisibleListener() {


        KeyboardUtils.registerSoftInputChangedListener(
            this
        ) { height ->
            val remainHeight = ScreenUtils.getScreenHeight() - height
            LogUtils.i("keyBorad height///$height remainHeight///$remainHeight")
            val softInputVisible = KeyboardUtils.isSoftInputVisible(this@LoginActivity)
            if (softInputVisible) {
                handler?.post(KeyBoardActionRunnable(View.GONE, remainHeight))
            } else {
                handler?.post(KeyBoardActionRunnable(View.VISIBLE, remainHeight))
            }
        }

    }

    private inner class KeyBoardActionRunnable(
        private val visibility: Int,
        private val remainHeight: Int
    ) : Runnable {

        override fun run() {
            binding.loginUserHeadRl.visibility = visibility
            binding.socialAccountLoginBtn.visibility = visibility
            binding.loginForgetPasswordBtn.visibility = visibility
            binding.loginNewUserRegisterBtn.visibility = visibility
            if (visibility == View.VISIBLE) {
                val layoutParams = binding.loginInputLl.layoutParams as LinearLayout.LayoutParams
                layoutParams.setMargins(0, 0, 0, 0)
                binding.loginInputLl.layoutParams = layoutParams
            } else {
                val layoutParams = binding.loginInputLl.layoutParams as LinearLayout.LayoutParams
                layoutParams.setMargins(
                    0,
                    (remainHeight - SizeUtils.dp2px(100f) - binding.loginInputLl.height) / 2,
                    0,
                    0
                )
                binding.loginInputLl.layoutParams = layoutParams
            }

        }

    }


    /**
     * @des: 社交登录dialog 点击事件监听
     * @params:
     * @return:
     */

    private val socialAccountDialogOnClickListener =
        object : SocialAccountDialog.SocialAccountDialogOnClickListener {
            override fun onAccountClick(viewId: Int) {
                when (viewId) {
                    R.id.weixin_account -> {
                        // 微信登录
                        val wechat = ShareSDK.getPlatform(Wechat.NAME)
                        authorize(wechat)
                    }

                    R.id.qq_account -> {
                        // qq登录
                        val qq = ShareSDK.getPlatform(QQ.NAME)
                        authorize(qq)
                    }

                    R.id.weibo_account -> {
                        // 微博登录
                        val weibo = ShareSDK.getPlatform(SinaWeibo.NAME)
                        authorize(weibo)
                    }

                    else -> {
                    }
                }
            }
        }

    private fun authorize(plat: Platform) {
        if (plat.isAuthValid()) {
            plat.removeAccount(true);
        }
        plat.SSOSetting(false)
        plat.platformActionListener = platformActionListener
//        plat.authorize()
        //获取用户资料
        plat.showUser(null)

    }

    private val platformActionListener = object : PlatformActionListener {
        override fun onComplete(platform: Platform, i: Int, hashMap: HashMap<String, Any>) {

            LogUtils.i(
                platform.name,
                platform.db.get("unionid"),
                platform.db.userId,
                platform.db.token
            )

            if (currentTimeMillis != 0L && System.currentTimeMillis() - currentTimeMillis <= 1600) {
                LogUtils.i("xxxxxxxxxxxxxxxxxxxx")
                return
            }

            currentTimeMillis = System.currentTimeMillis()
            //            hideProgressDialog();
            showLongMessage("授权成功")
            val platformName = platform.name
            val openId =
                if (platform.name == Wechat.NAME) platform.db.get("unionid") else platform.db.userId
            val token = platform.db.token
            val refreshToken = platform.db["refresh_token"]
            LogUtils.i(
                platformName
                        + "///success UserID///" + openId
                        + "///success token///" + token
            )
            loginByThirdParty(platformName, openId, token, refreshToken)
        }

        override fun onError(platform: Platform, i: Int, throwable: Throwable) {
            runOnUiThread {
                showMessage(R.string.request_third_party_failed_alert)
            }

        }

        override fun onCancel(platform: Platform, i: Int) {
            runOnUiThread {
                showMessage(R.string.request_third_party_cancel)
            }
        }
    }

    /**
     * @des: 第三方登录
     * @params:[platformName, openId, token]
     * @return:void
     */

    private fun loginByThirdParty(
        platformName: String,
        openId: String,
        token: String,
        refreshToken: String
    ) {

        var platName = ""
        when (platformName) {
            //微博授权
            SinaWeibo.NAME -> platName = Constants.WEIBO
            QQ.NAME -> platName = Constants.QQ
            //微信授权
            Wechat.NAME -> platName = Constants.WECHAT
        }
        val finalPlatName = platName
        HttpManager.getInstance().requestPassport().thirdPartyLogin(
            userPreferences!!.channel,
            userPreferences!!.imei,
            Constants.SCAN_PRO,
            platName,
            token,
            openId,
            RegisterModel::class.java,
            object : MiaoHttpManager.Callback<RegisterModel> {
                override fun onStart() {
                    showProgressDialog()
                }

                override fun onResponse(entity: MiaoHttpEntity<RegisterModel>) {

                    hideProgressDialog()
                    LogUtils.i(Gson().toJson(entity))
                    val registerModel = entity.body
                    if (!registerModel.isActive) {
                        val intent = Intent(this@LoginActivity, ThirdPartyBindActivity::class.java)
                        intent.putExtra("type", type)
                        intent.putExtra("thirdPartyToken", token)
                        intent.putExtra("thirdPartyOpenId", openId)
                        intent.putExtra("thirdPartyPlatName", platformName)
                        intent.putExtra("thirdPartyRefreshToken", refreshToken)
                        intent.putExtra("platName", finalPlatName)
                        intent.putExtra("userId", entity.body.id)
                        ActivityUtils.startActivity(intent)
                    } else {
                        confirmToClearLastUserData(
                            entity,
                            true,
                            openId,
                            token,
                            platformName,
                            finalPlatName,
                            refreshToken
                        )
                    }
                }

                override fun onFailure(entity: MiaoHttpEntity<RegisterModel>) {
                    hideProgressDialog()
                    if (entity.code == MiaoHttpManager.STATUS_INVALID_USER_OR_PASSWORD) {
                        showMessage(R.string.toast_error)
                    } else {
                        showMessage(R.string.request_failed_alert)
                    }
                }

                override fun onError(e: Exception) {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)
                }
            })
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtils.unregisterSoftInputChangedListener(window)
//        realm.close()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

//    override fun onBackPressed() {
//        exitApp()
//    }
//    private fun exitApp() {
//        if (System.currentTimeMillis() - exitTime > 2000) {
//            showMessage(R.string.confirm_exit)
//            exitTime = System.currentTimeMillis()
//        } else {
//            super.onBackPressed()
//        }
//    }
}
