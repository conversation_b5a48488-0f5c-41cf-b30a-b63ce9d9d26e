package com.czur.scanpro.ui.component

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.Animation
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.czur.scanpro.R
import com.czur.scanpro.databinding.ViewCameraGuideBinding

class CameraGuideView : ConstraintLayout {
    private lateinit var binding: ViewCameraGuideBinding

    private var mIsEdgeMode: Boolean = false
    private var sampleImage: ImageView? = null
    private var previewBorderView: ImageView? = null
    private var mListener: (() -> Unit)? = null

    constructor(context: Context?) : super(context!!) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context!!, attrs) {

        init()
    }

    fun init() {

//        val layoutInflater: LayoutInflater =
//            context!!.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
//        layoutInflater.inflate(R.layout.view_camera_guide, this)
        binding = ViewCameraGuideBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun startAnimEdgeMode() {
        mIsEdgeMode = true
        startAnim()
    }

    fun startAnimBookMode() {
        mIsEdgeMode = false
        startAnim()
    }

    private fun startAnim() {
        initAllDifferent()

        val firstAlertTextShowAnim = ObjectAnimator.ofFloat(binding.firstAlertText, "alpha", 0f, 1f)
        firstAlertTextShowAnim.startDelay = 1000
        firstAlertTextShowAnim.duration = 250

        val firstAlertTextHideAnim = ObjectAnimator.ofFloat(binding.firstAlertText, "alpha", 1f, 0f)
        firstAlertTextHideAnim.addListener(object : Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                animStep2()
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationStart(animation: Animator) {
            }
        })
        firstAlertTextHideAnim.startDelay = 1500
        firstAlertTextHideAnim.duration = 250

        val animatorSet = AnimatorSet()
        animatorSet.play(firstAlertTextShowAnim).before(firstAlertTextHideAnim)
        animatorSet.start()
    }

    private fun animStep2() {
        if (sampleImage == null) {
            sampleImage = makeSampleImage()
        }

        val animatorSetFirst = AnimatorSet()
        animatorSetFirst.playTogether(
            ObjectAnimator.ofFloat(binding.whiteBg, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.wrongBg, "alpha", 0f, 1f),
            ObjectAnimator.ofFloat(binding.alertBg, "alpha", 0f, 1f)
        )
        animatorSetFirst.duration = 250

        val animatorSetSecond = AnimatorSet()
        animatorSetSecond.playTogether(
            ObjectAnimator.ofFloat(sampleImage!!, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.alertBg, "alpha", 1f, 0f)
        )
        animatorSetSecond.duration = 250
        animatorSetSecond.startDelay = 1500

        val animatorSet = AnimatorSet()
        animatorSet.play(animatorSetFirst).before(animatorSetSecond)
        animatorSet.addListener(object : Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                animStep3()
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationStart(animation: Animator) {
            }
        })
        animatorSet.start()

        val sampleImageShowAnim = ObjectAnimator.ofFloat(sampleImage!!, "alpha", 0f, 1f)
        sampleImageShowAnim.duration = 700
        sampleImageShowAnim.start()
    }

    private fun animStep3() {
        binding.alertIcon.setImageResource(R.mipmap.camera_guide_alert_icon_right)
        binding.alertBg.z = binding.rightBg.z + 1

        val animatorSetFirst = AnimatorSet()
        animatorSetFirst.playTogether(
            ObjectAnimator.ofFloat(binding.wrongBg, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.rightBg, "alpha", 0f, 1f)
        )
        animatorSetFirst.duration = 250

        val animatorSetSecond = AnimatorSet()
        animatorSetSecond.playTogether(
            ObjectAnimator.ofFloat(sampleImage!!, "alpha", 0f, 1f),
            ObjectAnimator.ofFloat(binding.alertBg, "alpha", 0f, 1f)
        )
        animatorSetSecond.duration = 250

        val alertHideAnim = ObjectAnimator.ofFloat(binding.alertBg, "alpha", 1f, 0f)
        alertHideAnim.duration = 250
        alertHideAnim.startDelay = 1500
        alertHideAnim.addListener(object : Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                animStep4()
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationStart(animation: Animator) {
            }
        })

        val animatorSet = AnimatorSet()
        animatorSet.play(animatorSetSecond).before(alertHideAnim).after(animatorSetFirst)
        animatorSet.start()
    }

    @SuppressLint("ObjectAnimatorBinding")
    private fun animStep4() {
        val alertTextShowAnim = ObjectAnimator.ofFloat(binding.secondAlertText, "alpha", 0f, 1f)
        alertTextShowAnim.duration = 250

        val alertTextHideAnim = ObjectAnimator.ofFloat(binding.secondAlertText, "alpha", 1f, 0f)
        alertTextHideAnim.duration = 250

        val animatorSet = AnimatorSet()
        if (mIsEdgeMode) {
            val firstRotateAnim = ObjectAnimator.ofFloat(sampleImage, "rotation", 0f, 5f)
            firstRotateAnim.duration = 200
            firstRotateAnim.startDelay = 200

            val secondRotateAnim = ObjectAnimator.ofFloat(sampleImage, "rotation", 5f, -5f)
            secondRotateAnim.duration = 600

            val thirdRotateAnim = ObjectAnimator.ofFloat(sampleImage, "rotation", -5f, 0f)
            thirdRotateAnim.duration = 400

            val rotateAnimatorSet = AnimatorSet()
            rotateAnimatorSet.play(secondRotateAnim).before(thirdRotateAnim).after(firstRotateAnim)
            rotateAnimatorSet.start()

            animatorSet.play(rotateAnimatorSet).with(alertTextShowAnim).before(alertTextHideAnim)
        } else {
            alertTextHideAnim.startDelay = 1400
            animatorSet.play(alertTextShowAnim).before(alertTextHideAnim)
        }
        animatorSet.addListener(object : Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                animStep5()
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationStart(animation: Animator) {
            }
        })
        animatorSet.start()
    }

    private fun animStep5() {
        if (previewBorderView == null) {
            previewBorderView = makePreviewBorderImage()
        }
        binding.secondAlertText.text = context.getString(R.string.camera_guide_third_alert_text)

        // 提示文字显示动画
        val alertTextShowAnim = ObjectAnimator.ofFloat(binding.secondAlertText, "alpha", 0f, 1f)
        alertTextShowAnim.duration = 250
        alertTextShowAnim.start()

        previewBorderView!!.scaleX = 1.2f
        previewBorderView!!.scaleY = 1.2f

        val previewBorderZoomSmallAnim = ScaleAnimation(
            1f, 0.83f, 1f, 0.83f,
            Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f
        )
        previewBorderZoomSmallAnim.duration = 600
        previewBorderZoomSmallAnim.fillAfter = true
        previewBorderZoomSmallAnim.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationRepeat(animation: Animation?) {
            }

            override fun onAnimationEnd(animation: Animation?) {
                val handler = Handler()
                if (handler != null) {
                    handler.postDelayed({
                        previewBorderView!!.alpha = 0f
                        handler.postDelayed({
                            previewBorderView!!.alpha = 1f
                            handler.postDelayed({
                                previewBorderView!!.alpha = 0f
                                handler.postDelayed({
                                    previewBorderView!!.alpha = 1f

                                    val alertTextHideAnim =
                                        ObjectAnimator.ofFloat(binding.secondAlertText, "alpha", 1f, 0f)
                                    alertTextHideAnim.duration = 250
                                    alertTextHideAnim.startDelay = 1000
                                    alertTextHideAnim.addListener(object :
                                        Animator.AnimatorListener {
                                        override fun onAnimationRepeat(animation: Animator) {
                                        }

                                        override fun onAnimationEnd(animation: Animator) {
                                            animStep6()
                                        }

                                        override fun onAnimationCancel(animation: Animator) {
                                        }

                                        override fun onAnimationStart(animation: Animator) {
                                        }
                                    })
                                    alertTextHideAnim.start()
                                }, 500)
                            }, 500)
                        }, 500)
                    }, 1000)
                } else {
                    animStep6()
                }

            }

            override fun onAnimationStart(animation: Animation?) {
            }
        })
        previewBorderView!!.startAnimation(previewBorderZoomSmallAnim)
    }

    private fun animStep6() {
        binding.secondAlertText.text = context.getString(R.string.camera_guide_fourth_alert_text)

        val alertTextShowAnim = ObjectAnimator.ofFloat(binding.secondAlertText, "alpha", 0f, 1f)
        alertTextShowAnim.duration = 250

        val btnAnimatorSet = AnimatorSet()
        btnAnimatorSet.playTogether(
            ObjectAnimator.ofFloat(binding.replayBtn, "alpha", 0f, 1f),
            ObjectAnimator.ofFloat(binding.nextBtn, "alpha", 0f, 1f),
            ObjectAnimator.ofFloat(binding.nextBtnInsideBody, "alpha", 0f, 1f)
        )
        btnAnimatorSet.duration = 250

        val animatorSet = AnimatorSet()
        animatorSet.play(alertTextShowAnim).before(btnAnimatorSet)
        animatorSet.addListener(object : Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                binding.realReplayBtn.setOnClickListener {
                    replayClicked()
                }
                binding.realNextBtn.setOnClickListener {
                    nextClicked()
                }
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationStart(animation: Animator) {
            }
        })
        animatorSet.start()
    }

    private fun initAllDifferent() {
        if (mIsEdgeMode) {
           binding.firstAlertText.text = context.getString(R.string.camera_guide_first_alert_text_edge)
           binding.alertText.text = context.getString(R.string.camera_guide_check_bg_alert_text_edge)
           binding.secondAlertText.text = context.getString(R.string.camera_guide_second_alert_text_edge)
        } else {
            binding.firstAlertText.text = context.getString(R.string.camera_guide_first_alert_text_book)
            binding.alertText.text = context.getString(R.string.camera_guide_check_bg_alert_text_book)
            binding.secondAlertText.text = context.getString(R.string.camera_guide_second_alert_text_book)
        }
    }

    private fun makeSampleImage(): ImageView {
        val imageView = ImageView(context)
        imageView.id = R.id.CameraGuideSampleImage
        imageView.setImageResource(if (mIsEdgeMode) R.mipmap.camera_guide_sample_image_edge else R.mipmap.camera_guide_sample_image_book)
        imageView.alpha = 0f
        binding.layoutBody.addView(imageView)

        val scale: Float
        val imageScale: Float
        if (mIsEdgeMode) {
            scale = 450f / 750f
            imageScale = 450f / 608f
        } else {
            scale = 1380f / 1500f
            imageScale = 1380f / 1614f
        }

        val imageWidth = resources.displayMetrics.widthPixels.toFloat() * scale
        val imageHeight = imageWidth / imageScale

        val constraintSet = ConstraintSet()
        constraintSet.connect(imageView.id, ConstraintSet.START, R.id.wrongBg, ConstraintSet.START)
        constraintSet.connect(imageView.id, ConstraintSet.TOP, R.id.wrongBg, ConstraintSet.TOP)
        if (mIsEdgeMode) {
            constraintSet.connect(imageView.id, ConstraintSet.END, R.id.wrongBg, ConstraintSet.END)
            constraintSet.connect(
                imageView.id,
                ConstraintSet.BOTTOM,
                R.id.wrongBg,
                ConstraintSet.BOTTOM
            )
        }
        constraintSet.constrainWidth(imageView.id, imageWidth.toInt())
        constraintSet.constrainHeight(imageView.id, imageHeight.toInt())
        constraintSet.applyTo(binding.layoutBody)
        return imageView
    }

    private fun makePreviewBorderImage(): ImageView {
        val imageView = ImageView(context)
        imageView.id = R.id.CameraGuidePreviewBorderImage
        imageView.setImageResource(if (mIsEdgeMode) R.mipmap.camera_guide_preview_border_edge else R.mipmap.camera_guide_preview_border_book)
        imageView.alpha = 0f
        binding.layoutBody.addView(imageView)

        val constraintSet = ConstraintSet()
        constraintSet.connect(
            imageView.id,
            ConstraintSet.START,
            sampleImage!!.id,
            ConstraintSet.START
        )
        constraintSet.connect(imageView.id, ConstraintSet.TOP, sampleImage!!.id, ConstraintSet.TOP)
        constraintSet.connect(imageView.id, ConstraintSet.END, sampleImage!!.id, ConstraintSet.END)
        constraintSet.connect(
            imageView.id,
            ConstraintSet.BOTTOM,
            sampleImage!!.id,
            ConstraintSet.BOTTOM
        )
        constraintSet.constrainWidth(imageView.id, 0)
        constraintSet.constrainHeight(imageView.id, 0)
        constraintSet.applyTo(binding.layoutBody)
        return imageView
    }

    @SuppressLint("ObjectAnimatorBinding")
    private fun replayClicked() {
       binding.alertIcon.setImageResource(R.mipmap.camera_guide_alert_icon_wrong)
       binding.realReplayBtn.setOnClickListener(null)
       binding.realNextBtn.setOnClickListener(null)
       binding.wrongBg.alpha = 0f

        val animatorSet = AnimatorSet()
        animatorSet.playTogether(
            ObjectAnimator.ofFloat(binding.replayBtn, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.nextBtn, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.nextBtnInsideBody, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.secondAlertText, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.rightBg, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(binding.whiteBg, "alpha", 0f, 1f),
            ObjectAnimator.ofFloat(sampleImage, "alpha", 1f, 0f),
            ObjectAnimator.ofFloat(previewBorderView, "alpha", 1f, 0f)
        )
        animatorSet.duration = 250
        animatorSet.addListener(object : Animator.AnimatorListener {
            override fun onAnimationRepeat(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                startAnim()
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationStart(animation: Animator) {
            }
        })
        animatorSet.start()
    }

    private fun nextClicked() {
        mListener?.invoke()
    }

    fun setOnCameraGuideViewNextBtnOnClickListener(onCameraGuideViewNextBtnOnClickListener: () -> Unit) {
        mListener = onCameraGuideViewNextBtnOnClickListener
    }
}