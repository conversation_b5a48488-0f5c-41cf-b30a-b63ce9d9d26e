package com.czur.scanpro.wxapi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import com.czur.scanpro.event.EventType;
import com.czur.scanpro.event.WechatPayEvent;
import com.czur.scanpro.ui.base.SaomiaoApplication;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import org.greenrobot.eventbus.EventBus;

public class WXPayEntryActivity extends Activity implements IWXAPIEventHandler {

    private IWXAPI api;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        api = ((SaomiaoApplication) getApplication()).wechatPayApi();
        api.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq baseReq) {
    }

    @Override
    public void onResp(BaseResp baseResp) {
        if (baseResp.getType() == ConstantsAPI.COMMAND_PAY_BY_WX) {
//            AlertDialog.Builder builder = new AlertDialog.Builder(this);
//            builder.setTitle("onResp");
//            builder.setMessage(String.valueOf(baseResp.errCode));
//            builder.show();
            EventBus.getDefault().post(new WechatPayEvent(EventType.WECHAT_PAY_CALLBACK, baseResp.errCode));
            finish();
        }
    }
}
