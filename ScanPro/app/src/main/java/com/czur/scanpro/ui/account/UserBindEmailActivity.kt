package com.czur.scanpro.ui.account

import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityBindEmailBinding
import com.czur.scanpro.databinding.ActivityThirdBindBinding
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.UserInfoEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.user.UserInfoActivity
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator
import org.greenrobot.eventbus.EventBus

class UserBindEmailActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityBindEmailBinding by lazy{
        ActivityBindEmailBinding.inflate(layoutInflater)
    }

    private var codeHasContent = false
    private var timeCount: TimeCount? = null
    private var httpManager: HttpManager? = null
    private var userPreferences: UserPreferences? = null
    private var isChangeEmail: Boolean = false

    private val codeTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            codeHasContent = s.isNotEmpty()
            checkChangePhoneButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            codeHasContent = s.isNotEmpty()
            checkChangePhoneButtonToClick()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_bind_email)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        isChangeEmail = intent.getBooleanExtra("changeEmail", false)
        httpManager = HttpManager.getInstance()
        userPreferences = UserPreferences.getInstance(this)
        binding.registerTopBar.normalTitle.setText(R.string.user_change_email)

    }

    private fun registerEvent() {
        binding.getCodePswBtn.setOnClickListener(this)
        binding.registerTopBar.normalBackBtn.setOnClickListener(this)

        binding.nextStepBtn.setOnClickListener(this)
        binding.nextStepBtn.isSelected = false
        binding.nextStepBtn.isClickable = false
        binding.codeEdt.addTextChangedListener(codeTextWatcher)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            R.id.getCodePswBtn -> checkEmail()
            R.id.nextStepBtn -> checkIsHasMobileToBind()
            else -> {
            }
        }
    }


    /**
     * @des:校验是否有手机号
     * @params:
     * @return:
     */
    private fun checkIsHasMobileToBind() {
        if (isChangeEmail) {
            hasMailToBind()
        } else {
            noEmailToBind()
        }
    }

    /**
     * @des:没有邮箱绑定
     * @params:
     * @return:
     */
    private fun noEmailToBind() {

        val textMail = binding.accountEdt.text.toString()
        if (binding.accountEdt.text!!.isEmpty()) {
            showMessage(R.string.login_alert_mail_empty)
        } else if (binding.codeEdt.text!!.length <= 5) {
            showMessage(R.string.edit_text_code_length)
        } else if (!RegexUtils.isEmail(textMail)) {
            showMessage(R.string.login_alert_mail_error)
        } else if (textMail == userPreferences!!.userEmail) {
            showMessage(R.string.mail_toast_put_new_mail)
        } else {
            KeyboardUtils.hideSoftInput(this)
            httpManager!!.requestPassport().notBindUpdateMail(userPreferences!!.imei, Constants.SCAN_PRO,
                    userPreferences!!.channel, userPreferences!!.userId, userPreferences!!.token, userPreferences!!.userId,
                    textMail, binding.codeEdt.text.toString(), String::class.java, object : MiaoHttpManager.Callback<String> {
                override fun onStart() {
                    showProgressDialog(false)
                }

                override fun onResponse(entity: MiaoHttpEntity<String>) {
                    LogUtils.i(Gson().toJson(entity))
                    userPreferences!!.userEmail = textMail
                    EventBus.getDefault().post(UserInfoEvent(EventType.BIND_EMAIL))
                    ActivityUtils.finishToActivity(UserInfoActivity::class.java, false)

                }

                override fun onFailure(entity: MiaoHttpEntity<String>) {
                    hideProgressDialog()
                    when (entity.code) {
                        MiaoHttpManager.STATUS_EMAIL_BIND_OTHER_USER -> showMessage(R.string.mail_bind_other_user)
                        MiaoHttpManager.STATUS_INVALID_EMAIL -> showMessage(R.string.invalid_email)
                        MiaoHttpManager.STATUS_CODE_EXPIRED -> showMessage(R.string.mail_code_expired)
                    }

                }

                override fun onError(e: Exception) {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)
                }
            })
        }
    }


    /**
     * @des:有邮箱绑定
     * @params:
     * @return:
     */
    private fun hasMailToBind() {

        val textMail = binding.accountEdt.text.toString()
        val code = binding.codeEdt.text.toString()
        if (textMail.isEmpty()) {
            showMessage(R.string.login_alert_mail_empty)

        } else if (code.length <= 5) {
            showMessage(R.string.edit_text_code_length)

        } else if (!RegexUtils.isEmail(textMail)) {
            showMessage(R.string.login_alert_mail_error)

        } else if (textMail == userPreferences!!.userEmail) {
            showMessage(R.string.mail_toast_put_new_mail)

        } else {
            KeyboardUtils.hideSoftInput(this)

            httpManager!!.requestPassport().updateMailSecond(userPreferences!!.imei, Constants.SCAN_PRO,
                    userPreferences!!.channel, userPreferences!!.userId, userPreferences!!.token, userPreferences!!.userId,
                    textMail, userPreferences!!.ukey, code, String::class.java, object : MiaoHttpManager.Callback<String> {
                override fun onStart() {
                    showProgressDialog(false)
                }

                override fun onResponse(entity: MiaoHttpEntity<String>) {
                    hideProgressDialog()
                    ActivityUtils.finishActivity(this@UserBindEmailActivity)
                    LogUtils.i(Gson().toJson(entity))
                    userPreferences!!.userEmail = textMail
                    EventBus.getDefault().post(UserInfoEvent(EventType.CHANGE_EMAIL))
                    ActivityUtils.finishToActivity(UserInfoActivity::class.java, false)

                }

                override fun onFailure(entity: MiaoHttpEntity<String>) {
                    hideProgressDialog()
                    binding.codeEdt.setText(Constants.EMPTY)

                    when {
                        entity.code == MiaoHttpManager.STATUS_EMAIL_BIND_OTHER_USER -> showMessage(R.string.mail_bind_other_user)
                        entity.code == MiaoHttpManager.STATUS_CODE_EXPIRED -> showMessage(R.string.mail_code_expired)
                        entity.code == MiaoHttpManager.STATUS_INVALID_EMAIL -> showMessage(R.string.invalid_email)
                        entity.code == MiaoHttpManager.STATUS_CODE_EXPIRED -> showMessage(R.string.mail_code_expired)
                    }
                }

                override fun onError(e: Exception) {
                    hideProgressDialog()
                    binding.codeEdt.setText(Constants.EMPTY)

                    showMessage(R.string.request_failed_alert)
                }
            })
        }
    }


    /**
     * @des:校验邮箱
     * @params:
     * @return:
     */

    private fun checkEmail() {
        if (Validator.isNotEmpty(binding.accountEdt.text.toString())) {
            if (RegexUtils.isEmail(binding.accountEdt.text.toString())) {
                getEmailCode(binding.accountEdt.text.toString())

            } else {
                showMessage(R.string.login_alert_mail_error)
            }
        } else {
            showMessage(R.string.login_alert_mail_empty)
        }

    }

    /**
     * @des: 获取邮箱验证码
     * @params:[email]
     * @return:void
     */
    private fun getEmailCode(email: String) {
        val locale = resources.configuration.locale
        val language = locale.toString()
        httpManager!!.requestPassport().mailCode(email, EtUtils.getLocale(language), String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                LogUtils.iTag("send mail code", Gson().toJson(entity))
                timeCountBegin()
                showMessage(R.string.toast_code_send)
            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                when {
                    entity.code== MiaoHttpManager.STATUS_CODE_1_MIN -> showMessage(R.string.toast_code_1_min)
                    entity.code== MiaoHttpManager.STATUS_5_MIN_4_TIME -> showMessage(R.string.toast_5_min_4_time)
                    entity.code== MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY -> showMessage(R.string.toast_5_time_in_one_day)
                    else -> showMessage(R.string.request_failed_alert)
                }
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkChangePhoneButtonToClick() {

        val mailIsNotEmpty = Validator.isNotEmpty(binding.accountEdt.text.toString())


        if (mailIsNotEmpty && codeHasContent) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
            binding.nextStepBtn.background = drawable
            binding.nextStepBtn.isSelected = true
            binding.nextStepBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
            binding.nextStepBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
            binding.nextStepBtn.background = drawable
            binding.nextStepBtn.isSelected = false
            binding.nextStepBtn.setTextColor(resources.getColor(R.color.white))
            binding.nextStepBtn.isClickable = false
        }
    }


    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private fun timeCountBegin() {
        timeCount = TimeCount(60000, 1000)
        timeCount!!.start()

    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    internal inner class TimeCount(millisInFuture: Long, countDownInterval: Long) : CountDownTimer(millisInFuture, countDownInterval) {

        override fun onFinish() {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.red_de4d4d)).build()
            binding.getCodePswBtn.background = drawable
            binding.getCodePswBtn.setText(R.string.gain)
            binding.getCodePswBtn.isClickable = true
            binding.getCodePswBtn.isSelected = true
        }

        override fun onTick(millisUntilFinished: Long) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e1)).build()
            binding.getCodePswBtn.background = drawable
            binding.getCodePswBtn.isClickable = false
            binding.getCodePswBtn.text = (millisUntilFinished / 1000).toString() + " s"
            binding.getCodePswBtn.isSelected = false
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        if (Validator.isNotEmpty(timeCount)) {
            timeCount!!.cancel()
        }
    }
}
