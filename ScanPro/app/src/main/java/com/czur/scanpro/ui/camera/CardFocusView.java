package com.czur.scanpro.ui.camera;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;

import com.blankj.utilcode.util.SizeUtils;

/**
 * Extends View. Just used to draw Rect when the screen is touched
 * for auto focus.
 * <p/>
 * Use setHaveTouch function to set the status and the Rect to be drawn.
 * Call invalidate to draw Rect. Call invalidate again after
 * setHaveTouch(false, Rect(0, 0, 0, 0)) to hide the rectangle.
 */
public class CardFocusView extends View {
    private boolean haveTouch = false;
    private Rect touchArea;
    private Paint paint;
    private int color;
    private float x1;
    private float y1;

    public CardFocusView(Context context, AttributeSet attrs) {
        super(context, attrs);
        paint = new Paint();
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(SizeUtils.dp2px(1f));
        haveTouch = false;
    }


    public void show(Rect rect, int color) {
        setVisibility(VISIBLE);
        haveTouch = true;
        touchArea = rect;
        this.color = color;
        invalidate();
    }

    public void hide() {
        setVisibility(GONE);
        haveTouch = false;
        invalidate();
    }



    @Override
    public void onDraw(Canvas canvas) {
        if (haveTouch) {
            paint.setColor(color);
            canvas.drawRect(touchArea, paint);
            float recCenterX = touchArea.centerX();
            float recCenterY = touchArea.centerY();

//            canvas.drawLine(touchArea.left, recCenterY, touchArea.left + SizeUtils.dp2px(5f), recCenterY, paint);
//            canvas.drawLine(recCenterX, touchArea.top, recCenterX, touchArea.top + SizeUtils.dp2px(5f), paint);
//            canvas.drawLine( touchArea.right, recCenterY,touchArea.right - SizeUtils.dp2px(5f), recCenterY, paint);
//            canvas.drawLine(recCenterX, touchArea.bottom,recCenterX, touchArea.bottom -SizeUtils.dp2px(5f),  paint);

        }
    }
}