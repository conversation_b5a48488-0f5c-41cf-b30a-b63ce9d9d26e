package com.czur.scanpro.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.blankj.utilcode.util.BarUtils;
import com.czur.scanpro.R;


public class ShareBottomDialog extends Dialog implements View.OnClickListener {
    private Context mContext;
    private LinearLayout weixin;
    private LinearLayout qq;

    public ShareBottomDialog(Context context, ShareDialogOnClickListener shareDialogOnClickListener) {
        //重点实现R.style.DialogStyle 动画效果
        this(context, R.style.SocialAccountDialogStyle);
        this.shareDialogOnClickListener = shareDialogOnClickListener;
        mContext = context;
    }

    public ShareBottomDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_share_bottom_sheet);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        weixin = (LinearLayout) getWindow().findViewById(R.id.weixin_account);
        qq = (LinearLayout) getWindow().findViewById(R.id.qq_account);


        //设置显示的位置
        params.gravity = Gravity.BOTTOM;
        //设置dialog的宽度
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);
        BarUtils.setStatusBarColor( getWindow(), Color.TRANSPARENT);
        BarUtils.setNavBarColor( getWindow(), Color.TRANSPARENT);
        BarUtils.setStatusBarLightMode(getWindow(), true);
        weixin.setOnClickListener(this);
        qq.setOnClickListener(this);



    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.weixin_account) {
            if (shareDialogOnClickListener != null) {
                shareDialogOnClickListener.onClick(R.id.weixin_account);
            }
        } else if (id == R.id.qq_account) {
            if (shareDialogOnClickListener != null) {
                shareDialogOnClickListener.onClick(R.id.qq_account);
            }
        }
    }


    /**

       点击事件接口

     **/
    public interface ShareDialogOnClickListener {
        /**
         *
         * @param viewId
         */
        void onClick(int viewId);
    }
    private ShareDialogOnClickListener shareDialogOnClickListener;

    private void setShareDialogOnClickListener(ShareDialogOnClickListener shareDialogOnClickListener) {
        this.shareDialogOnClickListener = shareDialogOnClickListener;

    }

}