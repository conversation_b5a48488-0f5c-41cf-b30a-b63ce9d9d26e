package com.czur.scanpro.ui.file

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.UriUtils
import com.czur.scanpro.R
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.share.FileUtil
import com.czur.scanpro.utils.share.ShareContentType
import com.czur.scanpro.utils.share.ShareUtils
import com.github.barteksc.pdfviewer.PDFView
import com.github.barteksc.pdfviewer.scroll.DefaultScrollHandle
import com.github.barteksc.pdfviewer.util.FitPolicy.BOTH
import com.github.barteksc.pdfviewer.util.FitPolicy.WIDTH
import java.io.File

class PdfPreviewActivity : BaseActivity(), View.OnClickListener {
    private var previewNoIconShare: RelativeLayout? = null
    private var previewNoIconBackBtn: ImageView? = null
    private var previewNoIconTitle: TextView? = null
    private var pdfView: PDFView? = null
    private var pdfPath: String? = null
    private var pdfName: String? = null
    private val isEt: Boolean = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_pdf_preview)
        initComponent()
        previewPdf(pdfPath)
        registerEvent()
    }

    private fun initComponent() {

        previewNoIconShare = findViewById(R.id.preview_no_icon_share) as RelativeLayout
        previewNoIconBackBtn = findViewById(R.id.preview_no_icon_back_btn) as ImageView
        previewNoIconTitle = findViewById(R.id.preview_no_icon_title) as TextView
        pdfView = findViewById(R.id.pdfView) as PDFView
        pdfName = getIntent().getStringExtra("pdfName")
        pdfPath = getIntent().getStringExtra("pdfPath")
        LogUtils.i(pdfPath, pdfName)
        previewNoIconTitle!!.text = pdfName

    }

    private fun registerEvent() {
        previewNoIconBackBtn!!.setOnClickListener(this)
        previewNoIconShare!!.setOnClickListener(this)
    }

    /**
     * @des: 浏览PDF
     * @params:[path]
     * @return:void
     */
    private fun previewPdf(path: String?) {
        showProgressDialog(true)
        LogUtils.e(pdfPath!!)
        pdfView!!.fromFile(File(pdfPath!!))
                // allows to block changing pages using swipe
                .enableSwipe(true)
                .swipeHorizontal(false)
                .enableDoubletap(true)
                .pageFitPolicy(BOTH)
                .defaultPage(0)
                // allows to draw something on the current page, usually visible in the middle of the screen
                .onDraw { canvas, pageWidth, pageHeight, displayedPage -> Log.i("xxx onDraw", "pageWidth:$pageWidth //pageHeight:$pageHeight//displayedPage:$displayedPage") }
                // allows to draw something on all pages, separately for every page. Called only for visible pages

                .onLoad { nbPages ->
                    hideProgressDialog()
                    Log.i("xxx onLoadComplete", "nbPages:$nbPages")
                }
                .onPageScroll { page, positionOffset -> }
                .onError {
                    hideProgressDialog()
                    showLongMessage(R.string.load_pdf_error)
                }// called after document is loaded and starts to be rendered
                .onPageChange { page, pageCount -> Log.i("xxx onPageChange", "page:$page //pageCount:$pageCount") }.scrollHandle(DefaultScrollHandle(this, false))
                .onRender { nbPages -> Log.i("xxx onRender", "nbPages:$nbPages") } // called after document is rendered for the first time
                // called on single tap, return true if handled, false to toggle scroll handle visibility
                // render annotations (such as comments, colors or forms)
                .enableAnnotationRendering(false)
                .password(null)
                .scrollHandle(null)
                // improve rendering a little bit on low-res screens
                .enableAntialiasing(true)
                // spacing between pages in dp. To define spacing color, set view background
                .spacing(20)
                .pageFitPolicy(WIDTH)
                .load()
    }


    override fun onClick(v: View) {
        when (v.id) {

            R.id.preview_no_icon_back_btn -> ActivityUtils.finishActivity(this)

            R.id.preview_no_icon_share -> {
                LogUtils.i(pdfPath)

                ShareUtils.Builder(this)
                        .setOnActivityResult(REQUEST_SHARE_FILE_CODE)
                        .setContentType(ShareContentType.FILE)
                        .setShareFileUri(UriUtils.file2Uri(File(pdfPath ?: ""))
//                                FileUtil.getFileUri(this@PdfPreviewActivity, ShareContentType.FILE, File(pdfPath!!))
                )
                        .setTitle(getString(R.string.share_to))
                        .build()
                        .shareBySystem()
            }

            else -> {
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        LogUtils.i("requestCode=$requestCode", " resultCode=$resultCode")
        if (requestCode == REQUEST_SHARE_FILE_CODE) {
        }
    }

    companion object {

        private val REQUEST_SHARE_FILE_CODE = 666
    }
}
