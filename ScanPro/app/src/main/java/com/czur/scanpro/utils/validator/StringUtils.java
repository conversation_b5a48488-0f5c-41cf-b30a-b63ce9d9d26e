package com.czur.scanpro.utils.validator;

import android.text.TextUtils;

import org.json.JSONObject;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Map;

/**
 * Created by Yz on 2018/3/8
 * Email：<EMAIL>
 */

public class StringUtils {
    private static final String TAG = "StringUtils";

    public static final String EMPTY = "";


    public static String ensureNotNull(String value) {
        return value == null ? "" : value;
    }

    public static String addCommas(float num) {
        NumberFormat formatter = new DecimalFormat("###,###");
        return formatter.format(num);
    }

    public static boolean isBlank(CharSequence s) {
        if (isEmpty(s)) {
            return true;
        }

        final int len = s.length();
        char ch;
        for (int i = 0; i < len; ++i) {
            ch = s.charAt(i);
            switch (ch) {
                case ' ':
                case '\t':
                case '\n':
                case '\r':
                    break;
                default:
                    return false;
            }
        }
        return true;
    }

    public static boolean isNotBlank(CharSequence s) {
        return !isBlank(s);
    }

    public static boolean isEmpty(CharSequence s) {
        return (s == null) ? true : (s.length() == 0);
    }

    public static boolean isNotEmpty(CharSequence s) {
        return !isEmpty(s);
    }

    public static boolean isNumeric(String s) {
        if (isEmpty(s)) {
            return false;
        }

        final int len = s.length();
        char ch;
        for (int i = 0; i < len; ++i) {
            ch = s.charAt(i);
            switch (ch) {
                case '0':
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                    break;
                default:
                    return false;
            }
        }
        return true;
    }

    public static boolean isTrimEmpty(CharSequence str) {
        if (str == null || str.length() == 0 || str.toString().trim().length() == 0) {
            return true;
        }
        return false;
    }

    public static String trimUni(String s) {
        int len = s.length();
        int st = 0;
        char[] val = s.toCharArray();

        while (st < len && (val[st] <= ' ' || val[st] == '　')) {
            st++;
        }
        while (st < len && (val[len - 1] <= ' ' || val[len - 1] == '　')) {
            len--;
        }

        if (st > 0 || len < s.length()) {
            return s.substring(st, len);
        }

        return s;
    }


    public static boolean isDigitsOnlyIncludeNegativeNumber(String str) {
        if (isEmpty(str)) {
            return false;
        }

        if (str.startsWith("-") && str.length() > 1) {
            str = str.substring(1);
        }

        return TextUtils.isDigitsOnly(str);
    }

    public static boolean isDigitsOnly(String str) {
        if (isEmpty(str)) {
            return false;
        }

        return TextUtils.isDigitsOnly(str);
    }

    public static String replaceNewLineToSpace(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("\n", " ");
    }

    public static int getByteLengthToInt(String str) {
        if (isEmpty(str)) {
            return 0;
        }
        str = str.trim();

        int byteLength = 0;
        try {
            byteLength = str.getBytes("EUC-KR").length;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return byteLength;
    }

    @SuppressWarnings("rawtypes")
    public static String mapToString(Map map) {
        if (map == null) {
            throw new IllegalArgumentException("empty argument.");
        }

        String result = new JSONObject(map).toString();
        return result;
    }

    public static String getStackTrace(Throwable aThrowable) {
        if (aThrowable == null) {
            return "";
        }
        final Writer result = new StringWriter();
        final PrintWriter printWriter = new PrintWriter(result);
        aThrowable.printStackTrace(printWriter);
        return result.toString();
    }


    public static String nullToEmpty(String value) {
        if (null == value) {
            return "";
        }
        return value;
    }

    public static String arrayToString(String[] array, String saperator) {
        StringBuffer sb = new StringBuffer();
        if (array == null || array.length == 0) {
            return "";
        }

        sb.append(array[0]);

        if (array.length > 1) {
            for (int i = 1; i < array.length; i++) {
                sb.append(saperator);
                sb.append(array[i]);
            }
        }

        return sb.toString();
    }


}
