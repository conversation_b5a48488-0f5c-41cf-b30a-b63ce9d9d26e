package com.czur.scanpro.adapter

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.content.DialogInterface
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.fresco.cache.CustomImageRequest
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.NoHintEditText
import com.czur.scanpro.ui.component.popup.ChangeFileNamePopup
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.LongPressDialog
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.drawee.view.SimpleDraweeView
import com.facebook.imagepipeline.request.ImageRequestBuilder
import com.vicpin.krealmextensions.transaction
import io.realm.Realm
import io.realm.Sort
import java.text.SimpleDateFormat
import java.util.*


/**
 * Created by Yz on 2019/1/5.
 * Email：<EMAIL>
 */
class MainCategoryAdapter
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<CategoryEntity>?) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var realm = Realm.getDefaultInstance()
    var categoryName: String? = null
    var isHide: Boolean = false
    var isAnim: Boolean = false
    var cancelHide: Boolean = false
    private var clickTime = 0L

    var position: Int = -1
    var dialogEdt: NoHintEditText? = null
    var longPressDialog = LongPressDialog(context, LongPressDialog.OnDialogClickListener { viewId, pressDialog ->
        when (viewId) {
            R.id.delete_ll -> {
                showDeleteDialog(pressDialog)
            }
            R.id.edit_ll -> {
                pressDialog.dismiss()
                showChangeFilePopup()
            }

            else -> {
            }
        }
    })

    private fun getSp(): UserPreferences {
        return UserPreferences.getInstance(context)
    }

    private fun getUserIdIsLogin(): String {
        return if (getSp().isValidUser && getSp().isUserLogin) getSp().userId else Constants.NO_USER
    }

    private fun deleteCategory(pressDialog: LongPressDialog) {
        if (realm.isClosed) {
            //重新连接
            realm =Realm.getDefaultInstance();
        }
        realm.transaction {
            val curDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(System.currentTimeMillis()))
            val needDeletePaths = ArrayList<String>()
            val needDeleteDocs = realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).equalTo("userID", getUserIdIsLogin()).findAll()
            for (docEntity in needDeleteDocs) {
                docEntity.isDirty = 1
                docEntity.isDelete = 1
                docEntity.updateTime = curDate
                needDeletePaths.add(docEntity!!.processSmallImagePath!!)
                needDeletePaths.add(docEntity.processImagePath!!)
                needDeletePaths.add(docEntity.baseImagePath!!)
                needDeletePaths.add(docEntity.baseSmallImagePath!!)
                if (docEntity.originalImagePath != null) {
                    needDeletePaths.add(docEntity.originalImagePath!!)
                }
            }
            for (categoryEntity in realm.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).equalTo("userID", getUserIdIsLogin()).findAll()) {
                categoryEntity.isDirty = 1
                categoryEntity.isDelete = 1
                categoryEntity.updateTime = curDate
            }
            ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                override fun doInBackground(): Void? {
                    //删除sd卡上book page
                    for (needDeletePath in needDeletePaths) {
                        FileUtils.delete(needDeletePath)
                    }
                    return null
                }

                override fun onSuccess(result: Void?) {

                }
            })

        }
        pressDialog.dismiss()
        val baseActivity = ActivityUtils.getTopActivity() as BaseActivity
        baseActivity.startAutoSync()
    }

    private fun showDeleteDialog(pressDialog: LongPressDialog) {
        val builder = ScanProCommonPopup.Builder(context, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(context!!.resources.getString(R.string.prompt))
        builder.setMessage(context!!.resources.getString(R.string.confirm_delete))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            deleteCategory(pressDialog)
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    companion object {
        private const val ITEM_TYPE_CATEGORY = 0
        private const val ITEM_TYPE_FOOTER = 1
    }

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private fun showChangeFilePopup() {
        val builder = ChangeFileNamePopup.Builder(context, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setTitle(context!!.getString(R.string.change_category))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            realm!!.executeTransaction { realm ->
                if (!EtUtils.containsEmoji(dialogEdt!!.text.toString())) {
                    if (Validator.isNotEmpty(dialogEdt!!.text.toString())) {
                        val curDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(System.currentTimeMillis()))
                        for (docEntity in realm.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).equalTo("userID", getUserIdIsLogin()).findAll()) {
                            docEntity.isDirty = if (docEntity.isDirty == 1) 1 else 2
                            docEntity.categoryName = dialogEdt!!.text.toString()
                            docEntity.updateTime = curDate
                        }
                        for (categoryEntity in realm.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).equalTo("userID", getUserIdIsLogin()).findAll()) {
                            categoryEntity.isDirty = 1
                            categoryEntity.categoryName = dialogEdt!!.text.toString()
                            categoryEntity.updateTime = curDate
                        }
                        val baseActivity = ActivityUtils.getTopActivity() as BaseActivity
                        baseActivity.startAutoSync()
                    } else {
                        ToastUtils.showShort(R.string.category_not_be_empty)
                    }

                } else {
                    ToastUtils.showShort(R.string.nickname_toast_symbol)
                }
            }

            dialog.dismiss()
        })
        val commonPopup = builder.create()
        dialogEdt = commonPopup.window?.findViewById<NoHintEditText>(R.id.edt)
        commonPopup.show()
    }

    private var onItemClickListener: OnItemClickListener? = null

    fun refreshData(Categorys: List<CategoryEntity>) {
        this.datas = Categorys
        notifyDataSetChanged()

    }

    fun refreshData(Categorys: List<CategoryEntity>, cancelHide: Boolean) {
        this.cancelHide = cancelHide
        this.datas = Categorys
        notifyDataSetChanged()

    }

    fun refreshData(Categorys: List<CategoryEntity>, isAnim: Boolean, isHide: Boolean, position: Int) {
        this.datas = Categorys
        this.isAnim = isAnim
        this.isHide = isHide
        this.position = position
        notifyDataSetChanged()
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == ITEM_TYPE_CATEGORY) {
            val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_category, parent, false)
            return CategoryHolder(view)
        } else {
            val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_category, parent, false)
            return FooterHolder(view)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        if (holder is CategoryHolder) {
            holder.mItem = datas!![position]
            //计算item的长宽
            val itemLayoutParams = holder.categoryInnerItem.layoutParams as RelativeLayout.LayoutParams
            itemLayoutParams.width = ScreenUtils.getScreenWidth() * 576 / 640
            itemLayoutParams.height = itemLayoutParams.width * 292 / 576
            holder.categoryInnerItem.layoutParams = itemLayoutParams
            //计算item中图片的宽
            val imageParams = holder.categoryImage.layoutParams as RelativeLayout.LayoutParams
            imageParams.width = itemLayoutParams.height * 356 / 292
            holder.categoryImage.layoutParams = imageParams

            holder.categoryName.text = holder.mItem!!.categoryName
//            if (cancelHide) {
//                cancelHideAnim(holder)
//            } else {
//                if (!isHide) {
//                    if (isAnim && position == this.position) {
//                        addItemAnim(holder)
//                    }
//                } else {
//                    if (position == this.position) {
//                        hideAnim(holder)
//                    }
//                }
//            }
            if (realm.isClosed) {
                //重新连接
                realm =Realm.getDefaultInstance();
            }

            val results = realm.where(DocEntity::class.java)
                    .equalTo("isDelete", 0.toInt())
                    .equalTo("categoryName", holder.mItem?.categoryName)
                    .equalTo("userID", getUserIdIsLogin())
                    .findAll()
            val first = realm.where(DocEntity::class.java)
                    .equalTo("isDelete", 0.toInt())
                    .equalTo("categoryName", holder.mItem?.categoryName)
                    .sort("createTime", Sort.DESCENDING)
                    .equalTo("userID", getUserIdIsLogin())
                    .findFirst()

            holder.categoryCount.text = String.format(context!!.getString(R.string.category_counts), results.size.toString())
            holder.categoryDate.text = holder.mItem!!.createTime!!.substring(0, 10).replace("-", ".")

            if (first != null && !TextUtils.isEmpty(first.processSmallImagePath)) {
                holder.categoryImage.setImageURI("")
                if (FileUtils.getFileByPath(first.processSmallImagePath) != null) {
                    val controller = Fresco.newDraweeControllerBuilder()
                            .setImageRequest(CustomImageRequest(ImageRequestBuilder.newBuilderWithSource(UriUtils.file2Uri(FileUtils.getFileByPath(first.processSmallImagePath)))))
                            .setOldController(holder.categoryImage.controller)
                            .build()
                    holder.categoryImage.controller = controller

                } else {
                    val controller = Fresco.newDraweeControllerBuilder()
                            .setImageRequest(CustomImageRequest(ImageRequestBuilder.newBuilderWithResourceId(R.mipmap.default_category_icon)))
                            .setOldController(holder.categoryImage.controller)
                            .build()
                    holder.categoryImage.controller = controller

                }


            } else {

                val controller = Fresco.newDraweeControllerBuilder()
                        .setImageRequest(CustomImageRequest(ImageRequestBuilder.newBuilderWithResourceId(R.mipmap.default_category_icon)))
                        .setOldController(holder.categoryImage.controller)
                        .build()
                holder.categoryImage.controller = controller
            }

            holder.itemView.setOnClickListener {
                if (onItemClickListener != null && canClick()) {
                    onItemClickListener!!.onCategoryClick(holder.mItem, position)
                }
            }

            holder.itemView.setOnLongClickListener(View.OnLongClickListener {
                categoryName = holder.mItem!!.categoryName
                val startLocation = IntArray(2)
                holder.itemView.getLocationOnScreen(startLocation)
                longPressDialog.showInLocation(startLocation[1])
                if (onItemLongClickListener != null) {
                    onItemLongClickListener!!.onCategoryLongClick(holder.mItem, position)
                }
                return@OnLongClickListener false
            })


        } else if (holder is FooterHolder) {
            val mHolder = holder

        }

    }

    private fun canClick(): Boolean {
        if (System.currentTimeMillis() - clickTime > 700) {
            clickTime = System.currentTimeMillis()
            return true
        }
        return false
    }

    override fun getItemViewType(position: Int): Int = ITEM_TYPE_CATEGORY

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int {
        return datas!!.size
    }

    /**
     * @des: 条目添加动画
     * @params:
     * @return:
     */

    private fun addItemAnim(mHolder: CategoryHolder) {
        val animatorSet = AnimatorSet()//组合动画
        val scaleX = ObjectAnimator.ofFloat(mHolder.categoryInnerItem, "scaleX", 0.1f, 1.2f, 0.9f, 1.0f)
        val scaleY = ObjectAnimator.ofFloat(mHolder.categoryInnerItem, "scaleY", 0.1f, 1.2f, 0.9f, 1.0f)
        animatorSet.duration = 400
        animatorSet.interpolator = DecelerateInterpolator()
        animatorSet.play(scaleX).with(scaleY)//两个动画同时开始
        animatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
            }

            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
            }
        })

        val animatorSet1 = AnimatorSet()//组合动画
        val scaleX1 = ObjectAnimator.ofFloat(mHolder.categoryInnerItem, "scaleX", 0f, 0f)
        val scaleY1 = ObjectAnimator.ofFloat(mHolder.categoryInnerItem, "scaleY", 0f, 0f)
        animatorSet1.duration = 10
        animatorSet1.interpolator = AccelerateInterpolator()
        animatorSet1.play(scaleX1).with(scaleY1)//两个动画同时开始
        animatorSet1.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                animatorSet.start()
            }

            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
            }
        })
        animatorSet1.start()
        isAnim = false

    }

    private fun hideAnim(mHolder: CategoryHolder) {
        mHolder.categoryInnerItem.scaleX = 0f
        mHolder.categoryInnerItem.scaleY = 0f

        isHide = false

    }

    private fun cancelHideAnim(mHolder: CategoryHolder) {
        mHolder.categoryInnerItem.scaleX = 1f
        mHolder.categoryInnerItem.scaleY = 1f

        cancelHide = false

    }

    private inner class CategoryHolder internal constructor(val mView: View) : RecyclerView.ViewHolder(mView) {
        internal var mItem: CategoryEntity? = null
        internal var categoryItem: RelativeLayout
        internal var categoryName: TextView
        internal var categoryCount: TextView
        internal var categoryDate: TextView
        internal var categoryImage: SimpleDraweeView
        internal var categoryInnerItem: RelativeLayout


        init {

            categoryInnerItem = mView.findViewById<View>(R.id.category_inner_item) as RelativeLayout
            categoryItem = mView.findViewById<View>(R.id.category_item) as RelativeLayout
            categoryDate = mView.findViewById<View>(R.id.category_date) as TextView
            categoryCount = mView.findViewById<View>(R.id.category_count) as TextView
            categoryName = mView.findViewById<View>(R.id.category_name) as TextView
            categoryImage = mView.findViewById<View>(R.id.category_image) as SimpleDraweeView

        }


    }


    private inner class FooterHolder(val mView: View) : RecyclerView.ViewHolder(mView)

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    interface OnItemClickListener {
        fun onCategoryClick(CategoryEntity: CategoryEntity?, position: Int)
    }

    fun setOnItemLongClickListener(onItemLongClickListener: OnItemLongClickListener) {
        this.onItemLongClickListener = onItemLongClickListener
    }

    private var onItemLongClickListener: OnItemLongClickListener? = null

    interface OnItemLongClickListener {
        fun onCategoryLongClick(CategoryEntity: CategoryEntity?, position: Int)
    }


}
