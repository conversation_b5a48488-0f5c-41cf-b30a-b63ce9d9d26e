package com.czur.scanpro.ui.user

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityInputInviteBinding
import com.czur.scanpro.databinding.ActivityUserAboutBinding
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.InviteEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.umeng.analytics.MobclickAgent
import org.greenrobot.eventbus.EventBus
import java.util.*

/**
 * Created by Yz on 2019/3/14.
 * Email：<EMAIL>
 */
class InputInviteActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityInputInviteBinding by lazy{
        ActivityInputInviteBinding.inflate(layoutInflater)
    }

    private var httpManager: HttpManager? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_input_invite)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {

        httpManager = HttpManager.getInstance()
        binding.registerTopBar.normalTitle.setText(R.string.input_invite_code)
    }

    private fun registerEvent() {
        binding.registerTopBar.normalBackBtn.setOnClickListener(this)
        binding.confirmBtn.setOnClickListener(this)

    }

    /**
     * @des: 请求用户信息
     * @params:
     * @return:
     */

    private fun inputCode() {
        HttpManager.getInstance().request().invitation(UserPreferences.getInstance(this)!!.userId, binding.codeEdt.text.toString(), String::class.java, object : MiaoHttpManager.CallbackNetwork<String> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {
                showProgressDialog(false)
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                EventBus.getDefault().post(InviteEvent(EventType.INPUT_INVITE_CODE))
                mobClickInputCodeEvent(this@InputInviteActivity, BuildConfig.PHASE.invitationCode, binding.codeEdt.text.toString())
                val intent = Intent(this@InputInviteActivity, InviteResultActivity::class.java)
                intent.putExtra("days", entity.body)
                ActivityUtils.startActivity(intent)
                LogUtils.i(entity.body)


            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                when (entity.code) {
                    1065 -> showMessage(R.string.user_invite_code_warn)
                    1066 -> showMessage(R.string.user_invite_used)
                    else -> showMessage(R.string.request_failed_alert)

                }


            }

            override fun onError(e: Exception) {
                hideProgressDialog()
                showMessage(R.string.request_failed_alert)

            }
        })
    }

    fun mobClickInputCodeEvent(activity: Context?, eventId: String, code: String) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[Constants.USE_CODE] = code
        MobclickAgent.onEvent(activity, eventId, map)
    }

    override fun onClick(v: View) {
        when (v.getId()) {

            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            R.id.confirmBtn ->
                if (binding.codeEdt.text.toString().isEmpty()) showMessage(R.string.user_invite_code_empty) else inputCode()
            else -> {
            }

        }
    }


}