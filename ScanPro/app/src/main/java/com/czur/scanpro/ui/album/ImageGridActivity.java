package com.czur.scanpro.ui.album;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.entity.model.ImageEntity;
import com.czur.scanpro.ui.base.BaseActivity;
import com.czur.scanpro.ui.base.IndexActivity;
import com.czur.scanpro.ui.file.adjust.AdjustEdgeSimpleActivity2;
import com.czur.scanpro.utils.AnimationUtil;
import com.czur.scanpro.utils.PermissionUtil;
import com.czur.scanpro.utils.validator.Validator;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by Yz on 2018/3/15
 * Email：<EMAIL>
 */

public class ImageGridActivity extends BaseActivity implements  ImageDataSource.OnImagesLoadedListener,ImageRecyclerAdapter.OnImageItemClickListener, View.OnClickListener {


    private ImagePicker imagePicker;
    private RecyclerView mRecyclerView;
    private ImageRecyclerAdapter mRecyclerAdapter;
    private ImageFolder imageFolder;
    private TextView tvDes;
    private TextView tvImageSize;
    private RelativeLayout noBackTopBarCancel;
    private RelativeLayout imageOKbtn;
    private LinearLayout btnBack;
    private ImageView camera_album;
    private boolean isImport = false;
    private String tagId;
    private String tagName;
    private String categoryID;
    private String categoryName;
    private int type = 0;
    private ArrayList<ImageEntity> listImage = new ArrayList<ImageEntity>();
    private Boolean isChecked=false;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_image_grid);
        initComponent();
        initRecyclerView();
        registerEvent();

        initAlbumList();
        getPermission();
    }

    private void initComponent() {
        isImport = getIntent().getBooleanExtra("isImport", false);
        tagId = getIntent().getStringExtra("tagId");
        tagName = getIntent().getStringExtra("tagName");
        categoryID = getIntent().getStringExtra("categoryID");
        categoryName = getIntent().getStringExtra("categoryName");
        type = getIntent().getIntExtra("type", 0);
//        imageFolder = (ImageFolder) getIntent().getSerializableExtra("imageFolder");
        imagePicker = ImagePicker.getInstance();
        mRecyclerView = (RecyclerView) findViewById(R.id.recycler);
        btnBack = (LinearLayout) findViewById(R.id.btn_back_ll);
        camera_album = (ImageView) findViewById(R.id.btn_back);
        tvDes = (TextView) findViewById(R.id.tv_des);
        tvImageSize = (TextView) findViewById(R.id.tvImagesize);
        noBackTopBarCancel = (RelativeLayout) findViewById(R.id.album_top_bar_cancel);
        imageOKbtn = (RelativeLayout) findViewById(R.id.file_ok_btn);
        if (Validator.isNotEmpty(imageFolder)) {
            tvDes.setText(imageFolder.name);
        }

        albumFolderList = (RecyclerView) findViewById(R.id.album_folder_list);
    }

    private void registerEvent() {
        btnBack.setOnClickListener(this);
        noBackTopBarCancel.setOnClickListener(this);
        imageOKbtn.setOnClickListener(this);
    }


    private void initRecyclerView() {
        mRecyclerAdapter = new ImageRecyclerAdapter(this, null);
        mRecyclerAdapter.setOnImageItemClickListener(this);
        mRecyclerView.setLayoutManager(new GridLayoutManager(this, 4));
        mRecyclerView.addItemDecoration(new GridSpacingItemDecoration(4, SizeUtils.dp2px(10), false));
        mRecyclerView.setAdapter(mRecyclerAdapter);
        mRecyclerView.setItemViewCacheSize(100);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_back_ll) {//相机相册
            if (mRecyclerAdapter.getItemCount() == 0) return;
            if (!isChecked) {
                isChecked = true;
                mRecyclerView.getBackground().mutate().setAlpha(12);
                camera_album.setImageResource(R.mipmap.ic_up_expand);
                albumFolderList.setVisibility(View.VISIBLE);
                imageOKbtn.setVisibility(View.GONE);
                albumFolderList.setAnimation(AnimationUtil.moveToViewLocation());
                imageOKbtn.setAnimation(AnimationUtil.moveToViewTop());
            } else {
                isChecked = false;
                mRecyclerView.getBackground().mutate().setAlpha(200);
                camera_album.setImageResource(R.mipmap.ic_down_expand);
                albumFolderList.setVisibility(View.GONE);
                imageOKbtn.setVisibility(View.VISIBLE);
                albumFolderList.setAnimation(AnimationUtil.moveToViewTop());
                imageOKbtn.setAnimation(AnimationUtil.moveToViewLocation());
            }

//                ActivityUtils.finishActivity(this);
        } else if (id == R.id.album_top_bar_cancel) {
            ActivityUtils.startActivity(IndexActivity.class);
        } else if (id == R.id.file_ok_btn) {
            if (listImage != null && listImage.size() > 0) {
                Intent intent = new Intent(ImageGridActivity.this, AdjustEdgeSimpleActivity2.class);
//                Intent intent = new Intent(ImageGridActivity.this, AdjustEdgeViewPager.class);
                intent.putExtra("isImport", isImport);
                intent.putExtra("type", type);
                if (type == 2) {
                    intent.putExtra("tagName", tagName);
                    intent.putExtra("tagId", tagId);
                } else if (type == 1) {
                    intent.putExtra("categoryID", categoryID);
                    intent.putExtra("categoryName", categoryName);
                }
//                intent.putExtra("fileID", UUID.randomUUID().toString());
                intent.putParcelableArrayListExtra("imageList", listImage);
                ActivityUtils.startActivity(intent);
            }
        }
    }


    @Override
    public void onImageItemClick(View view, ImageItem imageItem, int position, Integer size, ArrayList<ImageEntity> listImage) {
        if (isImport) {
            tvImageSize.setText(getString(R.string.confirm_text)+" ("+String.valueOf(size)+")");
            this.listImage =listImage;

//           if(tvIndex.isEnabled()) tvIndex.setEnabled();
            //跳转调整画面
          /*  Intent intent = new Intent(ImageGridActivity.this, AdjustEdgeSimpleActivity.class);
            intent.putExtra("isImport", isImport);
            intent.putExtra("type", type);
            if (type == 2) {
                intent.putExtra("tagName", tagName);
                intent.putExtra("tagId", tagId);
            } else if (type == 1) {
                intent.putExtra("categoryID", categoryID);
                intent.putExtra("categoryName", categoryName);
            }
            intent.putExtra("fileID", UUID.randomUUID().toString());
            intent.putExtra("originalImagePath", imageItem.path);
            ActivityUtils.startActivity(intent);*/
        } else {
            Intent intent = new Intent(ImageGridActivity.this, ImageCropActivity.class);
            intent.putExtra("path", imageItem.path);
            LogUtils.i(imageItem.path);
            startActivityForResult(intent, ImagePicker.REQUEST_CODE_CROP);  //单选需要裁剪，进入裁剪界面
        }
    }


    private void initAlbumList() {
        mImageFolders = new ArrayList<>();
        albumFolderAdapter = new AlbumFolderAdapter(this, mImageFolders);
        albumFolderAdapter.setOnItemClickListener(onItemClickListener);
        albumFolderList.setAdapter(albumFolderAdapter);
        albumFolderList.setHasFixedSize(true);
        albumFolderList.setLayoutManager(new LinearLayoutManager(this));
        albumFolderList.setAnimation(AnimationUtil.moveToViewLocation());
    }
    private List<ImageFolder> mImageFolders;
    private AlbumFolderAdapter albumFolderAdapter;
    private RecyclerView albumFolderList;
    public static final int REQUEST_PERMISSION_STORAGE = 0x01;

    @Override
    public void onImagesLoaded(List<ImageFolder> imageFolders) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
                if (imageFolders != null && imageFolders.size()>0 && imageFolders.get(0).name == null){
                    //部分手机扫描出的第一个相册名为null, 内容是其他相册内容, 删除掉
                    imageFolders.remove(0);
                }
                mImageFolders = imageFolders;
                imagePicker.setImageFolders(mImageFolders);
                albumFolderAdapter.refreshData(mImageFolders);
            }
        });

    }
    private void getPermission123() {
        if (checkPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            showProgressDialog(false);
            new ImageDataSource(this, null, this);
        } else {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, REQUEST_PERMISSION_STORAGE);
        }
    }
    private void getPermission() {
        if (PermissionUtils.isGranted(PermissionUtil.getStoragePermission())) {
            showProgressDialog(false);
            new ImageDataSource(this, null, this);
        } else {
            ActivityCompat.requestPermissions(this, PermissionUtil.getStoragePermission(), REQUEST_PERMISSION_STORAGE);
        }
    }

    public boolean checkPermission(@NonNull String permission) {
        return ActivityCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSION_STORAGE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showProgressDialog(false);
                new ImageDataSource(this, null, this);
            } else {
                showMessage(R.string.can_not_open_album);
            }
        }
    }

    private AlbumFolderAdapter.OnItemClickListener onItemClickListener = new AlbumFolderAdapter.OnItemClickListener() {
        @Override
        public void OnItemClick(int position, ImageFolder imageFolder) {
            imagePicker.setCurrentImageFolderPosition(position);
            try {
                if (null != imageFolder) {
                    mRecyclerView.removeAllViews();
                    mRecyclerView.removeAllViewsInLayout();
                    mRecyclerView.swapAdapter(mRecyclerAdapter,true);
                    mRecyclerView.getRecycledViewPool().clear();
                    listImage.clear();
                    isChecked=false;
                    mRecyclerAdapter.refreshData(imageFolder.images);
                    tvImageSize.setText(getString(R.string.confirm_text)+" (0)");
                    mRecyclerView.getBackground().mutate().setAlpha(200);
                    camera_album.setImageResource(R.mipmap.ic_down_expand);
                    albumFolderList.setVisibility(View.GONE);
                    imageOKbtn.setVisibility(View.VISIBLE);
                }
            } catch (Exception e) {
                showMessage(R.string.crop_image_bad_image);
                e.printStackTrace();
            }
        }

    };
}