package com.czur.scanpro.ui.component.adjustEdge

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import androidx.core.content.ContextCompat
import android.view.View
import com.blankj.utilcode.util.SizeUtils
import com.czur.scanpro.R
import com.czur.scanpro.alg.Vec4f

class AdjustEdgeBookView(context: Context?) : View(context) {

    private var fillPaint = Paint()
    private var linePaint = Paint()
    var pointRadius = 0f
    val dp20 = SizeUtils.dp2px(20f)

    var leftPoints: MutableList<PointF> = mutableListOf()
    var rightPoints: MutableList<PointF> = mutableListOf()
    var leftRects: MutableList<RectF> = mutableListOf()
    var rightRects: MutableList<RectF> = mutableListOf()
    var leftPointCrossLine: MutableList<Vec4f> = mutableListOf()
    var rightPointCrossLine: MutableList<Vec4f> = mutableListOf()
    var topPointsList: MutableList<PointF> = mutableListOf()
    var bottomPointsList: MutableList<PointF> = mutableListOf()
    var topCurveList: MutableList<PointF> = mutableListOf()
    var bottomCurveList: MutableList<PointF> = mutableListOf()

    init {
        fillPaint.run {
            isAntiAlias = true
            color = ContextCompat.getColor(getContext(), R.color.adjustEdgeSimpleRectFillColor)
            style = Paint.Style.FILL
        }
        linePaint.run {
            isAntiAlias = true
            color = ContextCompat.getColor(getContext(), R.color.adjustEdgeSimpleLineColor)
            style = Paint.Style.STROKE
            strokeWidth = SizeUtils.dp2px(2f).toFloat()
        }
    }

    fun showPoints() {
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (topPointsList.size > 0) {
            // 左矩形


            leftRects.forEach {
                val leftRectF = RectF(it.left + dp20, it.top + dp20, it.right + dp20, it.bottom + dp20)

                // 矩形半透填充背景
                canvas!!.drawRect(leftRectF, fillPaint)
                // 矩形红色边框
                canvas.drawRect(leftRectF, linePaint)
            }
            // 右矩形
            rightRects.forEach {
                val rightRectF = RectF(it.left + dp20, it.top + dp20, it.right + dp20, it.bottom + dp20)

                // 矩形半透填充背景
                canvas!!.drawRect(rightRectF, fillPaint)
                // 矩形红色边框
                canvas.drawRect(rightRectF, linePaint)
            }
            // 左矩形连线
            leftPointCrossLine.forEach {
                // 矩形红色连接线
                canvas!!.drawLine(it.point1.x+ dp20, it.point1.y+ dp20, it.point2.x+ dp20, it.point2.y+ dp20, linePaint)
            }
            // 右矩形连线
            rightPointCrossLine.forEach {
                // 矩形红色连接线
                canvas!!.drawLine(it.point1.x+ dp20, it.point1.y+ dp20, it.point2.x+ dp20, it.point2.y+ dp20, linePaint)
            }
            // 上曲线
            topCurveList.forEachIndexed { index, pointF ->
                if (index != topCurveList.size - 1) {
                    canvas!!.drawLine(pointF.x+ dp20, pointF.y+ dp20, topCurveList[index + 1].x+ dp20, topCurveList[index + 1].y+ dp20, linePaint)
                }
            }
            // 下曲线
            bottomCurveList.forEachIndexed { index, pointF ->
                if (index != bottomCurveList.size - 1) {
                    canvas!!.drawLine(pointF.x+ dp20, pointF.y+ dp20, bottomCurveList[index + 1].x+ dp20, bottomCurveList[index + 1].y+ dp20, linePaint)
                }
            }
            // 上调整点
            topPointsList.forEach {
                // 圆形半透填充背景
                canvas!!.drawCircle(it.x+ dp20, it.y+ dp20, pointRadius, fillPaint)
                // 圆形红色边框
                canvas.drawCircle(it.x+ dp20, it.y+ dp20, pointRadius, linePaint)
            }
            // 下调整点
            bottomPointsList.forEach {
                // 圆形半透填充背景
                canvas!!.drawCircle(it.x+ dp20, it.y+ dp20, pointRadius, fillPaint)
                // 圆形红色边框
                canvas.drawCircle(it.x+ dp20, it.y+ dp20, pointRadius, linePaint)
            }
        }
    }
}