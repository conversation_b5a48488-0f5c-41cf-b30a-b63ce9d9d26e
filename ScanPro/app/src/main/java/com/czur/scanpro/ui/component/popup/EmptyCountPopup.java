package com.czur.scanpro.ui.component.popup;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.preferences.UserPreferences;
import com.czur.scanpro.ui.user.NewPayActivity;
import com.czur.scanpro.utils.validator.StringUtils;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class EmptyCountPopup extends Dialog {
    public enum EmptyType {
        PDF,
        CLOUD,
        OCR,
        HANDWRTING,
        CARD
    }

    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public EmptyCountPopup(Context context) {
        super(context);
    }

    public EmptyCountPopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String title;
        private String message;
        private EmptyType type;
        private View contentsView;

        private OnClickListener positiveListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }


        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setMessage(EmptyType type) {
            this.type = type;
            String temp = "";
            if (UserPreferences.getInstance(this.context).getIsVip()) {
                if (type == EmptyType.CARD) {
                    temp = context.getString(R.string.card_count_vip);
                } else if (type == EmptyType.HANDWRTING) {
                    temp = context.getString(R.string.handwriting_count_vip);
                } else if (type == EmptyType.CLOUD) {
                    temp = context.getString(R.string.cloud_count_vip);
                }
                this.message = String.format(context.getString(R.string.count_warn_vip_month), temp);
            } else {
                if (type == EmptyType.PDF) {
                    temp = context.getString(R.string.pdf_count_free);
                    this.message = String.format(context.getString(R.string.count_warn_free_day), temp);
                } else if (type == EmptyType.CARD) {
                    temp = context.getString(R.string.card_count_free);
                    this.message = String.format(context.getString(R.string.count_warn_free_day), temp);
                } else if (type == EmptyType.HANDWRTING) {
                    temp = context.getString(R.string.handwriting_count_free);
                    this.message = String.format(context.getString(R.string.count_warn_free_total), temp);
                } else if (type == EmptyType.CLOUD) {
                    temp = context.getString(R.string.cloud_count_free);
                    this.message = String.format(context.getString(R.string.count_warn_free_total), temp);
                } else {
                    temp = context.getString(R.string.ocr_count1_free);
                    this.message = String.format(context.getString(R.string.count_warn_free_day), temp);
                }
            }
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public EmptyCountPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final EmptyCountPopup dialog = new EmptyCountPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            BarUtils.setStatusBarColor(dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor(dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode(dialog.getWindow(), true);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final EmptyCountPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            View layout = inflater.inflate(R.layout.empty_count_popup, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);
//            startAnim(rotate_anim);
            TextView title = (TextView) layout.findViewById(R.id.title);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.backBtn);
            TextView goAdvanced = (TextView) layout.findViewById(R.id.confirm_advanced_btn);
            TextView cloudTv = layout.findViewById(R.id.cloud_btn);
            if (type == EmptyType.OCR) {
                cloudTv.setVisibility(View.VISIBLE);
                cloudTv.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (positiveListener != null) {
                            positiveListener.onClick(dialog, -1);
                        }
                    }
                });
            } else {
                cloudTv.setVisibility(View.GONE);
            }
            TextView messageTv = (TextView) layout.findViewById(R.id.message);
            messageTv.setText(message);
            if (UserPreferences.getInstance(this.context).getIsVip()) {
                if (UserPreferences.getInstance(this.context).getUserType().equals("svip")) {
                    goAdvanced.setVisibility(View.GONE);
                } else {
                    goAdvanced.setVisibility(View.VISIBLE);
                }
            } else {
                goAdvanced.setVisibility(View.VISIBLE);
            }
            if (contentsView == null) {
                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }
            } else {
                title.setVisibility(View.GONE);
            }
            goAdvanced.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    ActivityUtils.startActivity(NewPayActivity.class);
                    dialog.dismiss();
                }
            });
            backBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });

            return layout;
        }

        private void startAnim(View layout) {
            AnimatorSet animatorSet1 = new AnimatorSet();
            ObjectAnimator scaleX1 = ObjectAnimator.ofFloat(layout, "scaleX", 0.1f, 0.1f);
            ObjectAnimator scaleY1 = ObjectAnimator.ofFloat(layout, "scaleY", 0.1f, 0.1f);
            animatorSet1.setDuration(200);
            animatorSet1.setInterpolator(new DecelerateInterpolator());
            animatorSet1.play(scaleX1).with(scaleY1);
            animatorSet1.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animator) {

                }

                @Override
                public void onAnimationEnd(Animator animator) {
                    AnimatorSet animatorSet = new AnimatorSet();
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(layout, "scaleX", 0.1f, 1.2f, 0.9f, 1.0f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(layout, "scaleY", 0.1f, 1.2f, 0.9f, 1.0f);
                    animatorSet.setDuration(600);
                    animatorSet.setInterpolator(new DecelerateInterpolator());
                    animatorSet.play(scaleX).with(scaleY);
                    animatorSet.start();
                }

                @Override
                public void onAnimationCancel(Animator animator) {

                }

                @Override
                public void onAnimationRepeat(Animator animator) {

                }
            });
            animatorSet1.start();
        }

        private boolean isChinese(char c) {
            return c >= 0x4E00 && c <= 0x9FA5;// 根据字节码判断
        }
    }
}
