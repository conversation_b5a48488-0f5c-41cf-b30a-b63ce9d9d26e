package com.czur.scanpro.ui.base

import android.Manifest
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.os.*
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.RelativeLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager.widget.ViewPager
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.*
import com.blankj.utilcode.util.FileUtils
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityIndexBinding
import com.czur.scanpro.entity.model.ADModel
import com.czur.scanpro.entity.realm.*
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.UpdateEvent
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.album.ImageGridActivity
import com.czur.scanpro.ui.camera.CameraActivity
import com.czur.scanpro.ui.camera.CardCameraActivity
import com.czur.scanpro.ui.component.NoHintEditText
import com.czur.scanpro.ui.component.popup.*
import com.czur.scanpro.ui.component.popup.blur.MoreWindow
import com.czur.scanpro.ui.download.DownloadApkService
import com.czur.scanpro.ui.download.HttpDownloader
import com.czur.scanpro.ui.home.CategoryFragment
import com.czur.scanpro.ui.home.FileFragment
import com.czur.scanpro.ui.home.TagFragment
import com.czur.scanpro.ui.user.UserActivity
import com.czur.scanpro.utils.BitmapUtils
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.PermissionUtil
import com.czur.scanpro.utils.PermissionUtil.getStoragePermission
import com.czur.scanpro.utils.getAppUpdateUrl
import com.czur.scanpro.utils.launch
import com.czur.scanpro.utils.validator.Validator
import com.google.android.renderscript.Toolkit
import com.google.gson.Gson
import com.ymjz.ocr.OcrEngine
import io.realm.Realm
import okhttp3.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*


class IndexActivity : BaseActivity(), View.OnClickListener {

    private lateinit var indexFragmentAttacher: com.czur.scanpro.ui.home.IndexFragmentAttacher
    private var fragments = ArrayList<Fragment>()
    private val list = ArrayList<String>()
    private var exitTime: Long = 0
    private var userPreferences: UserPreferences? = null
    private var moreWindow: MoreWindow? = null
    private var position: Int = 0
    private var dialogEdt: EditText? = null
    private var dialogEdt1: EditText? = null
    private var realm: Realm? = null
    private var canTouch: Boolean = true

    private var finalCategoryName: String? = null
    private var finalCategoryId: String? = null

    private var updateUrl: String? = null
    private var notes: String? = null
    private var version: String? = null
    private var apkPath: String? = null
    private var apkName: String? = null
    private lateinit var binding: ActivityIndexBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_index)
        binding = ActivityIndexBinding.inflate(layoutInflater)
        setContentView(binding.root)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissions(arrayOf(Manifest.permission.POST_NOTIFICATIONS), 0)
        }
        initComponent()
        registerEvent()
//        requestPermission()
        initTab()

        requestAd()
    }


    private fun initComponent() {
        realm = Realm.getDefaultInstance()
        LogUtils.e(<EMAIL> + File.separator + "tessdata")
//        CZSaoMiao_Alg.getInstance(this)
//            .init_tess(<EMAIL> + File.separator + "tessdata")
        userPreferences = UserPreferences.getInstance(this)
        EventBus.getDefault().register(this)

        moreWindow = MoreWindow(this)
        moreWindow!!.setIndexAnimFinishedListener {
            LogUtils.e("可以点击再次开启")
            canTouch = true
        }

        moreWindow!!.setOnMainDialogOnClickListener { viewId: Int ->
            when (viewId) {
                R.id.card_btn -> cardMode()
                R.id.single_btn -> singleMode()
                R.id.surface_btn -> surfaceMode()
                R.id.import_ll -> import()
            }
        }
        moreWindow!!.init(<EMAIL>)
        initListAndLine()
        initViewPager()
        checkLogin()
        if (NetworkUtils.isConnected()) {
            checkUpdate()
        }
        getPushPhotoState()
    }


    private fun initTab() {
        binding.tablayout.post {
            val dp7 = SizeUtils.dp2px(7f)
            val dp05 = SizeUtils.dp2px(0.5f)
            val dp15 = SizeUtils.dp2px(1.5f)
            val bigWidth = binding.tablayout.textViewList[0].width - dp7 * 2
            val smallWidth = binding.tablayout.textViewList[1].width
            val layoutParam1: RelativeLayout.LayoutParams =
                binding.redLine1.layoutParams as RelativeLayout.LayoutParams
            layoutParam1.width = bigWidth
            layoutParam1.marginStart = dp7
            binding.redLine1.layoutParams = layoutParam1

            val layoutParam2: RelativeLayout.LayoutParams =
                binding.redLine2.layoutParams as RelativeLayout.LayoutParams
            layoutParam2.width = bigWidth
            layoutParam2.marginStart = smallWidth + dp7 + dp05
            binding.redLine2.layoutParams = layoutParam2

            val layoutParam3: RelativeLayout.LayoutParams =
                binding.redLine3.layoutParams as RelativeLayout.LayoutParams
            layoutParam3.width = bigWidth
            layoutParam3.marginStart = smallWidth * 2 + dp7 + dp15
            binding.redLine3.layoutParams = layoutParam3
        }
    }


    /**
     * 获取是否上传原图
     */
    private fun getPushPhotoState() {
        val ran = Calendar.getInstance().getTimeInMillis()
        val checkRequest =
            Request.Builder().url(getString(R.string.check_push_photo) + "?" + ran).get().build()
        val checkCall = MiaoHttpManager.getInstance().client.newCall(checkRequest)
        checkCall.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    val resultStr = response.body!!.string()
                    val jsonObject = JSONObject(resultStr)
                    val result = jsonObject.getInt("getPhoto")
                    userPreferences!!.setPushOriPhoto(if (result == 1) true else false)
                }
            }
        })
    }

    /**
     * @des: 检查更新
     * @params:
     * @return:
     */

    private fun checkUpdate() {
        apkPath =
            getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath.toString() + Constants.SD_PATH + Constants.APK_PATH + getString(
                R.string.app_name
            ) + "_" + "v2.2.69" + ".apk"
        LogUtils.i(apkPath, "apkPath" + apkPath)
        if (FileUtils.isFileExists(apkPath)) {
            LogUtils.i(apkPath, "apkPath" + apkPath)
        }
        Thread(Runnable {
            val clearCacheStr = "?" + UUID.randomUUID().toString()
            val checkRequest =
                Request.Builder().url(getString(getAppUpdateUrl()) + clearCacheStr).get()
                    .build()
            val checkCall = MiaoHttpManager.getInstance().client.newCall(checkRequest)
            try {
                val checkResponse = checkCall.execute()
                if (checkResponse.isSuccessful) {

                    val packageInfo = packageManager.getPackageInfo(packageName, 0)
                    val jsonObject = JSONObject(checkResponse.body?.string())
                    LogUtils.i("new version : $jsonObject")
                    updateUrl = jsonObject.getString("package")
                    notes = jsonObject.getString("notes")
                    version = jsonObject.getString("version")
                    apkPath =
                        getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath.toString() + Constants.SD_PATH + Constants.APK_PATH + getString(
                            R.string.app_name
                        ) + "_" + version + ".apk"
                    LogUtils.i(apkPath, "apkPath" + apkPath)
                    apkName = getString(R.string.app_name) + "_" + version + ".apk"
                    LogUtils.i(packageInfo.versionCode, jsonObject.getInt("build"))
                    if (packageInfo.versionCode < jsonObject.getInt("build")) {
                        EventBus.getDefault().post(UpdateEvent(EventType.HAS_NEW_VERSION))
                        Looper.prepare()
                        Handler().post(Runnable {
                            //apk是否存在，存在就进行安装
                            if (FileUtils.isFileExists(apkPath)) {
                                LogUtils.i(apkPath, " apk is exist!")
                                showUpdatePopup(true)
                            } else {
                                showUpdatePopup(false)

                            }
                        })
                        Looper.loop()
                    } else {
                        EventBus.getDefault().post(UpdateEvent(EventType.IS_LATEST_VERSION))
                    }

                }
            } catch (e: IOException) {
                LogUtils.e(e)
            } catch (e: PackageManager.NameNotFoundException) {
                LogUtils.e(e)
            } catch (e: JSONException) {
                LogUtils.e(e)
            }
        }).start()

    }


    private fun initViewPager() {
        //初始化ViewPager
        val myViewPager = MyViewPager(supportFragmentManager, list)
        binding.viewpager.setScroll(true)
        binding.viewpager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                onIndexFragmentChangeListener?.onChange(position)
                <EMAIL> = position
                when (position) {
                    0 -> {
                        binding.multiCheckBtn.isClickable = false
                        binding.indexAddBtn.isClickable = true

                        binding.indexAddBtn.visibility = View.VISIBLE
                        binding.multiCheckBtn.visibility = View.GONE
                        binding.indexScanBtn.visibility = View.VISIBLE


                        binding.redLine1.visibility = View.VISIBLE
                        binding.redLine2.visibility = View.GONE
                        binding.redLine3.visibility = View.GONE
                    }

                    1 -> {
                        binding.multiCheckBtn.isClickable = true
                        binding.indexAddBtn.isClickable = false

                        binding.indexAddBtn.visibility = View.GONE
                        binding.multiCheckBtn.visibility = View.VISIBLE
                        binding.indexScanBtn.visibility = View.VISIBLE


                        binding.redLine1.visibility = View.GONE
                        binding.redLine2.visibility = View.VISIBLE
                        binding.redLine3.visibility = View.GONE
                    }

                    else -> {
                        binding.multiCheckBtn.isClickable = false
                        binding.indexAddBtn.isClickable = true

                        binding.indexAddBtn.visibility = View.VISIBLE
                        binding.multiCheckBtn.visibility = View.GONE
                        binding.indexScanBtn.visibility = View.GONE


                        binding.redLine1.visibility = View.GONE
                        binding.redLine2.visibility = View.GONE
                        binding.redLine3.visibility = View.VISIBLE
                    }
                }
            }

            override fun onPageScrollStateChanged(state: Int) {

            }
        })

        binding.viewpager.adapter = myViewPager
        binding.viewpager.offscreenPageLimit = 3
        binding.tablayout.setupWithViewPager(binding.viewpager)
        binding.viewpager.currentItem = 0
    }

    private fun initListAndLine() {
        list.clear()
        fragments.clear()
        list.add("文档")
        list.add("图片")
        list.add("标签")
        binding.tablayout.setDataList(list)

        fragments.add(CategoryFragment.newInstance())
        fragments.add(FileFragment.newInstance())
        fragments.add(TagFragment.newInstance())

//        transparent_line_1.visibility = View.GONE
//        transparent_line_2.visibility = View.VISIBLE
//        transparent_line_3.visibility = View.VISIBLE
        binding.redLine1.visibility = View.VISIBLE
        binding.redLine2.visibility = View.GONE
        binding.redLine3.visibility = View.GONE

        binding.guideGroup.visibility = if (userPreferences!!.indexGuideFirst) View.GONE else View.VISIBLE
    }

    private fun singleMode() {
//        openCamera(0)
        requestPermissionDialog(0)

    }

    private fun surfaceMode() {
//        openCamera(1)
        requestPermissionDialog(1)

    }

    private fun cardMode() {
//        openCamera(2)
        requestPermissionDialog(2)

    }

    private fun import() {
        if (PermissionUtils.isGranted(*getStoragePermission())){
            val intent = Intent(this@IndexActivity, ImageGridActivity::class.java)
//        val intent = Intent(this@IndexActivity, SelectAlbumPhotoActivity::class.java)
            intent.putExtra("isImport", true)
            ActivityUtils.startActivity(intent)
        }else{
            PermissionUtil.checkPermissionWithDialog(
                this,
                getString(R.string.dialog_tips),
                getString(R.string.import_tips),
                getString(R.string.dialog_setting),
                getString(R.string.dialog_cancel)
            ) {
                if (it != null) {
                    val intent = Intent(this@IndexActivity, ImageGridActivity::class.java)
//        val intent = Intent(this@IndexActivity, SelectAlbumPhotoActivity::class.java)
                    intent.putExtra("isImport", true)
                    ActivityUtils.startActivity(intent)
                }
            }
        }
    }

    inner class MyViewPager(fm: FragmentManager, private val mDataList: List<String>) :
        FragmentStatePagerAdapter(fm) {
        private val mInit: BooleanArray

        init {
            mInit = BooleanArray(mDataList.size)
        }

        override fun getItem(position: Int): Fragment {
            return fragments[position]

        }

        override fun getCount(): Int {
            return fragments.size
        }

        override fun setPrimaryItem(container: ViewGroup, position: Int, `object`: Any) {
            super.setPrimaryItem(container, position, `object`)
        }


    }


    override fun onClick(v: View) {
        when (v.id) {

            R.id.indexUserHeadImg -> {
                LogUtils.i("IndexActivity.indexUserHeadImg")
                ActivityUtils.startActivity(UserActivity::class.java)
            }

            R.id.indexScanBtn -> {
                LogUtils.i("IndexActivity.indexScanBtn.canTouch=${canTouch}")
                if (canTouch) {
                    canTouch = false
                    launch {
                        val screenShot = BitmapUtils.getScreenShot(<EMAIL>)
                        var blur: Bitmap = screenShot
                        blur = Toolkit.blur(blur, 25)
                        moreWindow!!.showMoreWindow(binding.indexLayout,blur)
                    }

                }
            }

            R.id.indexAddBtn -> {
                LogUtils.i("IndexActivity.indexAddBtn.position=${position}")
//                test()
                if (position == 0) {
                    showAddFilePopup()
                } else if (position == 2) {
                    showAddTagDialog()
                }

            }

            R.id.guideBtn -> {
                LogUtils.i("IndexActivity.guideBtn")
                userPreferences!!.indexGuideFirst = true
                binding.guideGroup.visibility = View.GONE
            }

            R.id.multiCheckBtn -> {
                LogUtils.i("IndexActivity.multiCheckBtn")
//                onActivityClickListener!!.onClick( R.
// id.multiCheckBtn )
            }

            else -> {
            }
        }
    }

    /**
     * @des:显示新建标签弹窗
     * @params:[]
     * @return:[]
     */

    private fun showAddTagDialog() {
        val builder =
            AddTagPopup.Builder(this@IndexActivity, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setTitle(getString(R.string.create_tag))

        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, which ->
            val newTagName = dialogEdt!!.text.toString()
            realm!!.executeTransaction(Realm.Transaction {

                if (!EtUtils.containsEmoji(newTagName)) {
                    if (Validator.isNotEmpty(newTagName)) {
                        if (realm!!.where(TagEntity::class.java).equalTo("tagName", newTagName)
                                .equalTo("isDelete", 0.toInt())
                                .equalTo("userID", getUserIdIsLogin()).findFirst() == null
                        ) {
                            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                            val curDate = formatter.format(Date(System.currentTimeMillis()))
                            val tagEntity = TagEntity().apply {
                                isSelf = 1
                                tagId = UUID.randomUUID().toString()
                                tagName = newTagName
                                userID = getUserIdIsLogin()
                                isDirty = 1
                                createTime = curDate
                                updateTime = curDate
                            }
                            realm!!.copyToRealmOrUpdate(tagEntity)

                            startAutoSync()
                        } else {
                            showMessage(R.string.tag_is_exist)

                        }
                    } else {
                        showMessage(R.string.tag_should_not_be_empty)
                    }
                } else {
                    showMessage(R.string.nickname_toast_symbol)
                }
            })

            dialog.dismiss()
        })
        val tagPopup = builder.create()
        dialogEdt = tagPopup.window?.findViewById(R.id.edt) as EditText
        tagPopup.show()
    }

    private fun test() {
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<String>() {

            override fun doInBackground(): String? {
                val ocr = OcrEngine()
                val path = "/sdcard/aa.jpg"
                try {
                    val jiazInfo = ocr.getJZInfo(this@IndexActivity, path)
                    LogUtils.e(jiazInfo.yMrecognState)

                    if (jiazInfo.yMrecognState == OcrEngine.RECOGN_OK) {

//                        val res = String(jiazInfo.charInfo., Charset.forName("gbk"))
//                        LogUtils.e(res)
                        // 第二参数：传入一个对象
                        // 第三个数据是是否设置模糊判断
                        return "123"
                    } else {

                        return "123"
                    }

                } catch (e: Exception) {
                    LogUtils.i(e)

                    return null
                } finally {

                    return "123"
                }

            }

            override fun onSuccess(result: String?) {
                LogUtils.i(result)

            }

            override fun onFail(t: Throwable) {
                super.onFail(t)
            }
        })


    }

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private fun showAddFilePopup() {
        getNewCategoryName()
        val builder = AddCategoryPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setTitle(getString(R.string.add_file))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            realm!!.executeTransaction { _ ->
                if (!EtUtils.containsEmoji(dialogEdt1!!.text.toString())) {
                    createCategory()
                    startAutoSync()

                } else {
                    ToastUtils.showShort(R.string.nickname_toast_symbol)
                }
            }

            dialog.dismiss()
        })
        val commonPopup = builder.create()
        dialogEdt1 = commonPopup.window?.findViewById<NoHintEditText>(R.id.edt)
        dialogEdt1!!.hint = finalCategoryName
        commonPopup.show()
    }

    private fun showUpdatePopup(isFileExist: Boolean) {
        val builder = UpdatePopup.Builder(this, CloudCommonPopupConstants.UPDATE_ONE_BUTTON)
        builder.setTitle(getString(R.string.app_update_title))
        builder.setMessage(notes)
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            if (!ServiceUtils.isServiceRunning(DownloadApkService::class.java)) {
                if (isFileExist) {
                    showInstallPopup()
                } else {

                    if (NetworkUtils.isWifiConnected()) {
                        showLongMessage(R.string.wifi_download)
                        val intent = Intent(this@IndexActivity, DownloadApkService::class.java)
                        intent.putExtra("updateUrl", updateUrl)
                        intent.putExtra("notes", notes)
                        intent.putExtra("apkName", apkName)
                        startService(intent)
                    } else {
                        showLongMessage(R.string.network_download)

                    }
                }

            }
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        if (!<EMAIL>) {
            commonPopup.show()
        }
    }

    private fun showInstallPopup() {

        val builder = ScanProCommonPopup.Builder(this, CloudCommonPopupConstants.INSTALL_ONE_BUTTON)
        builder.setTitle(getString(R.string.download_complete))
        builder.setMessage(
            String.format(
                getString(R.string.install_now),
                getString(R.string.app_name) + version
            )
        )
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            AppUtils.installApp(apkPath)
            dialog.dismiss()
        })
        val commonPopup = builder.create()

        commonPopup.show()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            //登录注册
            EventType.LOG_IN,
            EventType.THIRD_PARTY_LOGIN_IN,
            EventType.REGISTER_SUCCESS,
            EventType.THIRD_PARTY_REGISTER_SUCCESS,
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
            EventType.CHANGE_PASSWORD,
            EventType.BIND_PHONE,
            EventType.INPUT_INVITE_CODE,
            EventType.CHANGE_PHONE,
            EventType.BIND_EMAIL,
            EventType.CHANGE_EMAIL,
            EventType.EDIT_USER_IMAGE,
            EventType.USER_EDIT_NAME,
            EventType.LOG_OUT,
            EventType.SYNC_IS_FINISH,
            EventType.SYNC_IS_STOP,
            EventType.UPDATE_CACHE ->
                checkLogin()

            else -> {
            }
        }
    }


    private fun createCategory() {
        val categoryEntity = realm!!.createObject(CategoryEntity::class.java, finalCategoryId)
        categoryEntity.userID = getUserIdIsLogin()
        categoryEntity.isDirty = 1
        categoryEntity.categoryName = if (dialogEdt1!!.text.toString()
                .isNullOrEmpty()
        ) finalCategoryName else dialogEdt1!!.text.toString()
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        val curDate = formatter.format(Date(System.currentTimeMillis()))
        categoryEntity.createTime = curDate
        categoryEntity.updateTime = curDate
        finalCategoryName = null

    }

    private fun getNewCategoryName() {
        if (finalCategoryName.isNullOrEmpty()) {
            var i = 0
            val bookName = Constants.CATEGORY_DEFAULT_NAME
            var finalName = bookName
            val realm = Realm.getDefaultInstance()
            var sameCategory = realm.where(CategoryEntity::class.java)
                .equalTo("categoryName", Constants.CATEGORY_DEFAULT_NAME)
                .equalTo("isDelete", 0.toInt())
                .findFirst()
            while (Validator.isNotEmpty(sameCategory)) {
                val current = System.currentTimeMillis()
                i++
                finalName = bookName + i
                sameCategory = realm.where(CategoryEntity::class.java)
                    .equalTo("categoryName", finalName)
                    .equalTo("isDelete", 0.toInt())
                    .findFirst()
                LogUtils.i(System.currentTimeMillis() - current)
            }
            finalCategoryId = UUID.randomUUID().toString()

            finalCategoryName = finalName

        }
    }

    private fun checkLogin() {
        if (userPreferences!!.isValidUser) {
            if (userPreferences!!.userPhoto.isNullOrEmpty()) {

                binding.indexUserHeadImg.setImageResource(R.mipmap.user_login_icon)
            } else {
                binding.indexUserHeadImg.setImageURI(userPreferences!!.userPhoto)
            }
        } else {
            binding.indexUserHeadImg.setImageResource(R.mipmap.user_not_login_icon)
        }
    }


    private fun registerEvent() {
        binding.indexScanBtn.setOnClickListener(this)
        binding.indexUserHeadImg.setOnClickListener(this)
        binding.indexAddBtn.setOnClickListener(this)
        binding.guideBtn.setOnClickListener(this)
        binding.multiCheckBtn.setOnClickListener(this)

    }

    interface OnIndexFragmentChangeListener {
        fun onChange(position: Int)
    }

    private var onIndexFragmentChangeListener: OnIndexFragmentChangeListener? = null
    public fun setOnIndexFragmentChangeListener(onIndexFragmentChangeListener: OnIndexFragmentChangeListener) {
        this.onIndexFragmentChangeListener = onIndexFragmentChangeListener
    }


    override fun onPause() {
        super.onPause()
//        canTouch = true
//        moreWindow!!.setCanClose(false)
    }

    private fun exitApp() {
        if (System.currentTimeMillis() - exitTime > 2000) {
            showMessage(R.string.confirm_exit)
            exitTime = System.currentTimeMillis()
        } else {
            super.onBackPressed()
        }
    }

    override fun onBackPressed() {
        exitApp()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (ServiceUtils.isServiceRunning(DownloadApkService::class.java)) {
            ServiceUtils.stopService(DownloadApkService::class.java)
        }
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
        realm!!.close()

    }

    private fun requestPermissionDialog(cameraType: Int) {
        if (PermissionUtils.isGranted(*getStoragePermission())
            && PermissionUtils.isGranted(PermissionConstants.CAMERA)
        ) {
            openCamera(cameraType)
        } else {
            PermissionUtil.checkPermissionWithDialog(
                this,
                getString(R.string.dialog_tips),
                getString(R.string.scan_power_tips),
                getString(R.string.dialog_setting),
                getString(R.string.dialog_cancel)
            ) {
                if (it != null) {
                    openCamera(cameraType)
                }
            }
        }


    }

    /**
     * @des: 申请权限并且打开相机页
     * @params:
     * @return:
     */
    private fun openCamera(cameraType: Int) {
        LogUtils.i("IndexActivity.openCamera.cameraType=${cameraType}")
        PermissionUtils.permission(
            PermissionUtil.getStoragePermission()[0],
            PermissionUtil.getStoragePermission()[1],
            PermissionConstants.CAMERA
        )
            .rationale { activity, shouldRequest ->
                showMessage(R.string.denied_camera)
                shouldRequest.again(true)
            }
            .callback(object : PermissionUtils.FullCallback {
                override fun onGranted(permissionsGranted: List<String>) {
                    LogUtils.i(permissionsGranted)
                    if (cameraType == 2) {
                        val intent = Intent(this@IndexActivity, CardCameraActivity::class.java)
                        intent.putExtra("isSingle", true)
                        ActivityUtils.startActivity(intent)
                    } else {
                        val intent = Intent(this@IndexActivity, CameraActivity::class.java)
                        intent.putExtra("isSingle", cameraType == 0)
                        ActivityUtils.startActivity(intent)
                    }
                }

                override fun onDenied(
                    permissionsDeniedForever: List<String>,
                    permissionsDenied: List<String>
                ) {
                    showMessage(R.string.denied_camera)
                    LogUtils.i(permissionsDeniedForever, permissionsDenied)
                }
            })
            .theme { activity -> ScreenUtils.setFullScreen(activity) }
            .request()
    }

    val SHARE_SUCCESS_CODE = 666

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == SHARE_SUCCESS_CODE) {
            fragments[1].onActivityResult(requestCode, resultCode, data)
        }

    }

    var adModel: ADModel? = null
    var downloadModelList: ArrayList<DownloadModel> = arrayListOf()
    var AD_DOCUMENT_PATH = ""
    var nowDownloadIndex = 0

    private fun checkAD() {
        formatData()
        doDownload()
    }


    private fun requestAd() {
        AD_DOCUMENT_PATH =
            getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.getAbsolutePath()
                .toString() + Constants.SD_PATH + Constants.AD_PATH
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.no_connection_network)
            return
        }


        val okHttpClient = MiaoHttpManager.getInstance().client
        //Form表单格式的参数传递
        val formBody = FormBody.Builder()
            .add("code", "com.czur.scanpro")
            .add("version", AppUtils.getAppVersionName())
            .add("os", "android")
            .build()
        val request = Request.Builder()
            .post(formBody)//Post请求的参数传递
            .url(BuildConfig.PHASE.adPassportServiceUrl + Constants.REQUEST_STARTUP_ADVERTISEMENT)
            .build()
        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                LogUtils.e(e)
            }

            @Throws(IOException::class)
            override fun onResponse(call: Call, response: Response) {
                //此方法运行在子线程中，不能在此方法中进行UI操作。
                val result = response.body?.string()
                val adModel = Gson().fromJson(result, ADModel::class.java)
                <EMAIL> = adModel
                checkAD()
                LogUtils.e("adPassportServiceUrl:$result")
//                ActivityUtils.finishActivity(this@WelcomeADActivity)
//                runOnUiThread { showMessage(R.string.commit_success) }
            }
        })


    }


    private fun doDownload() {
        if (downloadModelList.size > 0 && nowDownloadIndex <= downloadModelList.size - 1) {
            downloadAdvertisingContent(
                downloadModelList[nowDownloadIndex].urlStr,
                downloadModelList[nowDownloadIndex].filePath,
                downloadModelList[nowDownloadIndex].fileName
            )
        }
        nowDownloadIndex++
    }

    //下载广告内容
    private fun downloadAdvertisingContent(
        urlStr: String,
        filePath: String,
        fileName: String,
    ) {
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Int>() {
            override fun doInBackground(): Int {
                val httpDownLoader = HttpDownloader()
                val downFile = httpDownLoader.downFile(urlStr, filePath, fileName)
                return downFile
            }

            override fun onSuccess(result: Int?) {
                if (result == 0 || result == 1 || result == -1) {
                    doDownload()
                }
            }
        })
    }

    //检查本地数据, 删除该删除的, 下载接口里没有的, 保存json
    private fun formatData() {
        //增加一个 接口里没有的数据就删除掉
        val data = adModel?.data
        val allFilesNameList = arrayListOf<String>()
        if (data != null) {
            if (data.size == 0) {
                FileUtils.delete(AD_DOCUMENT_PATH)
                return
            }
            for (value in data) {
                for (imageUrl in value.imagesVo.verticalImages) {//正常情况只应该有一张图片或者视频,因为暂时不支持轮播图模式
                    val fileName1 = FileUtils.getFileName(imageUrl.imageUrl.toString())
                    allFilesNameList.add(fileName1)
                    if (!File(AD_DOCUMENT_PATH + fileName1).exists()) {
                        //没有文件, 直接下载
                        downloadModelList.add(
                            DownloadModel(
                                imageUrl.imageUrl, AD_DOCUMENT_PATH,
                                FileUtils.getFileName(imageUrl.imageUrl)
                            )
                        )
                    } else {// 有文件

                    }
                }

            }
        } else {
            FileUtils.delete(AD_DOCUMENT_PATH)
        }

        val readDocumentFileList = HttpDownloader.readAllFileName(AD_DOCUMENT_PATH)
        val shouldDeleteDocument = readDocumentFileList.filterNot {
            allFilesNameList.contains(it)
        }

        for (name in shouldDeleteDocument) {
            FileUtils.delete(AD_DOCUMENT_PATH + name)
        }

        launch { //保存json
            val result = FileUtils.delete(AD_DOCUMENT_PATH + "adJson.txt")
            if (result) {
                val toJson = Gson().toJson(adModel)
                HttpDownloader.writeTxtToFile(toJson, AD_DOCUMENT_PATH, "adJson.txt")//保存json文件
            }
        }
    }

    class DownloadModel(
        var urlStr: String,
        var filePath: String,
        var fileName: String
    )
}