package com.czur.scanpro.ui.user

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityNewPayBinding
import com.czur.scanpro.databinding.ActivityUserBinding
import com.czur.scanpro.entity.model.UserAllInfoModel
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.account.LoginActivity
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.WebViewActivity
import com.czur.scanpro.ui.component.MediumBoldTextView
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.file.PdfActivity
import com.czur.scanpro.ui.sync.SyncService
import com.czur.scanpro.utils.EtUtils
import com.facebook.drawee.view.SimpleDraweeView
import com.suke.widget.SwitchButton
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by shaojun
 */
class UserActivity : BaseActivity(), View.OnClickListener {

    private lateinit var userPreferences: UserPreferences
    private var isLogin: Boolean? = false
    private var isAutoSync: Boolean = false
    private lateinit var formatter: SimpleDateFormat
    private lateinit var syncAnim: ObjectAnimator
    private var isFinish: Boolean = false
    private lateinit var hideSyncImgAnim: ObjectAnimator

    private lateinit var sync_switch_btn: SwitchButton
    private lateinit var book_menu_last_sync_time: MediumBoldTextView
    private lateinit var sync_now_img: ImageView
    private lateinit var ll_login: LinearLayout
    private lateinit var ll_no_login: LinearLayout
    private lateinit var user_vip_red_point: ImageView
    private lateinit var user_input_invite_red_point: ImageView
    private lateinit var user_head_img: SimpleDraweeView
    private lateinit var login_name: MediumBoldTextView
    private lateinit var vip_icon: ImageView
    private lateinit var card_tv: MediumBoldTextView
    private lateinit var cloudOcrTv: MediumBoldTextView
    private lateinit var handwriting_tv: MediumBoldTextView
    private lateinit var user_ocr_rl: RelativeLayout
    private lateinit var user_info_pdf_rl: RelativeLayout
    private lateinit var line: View
    private lateinit var user_card_ocr_rl: RelativeLayout
    private lateinit var user_hand_ocr_rl: RelativeLayout
    private lateinit var user_cloud_ocr_rl: RelativeLayout
    private lateinit var btn_update_vip: MediumBoldTextView
    private lateinit var ocr_tv: MediumBoldTextView
    private lateinit var pdf_tv: MediumBoldTextView
    private lateinit var user_input_invite_rl: RelativeLayout
    private lateinit var tv_cloud_size: MediumBoldTextView
    private lateinit var tv_my_setting: MediumBoldTextView
    private lateinit var user_pdf: RelativeLayout
    private lateinit var backBtn: ImageView
    private lateinit var user_vip_rl: RelativeLayout
    private lateinit var user_sync_now_rl: RelativeLayout
    private lateinit var img_login: ImageView
    private lateinit var tv_login: MediumBoldTextView
    private lateinit var rl_about: RelativeLayout
    private lateinit var rl_privacy: RelativeLayout
    private lateinit var rl_privacy2: RelativeLayout
    private lateinit var rl_my_invite: RelativeLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_user)
        initView()
        initComponent()
        registerEvent()
    }

    private fun initView() {
        sync_switch_btn = findViewById(R.id.sync_switch_btn)
        book_menu_last_sync_time = findViewById(R.id.book_menu_last_sync_time)
        sync_now_img = findViewById(R.id.sync_now_img)
        ll_login = findViewById(R.id.ll_login)
        ll_no_login = findViewById(R.id.ll_no_login)
        user_vip_red_point = findViewById(R.id.user_vip_red_point)
        user_input_invite_red_point = findViewById(R.id.user_input_invite_red_point)
        user_head_img = findViewById(R.id.user_head_img)
        login_name = findViewById(R.id.login_name)
        vip_icon = findViewById(R.id.vip_icon)
        card_tv = findViewById(R.id.card_tv)
        cloudOcrTv = findViewById(R.id.cloudOcrTv)
        handwriting_tv = findViewById(R.id.handwriting_tv)
        user_ocr_rl = findViewById(R.id.user_ocr_rl)
        user_info_pdf_rl = findViewById(R.id.user_info_pdf_rl)
        line = findViewById(R.id.line)
        user_card_ocr_rl = findViewById(R.id.user_card_ocr_rl)
        user_hand_ocr_rl = findViewById(R.id.user_hand_ocr_rl)
        user_cloud_ocr_rl = findViewById(R.id.user_cloud_ocr_rl)
        btn_update_vip = findViewById(R.id.btn_update_vip)
        ocr_tv = findViewById(R.id.ocr_tv)
        pdf_tv = findViewById(R.id.pdf_tv)
        user_input_invite_rl = findViewById(R.id.user_input_invite_rl)
        tv_cloud_size = findViewById(R.id.tv_cloud_size)
        tv_my_setting = findViewById(R.id.tv_my_setting)
        user_pdf = findViewById(R.id.user_pdf)
        backBtn = findViewById(R.id.backBtn)
        user_vip_rl = findViewById(R.id.user_vip_rl)
        user_sync_now_rl = findViewById(R.id.user_sync_now_rl)
        img_login = findViewById(R.id.img_login)
        tv_login = findViewById(R.id.tv_login)
        rl_about = findViewById(R.id.rl_about)
        rl_privacy = findViewById(R.id.rl_privacy)
        rl_privacy2 = findViewById(R.id.rl_privacy2)
        rl_my_invite = findViewById(R.id.rl_my_invite)
    }

    @SuppressLint("SimpleDateFormat")
    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        isAutoSync = userPreferences.isAutoSync
        sync_switch_btn.isChecked = isAutoSync
        if (userPreferences.isUserLogin) {
            requestUserInfoNow()
        } else {
            checkIsLogin()
        }
    }

    override fun onResume() {
        super.onResume()
    }

    /**
     * @des: 设置同步时间文字
     * @params:
     * @return:
     */
    private fun setSyncText() {
        if (userPreferences.syncTime == 0L) {
            book_menu_last_sync_time.text = getString(R.string.has_not_sync)
        } else {
            val curDate = formatter.format(userPreferences.syncTime)
            book_menu_last_sync_time.text = String.format(getString(R.string.last_sync_time), curDate)
        }
        book_menu_last_sync_time.visibility = View.VISIBLE
        sync_now_img.visibility = View.GONE
    }

    /**
     * @des: 请求用户信息
     * @params:
     * @return:
     */

    private fun requestUserInfoNow() {
        ll_login.visibility = View.VISIBLE
        ll_no_login.visibility = View.GONE
        HttpManager.getInstance().request().userAllInfo(userPreferences.userId, UserAllInfoModel::class.java, object : MiaoHttpManager.CallbackNetwork<UserAllInfoModel> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {
                showProgressDialog()

            }

            override fun onResponse(entity: MiaoHttpEntity<UserAllInfoModel>) {
                hideProgressDialog()
                userPreferences.usages = entity.body.usages
                userPreferences.usagesLimit = entity.body.usagesLimit
                userPreferences.photoOssKey = entity.body.photoOssKey
                userPreferences.isInvited = entity.body.isIsInvitate
                userPreferences.userMobile = entity.body.mobile
                userPreferences.isVip = entity.body.isIsVip
                userPreferences.userType = entity.body.userType
                userPreferences.inviteCount = entity.body.invitationCount
                userPreferences.vipEndOn = entity.body.vipEndOn
                userPreferences.svipEndOn = entity.body.svipEndOn
                userPreferences.inviteCode = entity.body.invitationCode

                userPreferences.remainOcr = entity.body.remainingOcr
                userPreferences.remainVip = entity.body.remainingVip
                userPreferences.remainCard = entity.body.remainingCertificate
                userPreferences.remainPdf = entity.body.remainingPdf
                if (userPreferences.isVip) {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrVip
                    userPreferences.remainHandwriting = entity.body.remainingHandwriting
                } else {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrNormal
                    userPreferences.remainHandwriting = entity.body.remainingHandwritingNormal
                }
                if (userPreferences.goAdvancedFirst) {
                    user_vip_red_point.visibility = View.GONE
                } else {
                    user_vip_red_point.visibility = View.VISIBLE
                }
                if (userPreferences.goInviteFirst) {
                    user_input_invite_red_point.visibility = View.GONE
                } else {
                    user_input_invite_red_point.visibility = View.VISIBLE
                }
                checkIsLogin()
            }

            override fun onFailure(entity: MiaoHttpEntity<UserAllInfoModel>) {
                hideProgressDialog()
            }

            override fun onError(e: Exception) {
                hideProgressDialog()
            }
        })
    }

    private fun checkIsLogin() {
        if (userPreferences.isUserLogin) {
            ll_login.visibility = View.VISIBLE
            ll_no_login.visibility = View.GONE
            setSyncText()
            initAnim()
            if (ServiceUtils.isServiceRunning(SyncService::class.java)) {
                startLoadingAnim()
            }
            if (userPreferences.userPhoto.isNullOrEmpty()) {
                user_head_img.setImageResource(R.mipmap.user_login_icon)
            } else {
                user_head_img.setImageURI(userPreferences.userPhoto)
            }
            login_name.text = userPreferences.userName
            //判断vip类型
            if (userPreferences.isVip) {
                vip_icon.visibility = View.VISIBLE
                card_tv.text = convertTimes(userPreferences.remainCard, 3)
                cloudOcrTv.text = convertTimes(userPreferences.remainCloudOcr, 3)
                handwriting_tv.text = convertTimes(userPreferences.remainHandwriting, 3)
                user_ocr_rl.visibility = View.GONE
                user_info_pdf_rl.visibility = View.GONE
                line.visibility = View.GONE
                user_card_ocr_rl.visibility = View.VISIBLE
                user_hand_ocr_rl.visibility = View.VISIBLE
                user_cloud_ocr_rl.visibility = View.VISIBLE
                if (userPreferences.userType == "svip") {
                    val layoutParams = vip_icon.layoutParams
                    layoutParams.width = SizeUtils.dp2px(40f)
                    vip_icon.layoutParams = layoutParams
                    vip_icon.setImageResource(R.mipmap.img_svip)
                    btn_update_vip.visibility = View.GONE
                } else {
                    val layoutParams = vip_icon.layoutParams
                    layoutParams.width = SizeUtils.dp2px(26f)
                    vip_icon.layoutParams = layoutParams
                    vip_icon.setImageResource(R.mipmap.img_vip)
                    if (userPreferences.remainCard == 0 || userPreferences.remainCloudOcr == 0 || userPreferences.remainHandwriting == 0) {
                        btn_update_vip.visibility = View.VISIBLE
                    } else {
                        btn_update_vip.visibility = View.GONE
                    }
                }
            } else {
                vip_icon.visibility = View.GONE
                cloudOcrTv.text = convertTimes(userPreferences.remainCloudOcr, 1)
                handwriting_tv.text = convertTimes(userPreferences.remainHandwriting, 1)
                ocr_tv.text = convertTimes(userPreferences.remainOcr, 2)
                pdf_tv.text = convertTimes(userPreferences.remainPdf, 2)
                card_tv.text = convertTimes(userPreferences.remainCard, 2)
                user_ocr_rl.visibility = View.VISIBLE
                user_info_pdf_rl.visibility = View.VISIBLE
                line.visibility = View.VISIBLE

                user_card_ocr_rl.visibility = View.VISIBLE
                user_hand_ocr_rl.visibility = View.VISIBLE
                user_cloud_ocr_rl.visibility = View.VISIBLE

                if (userPreferences.remainCloudOcr == 0 || userPreferences.remainHandwriting == 0 || userPreferences.remainOcr == 0
                        || userPreferences.remainPdf == 0 || userPreferences.remainCard == 0) {
                    btn_update_vip.visibility = View.VISIBLE
                } else {
                    btn_update_vip.visibility = View.GONE
                }
            }
            user_input_invite_rl.visibility = if (userPreferences.isInvited) View.GONE else View.VISIBLE
            tv_cloud_size.text = String.format(getString(R.string.cloud_size_detail), EtUtils.convertFileSize(userPreferences.usages), EtUtils.convertFileSize(userPreferences.usagesLimit))
        } else {
            ll_login.visibility = View.GONE
            ll_no_login.visibility = View.VISIBLE
        }
    }

    private fun registerEvent() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        tv_my_setting.setOnClickListener(this)
        user_pdf.setOnClickListener(this)
        vip_icon.setOnClickListener(this)
        login_name.setOnClickListener(this)
        backBtn.setOnClickListener(this)
        user_head_img.setOnClickListener(this)
        user_vip_rl.setOnClickListener(this)
        user_sync_now_rl.setOnClickListener(this)
        user_input_invite_rl.setOnClickListener(this)
        sync_switch_btn.setOnCheckedChangeListener(SwitchButton.OnCheckedChangeListener { _, isChecked ->
            userPreferences.isAutoSync = isChecked
            LogUtils.i(userPreferences.isAutoSync)
        })
        btn_update_vip.setOnClickListener(this)
        img_login.setOnClickListener(this)
        tv_login.setOnClickListener(this)
        rl_about.setOnClickListener(this)
        rl_privacy.setOnClickListener(this)
        rl_privacy2.setOnClickListener(this)
        rl_my_invite.setOnClickListener(this)
    }

    private fun convertTimes(times: Int, type: Int): String {
        return when (type) {
            1 -> {
                String.format(getString(R.string.convert_times), times.toString())
            }
            2 -> {
                String.format(getString(R.string.day_convert_times), times.toString())
            }
            else -> {
                String.format(getString(R.string.month_convert_times), times.toString())
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            EventType.LOG_OUT -> {
                checkIsLogin()
            }
            //登录注册
            EventType.LOG_IN,
            EventType.THIRD_PARTY_LOGIN_IN,
            EventType.REGISTER_SUCCESS,
            EventType.THIRD_PARTY_REGISTER_SUCCESS,
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
            EventType.CHANGE_PASSWORD,
            EventType.BIND_PHONE,
            EventType.CHANGE_PHONE,
            EventType.BIND_EMAIL,
            EventType.CHANGE_EMAIL,
            EventType.EDIT_USER_IMAGE,
            EventType.USER_EDIT_NAME,
            EventType.UPDATE_CACHE,
            EventType.INPUT_INVITE_CODE,
            EventType.PDF_COUNT_REDUCE,
            EventType.BAIDU_OCR_COUNT_REDUCE,
            EventType.OCR_COUNT_REDUCE,
            EventType.HANDWRITING_COUNT_REDUCE,
            EventType.CARD_COUNT_REDUCE,
            EventType.BECAME_VIP, EventType.PAY_SUCCESS ->
                requestUserInfoNow()
            EventType.IS_SYNCHRONIZING -> if (ServiceUtils.isServiceRunning(SyncService::class.java)) {
                syncAnim = ObjectAnimator.ofFloat(sync_now_img, "rotation", 0f, 720f)
                syncAnim.interpolator = LinearInterpolator()
                syncAnim.repeatCount = ObjectAnimator.INFINITE
                syncAnim.duration = 2000
                startLoadingAnim()
            }
            EventType.SYNC_IS_FINISH -> setSyncText()
            EventType.SYNC_ANIM_FINISH -> {
                isFinish = true
                stopSyncAnim()
                LogUtils.i("receive service EventBUS 完成")
                checkIsLogin()
            }
            EventType.SYNC_SPACE_IS_NOT_ENOUGH -> {
                LogUtils.e("SYNC_SPACE_IS_NOT_ENOUGH")
                isFinish = false
                stopSyncAnim()
                LogUtils.i("receive service EventBUS 中断")
            }
            EventType.SYNC_IS_STOP -> {
                isFinish = false
                stopSyncAnim()
                LogUtils.i("receive service EventBUS 中断")
            }
            else -> {

            }
        }
    }

    private fun startSyncAnim() {
        LogUtils.i("启动同步服务")
        startSyncNowByHand()
    }

    private fun stopSyncAnim() {
        hideSyncImgAnim.start()
    }

    private fun startLoadingAnim() {
        sync_now_img.alpha = 1.0f
        LogUtils.i("isFinish: ", isFinish)
        sync_now_img.visibility = View.VISIBLE
        book_menu_last_sync_time.visibility = View.GONE
        syncAnim.start()
    }

    private fun stopLoadingAnim() {
        sync_now_img.visibility = View.GONE
        if (syncAnim.isRunning) {
            syncAnim.cancel()
        }
        book_menu_last_sync_time.visibility = View.VISIBLE
    }

    /**
     * @des: 初始化动画
     * @params:
     * @return:
     */

    private fun initAnim() {
        //进度圆圈旋转动画
        syncAnim = ObjectAnimator.ofFloat(sync_now_img, "rotation", 0f, 720f)
        syncAnim.interpolator = LinearInterpolator()
        syncAnim.repeatCount = ObjectAnimator.INFINITE
        syncAnim.duration = 2000


        //圆圈进度消失渐变动画
        hideSyncImgAnim = ObjectAnimator.ofFloat(sync_now_img, "alpha", 1.0f, 0f)
        hideSyncImgAnim.duration = 500
        hideSyncImgAnim.interpolator = LinearInterpolator()
        hideSyncImgAnim.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {}

            override fun onAnimationEnd(animation: Animator) {
                LogUtils.i("isFinish: ", isFinish)
                stopLoadingAnim()

            }

            override fun onAnimationCancel(animation: Animator) {}

            override fun onAnimationRepeat(animation: Animator) {}
        })
    }

    private fun startSyncNowByHand() {
        if (UserPreferences.getInstance(this).isUserLogin) {
            if (!ServiceUtils.isServiceRunning(SyncService::class.java)) {
                if (UserPreferences.getInstance(this).isAutoSync) {
                    if (UserPreferences.getInstance(this).isSyncOnlyWifi) {
                        if (NetworkUtils.isConnected()) {
                            startSyncService()
                        }
                    } else {
                        startSyncService()
                    }
                } else {
                    startSyncService()
                }
            }
        }
    }

    private fun syncNow() {
        if (!ServiceUtils.isServiceRunning(SyncService::class.java)) {
            if (NetworkUtils.isConnected()) {
                if (userPreferences.isSyncOnlyWifi) {
                    if (!NetworkUtils.isWifiConnected()) {
                        showNotInWifiDialog()
                    } else {
                        startSyncAnim()
                    }
                } else {
                    startSyncAnim()
                }
            } else {
                showMessage(R.string.cant_sync_no_network)
            }

        } else {
            LogUtils.i("同步服务正在运行！不需要启动")
        }

    }

    /**
     * @des: 手动同步不在wifi下提示
     * @params:
     * @return:
     */

    private fun showNotInWifiDialog() {
        val builder = ScanProCommonPopup.Builder(this@UserActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.sync_in_4g_prompt))
        builder.setOnPositiveListener { dialog, _ ->
            dialog.dismiss()
            LogUtils.i("启动服务")
            startSyncAnim()
        }
        builder.setOnNegativeListener { dialog, which -> dialog.dismiss() }
        val commonPopup = builder.create()
        commonPopup.show()
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.tv_my_setting -> {
                ActivityUtils.startActivity(UserInfoActivity::class.java)
            }
            R.id.user_sync_now_rl -> syncNow()
            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.user_pdf -> {
                ActivityUtils.startActivity(PdfActivity::class.java)
            }
            R.id.login_name, R.id.vip_icon, R.id.user_head_img -> {
                ActivityUtils.startActivity(UserInfoActivity::class.java)
            }
            R.id.img_login, R.id.tv_login -> {
                ActivityUtils.startActivity(LoginActivity::class.java)
            }
            R.id.rl_privacy -> {
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_policy1))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", Constants.PRIVACY_AGREEMENT)
                ActivityUtils.startActivity(intent)
            }
            R.id.rl_privacy2 -> {
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.privacy_terms))
                val cTime = Calendar.getInstance().timeInMillis
                intent.putExtra("url", Constants.PRIVACY_TERMS)
                ActivityUtils.startActivity(intent)
            }
            R.id.rl_about -> {
                ActivityUtils.startActivity(AboutActivity::class.java)
            }
            R.id.user_vip_rl -> {
                userPreferences.goAdvancedFirst = true
                user_vip_red_point.visibility = View.GONE
                ActivityUtils.startActivity(NewPayActivity::class.java)
            }
            R.id.btn_update_vip -> {
                ActivityUtils.startActivity(NewPayActivity::class.java)
            }
            R.id.user_input_invite_rl -> {
                ActivityUtils.startActivity(InputInviteActivity::class.java)
            }
            R.id.rl_my_invite -> {
                userPreferences.goInviteFirst = true
                user_input_invite_red_point.visibility = View.GONE
                ActivityUtils.startActivity(UserInviteActivity::class.java)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}
