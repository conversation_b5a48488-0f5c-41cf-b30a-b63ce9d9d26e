package com.czur.scanpro.ui.user

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import com.blankj.utilcode.util.*
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.MediumBoldTextView
import com.czur.scanpro.utils.validator.Validator
import okhttp3.*
import java.io.IOException

/**
 * Created by Yz on 2019/3/14.
 * Email：<EMAIL>
 */
class UserFeedbackActivity : BaseActivity(), View.OnClickListener {

    private var userFeedbackEdt: EditText? = null
    private var httpManager: HttpManager? = null

    private lateinit var userTitle: MediumBoldTextView
    private lateinit var commitBtn: MediumBoldTextView
    private lateinit var userBackBtn: ImageView
    private lateinit var user_mail_edt: EditText
    private lateinit var user_feedback_edt: EditText

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_user_feedback)
        initView()
        initComponent()
        registerEvent()
    }

    private fun initView() {
        userTitle = findViewById(R.id.userTitle)
        commitBtn = findViewById(R.id.commitBtn)
        userBackBtn = findViewById(R.id.userBackBtn)
        user_mail_edt = findViewById(R.id.user_mail_edt)
        user_feedback_edt = findViewById(R.id.user_feedback_edt)
    }

    private fun initComponent() {
        httpManager = HttpManager.getInstance()
        userTitle!!.setText(R.string.feedback)
    }

    private fun registerEvent() {
        commitBtn.setOnClickListener(this)
        userBackBtn.setOnClickListener(this)
        user_mail_edt.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (user_mail_edt.text.isNotEmpty() && user_feedback_edt.text.isNotEmpty()) {
                    commitBtn.isClickable = true
                    commitBtn.isEnabled = true
                    commitBtn.setTextColor(ColorUtils.getColor(R.color.red_de4d4d))
                } else {
                    commitBtn.isClickable = false
                    commitBtn.isEnabled = true
                    commitBtn.setTextColor(ColorUtils.getColor(R.color.gray_c4))
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })
        user_feedback_edt.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (user_mail_edt.text.isNotEmpty() && user_feedback_edt.text.isNotEmpty()) {
                    commitBtn.isClickable = true
                    commitBtn.isEnabled = true
                    commitBtn.setTextColor(ColorUtils.getColor(R.color.red_de4d4d))
                } else {
                    commitBtn.isClickable = false
                    commitBtn.isEnabled = true
                    commitBtn.setTextColor(ColorUtils.getColor(R.color.gray_c4))
                }
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

        })
    }

    private fun feedback() {
        if (!NetworkUtils.isConnected()) {
            showMessage(R.string.no_connection_network)
            return
        }
        if (!RegexUtils.isEmail(user_mail_edt.text.toString())) {
            showMessage(R.string.login_alert_mail_error)
            return
        }

        val okHttpClient = MiaoHttpManager.getInstance().client
        //Form表单格式的参数传递
        val formBody = FormBody.Builder()
                .add("os", "Android")//设置参数名称和参数值
                .add("type", "1")//设置参数名称和参数值
                .add("email", if (Validator.isEmpty(user_mail_edt.text.toString())) "" else user_mail_edt.text.toString())//设置参数名称和参数值
                .add("productName", Constants.SCAN_PRO_CN)//设置参数名称和参数值
                .add("content", user_feedback_edt.text.toString())//设置参数名称和参数值
                .build()
        val request = Request.Builder()
                .post(formBody)//Post请求的参数传递
                .url(BuildConfig.PHASE.feedBackUrl + Constants.FEEDBACK_URL)
                .build()
        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                LogUtils.e(e)
            }

            @Throws(IOException::class)
            override fun onResponse(call: Call, response: Response) {
                //此方法运行在子线程中，不能在此方法中进行UI操作。
                val result = response.body!!.string()
                LogUtils.e(result)
                ActivityUtils.finishActivity(this@UserFeedbackActivity)
                runOnUiThread { showMessage(R.string.commit_success) }
            }
        })


    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.userBackBtn -> ActivityUtils.finishActivity(this)
            R.id.commitBtn -> if (Validator.isNotEmpty(user_feedback_edt.text.toString())) {
                feedback()
            } else {
                showMessage(R.string.commit_empty)
            }
            else -> {
            }
        }
    }


}