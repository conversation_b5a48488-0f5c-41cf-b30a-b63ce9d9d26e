package com.czur.scanpro.ui.user

import android.Manifest
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.ServiceUtils
import com.czur.scanpro.R
import com.czur.scanpro.databinding.ActivityUserBinding
import com.czur.scanpro.databinding.ActivityUserInfoBinding
import com.czur.scanpro.entity.model.UserAllInfoModel
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.StopServiceEvent
import com.czur.scanpro.event.StopSyncTimeCountEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.account.*
import com.czur.scanpro.ui.album.SelectAlbumPhotoActivity
import com.czur.scanpro.ui.album.SelectAlbumPhotoActivity2
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.WebViewActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.sync.AutoSyncTimeCountService
import com.czur.scanpro.ui.sync.SyncService
import com.czur.scanpro.utils.PermissionUtil
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Created by shaojun
 */
class UserInfoActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityUserInfoBinding by lazy{
        ActivityUserInfoBinding.inflate(layoutInflater)
    }
    
    
    private lateinit var userPreferences: UserPreferences
    private var isLogin: Boolean? = false
    private var isMobileBind = false
    private var isEmailBind = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_fa)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_user_info)
        setContentView(binding.root)
        initComponent()
        registerEvent()
        requestUserInfo {
            binding.phoneTv.text = userPreferences.userMobile
        }
    }

    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        binding.userTopBar.normalTitle.text = getString(R.string.user_info)
        checkIsLogin()
        requestUserInfoNow()
    }

    /**
     * @des: 请求用户信息
     * @params:
     * @return:
     */

    private fun requestUserInfoNow() {
        HttpManager.getInstance().request().userAllInfo(
            userPreferences.userId,
            UserAllInfoModel::class.java,
            object : MiaoHttpManager.CallbackNetwork<UserAllInfoModel> {
                override fun onNoNetwork() {
                }

                override fun onStart() {
                }

                override fun onResponse(entity: MiaoHttpEntity<UserAllInfoModel>) {
                    hideProgressDialog()
                    LogUtils.i(Gson().toJson(entity.body))
                    userPreferences.usages = entity.body.usages
                    userPreferences.usagesLimit = entity.body.usagesLimit
                    userPreferences.photoOssKey = entity.body.photoOssKey
                    userPreferences.isInvited = entity.body.isIsInvitate

                    userPreferences.isVip = entity.body.isIsVip
                    userPreferences.userType = entity.body.userType
                    userPreferences.vipEndOn = entity.body.vipEndOn
                    userPreferences.svipEndOn = entity.body.svipEndOn
                    userPreferences.inviteCode = entity.body.invitationCode
                    userPreferences.inviteCount = entity.body.invitationCount

                    userPreferences.remainOcr = entity.body.remainingOcr
                    userPreferences.remainVip = entity.body.remainingVip
                    userPreferences.remainCard = entity.body.remainingCertificate
                    userPreferences.remainPdf = entity.body.remainingPdf
                    if (userPreferences.isVip) {
                        userPreferences.remainCloudOcr = entity.body.remainingCloudocrVip
                        userPreferences.remainHandwriting = entity.body.remainingHandwriting
                    } else {
                        userPreferences.remainCloudOcr = entity.body.remainingCloudocrNormal
                        userPreferences.remainHandwriting = entity.body.remainingHandwritingNormal
                    }
                    checkIsLogin()
                }

                override fun onFailure(entity: MiaoHttpEntity<UserAllInfoModel>) {
                    showMessage(R.string.request_failed_alert)
                }

                override fun onError(e: Exception) {
                    showMessage(R.string.request_failed_alert)
                }
            })
    }

    private fun checkIsLogin() {
        if (userPreferences.isUserLogin) {
            if (userPreferences.userPhoto.isNullOrEmpty()) {
                binding.userInfoHeadImg.setImageResource(R.mipmap.user_login_icon)
            } else {
                binding.userInfoHeadImg.setImageURI(userPreferences.userPhoto)
            }
            binding.nameTv.text = userPreferences.userName
            if (Validator.isNotEmpty(userPreferences.userMobile)) {
                binding.phoneTv.text = userPreferences.userMobile
                isMobileBind = true
            } else {
                binding.phoneTv.setText(R.string.not_bind)
                isMobileBind = false
            }
            if (Validator.isNotEmpty(userPreferences.userEmail)) {
                binding.emailTv.text = userPreferences.userEmail
                isEmailBind = true
            } else {
                binding.emailTv.setText(R.string.not_bind)
                isEmailBind = false
            }
        }
    }

    private fun registerEvent() {
        EventBus.getDefault().register(this)
        binding.userInfoHeadImg.setOnClickListener(this)
        binding.userPswRl.setOnClickListener(this)
        binding.userInfoHeadRl.setOnClickListener(this)
        binding.userTopBar.backBtn.setOnClickListener(this)
        binding.userPhoneRl.setOnClickListener(this)
        binding.userEmailRl.setOnClickListener(this)
        binding.userNameRl.setOnClickListener(this)
        binding.btnLogout.setOnClickListener(this)
        binding.userPrivacyRl.setOnClickListener(this)
        binding.userAboutRl.setOnClickListener(this)
        binding.userFeedbackRl.setOnClickListener(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            //登录注册
            EventType.LOG_IN,
            EventType.THIRD_PARTY_LOGIN_IN,
            EventType.REGISTER_SUCCESS,
            EventType.THIRD_PARTY_REGISTER_SUCCESS,
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
            EventType.CHANGE_PASSWORD,
            EventType.BIND_PHONE,
            EventType.INPUT_INVITE_CODE,
            EventType.CHANGE_PHONE,
            EventType.BIND_EMAIL,
            EventType.CHANGE_EMAIL,
            EventType.EDIT_USER_IMAGE,
            EventType.USER_EDIT_NAME,
            EventType.LOG_OUT,
            EventType.SYNC_IS_FINISH,
            EventType.SYNC_IS_STOP,
            EventType.UPDATE_CACHE,
            EventType.BECAME_VIP, EventType.PAY_SUCCESS ->
                checkIsLogin()

            else -> {
            }
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.userPrivacyRl -> {
                val intent = Intent(this, UserPrivacyActivity::class.java)
                ActivityUtils.startActivity(intent)
            }

            R.id.userAboutRl -> {
                ActivityUtils.startActivity(AboutActivity::class.java)
            }

            R.id.userFeedbackRl -> {
                ActivityUtils.startActivity(UserFeedbackActivity::class.java)
            }

            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }

            R.id.userPhoneRl -> {
                if (isMobileBind) {
                    ActivityUtils.startActivity(UserChangePhoneActivity::class.java)
                } else {
                    ActivityUtils.startActivity(UserBindPhoneActivity::class.java)
                }
            }

            R.id.userPswRl -> {
                ActivityUtils.startActivity(UserChangePasswordActivity::class.java)
            }

            R.id.userEmailRl -> {
                if (isEmailBind) {
                    ActivityUtils.startActivity(UserChangeEmailActivity::class.java)
                } else {
                    ActivityUtils.startActivity(UserBindEmailActivity::class.java)
                }
            }

            R.id.userInfoHeadImg, R.id.userInfoHeadRl -> {
                if (PermissionUtils.isGranted(*PermissionUtil.getStoragePermission())
                ) {
                    ActivityUtils.startActivity(SelectAlbumPhotoActivity2::class.java)
                } else {
                    PermissionUtil.checkPermissionWithDialog(
                        this,
                        getString(R.string.dialog_tips),
                        getString(R.string.user_photo_setting_power_tips),
                        getString(R.string.dialog_setting),
                        getString(R.string.dialog_cancel)
                    ) {
                        if (it != null) {
                            ActivityUtils.startActivity(SelectAlbumPhotoActivity2::class.java)
                        }
                    }
                }


            }

            R.id.userNameRl -> {
                ActivityUtils.startActivity(UserChangeUserNameActivity::class.java)
            }

            R.id.btnLogout -> {
                confirmExit()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    private fun confirmExit() {
        val builder = ScanProCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.confirm_esc))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            EventBus.getDefault().post(StopServiceEvent(EventType.STOP_SYNC))
            EventBus.getDefault().post(StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT))
            if (ServiceUtils.isServiceRunning(SyncService::class.java)) {
                ServiceUtils.stopService(SyncService::class.java)
            }
            if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
                ServiceUtils.stopService(AutoSyncTimeCountService::class.java)
            }
            showMessage(R.string.logout_success)
            logout()
            dialog.dismiss()
            isLogin = false
        })
        builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, _ -> dialog.dismiss() })
        val commonPopup = builder.create()
        commonPopup.show()
    }
}
