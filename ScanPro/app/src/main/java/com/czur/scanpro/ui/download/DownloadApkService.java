package com.czur.scanpro.ui.download;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.LogUtils;
import com.czur.scanpro.R;

public class DownloadApkService extends Service {

    private AppDownloadManager downloadManager;
    private String updateUrl;
    private String notes;
    private String apkName;


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        updateUrl = intent.getStringExtra("updateUrl");
        notes = intent.getStringExtra("notes");
        apkName = intent.getStringExtra("apkName");
        downloadManager = new AppDownloadManager(this);
        downloadManager.setApkName(apkName);
        downloadManager.resume();
        check();
        return super.onStartCommand(intent, flags, startId);
    }

    private void check() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                downloadManager.downloadApk(updateUrl, getString(R.string.app_update_title), notes);
                downloadManager.setUpdateListener(new AppDownloadManager.OnUpdateListener() {
                    @Override
                    public void update(int currentByte, int totalByte) {
                        LogUtils.i(currentByte, totalByte, (int) ((float) currentByte * 100.0f / (float) totalByte) + "%");
                    }
                });
            }
        }).start();

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        downloadManager.onPause();
    }
}
