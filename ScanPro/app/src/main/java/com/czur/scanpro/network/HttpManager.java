package com.czur.scanpro.network;


import com.czur.scanpro.network.core.MiaoHttpManager;

public class HttpManager {

    private HttpManager() {
    }

    public static HttpManager getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        protected static final HttpManager instance = new HttpManager();
    }

    public HttpServices request() {
        return MiaoHttpManager.getInstance().create(HttpServices.class, MiaoHttpManager.getInstance().urlServer, true);
    }

    public PassportServices requestPassport() {

        return MiaoHttpManager.getInstance().create(PassportServices.class, MiaoHttpManager.getInstance().urlPassport, false);
    }
}
