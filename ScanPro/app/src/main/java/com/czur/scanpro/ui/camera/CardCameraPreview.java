package com.czur.scanpro.ui.camera;

import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.hardware.Camera;
import android.hardware.Camera.AutoFocusCallback;
import android.os.Handler;
import android.util.Log;
import android.view.SurfaceHolder;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.google.gson.Gson;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class CardCameraPreview implements SurfaceHolder.Callback {

    private Context context;
    private final static String TAG = "CameraPreview";
    private Camera mCamera = null;
    private int mCameraId = 0;
    //    private SensorController sensorController;
    private Camera.Parameters mCameraParameters;
    private boolean isFocusByHand;


    public CardCameraPreview(Context context) {
        this.context = context;
    }

    /**
     * 开启camera并且预览
     *
     * @return
     */
    private void safeOpenCamera() {
        try {
            releaseCamera();
            mCamera = Camera.open(mCameraId);
        } catch (Exception e) {
            LogUtils.e("failed to open Camera");
            e.printStackTrace();
        }
    }

    /**
     * 预览相机
     */
    private void startPreview(Camera camera, SurfaceHolder holder) {
        try {
            setupCamera(camera);
            mCamera.setPreviewDisplay(holder);
            mCamera.setDisplayOrientation(90);
            mCamera.startPreview();
            mCamera.cancelAutoFocus();
            if (listener != null) {
                listener.cameraIsReady();
            }

            mCameraParameters = mCamera.getParameters();
//            sensorController = SensorController.getInstance(context);
//            sensorController.setCameraFocusListener(new SensorController.CameraFocusListener() {
//                @Override
//                public void onFocus() {
//
//                    if (mCamera != null) {
//                        DisplayMetrics mDisplayMetrics = context.getResources()
//                                .getDisplayMetrics();
//                        int mScreenWidth = mDisplayMetrics.widthPixels;
//                        if (!sensorController.isFocusLocked()) {
//                            if (newFocus(mScreenWidth / 2, mScreenWidth / 2)) {
//                                sensorController.lockFocus();
//                            }
//                        }
//                    }
//                }
//            });
//            sensorController.start();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private boolean isFocusing;

    private boolean newFocus(int x, int y) {
        //正在对焦时返回
        if (mCamera == null || isFocusing || isFocusByHand) {
            return false;
        }
        isFocusing = true;
        setMeteringRect(x, y);
        if (mCameraParameters == null){
            return false;
        }
        mCameraParameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
        mCamera.cancelAutoFocus(); // 先要取消掉进程中所有的聚焦功能
        try {
            mCamera.setParameters(mCameraParameters);
            mCamera.autoFocus(autoFocusCallback);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 设置感光区域
     * 需要将屏幕坐标映射到Rect对象对应的单元格矩形
     *
     * @param x
     * @param y
     */
    private void setMeteringRect(int x, int y) {
        if (mCameraParameters == null){
            return;
        }
        if (mCameraParameters.getMaxNumMeteringAreas() > 0) {
            List<Camera.Area> areas = new ArrayList<Camera.Area>();
            Rect rect = new Rect(x - 100, y - 100, x + 100, y + 100);
            int left = rect.left * 2000 / ScreenUtils.getScreenWidth() - 1000;

            int top = rect.top * 2000 / ScreenUtils.getScreenHeight() - 1000;
            int right = rect.right * 2000 / ScreenUtils.getScreenWidth() - 1000;
            int bottom = rect.bottom * 2000 / ScreenUtils.getScreenHeight() - 1000;
            // 如果超出了(-1000,1000)到(1000, 1000)的范围，则会导致相机崩溃
            left = left < -1000 ? -1000 : left;
            top = top < -1000 ? -1000 : top;
            right = right > 1000 ? 1000 : right;
            bottom = bottom > 1000 ? 1000 : bottom;
            Rect area1 = new Rect(left, top, right, bottom);
            //只有一个感光区，直接设置权重为1000了
            areas.add(new Camera.Area(area1, 1000));
            mCameraParameters.setMeteringAreas(areas);
        }
    }

    private Handler mHandler = new Handler();
    private final AutoFocusCallback autoFocusCallback = new AutoFocusCallback() {

        @Override
        public void onAutoFocus(boolean success, Camera camera) {
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    //500ms之后才能再次对焦
                    isFocusing = false;
//                    sensorController.unlockFocus();

                }
            }, 500);
        }
    };

    private void setupCamera(Camera camera) {
        if (camera != null) {
            Camera.Parameters parameters = mCamera.getParameters();
            Camera.Size previewSize = CameraUtil.getInstance().getCloselyPreSize(800, 600, parameters.getSupportedPreviewSizes());
            parameters.setPreviewSize(previewSize.width, previewSize.height);
            //设置pictureSize
            Camera.Size pictureSize = CameraUtil.getInstance().getPictureSize(parameters.getSupportedPictureSizes());
            parameters.setPictureSize(pictureSize.width, pictureSize.height);
            parameters.setPictureFormat(ImageFormat.JPEG);
            //设置对焦模式，低端机型可能不支持快速对焦，当然现在大部分机型支持，但是也得适配啊
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
            parameters.setJpegQuality(90); // 设置照片质量
            mCamera.setDisplayOrientation(90);
            mCamera.setParameters(parameters);
        }
    }


    /**
     * 释放相机资源
     */
    private void releaseCamera() {
        if (mCamera != null) {
            mCamera.stopPreview();
            mCamera.setPreviewCallback(null);
            mCamera.release();
            mCamera = null;
        }
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        safeOpenCamera();
        startPreview(mCamera, holder);
    }

    @Override
    public void surfaceChanged(final SurfaceHolder holder, int format, int width, int height) {
        //实现自动对焦
        mCamera.autoFocus(new AutoFocusCallback() {
            @Override
            public void onAutoFocus(boolean success, Camera camera) {
                if (success) {
                    startPreview(camera, holder);
                }
            }
        });
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        releaseCamera();
    }

    //设置开启闪光灯(重新预览)
    public void setIsOpenFlashMode(String mIsOpenFlashMode) {
        Camera.Parameters mParameters = mCamera.getParameters();
        //设置闪光灯模式
        mParameters.setFlashMode(mIsOpenFlashMode);
        try {
            mCamera.setParameters(mParameters);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @des: 手动对焦
     * @params:
     * @return:
     */

    public void doTouchFocus(final Rect tfocusRect, CardFocusView focusView, Rect touchRect) {
        if (mCameraParameters == null){
            return;
        }
        try {
            isFocusByHand = true;
            final List<Camera.Area> focusList = new ArrayList<>();
            Camera.Area focusArea = new Camera.Area(tfocusRect, 1000);
            focusList.add(focusArea);
            mCameraParameters.setFocusAreas(focusList);
            mCameraParameters.setMeteringAreas(focusList);
            mCamera.setParameters(mCameraParameters);
            this.touchRect = touchRect;
            this.focusView = focusView;
            mCamera.autoFocus(autoFocusCallbackByHand);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Rect touchRect;
    private CardFocusView focusView;
    private AutoFocusCallback autoFocusCallbackByHand = new AutoFocusCallback() {


        @Override
        public void onAutoFocus(boolean success, Camera camera) {
            if (success) {
                focusView.show(touchRect, 0xFF00FF00);
            } else {
                focusView.show(touchRect, 0x7300FF00);
            }
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    //一秒之后才能再次对焦
                    isFocusing = false;
//                    sensorController.unlockFocus();
                    focusView.hide();
                    isFocusByHand = false;
                }
            }, 500);

        }
    };

    public Camera getCamera() {
        return mCamera;
    }


    private OnCameraReadyListener listener;

    public void setOnCameraReadyListener(OnCameraReadyListener listener) {
        this.listener = listener;
    }

    public interface OnCameraReadyListener {
        void cameraIsReady();
    }


}