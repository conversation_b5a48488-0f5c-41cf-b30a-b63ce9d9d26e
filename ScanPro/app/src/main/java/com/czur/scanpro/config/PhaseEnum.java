package com.czur.scanpro.config;

/**
 * Created by <PERSON>z on 2018/3/28.
 * Email：<EMAIL>
 */

public enum PhaseEnum {

    BETA(PhaseEnum.BETA_SERVICE_URL, PhaseEnum.BETA_PASSPORT_SERVICE_URL, PhaseEnum.BETA_FEEDBACK_SERVICE_URL, PhaseEnum.BETA_CERTIFICATES, PhaseEnum.BETA_IS_DEBUG,
            PhaseEnum.BETA_INVITATION_CODE, PhaseEnum.BETA_OCR_COUNT, PhaseEnum.BETA_GENERATE_PDF_COUNT, PhaseEnum.BETA_BUSINESS_LICENSE_COUNT,PhaseEnum.BETA_BOOT_AD_PAGE_URL),

    RELEASE(PhaseEnum.RELEASE_SERVICE_URL, PhaseEnum.RELEASE_PASSPORT_SERVICE_URL, PhaseEnum.RELEASE_FEEDBACK_SERVICE_URL, PhaseEnum.RELEASE_CERTIFICATES, PhaseEnum.RELEASE_IS_DEBUG,
            PhaseEnum.RELEASE_INVITATION_CODE, PhaseEnum.RELEASE_OCR_COUNT, PhaseEnum.RELEASE_GENERATE_PDF_COUNT, PhaseEnum.RELEASE_BUSINESS_LICENSE_COUNT,PhaseEnum.RELEASE_BOOT_AD_PAGE_URL),
    RELEASE_TEST(PhaseEnum.RELEASE_SERVICE_URL, PhaseEnum.RELEASE_PASSPORT_SERVICE_URL, PhaseEnum.RELEASE_FEEDBACK_SERVICE_URL, PhaseEnum.RELEASE_CERTIFICATES, PhaseEnum.RELEASE_IS_DEBUG,
                 PhaseEnum.BETA_INVITATION_CODE, PhaseEnum.BETA_OCR_COUNT, PhaseEnum.BETA_GENERATE_PDF_COUNT, PhaseEnum.RELEASE_BUSINESS_LICENSE_COUNT,PhaseEnum.RELEASE_BOOT_AD_PAGE_URL);

    // 服务器地址
//    public static final String BETA_SERVICE_URL = "https://test.czur.cc/api/";
    public static final String BETA_SERVICE_URL = "https://ws-test.czur.cc/api/";
    public static final String RELEASE_SERVICE_URL = "https://cn.czur.cc/api/";
    // 服务器个人信息地址
    public static final String BETA_PASSPORT_SERVICE_URL = "https://test-passport.czur.cc/api/";
    public static final String RELEASE_PASSPORT_SERVICE_URL = "https://api-passport.czur.cc/api/";
    //服务器 广告信息
    public static final String BETA_BOOT_AD_PAGE_URL = "https://starry-market0927.czur.cc/api/";
    public static final String RELEASE_BOOT_AD_PAGE_URL = "https://starry-market.czur.cc/api/";

    // 服务器feedback地址
    public static final String BETA_FEEDBACK_SERVICE_URL = "https://inner-dubbo.czur.cc/";
    public static final String RELEASE_FEEDBACK_SERVICE_URL = "https://internal.czur.cc/";
    // 证书
    public static final String BETA_CERTIFICATES = "czur_test.cer";
    public static final String RELEASE_CERTIFICATES = "czur.cer";

    // 是否debug
    public static final boolean BETA_IS_DEBUG = true;
    public static final boolean RELEASE_IS_DEBUG = false;
    //友盟事件

    //邀请码
    public static final String BETA_INVITATION_CODE = "TestInvitationCode";
    public static final String RELEASE_INVITATION_CODE = "InvitationCode";

    //笔记本手写体识别次数
    public static final String BETA_OCR_COUNT = "TestBookOCRCount";
    public static final String RELEASE_OCR_COUNT = "BookOCRCount";

    //生成PDF次数
    public static final String BETA_GENERATE_PDF_COUNT = "TestGeneratePDFTimes";
    public static final String RELEASE_GENERATE_PDF_COUNT = "GeneratePDFTimes";

    //营业执照OCR次数
    public static final String BETA_BUSINESS_LICENSE_COUNT = "TestBusinessLicenseOCRTimes";
    public static final String RELEASE_BUSINESS_LICENSE_COUNT = "BusinessLicenseOCRTimes";

    private final String feedBackUrl;
    private final String serviceUrl;
    private final String certificates;
    private final String passportServiceUrl;
    private final boolean isDebug;
    private final String invitationCode;
    private final String ocrCount;
    private final String generatePdfCount;
    private final String businessLicenseCount;
    private final String adPassportServiceUrl;

    PhaseEnum(String serviceUrl, String passportServiceUrl, String feedBackUrl, String certificates, boolean isDebug, String invitationCode
            , String ocrCount, String generatePdfCount,String businessLicenseCount,String adPassportServiceUrl) {
        this.serviceUrl = serviceUrl;
        this.feedBackUrl=feedBackUrl;
        this.passportServiceUrl = passportServiceUrl;
        this.certificates = certificates;
        this.isDebug = isDebug;
        this.invitationCode = invitationCode;
        this.ocrCount = ocrCount;
        this.generatePdfCount = generatePdfCount;
        this.businessLicenseCount = businessLicenseCount;
        this.adPassportServiceUrl = adPassportServiceUrl;
    }
    public String getFeedBackUrl() {
        return feedBackUrl;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public String getOcrCount() {
        return ocrCount;
    }

    public String getGeneratePdfCount() {
        return generatePdfCount;
    }

    public String getServiceUrl() {
        return serviceUrl;
    }

    public String getPassportServiceUrl() {
        return passportServiceUrl;
    }

    public String getAdPassportServiceUrl() {
        return adPassportServiceUrl;
    }

    public String getCertificates() {
        return certificates;
    }

    public boolean isDebug() {
        return isDebug;
    }

}
