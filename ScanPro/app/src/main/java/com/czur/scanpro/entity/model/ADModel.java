package com.czur.scanpro.entity.model;

import java.io.Serializable;
import java.util.List;

public class ADModel implements Serializable {

    /**
     * code : 1000
     * message : success
     * data : [{"name":"测试1","triggerWay":1,"productId":"productId111","platform":"京东平台","beginTime":1669262400000,"endTime":1671854401000,"targetUrl":"https://item.jd.com/100035997639.html","displayType":2,"jumpWay":0,"backupTargetUrl":"taobao://2123","format":1,"countUrl":"https://test.czur.cc","imagesVo":{"horizontalImages":[{"imageUrl":"https://resource.czur.cc/ad/2205200e-c421-49de-b532-2d0dd2c5662b_480x360.png"}],"verticalImages":[{"imageUrl":"https://resource.czur.cc/ad/f724d1d8-758d-4055-8da5-c2c6b2b323f1_480x360.png"}]},"seconds":4,"closeButton":1}]
     */

    private int code;
    private String message;
    private List<DataBean> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean implements Serializable{
        /**
         * name : 测试1
         * triggerWay : 1
         * productId : productId111
         * platform : 京东平台
         * beginTime : 1669262400000
         * endTime : 1671854401000
         * targetUrl : https://item.jd.com/100035997639.html
         * displayType : 2
         * jumpWay : 0
         * backupTargetUrl : taobao://2123
         * format : 1
         * countUrl : https://test.czur.cc
         * imagesVo : {"horizontalImages":[{"imageUrl":"https://resource.czur.cc/ad/2205200e-c421-49de-b532-2d0dd2c5662b_480x360.png"}],"verticalImages":[{"imageUrl":"https://resource.czur.cc/ad/f724d1d8-758d-4055-8da5-c2c6b2b323f1_480x360.png"}]}
         * seconds : 4
         * closeButton : 1
         */

        private String name;
        private int triggerWay;
        private String productId;
        private String platform;
        private long beginTime;
        private long endTime;
        private String targetUrl;
        private int displayType;
        private int jumpWay;
        private String backupTargetUrl;
        private int format;
        private String countUrl;
        private ImagesVoBean imagesVo;
        private int seconds;
        private int closeButton;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getTriggerWay() {
            return triggerWay;
        }

        public void setTriggerWay(int triggerWay) {
            this.triggerWay = triggerWay;
        }

        public String getProductId() {
            return productId;
        }

        public void setProductId(String productId) {
            this.productId = productId;
        }

        public String getPlatform() {
            return platform;
        }

        public void setPlatform(String platform) {
            this.platform = platform;
        }

        public long getBeginTime() {
            return beginTime;
        }

        public void setBeginTime(long beginTime) {
            this.beginTime = beginTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public String getTargetUrl() {
            return targetUrl;
        }

        public void setTargetUrl(String targetUrl) {
            this.targetUrl = targetUrl;
        }

        public int getDisplayType() {
            return displayType;
        }

        public void setDisplayType(int displayType) {
            this.displayType = displayType;
        }

        public int getJumpWay() {
            return jumpWay;
        }

        public void setJumpWay(int jumpWay) {
            this.jumpWay = jumpWay;
        }

        public String getBackupTargetUrl() {
            return backupTargetUrl;
        }

        public void setBackupTargetUrl(String backupTargetUrl) {
            this.backupTargetUrl = backupTargetUrl;
        }

        public int getFormat() {
            return format;
        }

        public void setFormat(int format) {
            this.format = format;
        }

        public String getCountUrl() {
            return countUrl;
        }

        public void setCountUrl(String countUrl) {
            this.countUrl = countUrl;
        }

        public ImagesVoBean getImagesVo() {
            return imagesVo;
        }

        public void setImagesVo(ImagesVoBean imagesVo) {
            this.imagesVo = imagesVo;
        }

        public int getSeconds() {
            return seconds;
        }

        public void setSeconds(int seconds) {
            this.seconds = seconds;
        }

        public int getCloseButton() {
            return closeButton;
        }

        public void setCloseButton(int closeButton) {
            this.closeButton = closeButton;
        }

        public static class ImagesVoBean implements Serializable{
            private List<HorizontalImagesBean> horizontalImages;
            private List<VerticalImagesBean> verticalImages;

            public List<HorizontalImagesBean> getHorizontalImages() {
                return horizontalImages;
            }

            public void setHorizontalImages(List<HorizontalImagesBean> horizontalImages) {
                this.horizontalImages = horizontalImages;
            }

            public List<VerticalImagesBean> getVerticalImages() {
                return verticalImages;
            }

            public void setVerticalImages(List<VerticalImagesBean> verticalImages) {
                this.verticalImages = verticalImages;
            }

            public static class HorizontalImagesBean implements Serializable{
                /**
                 * imageUrl : https://resource.czur.cc/ad/2205200e-c421-49de-b532-2d0dd2c5662b_480x360.png
                 */

                private String imageUrl;

                public String getImageUrl() {
                    return imageUrl;
                }

                public void setImageUrl(String imageUrl) {
                    this.imageUrl = imageUrl;
                }
            }

            public static class VerticalImagesBean implements Serializable{
                /**
                 * imageUrl : https://resource.czur.cc/ad/f724d1d8-758d-4055-8da5-c2c6b2b323f1_480x360.png
                 */

                private String imageUrl;

                public String getImageUrl() {
                    return imageUrl;
                }

                public void setImageUrl(String imageUrl) {
                    this.imageUrl = imageUrl;
                }
            }
        }
    }
}
