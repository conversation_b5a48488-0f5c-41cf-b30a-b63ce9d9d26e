package com.czur.scanpro.ui.file

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.annotation.Dimension.Companion.DP
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.*
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.alg.CZSaoMiao_Alg
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityFileMoveBinding
import com.czur.scanpro.databinding.ActivityFilePreviewBinding
import com.czur.scanpro.entity.model.BusinessLicenseModel
import com.czur.scanpro.entity.model.OssModel
import com.czur.scanpro.entity.model.ym.DriveEntity
import com.czur.scanpro.entity.model.ym.IDCardEntity
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.TagEntity
import com.czur.scanpro.entity.realm.TempDocEntity
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.ConsumptionEvent
import com.czur.scanpro.event.DeleteEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.callback.ProgressHelper
import com.czur.scanpro.network.callback.UIProgressRequestListener
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.account.LoginActivity
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.component.popup.*
import com.czur.scanpro.ui.sync.SyncService
import com.czur.scanpro.utils.AppendYmStringUtils
import com.czur.scanpro.utils.FormatOcrLicenseUtil
import com.czur.scanpro.utils.PermissionUtil
import com.czur.scanpro.utils.PermissionUtil.getStoragePermission
import com.czur.scanpro.utils.SaveUtils
import com.czur.scanpro.utils.SignV3Utils
import com.czur.scanpro.utils.share.ShareContentType
import com.czur.scanpro.utils.share.ShareUtils
import com.czur.scanpro.utils.validator.Validator
import com.davemorrissey.labs.subscaleview.ImageSource
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView
import com.facebook.drawee.backends.pipeline.Fresco
import com.google.gson.Gson
import com.umeng.analytics.MobclickAgent
import com.yunmai.android.engine.OcrEngine
import io.realm.Realm
import io.realm.Sort
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.nio.charset.Charset
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Created by Yz on 2019/4/8.
 * Email：<EMAIL>
 */
class FilePreviewActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityFilePreviewBinding by lazy{
        ActivityFilePreviewBinding.inflate(layoutInflater)
    }


    private val viewMap = HashMap<Int, SubsamplingScaleImageView>()
    private var mDataList: List<DocEntity> = ArrayList<DocEntity>()
    private val showList = ArrayList<TempDocEntity>()
    private lateinit var mAdapter: ImagePagerAdapter
    private var handler: WeakHandler? = null
    private lateinit var userPreferences: UserPreferences
    private var count = 0
    private var currentItem = 0
    private var realm: Realm? = null
    private var categoryID: String? = null
    private var isItemTag: Int = 0
    private var isCamera: Boolean = false
    private var httpManager: HttpManager? = null
    private val localPath: String? = null
    private var sdPath: String? = null
    private var formatter: SimpleDateFormat? = null

    private var fileType: Int = 0
    private var categoryName: String? = null
    private var tagName: String? = null
    private var type: Int = 0
    private var whiteRightPopup: WhiteRightPopup? = null
    private var shareBottomDialog: ShareBottomDialogPopup? = null
    private var moreBottomDialogPopup: MoreBottomDialogPopup? = null

    private lateinit var shareSdPicPath: String
    private lateinit var sharePath: String

    private val viewPageListener = object : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {

        }

        @SuppressLint("StringFormatMatches")
        override fun onPageSelected(position: Int) {
            currentItem = position
            isItemTag = if (Validator.isNotEmpty(showList[position].tagName)) 1 else 0
            showIsTag()
            fileType = showList[position].fileType
            checkFileType()
            binding.previewFileTitle.text = String.format(
                getString(R.string.title_format),
                binding.viewPager.currentItem + 1,
                showList.size
            )
        }

        override fun onPageScrollStateChanged(state: Int) {

        }
    }

    private fun checkFileType() {
        if (fileType == 0 || fileType == 1) showHandwriting() else darkHandwriting()
        when (fileType) {
            0, 1 -> binding.filePreviewOcrTv.text = getString(R.string.ocr)
            else -> binding.filePreviewOcrTv.text = getString(R.string.card_ocr)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(binding.root)
        initComponent()
        registerEvent()
        initEvents()
    }

    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
            getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.Q
        ) {
            sdPath =
                getExternalFilesDir(Environment.DIRECTORY_PICTURES)?.absoluteFile.toString() + "/"

        } else {
            sdPath = Environment.getExternalStorageDirectory().absolutePath + Constants.SD_PATH
        }

        val builder = WhiteRightPopup.Builder(this@FilePreviewActivity)
        builder.setTitle(getString(R.string.saved))
        whiteRightPopup = builder.create()
        Fresco.getImagePipeline().clearCaches()
        handler = WeakHandler()
        realm = Realm.getDefaultInstance()
        httpManager = HttpManager.getInstance()
        isCamera = intent.getBooleanExtra("isCamera", false)
        isItemTag = intent.getIntExtra("isItemTag", 0)
        categoryID = intent.getStringExtra("categoryID")
        fileType = intent.getIntExtra("fileType", 0)
        categoryName = intent.getStringExtra("categoryName")
        tagName = intent.getStringExtra("tagName")
        type = intent.getIntExtra("type", 0)
        currentItem = intent.getIntExtra("index", 0)
        EventBus.getDefault().register(this)
        checkFileType()
    }


    private fun registerEvent() {
        getData()
        initViewPager()
        showIsTag()
    }

    /**
     * @des: 初始化ViewPager
     * @params:
     * @return:
     */

    @SuppressLint("StringFormatMatches")
    private fun initViewPager() {
        if (showList.size == 0) {
            finish()
            return
        }
        count = showList.size
        mAdapter = ImagePagerAdapter()
        binding.viewPager.pageMargin = 10 * DP
        binding.viewPager.adapter = mAdapter
        binding.viewPager.offscreenPageLimit = 3
        binding.viewPager.setCurrentItem(currentItem, false)
        binding.previewFileTitle.text = String.format(
            getString(R.string.title_format),
            binding.viewPager.currentItem + 1,
            showList.size
        )
    }

    /**
     * @des: 根据条件查询数据源
     * @params:
     * @return:
     */

    private fun getData() {
        val realm = Realm.getDefaultInstance()
        when (type) {
            0 -> mDataList = realm.where(DocEntity::class.java)
                .equalTo("isDelete", 0.toInt())
                .equalTo("userID", getUserIdIsLogin())
                .sort("createTime", Sort.DESCENDING)
                .findAll()
            1 -> mDataList = realm.where(DocEntity::class.java)
                .equalTo("categoryName", categoryName)
                .equalTo("isDelete", 0.toInt())
                .equalTo("userID", getUserIdIsLogin())
                .sort("createTime", Sort.ASCENDING)
                .findAll()
            2 -> mDataList = realm.where(DocEntity::class.java)
                .equalTo("tagName", tagName)
                .equalTo("isDelete", 0.toInt())
                .equalTo("userID", getUserIdIsLogin())
                .sort("createTime", Sort.DESCENDING)
                .findAll()
            3 -> mDataList = realm.where(DocEntity::class.java)
                .equalTo("fileType", fileType)
                .equalTo("isDelete", 0.toInt())
                .equalTo("userID", getUserIdIsLogin())
                .sort("createTime", Sort.DESCENDING)
                .findAll()
            else -> mDataList = realm.where(DocEntity::class.java)
                .equalTo("isTemp", 1.toInt())
                .equalTo("isDelete", 0.toInt())
                .equalTo("userID", getUserIdIsLogin())
                .findAll()
                .sort("takePhotoTime", Sort.ASCENDING)
        }

        if (Validator.isNotEmpty(showList)) {
            showList.clear()
        }
        for (docEntity in mDataList) {
            val tempDocEntity = TempDocEntity()
            tempDocEntity.categoryName = docEntity.categoryName
            tempDocEntity.categoryID = docEntity.categoryID
            tempDocEntity.createTime = docEntity.createTime
            tempDocEntity.ocrResult = docEntity.ocrResult
            tempDocEntity.fileID = docEntity.fileID
            tempDocEntity.tagName = docEntity.tagName
            tempDocEntity.fileType = docEntity.fileType
            tempDocEntity.tagId = docEntity.tagId
            tempDocEntity.isNewAdd = docEntity.isNewAdd
            tempDocEntity.processImagePath = docEntity.processImagePath
            tempDocEntity.processSmallImagePath = docEntity.processSmallImagePath
            tempDocEntity.baseImagePath = docEntity.baseImagePath
            tempDocEntity.baseSmallImagePath = docEntity.baseSmallImagePath
            tempDocEntity.originalImagePath = docEntity.originalImagePath
            showList.add(tempDocEntity)
        }
        LogUtils.i(Gson().toJson(showList))
    }

    private fun showIsTag() {
        if (isItemTag == 0) {
            setNormalTag()
        } else {
            binding.previewAddTagTv.text = showList[binding.viewPager!!.currentItem].tagName
            binding.previewAddTagTv.setTextColor(resources.getColor(R.color.white))
            binding.previewAddTagImg.setImageResource(R.mipmap.preview_white_tag)
            binding.addTagRl.background = resources.getDrawable(R.drawable.btn_rec_5_bg_with_red_de4d4d)
        }


    }

    private fun setNormalTag() {
        binding.previewAddTagTv.text = getString(R.string.add_tag)
        binding.previewAddTagTv.setTextColor(resources.getColor(R.color.red_de4d4d))
        binding.previewAddTagImg.setImageResource(R.mipmap.preview_red_tag)
        binding.addTagRl.background = resources.getDrawable(R.drawable.btn_rec_5_bg_with_white)
    }


    private fun initEvents() {
       binding.filePreviewOcrRl.setOnClickListener(this)
       binding.addTagRl.setOnClickListener(this)
       binding.addTagRl.setOnLongClickListener {
            val sdPicPath =
                sdPath + showList[binding.viewPager!!.currentItem].fileID + Constants.ORIGINAL_JPG
            ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                override fun doInBackground(): Void? {
                    FileUtils.copy(
                        showList[binding.viewPager!!.currentItem].originalImagePath,
                        sdPicPath
                    ) { _, _ -> true }
                    return null
                }

                override fun onSuccess(result: Void?) {
                    handler!!.post {
                        noticeAlbumUpdate(sdPicPath)
                    }
                }
            })
            return@setOnLongClickListener true
        }
        binding.filePreviewHandwritingRl.setOnClickListener(this)
        binding.filePreviewMarkRl.setOnClickListener(this)
        binding.previewFileShare.setOnClickListener(this)
        binding.previewFileBackBtn.setOnClickListener(this)
        binding.filePreviewEditRl.setOnClickListener(this)
        binding.filePreviewMoreRl.setOnClickListener(this)
        binding.viewPager!!.addOnPageChangeListener(viewPageListener)

    }

    private fun showHandwriting() {
        binding.filePreviewHandwritingRl.isClickable = true
        binding.filePreviewHandwritingRl.isEnabled = true
        binding.filePreviewHandwritingImg.isSelected = true
        binding.filePreviewHandwritingTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkHandwriting() {
        binding.filePreviewHandwritingRl.isClickable = false
        binding.filePreviewHandwritingRl.isEnabled = false
        binding.filePreviewHandwritingImg.isSelected = false
        binding.filePreviewHandwritingTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        val realm = Realm.getDefaultInstance()
        when (event.eventType) {
            EventType.SYNC_IS_FINISH -> refreshDataAndTitle()
            EventType.CATEGORY_OR_DOCS_CHANGED -> {
                val categoryEntity = realm.where(CategoryEntity::class.java)
                    .equalTo("categoryName", categoryName)
                    .equalTo("isDelete", 1.toInt())
                    .findFirst()
                val docEntity = realm.where(DocEntity::class.java)
                    .equalTo("fileID", showList[binding.viewPager!!.currentItem].fileID)
                    .equalTo("isDelete", 1.toInt())
                    .findFirst()

                val tagEntity = realm.where(TagEntity::class.java)
                    .equalTo("tagName", tagName)
                    .equalTo("isDelete", 1.toInt())
                    .findFirst()


                when {
                    categoryEntity != null -> {
                        ActivityUtils.finishToActivity(IndexActivity::class.java, false)
                        LogUtils.i("need  return to bookshelf")
                        return
                    }
                    docEntity != null -> {
                        ActivityUtils.finishToActivity(IndexActivity::class.java, false)
                        LogUtils.i("need return to book page")
                        return
                    }
                    tagEntity != null -> {
                        ActivityUtils.finishToActivity(IndexActivity::class.java, false)
                        LogUtils.i("need  return to bookshelf")
                    }
                }

                refreshDataAndTitle()
            }


            EventType.ADD_TAG -> {
                val docEntity = realm.where(DocEntity::class.java)
                    .equalTo("fileID", showList[binding.viewPager!!.currentItem].fileID).findFirst()

                showList[binding.viewPager!!.currentItem].tagName = docEntity!!.tagName
                showList[binding.viewPager!!.currentItem].tagId = docEntity.tagId

                binding.previewAddTagTv.text = docEntity.tagName
                binding.previewAddTagTv.setTextColor(resources.getColor(R.color.white))
                binding.previewAddTagImg.setImageResource(R.mipmap.preview_white_tag)
                binding.addTagRl.background =
                    resources.getDrawable(R.drawable.btn_rec_5_bg_with_red_de4d4d)
            }
            EventType.DELETE_TAG -> {
                showList[binding.viewPager!!.currentItem].tagName = ""
                showList[binding.viewPager!!.currentItem].tagId = ""
                setNormalTag()
            }
            EventType.ROTATE, EventType.COLOR, EventType.CUT, EventType.ADJUST -> {
                refreshDataAndTitle()
            }
            EventType.LOG_IN,
            EventType.THIRD_PARTY_LOGIN_IN,
            EventType.REGISTER_SUCCESS,
            EventType.THIRD_PARTY_REGISTER_SUCCESS,
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
            EventType.CHANGE_PASSWORD,
            EventType.BIND_PHONE,
            EventType.CHANGE_PHONE,
            EventType.BIND_EMAIL,
            EventType.CHANGE_EMAIL,
            EventType.EDIT_USER_IMAGE,
            EventType.USER_EDIT_NAME,
            EventType.UPDATE_CACHE,
            EventType.INPUT_INVITE_CODE,
            EventType.BECAME_VIP, EventType.PAY_SUCCESS ->
                refreshDataAndTitle()
            else -> {
            }
        }
    }

    /**
     * @des: 刷新数据和标题
     * @params:
     * @return:
     */

    @SuppressLint("StringFormatMatches")
    private fun refreshDataAndTitle() {
        getData()
        count = showList.size
        if (count <= 0) {
            ActivityUtils.finishActivity(this@FilePreviewActivity)
            binding.previewFileTitle.text = ""
        } else {
            binding.previewFileTitle.text = String.format(
                getString(R.string.title_format),
                binding.viewPager!!.currentItem + 1,
                showList.size
            )
        }
        mAdapter.notifyDataSetChanged()

    }

    @SuppressLint("StringFormatMatches")
    override fun onClick(v: View) {
        when (v.id) {
            R.id.previewFileBackBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.filePreviewEditRl -> {
                if (showList[binding.viewPager.currentItem].fileType == 0 || showList[binding.viewPager.currentItem].fileType == 1) {
                    val intent = Intent(this@FilePreviewActivity, FileEditActivity::class.java)
                    intent.putExtra("isSingle", showList[binding.viewPager.currentItem].fileType == 0)
                    intent.putExtra("fileId", showList[binding.viewPager.currentItem].fileID)
                    ActivityUtils.startActivity(intent)
                } else {
                    val intent = Intent(this@FilePreviewActivity, FileCardEditActivity::class.java)
                    intent.putExtra("isCard", showList[binding.viewPager.currentItem].fileType == 2)
                    intent.putExtra("cardType", showList[binding.viewPager.currentItem].fileType)
                    intent.putExtra("fileId", showList[binding.viewPager.currentItem].fileID)
                    ActivityUtils.startActivity(intent)
                }
            }
            R.id.filePreviewMarkRl -> {
                val intent = Intent(this@FilePreviewActivity, FileMarkActivity::class.java)
                intent.putExtra("fileId", showList[binding.viewPager.currentItem].fileID)
                intent.putExtra(
                    "title",
                    String.format(
                        getString(R.string.title_format),
                        binding.viewPager.currentItem + 1,
                        showList.size
                    )
                )
                ActivityUtils.startActivity(intent)
            }
            R.id.previewFileShare -> {
                requestCopyToSdPermission(true)
            }

            R.id.filePreviewOcrRl -> {
                if (checkLogin()) {
                    goOcrOrHandwriting(true)
                }
            }
            R.id.filePreviewHandwritingRl -> {
                if (checkLogin()) {
                    goOcrOrHandwriting(false)
                }
            }

            R.id.addTagRl -> {
                val intent = Intent(this@FilePreviewActivity, EditTagActivity::class.java)
                intent.putExtra("isPreview", true)
                intent.putExtra("fileId", showList[binding.viewPager!!.currentItem].fileID)
                ActivityUtils.startActivity(intent)
            }

            R.id.filePreviewMoreRl -> {
                showMoreDialog()
            }

        }
    }

    private fun goOcrOrHandwriting(isOCR: Boolean) {
        showProgressDialog(true)
        if (showList.size > 0) {
            val realm = Realm.getDefaultInstance()
            val docEntity = realm.where(DocEntity::class.java)
                .equalTo("fileID", showList[binding.viewPager.currentItem].fileID)
                .equalTo("isDelete", 0.toInt())
                .findFirst()
            var isResultEmpty: Boolean? = null
            var result: String? = null
            if (isOCR) {
                result = docEntity!!.ocrResult
                isResultEmpty = !docEntity.ocrResult.isNullOrEmpty()
            } else {
                result = docEntity!!.ocrHandResult
                isResultEmpty = !docEntity.ocrHandResult.isNullOrEmpty()
            }

            if (isResultEmpty) {
                if (showList[binding.viewPager.currentItem].fileType == 2 || showList[binding.viewPager.currentItem].fileType == 3 || showList[binding.viewPager.currentItem].fileType == 4) {
                    val intent = Intent(this@FilePreviewActivity, OtherResultActivity::class.java)
                    intent.putExtra("fileId", showList[binding.viewPager.currentItem].fileID)
                    intent.putExtra("position", binding.viewPager.currentItem)
                    intent.putExtra("url", showList[binding.viewPager.currentItem].processImagePath)
                    intent.putExtra("ocrType", getCardType())
                    intent.putExtra("resultText", result)
                    ActivityUtils.startActivity(intent)
                } else {
                    val intent2 = Intent(this@FilePreviewActivity, OcrResultActivity::class.java)
                    intent2.putExtra("fileId", showList[binding.viewPager.currentItem].fileID)
                    intent2.putExtra("position", binding.viewPager.currentItem)
                    intent2.putExtra("ocrType", if (isOCR) 1 else 2)
                    intent2.putExtra("isOCR", isOCR)
                    intent2.putExtra("url", showList[binding.viewPager.currentItem].processImagePath)
                    intent2.putExtra("resultText", result)
                    intent2.putExtra("hasContent", true)
                    ActivityUtils.startActivity(intent2)
                }
                hideProgressDialog()
            } else {
                Thread(Runnable {
                    requestUserInfoSync()
                    runOnUiThread {
                        when (showList[binding.viewPager.currentItem].fileType) {
                            2 -> {
                                if (getSp().isVip || userPreferences.remainCard > 0) {
                                    val idCardBitmaps = CZSaoMiao_Alg.getInstance(this)
                                        .cut_idcard_from_merged_img(showList[binding.viewPager.currentItem].processImagePath);
                                    val frontBitmaps = ImageUtils.bitmap2Bytes(
                                        idCardBitmaps[0],
                                        Bitmap.CompressFormat.JPEG,
                                        90
                                    )
                                    val backBitmaps =
                                        if (idCardBitmaps.size > 1) ImageUtils.bitmap2Bytes(
                                            idCardBitmaps[1],
                                            Bitmap.CompressFormat.JPEG,
                                            90
                                        ) else null
                                    executeIdCardBitmap(frontBitmaps, backBitmaps)
                                } else {
                                    hideProgressDialog()
                                    showEmptyCountDialog(
                                        this@FilePreviewActivity,
                                        EmptyCountPopup.EmptyType.CARD
                                    )
                                }

                            }
                            3 -> {
                                executeBusinessLicenseBitmap()
                            }
                            4 -> {
                                val driveBitmaps = CZSaoMiao_Alg.getInstance(this)
                                    .cut_idcard_from_merged_img(showList[binding.viewPager.currentItem].processImagePath);
                                val frontBitmaps = ImageUtils.bitmap2Bytes(
                                    driveBitmaps[0],
                                    Bitmap.CompressFormat.JPEG,
                                    90
                                )
                                val backBitmaps =
                                    if (driveBitmaps.size > 1) ImageUtils.bitmap2Bytes(
                                        driveBitmaps[1],
                                        Bitmap.CompressFormat.JPEG,
                                        90
                                    ) else null
                                executeDriveBitmap(frontBitmaps, backBitmaps)

                            }
                            else -> {
                                hideProgressDialog()
                                val intent1 =
                                    Intent(this@FilePreviewActivity, OcrActivity::class.java)
                                intent1.putExtra("fileId", showList[binding.viewPager.currentItem].fileID)
                                intent1.putExtra("isOCR", isOCR)
                                intent1.putExtra("ocrType", getOcrType(isOCR))
                                LogUtils.e(getOcrType(isOCR))
                                intent1.putExtra("position", binding.viewPager.currentItem)
                                intent1.putExtra(
                                    "url",
                                    showList[binding.viewPager.currentItem].processImagePath
                                )
                                startActivity(intent1)
                            }
                        }
                    }
                }).start()
            }
        }

    }

    private fun getCardType(): Int {
        return when (showList[binding.viewPager.currentItem].fileType) {
            2 -> 4
            4 -> 6
            else -> 5
        }
    }

    private fun getOcrType(isOCR: Boolean): Int {
        return when (showList[binding.viewPager.currentItem].fileType) {
            2 -> 4
            3, 4 -> 5
            else -> if (isOCR) 1 else 2
        }
    }


    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private fun confirmDeleteDialog() {
        val builder = ScanProCommonPopup.Builder(
            this@FilePreviewActivity,
            CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.confirm_delete))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            dialog.dismiss()
            deletePage()
        })
        val commonPopup = builder.create()
        if (isActive) {
            commonPopup.show()
        }
    }

    /**
     * @des: 申请权限
     * @params:
     * @return:
     */

    private fun requestCopyToSdPermission(isPrepared: Boolean) {
        PermissionUtils.permission(PermissionUtil.getStoragePermission()[0],PermissionUtil.getStoragePermission()[1])
            .rationale { _, shouldRequest ->
                showMessage(R.string.denied_sdcard)
                shouldRequest.again(true)
            }
            .callback(object : PermissionUtils.FullCallback {
                override fun onGranted(permissionsGranted: List<String>) {
                    LogUtils.i(permissionsGranted)
                    if (isPrepared) {
                        copyToSdPrePare()
                    } else {
                        copyToSd()
                    }
                }

                override fun onDenied(
                    permissionsDeniedForever: List<String>,
                    permissionsDenied: List<String>
                ) {
                    showMessage(R.string.denied_sdcard)
                    LogUtils.i(permissionsDeniedForever, permissionsDenied)
                }
            })
            .theme { activity -> ScreenUtils.setFullScreen(activity) }
            .request()
    }


    /**
     * @des: 保存至相册
     * @params:
     * @return:
     */

    private fun copyToSd() {
        val sdPicPath = sdPath + UUID.randomUUID() + Constants.JPG
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
                    getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.Q
                ) {
                    SaveUtils.saveImgToAlbum(
                        this@FilePreviewActivity,
                        showList[binding.viewPager!!.currentItem].processImagePath
                    )
                } else {
                    FileUtils.copy(
                        showList[binding.viewPager!!.currentItem].processImagePath,
                        sdPicPath
                    ) { _, _ -> true }
                }

                runOnUiThread(Runnable {
                    whiteRightPopup!!.show()
                    noticeAlbumUpdate(sdPicPath)
                })
                return null
            }

            override fun onSuccess(result: Void?) {
                handler!!.postDelayed({ whiteRightPopup!!.dismiss() }, 900)
            }
        })


    }

    private fun noticeAlbumUpdate(sdPicPath: String) {
        //通知系统相册更新
        val sdPicFile = FileUtils.getFileByPath(sdPicPath)
        val contentUri = Uri.fromFile(sdPicFile)
        MediaScannerConnection.scanFile(this@FilePreviewActivity,
            arrayOf(sdPicFile.absolutePath), arrayOf("image/jpeg"),
            MediaScannerConnection.OnScanCompletedListener { path, uri ->
                LogUtils.i(
                    "file $path",
                    " scanned seccessfully: $uri"
                )
            })
    }


    /**
     * @des: 保存至相册
     * @params:
     * @return:
     */

    private fun copyToSdPrePare() {
        shareSdPicPath = sdPath + showList[binding.viewPager!!.currentItem].fileID + Constants.JPG

        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                FileUtils.copy(
                    showList[binding.viewPager!!.currentItem].processImagePath,
                    shareSdPicPath
                ) { _, _ -> true }
                return null
            }

            override fun onSuccess(result: Void?) {
                handler!!.post {
                    noticeAlbumUpdate(shareSdPicPath)
                    showShareDialog()
                }
            }
        })


    }


    /**
     * @des: 分享
     * @params:
     * @return:
     */
    private fun share() {
        var fileUri = UriUtils.file2Uri(File(shareSdPicPath))

        ShareUtils.Builder(this@FilePreviewActivity)
            .setOnActivityResult(SHARE_SUCCESS_CODE)
            // 指定分享的文件类型
            .setContentType(ShareContentType.IMAGE)
            // 设置要分享的文件 Uri
            .setShareFileUri(fileUri)
            // 设置分享选择器的标题
            .setTitle(getString(R.string.share_to))
//                .setMulti(true)
            .build()
            // 发起分享
            .shareBySystem()
    }


    /**
     * @des: 删除页
     * @params:
     * @return:
     */
    private fun deletePage() {
        if (count == 1) {
            ActivityUtils.finishActivity(this@FilePreviewActivity)
        }

        val deleteDocEntity = realm!!.where(DocEntity::class.java)
            .equalTo("fileID", showList[binding.viewPager!!.currentItem].fileID)
            .equalTo("isDelete", 0.toInt())
            .findFirst()
        //删除本地文件
        val needDeletePaths = ArrayList<String>()
        needDeletePaths.add(deleteDocEntity!!.processImagePath!!)
        needDeletePaths.add(deleteDocEntity.processSmallImagePath!!)
        needDeletePaths.add(deleteDocEntity.baseImagePath!!)
        needDeletePaths.add(deleteDocEntity.baseSmallImagePath!!)
        if (deleteDocEntity.originalImagePath != null) {
            needDeletePaths.add(deleteDocEntity.originalImagePath!!)
        }
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                for (needDeletePath in needDeletePaths) {
                    FileUtils.delete(needDeletePath)
                }
                return null
            }

            @SuppressLint("StringFormatMatches")
            override fun onSuccess(result: Void?) {
                EventBus.getDefault().post(
                    DeleteEvent(
                        EventType.DELETE_FILE,
                        showList[binding.viewPager!!.currentItem].fileID
                    )
                )
                realm!!.executeTransaction {
                    val deleteDocEntity = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", showList[binding.viewPager!!.currentItem].fileID)
                        .equalTo("isDelete", 0.toInt()).findFirst()

                    deleteDocEntity!!.isDelete = 1
                    deleteDocEntity.isDirty = 1
                    val curDate = formatter!!.format(Date(System.currentTimeMillis()))
                    deleteDocEntity.updateTime = curDate
                    startAutoSync()
                }
                showList.removeAt(currentItem)
                count = showList.size
                if (count <= 0) {
                    ActivityUtils.finishActivity(this@FilePreviewActivity)
                    binding.previewFileTitle.text = ""
                } else {
                    binding.previewFileTitle.text = String.format(
                        getString(R.string.title_format),
                        binding.viewPager.currentItem + 1,
                        showList.size
                    )

                }
                if (count >= 1) {
                    binding.viewPager.setCurrentItem(currentItem, false)
                    var checkCurrentItem = 0
                    if (currentItem == 0) {
                        checkCurrentItem = 0
                    } else if (currentItem == count) {
                        checkCurrentItem = count - 1
                    } else {
                        checkCurrentItem = currentItem
                    }
                    isItemTag =
                        if (Validator.isNotEmpty(mDataList[checkCurrentItem].tagName)) 1 else 0
                    fileType = mDataList[checkCurrentItem].fileType
                    checkFileType()
                    showIsDeleteTag(checkCurrentItem)
                }
                viewMap.remove(currentItem)
                mAdapter.notifyDataSetChanged()
            }
        })

    }

    private fun showIsDeleteTag(checkCurrentItem: Int) {
        if (isItemTag == 0) {
            setNormalTag()
        } else {
            binding.previewAddTagTv.text = showList[checkCurrentItem].tagName
            binding.previewAddTagTv.setTextColor(resources.getColor(R.color.white))
            binding.previewAddTagImg.setImageResource(R.mipmap.preview_white_tag)
            binding.addTagRl.background = resources.getDrawable(R.drawable.btn_rec_5_bg_with_red_de4d4d)
        }

    }

    private fun showLoginDialog() {
        val builder = ScanProCommonPopup.Builder(
            this@FilePreviewActivity,
            CloudCommonPopupConstants.COMMON_ONE_BUTTON
        )
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.please_login))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            val intent = Intent(this@FilePreviewActivity, LoginActivity::class.java)
            intent.putExtra("type", 1)
            ActivityUtils.startActivity(intent)
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    private fun checkLogin(): Boolean {
        if (!userPreferences.isUserLogin) {
            showLoginDialog()
            return false
        }
        return true
    }

    private fun showShareDialog() {
        if (userPreferences.isVip) {
            if (isActive) {
                if (shareBottomDialog == null) {
                    shareBottomDialog =
                        ShareBottomDialogPopup.Builder(this, onBottomSheetClickListener1).create()
                }
                shareBottomDialog?.show()
            }
        } else {
            share()
        }
    }


    private fun showMoreDialog() {
        if (isActive) {
            if (moreBottomDialogPopup == null) {
                moreBottomDialogPopup =
                    MoreBottomDialogPopup.Builder(this, onBottomSheetClickListener).create()
            }
            moreBottomDialogPopup?.show()
        }

    }


    private val onBottomSheetClickListener =
        MoreBottomDialogPopup.Builder.OnBottomSheetClickListener { viewId ->
            when (viewId) {
                R.id.save_btn -> {
                    requestCopyToSdPermission(false)
                    moreBottomDialogPopup!!.dismiss()
                }
                R.id.deleteBtn -> {
                    confirmDeleteDialog()
                    moreBottomDialogPopup!!.dismiss()
                }
                R.id.cancelBtn -> moreBottomDialogPopup?.dismiss()
            }
        }

    private val onBottomSheetClickListener1 =
        ShareBottomDialogPopup.Builder.OnBottomSheetClickListener { viewId ->
            when (viewId) {
                R.id.share_system_btn -> {
                    share()
                    shareBottomDialog!!.dismiss()
                }
                R.id.share_vip_btn -> {
                    getOssClient()
                    shareBottomDialog!!.dismiss()
                }

                R.id.share_cancel_btn -> shareBottomDialog!!.dismiss()
                else -> {
                }
            }
        }

    private fun getOssClient() {
        HttpManager.getInstance().request().getOssInfo(
            userPreferences.imei.substring(0, 18),
            OssModel::class.java,
            object : MiaoHttpManager.CallbackNetwork<OssModel> {
                override fun onNoNetwork() {
                    hideProgressDialog()
                }

                override fun onStart() {
                    showProgressDialog(
                        cancelable = false,
                        touchable = false,
                        tag = "",
                        isDark = false
                    )
                }

                override fun onResponse(entity: MiaoHttpEntity<OssModel>) {
                    Thread(Runnable {
                        val credentialProvider = OSSStsTokenCredentialProvider(
                            entity.body.accessKeyId,
                            entity.body.accessKeySecret,
                            entity.body.securityToken
                        )
                        val conf = ClientConfiguration()
                        // 连接超时，默认15秒
                        conf.connectionTimeout = 15 * 1000
                        // socket超时，默认15秒
                        conf.socketTimeout = 15 * 1000
                        // 最大并发请求数，默认5个
                        conf.maxConcurrentRequest = 10
                        // 失败后最大重试次数，默认2次
                        conf.maxErrorRetry = 2
                        val ossClient = OSSClient(
                            this@FilePreviewActivity,
                            userPreferences.endpoint,
                            credentialProvider,
                            conf
                        )
                        if (doesOssHasFile(ossClient)) {
                            runOnUiThread {
                                hideProgressDialog()
                                val intent =
                                    Intent(this@FilePreviewActivity, ShareActivity::class.java)
                                intent.putExtra("sharePaths", arrayListOf(sharePath))
                                ActivityUtils.startActivity(intent)
                            }
                        } else {
                            runOnUiThread {
                                hideProgressDialog()
                                showMessage(R.string.share_defeat)
                            }
                        }
                    }).start()

                }

                override fun onFailure(entity: MiaoHttpEntity<OssModel>) {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)

                }

                override fun onError(e: Exception) {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)

                }
            });

    }

    private fun doesOssHasFile(ossClient: OSSClient): Boolean {
        val realm = Realm.getDefaultInstance()
        var isSuccess = true
        //先查找后得到PageEntity对象
        val page = realm.where(DocEntity::class.java)
            .equalTo("fileID", showList[binding.viewPager.currentItem].fileID)
            .equalTo("isDelete", 0.toInt())
            .findFirst()
        val path = page!!.fileID + "_" + page.uuid + Constants.BASE_JPG
        val ossPath = userPreferences.userId + File.separator + path
        sharePath = "share/$ossPath"
        try {
            if (ossClient.doesObjectExist(Constants.BUCKET, sharePath)) {
                LogUtils.d("doesObjectExist", "object exist.", sharePath);
            } else {
                LogUtils.d("doesObjectExist", "object does not exist.", sharePath);
                val uploadFiles = uploadFiles(page.baseImagePath!!, sharePath, ossClient)
                if (!uploadFiles) isSuccess = false
            }
        } catch (e: ClientException) {
            isSuccess = false
            // 本地异常如网络异常等
            LogUtils.e(e)
        } catch (e: ServiceException) {
            isSuccess = false
            // 服务异常
            LogUtils.e("ErrorCode", e.errorCode);
            LogUtils.e("RequestId", e.requestId);
            LogUtils.e("HostId", e.hostId);
            LogUtils.e("RawMessage", e.rawMessage);
        }
        return isSuccess
    }

    private fun uploadFiles(sourcePath: String, sharePath: String, ossClient: OSSClient): Boolean {
        var isUploadSuccess = false
        LogUtils.i("upload", sourcePath, sharePath)
        // 构造上传请求
        val put = PutObjectRequest(Constants.BUCKET, sharePath, sourcePath)

        try {
            val putResult = ossClient.putObject(put)
            isUploadSuccess = true
            LogUtils.i(
                "PutObject: UploadSuccess",
                "ETag :" + putResult.eTag,
                "RequestId: " + putResult.getRequestId()
            )
        } catch (e: ClientException) {
            isUploadSuccess = false
            // 本地异常如网络异常等
            e.printStackTrace()
        } catch (e: ServiceException) {
            isUploadSuccess = false
            // 服务异常
            LogUtils.e("RequestId", e.requestId)
            LogUtils.e("ErrorCode", e.errorCode)
            LogUtils.e("HostId", e.hostId)
            LogUtils.e("RawMessage", e.rawMessage)
        }

        return isUploadSuccess
    }

    inner class ImagePagerAdapter : PagerAdapter() {
        override fun getCount(): Int {
            return showList.size
        }


        override fun setPrimaryItem(container: ViewGroup, position: Int, `object`: Any) {
            super.setPrimaryItem(container, position, `object`)
            val zoomImage = viewMap[position]
        }

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            var zoomImage: SubsamplingScaleImageView? = viewMap[position]
            if (zoomImage == null) {
                zoomImage = setImageToIndex(position)
            }
            val path = showList[position].processImagePath
            if (path != null) {
                loadBigImage(zoomImage, path)
            }

            container.addView(zoomImage)
            return zoomImage
        }

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            Log.i("czur", "remove: $position", null)
            container.removeView(`object` as View)
            /* val zoomImage = viewMap[position]
             if (zoomImage != null) {
                 viewMap.remove(position)
             }*/
        }


        override fun isViewFromObject(view: View, `object`: Any): Boolean {
            return view === `object`
        }

        override fun getItemPosition(`object`: Any): Int {
            return PagerAdapter.POSITION_NONE
        }

    }

    private fun setImageToIndex(index: Int): SubsamplingScaleImageView {
        val zoomImage = SubsamplingScaleImageView(this)
        val path = showList[index].processImagePath
        if (path != null) {
            loadBigImage(zoomImage, path)
        }
        viewMap[index] = zoomImage
        return zoomImage
    }

    private fun loadBigImage(imageView: SubsamplingScaleImageView, path: String) {
        imageView.setImage(ImageSource.uri(path))
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
        realm!!.close()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        mAdapter.notifyDataSetChanged()
        super.onActivityResult(requestCode, resultCode, data)
    }

    companion object {
        private val SHARE_SUCCESS_CODE = 668
    }

    private fun executeIdCardBitmap(frontBitmap: ByteArray?, backBitmap: ByteArray?) {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<String>() {
            override fun doInBackground(): String? {
                val ocrEngine = OcrEngine()
                try {
                    val idCardFront = ocrEngine.recognize(this@FilePreviewActivity, frontBitmap, "")
                    if (backBitmap != null) {
                        val idCardBack =
                            ocrEngine.recognize(this@FilePreviewActivity, backBitmap, "")
                        if (idCardFront.recogStatus == OcrEngine.RECOG_OK && idCardBack.recogStatus == OcrEngine.RECOG_OK) {
                            val res = String(idCardFront.charInfo, Charset.forName("gbk"))
                            val res1 = String(idCardBack.charInfo, Charset.forName("gbk"))
                            val idCardModel = Gson().fromJson(
                                res.trim { it <= ' ' }.toString(),
                                IDCardEntity::class.java
                            )
                            val idCardModel1 = Gson().fromJson(
                                res1.trim { it <= ' ' }.toString(),
                                IDCardEntity::class.java
                            )

                            val result = AppendYmStringUtils.idCardToString(idCardModel).append()
                                .append(AppendYmStringUtils.idCardToString(idCardModel1)).toString()
                            consumption(result, "highCertificate", 4);
                            return result

                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    } else {
                        if (idCardFront.recogStatus == OcrEngine.RECOG_OK) {
                            val res = String(idCardFront.charInfo, Charset.forName("gbk"))
                            val idCardModel = Gson().fromJson(
                                res.trim { it <= ' ' }.toString(),
                                IDCardEntity::class.java
                            )
                            val result =
                                AppendYmStringUtils.idCardToString(idCardModel).append().toString()
                            consumption(result, "highCertificate", 4);
                            return result
                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    }
                } catch (e: Exception) {
                    LogUtils.i(e)
                    ocrFailed(-2)
                    return null
                } finally {
                    LogUtils.i("finally")
                    ocrEngine.finalize()
                }
                return null

            }

            override fun onSuccess(result: String?) {
                LogUtils.i(result)
                hideProgressDialog()
            }

            override fun onFail(t: Throwable) {
                super.onFail(t)
                hideProgressDialog()
                ocrFailed(-2)
            }
        })
    }

    private fun executeDriveBitmap(frontBitmap: ByteArray?, backBitmap: ByteArray?) {
        showProgressDialog(true)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<String>() {
            override fun doInBackground(): String? {
                val ocrEngine = com.ymjz.ocr.OcrEngine()
                try {
                    val driveFront =
                        ocrEngine.getJZInfo(this@FilePreviewActivity, frontBitmap, "", "")
                    if (backBitmap != null) {
                        val driveBack =
                            ocrEngine.getJZInfo(this@FilePreviewActivity, backBitmap, "", "")
                        if (driveFront.yMrecognState == OcrEngine.RECOG_OK && driveBack.yMrecognState == OcrEngine.RECOG_OK) {
                            val res = String(driveFront.charInfo, Charset.forName("gbk"))
                            val res1 = String(driveBack.charInfo, Charset.forName("gbk"))
                            val driveModel = Gson().fromJson(
                                res.trim { it <= ' ' }.toString(),
                                DriveEntity::class.java
                            )
                            val driveModel1 = Gson().fromJson(
                                res1.trim { it <= ' ' }.toString(),
                                DriveEntity::class.java
                            )
                            LogUtils.e(res)
                            LogUtils.e(res1)
                            val result = AppendYmStringUtils.driveToString(driveModel).append()
                                .append(AppendYmStringUtils.driveToString(driveModel1)).toString()
                            consumption(result, "highCertificate", 6);
                            return result

                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    } else {
                        if (driveFront.yMrecognState == OcrEngine.RECOG_OK) {
                            val res = String(driveFront.charInfo, Charset.forName("gbk"))
                            val driveModel = Gson().fromJson(
                                res.trim { it <= ' ' }.toString(),
                                DriveEntity::class.java
                            )
                            val result =
                                AppendYmStringUtils.driveToString(driveModel).append().toString()
                            consumption(result, "highCertificate", 6);
                            return result
                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    }
                } catch (e: Exception) {
                    LogUtils.i(e)
                    ocrFailed(-2)
                    return null
                } finally {
                    LogUtils.i("finally")
                    ocrEngine.finalize()
                }
                return null

            }

            override fun onSuccess(result: String?) {
                LogUtils.i(result)
                hideProgressDialog()
            }

            override fun onFail(t: Throwable) {
                super.onFail(t)
                hideProgressDialog()
                ocrFailed(-2)
            }
        })
    }

    private fun mobClickBusinessLicenseEvent(activity: Context?, eventId: String) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[Constants.BUSINESS_LICENSE_EVENT] = Constants.BUSINESS_LICENSE_EVENT
        MobclickAgent.onEvent(activity, eventId, map)
    }

    private fun executeBusinessLicenseBitmap() {
        showProgressDialog(true)
        Thread(Runnable {
            val path = userPreferences.sdPath + Constants.HANDWRITING_PATH + UUID.randomUUID()
                .toString() + Constants.JPG
            FileUtils.copy(showList[binding.viewPager.currentItem].processImagePath, path) { _, _ -> true }
            val file = FileUtils.getFileByPath(path)
            LogUtils.i("path:" + file.absolutePath)
            try {
                //这个是ui线程回调，可直接操作UI
                val uiProgressRequestListener = object : UIProgressRequestListener() {
                    override fun onUIRequestProgress(
                        bytesWrite: Long,
                        contentLength: Long,
                        done: Boolean
                    ) {
                        Log.i("czurxx", "bytesWrite:$bytesWrite")
                        Log.i("czurxx", "contentLength$contentLength")
                        Log.i("czurxx", (100 * bytesWrite / contentLength).toString() + " % done ")
                        Log.i("czurxx", "done:$done")
                        Log.i("czurxx", "================================")

                    }
                }

                val jsonObject = JSONObject()
                jsonObject.put("base64", SignV3Utils.imageToBase64(file.path))
                val syncJson = jsonObject.toString()
                val requestBody: RequestBody = RequestBody.create(SyncService.JSON, syncJson)

                val request = Request.Builder()
                    .header("Content-Type", "application/json")
                    .header("T-ID", userPreferences!!.token)
                    .header("U-ID", userPreferences!!.userId)
                    .url(Constants.BUSINESS_LICENSE_URL)
                    .post(
                        ProgressHelper.addProgressRequestListener(
                            requestBody,
                            uiProgressRequestListener
                        )
                    )
                    .build()
                val okHttpClient = OkHttpClient.Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .writeTimeout(15, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build()

                val response = okHttpClient.newCall(request).execute()
                LogUtils.i("-- 上传营业执照 start --")
                val jsonString = response.body!!.string()
                LogUtils.i(jsonString)
                val businessLicenceEntity = Gson().fromJson<BusinessLicenseModel>(
                    jsonString,
                    BusinessLicenseModel::class.java
                )
                LogUtils.i("-- 上传营业执照 end --")
                // 请求成功
                if (response.isSuccessful) {
                    val code = businessLicenceEntity.code
                    LogUtils.e(code)
                    // 上传成功
                    if (code == 1000) {
                        mobClickBusinessLicenseEvent(
                            this@FilePreviewActivity,
                            BuildConfig.PHASE.generatePdfCount
                        )
                        val stringSpan = SpanUtils()
                        val textDetectionsBean = Gson().fromJson<BusinessLicenseModel.DataBean>(businessLicenceEntity.data, BusinessLicenseModel.DataBean::class.java)
                        businessLicenceEntity.dataBean = textDetectionsBean
                        val resultString = FormatOcrLicenseUtil.formatLicenseStr(businessLicenceEntity.dataBean,businessLicenceEntity.data)
//                        val resultItems = businessLicenceEntity!!.dataBean.items
//                        for (i in resultItems.indices) {
//                            stringSpan.appendLine(resultItems[i].item + ":" + resultItems[i].itemstring)
//                        }
//                        val resultString = stringSpan.create()
                        consumption(resultString.toString(), "highCertificate", 5)
                    } else if (code == -9011) {
                        ocrFailed(-2)
                    } else {
                        ocrFailed(-2)
                    }
                } else {
                    ocrFailed(-2)
                }// 请求失败
            } catch (e: IOException) {
                LogUtils.e(e)
                ocrFailed(-2)
            } catch (e: JSONException) {
                LogUtils.e(e)
                ocrFailed(-2)
            } catch (e: Exception) {
                LogUtils.e(e)
                ocrFailed(-2)
            }
        }).start()
    }

    private fun ocrFailed(code: Int) {
        runOnUiThread(Runnable {
            if (code == 2) {
                failedDelay(R.string.ocr_language_failed)
            } else if (code == 3) {
                failedDelay(R.string.ocr_blur_failed)
            } else {
                failedDelay(R.string.ocr_failed)
            }
        })
    }

    /**
     * @des: 请求用户信息certificate
     * @params:
     * @return:
     */

    private fun consumption(result: String?, func: String, type: Int) {
        HttpManager.getInstance().request().consumption(
            userPreferences.userId,
            func,
            String::class.java,
            object : MiaoHttpManager.CallbackNetwork<String> {
                override fun onNoNetwork() {
                    hideProgressDialog(true)
                }

                override fun onStart() {
                }

                override fun onResponse(entity: MiaoHttpEntity<String>) {
                    hideProgressDialog(true)
                    finishOcr(result, type)
                    commonRequestUserInfoNow()
                    EventBus.getDefault().post(ConsumptionEvent(EventType.CARD_COUNT_REDUCE))
                }

                override fun onFailure(entity: MiaoHttpEntity<String>) {
                    showEmptyCountDialog(this@FilePreviewActivity, EmptyCountPopup.EmptyType.CARD)
                    hideProgressDialog(true)
                }

                override fun onError(e: Exception) {
                    hideProgressDialog(true)
                }
            })
    }

    private fun failedDelay(failedText: Int) {
        hideProgressDialog(true)
        ToastUtils.showShort(getString(failedText))

    }

    private fun finishOcr(resultText: String?, ocrType: Int) {
        val realm = Realm.getDefaultInstance()
        realm.beginTransaction()
        val docEntity = realm.where(DocEntity::class.java)
            .equalTo("fileID", showList[binding.viewPager.currentItem].fileID)
            .equalTo("isDelete", 0.toInt()).findFirst()
        docEntity!!.ocrResult = resultText
        realm.commitTransaction()

        val intent = Intent(this@FilePreviewActivity, OtherResultActivity::class.java)
        intent.putExtra("fileId", showList[binding.viewPager.currentItem].fileID)
        intent.putExtra("position", binding.viewPager.currentItem)
        intent.putExtra("url", showList[binding.viewPager.currentItem].processImagePath)
        intent.putExtra("ocrType", ocrType)
        intent.putExtra("resultText", resultText)
        ActivityUtils.startActivity(intent)

    }




}
