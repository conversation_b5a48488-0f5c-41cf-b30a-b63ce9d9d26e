package com.czur.scanpro.ui.account

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityResetPasswordBinding
import com.czur.scanpro.databinding.ActivityThirdBindBinding
import com.czur.scanpro.entity.model.ChannelModel
import com.czur.scanpro.entity.model.RegisterModel
import com.czur.scanpro.entity.realm.*
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.LoginEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.file.FilePreviewActivity
import com.czur.scanpro.ui.home.FileActivity
import com.czur.scanpro.ui.user.UserActivity
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import com.facebook.drawee.backends.pipeline.Fresco
import com.google.gson.Gson
import com.mob.tools.utils.FileUtils
import com.noober.background.drawable.DrawableCreator
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import java.io.File

/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class ThirdPartyBindActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityThirdBindBinding by lazy{
        ActivityThirdBindBinding.inflate(layoutInflater)
    }

    private var timeCount: TimeCount? = null
    private var isMobile = false
    private var isEmail = false
    private var codeHasContent = false
    private var httpManager: HttpManager? = null
    private var userPreferences: UserPreferences? = null
    private var platName: String? = null
    private var userId: String? = null
    private var thirdPartyToken: String? = null
    private var thirdPartyOpenId: String? = null
    private var thirdPartyPlatName: String? = null
    private var thirdPartyRefreshToken: String? = null
    private var realm: Realm? = null
    private var commonPopup: ScanProCommonPopup? = null
    private var currentTime: Long = 0
    private var account: String? = null
    private var code: String? = null
    private var isBind: Boolean = false
    //0：默认，1：图片浏览
    private var type: Int = 0

    private val codeTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            codeHasContent = s.isNotEmpty()
            checkNextStepButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            codeHasContent = s.isNotEmpty()
            checkNextStepButtonToClick()
        }
    }

    private val accountTextWatcher = object : TextWatcher {

        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            judgingAccount(s)

        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            judgingAccount(s)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this,true)
        setContentView(R.layout.activity_third_bind)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        realm = Realm.getDefaultInstance()
        httpManager = HttpManager.getInstance()
        userPreferences = UserPreferences.getInstance(this)
        type = intent.getIntExtra("type", 0)
        thirdPartyToken = intent.getStringExtra("thirdPartyToken")
        thirdPartyOpenId = intent.getStringExtra("thirdPartyOpenId")
        thirdPartyPlatName = intent.getStringExtra("thirdPartyPlatName")
        thirdPartyRefreshToken = intent.getStringExtra("thirdPartyRefreshToken")


        platName = intent.getStringExtra("platName")
        userId = intent.getStringExtra("userId")
        LogUtils.i(userId!! + "///userId")


        //设置标题
        binding.registerTopBar.normalTitle.setText(R.string.bind_third_party_account)

    }

    private fun registerEvent() {
        binding.registerTopBar.normalBackBtn.setOnClickListener(this)
        binding.getCodeBtn.setOnClickListener(this)
        binding.getCodeBtn.isSelected = true

        binding.nextStepBtn.setOnClickListener(this)
        binding.nextStepBtn.isSelected = false
        binding.nextStepBtn.isClickable = false
        binding.accountEdt.addTextChangedListener(accountTextWatcher)
        binding.codeEdt.addTextChangedListener(codeTextWatcher)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.getCodeBtn -> validatorAccount()
            R.id.nextStepBtn -> when {
                isMobile -> {
                    confirmMobileIdentifyCode()
                }
                isEmail -> {
                    confirmMailIdentifyCode()
                }
                else -> {
                    showMessage(R.string.account_format_error)
                }
            }
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            else -> {
            }
        }
    }


    /**
     * @des: 验证手机号或者邮箱
     * @params:[]
     * @return:void
     */
    private fun validatorAccount() {
        val accountStr = binding.accountEdt.text.toString()
        if (Validator.isEmpty(accountStr)) {
            showMessage(R.string.account_empty)
        } else {
            when {
                RegexUtils.isMobileExact(accountStr) -> {
                    getMobileCode(accountStr)
                }
                RegexUtils.isEmail(accountStr) -> {
                    getMailCode(accountStr)
                }
                else -> {
                    showMessage(R.string.account_format_error)
                }
            }

        }
    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private fun getMobileCode(accountStr: String) {

        httpManager!!.requestPassport().mobileCode(accountStr, String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {

                LogUtils.iTag("send mobile code", Gson().toJson(entity))
                timeCountBegin()
                showMessage(R.string.toast_code_send)

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {

                when {
                    entity.code == MiaoHttpManager.STATUS_CODE_1_MIN -> showMessage(R.string.toast_code_1_min)
                    entity.code == MiaoHttpManager.STATUS_5_MIN_4_TIME -> showMessage(R.string.toast_5_min_4_time)
                    entity.code == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY -> showMessage(R.string.toast_5_time_in_one_day)
                    else -> showMessage(R.string.request_failed_alert)
                }

            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    /**
     * @des: 获取邮箱验证码
     * @params:[accountStr]
     * @return:void
     */
    private fun getMailCode(accountStr: String) {

        val locale = resources.configuration.locale
        val language = locale.toString()
        HttpManager.getInstance().requestPassport().mailCode(accountStr, EtUtils.getLocale(language), String::class.java, object : MiaoHttpManager.Callback<String>{
            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {

                LogUtils.iTag("send mail code", Gson().toJson(entity))
                timeCountBegin()
                showMessage(R.string.toast_code_send)

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {

                when (entity.code) {
                    MiaoHttpManager.STATUS_CODE_1_MIN -> showMessage(R.string.toast_code_1_min)
                    MiaoHttpManager.STATUS_5_MIN_4_TIME -> showMessage(R.string.toast_5_min_4_time)
                    MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY -> showMessage(R.string.toast_5_time_in_one_day)
                    else -> showMessage(R.string.request_failed_alert)
                }

            }

            override fun onError(e: Exception) {

                showMessage(R.string.request_failed_alert)

            }
        })
    }

    /**
     * @des: 校验手机验证码
     * @params:
     * @return:
     */

    private fun confirmMobileIdentifyCode() {
        currentTime = System.currentTimeMillis()
        KeyboardUtils.hideSoftInput(this)
        account = binding.accountEdt.text.toString()
        code = binding.codeEdt.text.toString()
        httpManager!!.requestPassport().confirmThirdPartyByMobile(userPreferences!!.channel, userPreferences!!.imei, Constants.SCAN_PRO,
                account, platName, code, String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {
                showProgressDialog(false)
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                isBind = false
                registerSuccess()
            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()

                when (entity.code) {
                    MiaoHttpManager.STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_MOBILE -> {
                        isBind = true
                        registerSuccess()
                    }
                    MiaoHttpManager.STATUS_HAS_BINDED_THIRD_PARTY_BY_MOBILE -> showMessage(String.format(getString(R.string.mobile_has_binded), platName))
                    MiaoHttpManager.STATUS_INVALID_MOBILE -> showMessage(R.string.invalid_mobile)
                    MiaoHttpManager.STATUS_INVALID_CODE -> showMessage(R.string.toast_code_error)
                    MiaoHttpManager.STATUS_FAIL -> showMessage(R.string.toast_internal_error)
                    else -> showMessage(R.string.request_failed_alert)
                }
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    private fun registerSuccess() {
        if (isBind) {
            bindAccount()
        } else {
            goRegister()
        }
    }


    private fun goRegister() {
        val intent = Intent(this@ThirdPartyBindActivity, ThirdPartyRegisterActivity::class.java)
        intent.putExtra("type",type)
        intent.putExtra("account", account)
        intent.putExtra("code", code)
        intent.putExtra("userId", userId)
        intent.putExtra("thirdPartyOpenId", thirdPartyOpenId)
        intent.putExtra("thirdPartyToken", thirdPartyToken)
        intent.putExtra("thirdPartyPlatName", thirdPartyPlatName)
        intent.putExtra("platName", platName)
        intent.putExtra("thirdPartyRefreshToken", thirdPartyRefreshToken)
        ActivityUtils.startActivity(intent)
    }


    /**
     * @des: 校验邮箱验证码
     * @params:
     * @return:
     */

    private fun confirmMailIdentifyCode() {
        currentTime = System.currentTimeMillis()
        KeyboardUtils.hideSoftInput(this)
        account = binding.accountEdt.text.toString()
        code = binding.codeEdt.text.toString()
        httpManager!!.requestPassport().confirmThirdPartyByEmail(userPreferences!!.channel, userPreferences!!.imei, Constants.SCAN_PRO,
                account, platName, code, String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {
                showProgressDialog(false)
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                isBind = false
                registerSuccess()
            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                when (entity.code) {
                    MiaoHttpManager.STATUS_HAS_NOT_BINDED_THIRD_PARTY_BY_EMAIL -> {
                        isBind = true
                        registerSuccess()
                    }
                    MiaoHttpManager.STATUS_HAS_BINDED_THIRD_PARTY_BY_EMAIL -> showMessage(String.format(getString(R.string.email_has_binded), platName))
                    MiaoHttpManager.STATUS_INVALID_EMAIL -> showMessage(R.string.invalid_email)
                    MiaoHttpManager.STATUS_INVALID_CODE -> showMessage(R.string.toast_code_error)
                    MiaoHttpManager.STATUS_FAIL -> showMessage(R.string.toast_internal_error)
                    else -> showMessage(R.string.request_failed_alert)
                }
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    private fun bindAccount() {
//        val builder = ScanProCommonPopup.Builder(this@ThirdPartyBindActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
//        builder.setTitle(resources.getString(R.string.prompt))
//        builder.setMessage(getString(R.string.exist_to_merge))
//        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
//            if (TextUtils.isEmpty(userPreferences!!.channel)) {
//                getChannel()
//            } else {
//                bindThirdPartyAccount()
//            }
//            dialog.dismiss()
//        })
//        builder.setOnNegativeListener(DialogInterface.OnClickListener { _, _ -> ActivityUtils.finishActivity(this@ThirdPartyBindActivity) })
//        val commonPopup = builder.create()
//        commonPopup.show()
        if (TextUtils.isEmpty(userPreferences!!.channel)) {
            getChannel()
        } else {
            bindThirdPartyAccount()
        }
    }

    private fun bindThirdPartyAccount() {
        val account = binding.accountEdt.text.toString()
        val code = binding.codeEdt.text.toString()
        LogUtils.i("userId///" + userId + "account///" + account + "code///" + code)
        //userPreferences.getChannel()
        HttpManager.getInstance().requestPassport().thirdPartyBind(userPreferences!!.channel, userPreferences!!.imei, Constants.SCAN_PRO, userId, account, code,
                RegisterModel::class.java, object : MiaoHttpManager.Callback<RegisterModel> {
            override fun onStart() {
                showProgressDialog()
            }

            override fun onResponse(entity: MiaoHttpEntity<RegisterModel>) {
                hideProgressDialog()
                confirmToClearLastUserData(entity)
            }

            override fun onFailure(entity: MiaoHttpEntity<RegisterModel>) {
                hideProgressDialog()
                when (entity.code) {
                    MiaoHttpManager.STATUS_INVALID_THIRD_PARTY -> showMessage(R.string.toast_error_third_party_user)
                    MiaoHttpManager.STATUS_NOT_USER -> showMessage(R.string.toast_user_no_exist)
                    MiaoHttpManager.STATUS_INVALID_CODE -> showMessage(R.string.toast_code_error)
                    MiaoHttpManager.STATUS_ERROR -> showMessage(R.string.toast_internal_error)
                    else -> showMessage(R.string.request_failed_alert)
                }
            }

            override fun onError(e: Exception) {
                LogUtils.e(e)
                hideProgressDialog()
                showMessage(R.string.request_failed_alert)
            }
        })


    }

    /**
     * @des:获取channel
     * @params:[userId, token, platformName, isThirdParty, mobileMail, pwd]
     * @return:void
     */
    private fun getChannel() {
        HttpManager.getInstance().request().channel(ChannelModel::class.java, object : MiaoHttpManager.Callback<ChannelModel> {
            override fun onStart() {
                showProgressDialog()
            }

            override fun onResponse(entity: MiaoHttpEntity<ChannelModel>) {
                hideProgressDialog()
                userPreferences!!.channel = entity.body.channel
                userPreferences!!.endpoint = entity.body.endPoint
                LogUtils.i(entity.body)
                bindThirdPartyAccount()
            }

            override fun onFailure(entity: MiaoHttpEntity<ChannelModel>) {
                hideProgressDialog()
                showMessage(R.string.request_failed_alert)
            }

            override fun onError(e: Exception) {
                hideProgressDialog()
            }
        })
    }

    /**
     * @des: 确认删除用户信息
     * @params:
     * @return:
     */

    private fun confirmToClearLastUserData(entity: MiaoHttpEntity<RegisterModel>) {
        val currentUserId = entity.body.id
        LogUtils.i(currentUserId, userId)
        if (!StringUtils.equals(userPreferences!!.lastUserId, currentUserId) && Validator.isNotEmpty(userPreferences!!.lastUserId)) {

            val builder = ScanProCommonPopup.Builder(this@ThirdPartyBindActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
            builder.setTitle(resources.getString(R.string.prompt))
            val title = String.format(getString(R.string.confirm_to_clear_account), userPreferences!!.userName)
            builder.setMessage(title)
            builder.setOnPositiveListener(DialogInterface.OnClickListener { _, _ ->
                if (commonPopup != null) {
                    commonPopup!!.dismiss()
                }
                clearLastUserDataAndSetCurrentData(entity)
            })
            builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, which ->
                dialog.dismiss()
                ActivityUtils.finishActivity(this@ThirdPartyBindActivity)
            })
            commonPopup = builder.create()
            commonPopup!!.show()

        } else {
            setCurrentUserData(entity)
        }

    }

    /**
     * @des: 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     * @params:
     * @return:
     */

    private fun clearLastUserDataAndSetCurrentData(entity: MiaoHttpEntity<RegisterModel>) {
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                LogUtils.i("clean last user file and sp")
                val filePath = filesDir.toString() + File.separator + userPreferences!!.lastUserId
                FileUtils.deleteDir(File(filePath))
                runOnUiThread {
                    //清空sp
                    userPreferences!!.resetUser()
                    //清空数据库
                    realm!!.executeTransaction {
                        realm!!.where(DocEntity::class.java).notEqualTo("userID",Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm!!.where(CategoryEntity::class.java).notEqualTo("userID",Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm!!.where(TagEntity::class.java).notEqualTo("userID",Constants.NO_USER).findAll().deleteAllFromRealm()
                        realm!!.where(PdfEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(OcrModeEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncTagEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncDocEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(SyncCategoryEntity::class.java).findAll().deleteAllFromRealm()
                        realm!!.where(DownloadEntity::class.java).findAll().deleteAllFromRealm()
                    }
                    Fresco.getImagePipeline().clearCaches()
                    CleanUtils.cleanCustomDir(Utils.getApp().filesDir.toString() + File.separator + Constants.PDF_PATH)
                    setCurrentUserData(entity)
                }
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })

    }

    /**
     * @des: 显示登录成功并且跳转到主页
     * @params:
     * @return:
     */
    private fun showLoginSuccessAndGoIndex() {
        requestUserInfo()

        EventBus.getDefault().post(LoginEvent(EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS))
        when(type){
            0->{
                if (ActivityUtils.isActivityExistsInStack(UserActivity::class.java)) {
                    ActivityUtils.finishToActivity(UserActivity::class.java, false)
                }else{
                    val intent = Intent(this@ThirdPartyBindActivity, UserActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                    ActivityUtils.startActivity(intent)
                }
            }
            1->{
                ActivityUtils.finishToActivity(FilePreviewActivity::class.java,false)
            }
            2 -> {
                ActivityUtils.finishToActivity(IndexActivity::class.java, false)
            }
            3->{
                ActivityUtils.finishToActivity(FileActivity::class.java, false)
            }
        }
    }

    /**
     * @des: 存储当前用户信息到SP
     * @params:
     * @return:
     */
    private fun setCurrentUserData(entity: MiaoHttpEntity<RegisterModel>) {
        userPreferences!!.user = entity.body
        userPreferences!!.setIsUserLogin(true)
        userPreferences!!.lastUserId=userPreferences!!.userId

        LogUtils.i(Gson().toJson(userPreferences!!.user))

        userPreferences!!.setIsThirdParty(true)
        userPreferences!!.thirdPartyOpenid = thirdPartyOpenId
        userPreferences!!.thirdPartyToken = thirdPartyToken
        userPreferences!!.thirdPartyPlatName = thirdPartyPlatName
        userPreferences!!.thirdPartyRefreshToken = thirdPartyRefreshToken
        userPreferences!!.servicePlatName = platName
        LogUtils.i(userPreferences!!.isThirdParty, userPreferences!!.thirdPartyOpenid, userPreferences!!.thirdPartyPlatName, userPreferences!!.thirdPartyToken, userPreferences!!.servicePlatName)
        transformCategory(realm!!)
        showProgressDialog(false)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {

                transformRealCategory()
                return null
            }

            override fun onSuccess(result: Void?) {
                hideProgressDialog()
                showLoginSuccessAndGoIndex()

            }
        })
    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    internal inner class TimeCount(millisInFuture: Long, countDownInterval: Long) : CountDownTimer(millisInFuture, countDownInterval) {

        override fun onFinish() {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.red_de4d4d)).build()
            binding.getCodeBtn.background = drawable

            binding.getCodeBtn.setText(R.string.gain)
            binding.getCodeBtn.setClickable(true)
            binding.getCodeBtn.setSelected(true)


        }

        override fun onTick(millisUntilFinished: Long) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e1)).build()
            binding.getCodeBtn.background = drawable
            binding.getCodeBtn.setClickable(false)
            binding.getCodeBtn.setText((millisUntilFinished / 1000).toString() + " s")
            binding.getCodeBtn.setSelected(false)

        }

    }


    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private fun timeCountBegin() {
        timeCount = TimeCount(60000, 1000)
        timeCount!!.start()

    }

    private fun judgingAccount(s: CharSequence) {
        if (RegexUtils.isMobileExact(s)) {
            isMobile = true
            isEmail = false
        } else if (RegexUtils.isEmail(s)) {
            isMobile = false
            isEmail = true
        } else {
            isMobile = false
            isEmail = false
        }
        checkNextStepButtonToClick()
    }

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkNextStepButtonToClick() {

        val accountIsNotEmpty = Validator.isNotEmpty(binding.accountEdt.getText().toString())
        val codeIsNotEmpty = Validator.isNotEmpty(binding.codeEdt.getText().toString())

        if (accountIsNotEmpty && codeIsNotEmpty && codeHasContent) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
            binding.nextStepBtn.background = drawable
            binding.nextStepBtn.isSelected = true
            binding.nextStepBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
            binding.nextStepBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
            binding.nextStepBtn.background = drawable
            binding.nextStepBtn.isSelected = false
            binding.nextStepBtn.setTextColor(resources.getColor(R.color.white))
            binding.nextStepBtn.isClickable = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (Validator.isNotEmpty(timeCount)) {
            timeCount!!.cancel()
        }

    }
}

