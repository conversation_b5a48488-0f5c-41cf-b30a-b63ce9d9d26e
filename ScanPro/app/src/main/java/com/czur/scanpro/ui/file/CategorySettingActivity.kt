package com.czur.scanpro.ui.file

import android.content.DialogInterface
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.databinding.ActivityChangePasswordBinding
import com.czur.scanpro.databinding.ActivityDocumentsSettingBinding
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.ChangeCategoryNameEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import com.vicpin.krealmextensions.transaction
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import java.text.SimpleDateFormat
import java.util.*

class CategorySettingActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityDocumentsSettingBinding by lazy{
        ActivityDocumentsSettingBinding.inflate(layoutInflater)
    }
    private var categoryName: String? = null
    private var realm: Realm? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this,true)
        setContentView(R.layout.activity_documents_setting)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        realm = Realm.getDefaultInstance()
        categoryName = intent.getStringExtra("categoryName")
        val results = realm!!.where(DocEntity::class.java).equalTo("categoryName", categoryName).equalTo("isDelete", 0.toInt()).findAll()
        binding.pageCount.text = results.size.toString()
        var fileSize: Long = 0
        for (docEntity in results) {
            fileSize += FileUtils.getFileLength(docEntity.baseImagePath)
        }
        binding.pageSize.text = EtUtils.convertFileSize(fileSize)
        binding.pageNewName.setText(categoryName)
    }

    private fun registerEvent() {
        binding.deleteBtn.setOnClickListener(this)
        binding.backBtn.setOnClickListener(this)
        binding.pageNewName.addTextChangedListener(object : TextWatcher {


            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                rename(s.toString())
            }

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                           after: Int) {
            }

            override fun afterTextChanged(s: Editable) {
                rename(binding.pageNewName.text.toString())
            }
        }
        )
    }

    private fun rename(s: String) {
        realm!!.executeTransaction { realm ->
            if (!EtUtils.containsEmoji(s)) {
                if (Validator.isNotEmpty(s)) {
                    val curDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(System.currentTimeMillis()))
                    for (docEntity in realm.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("isNameTemp", 1.toInt()).findAll()) {
                        docEntity.categoryName = s
                        docEntity.updateTime = curDate
                        docEntity.isDirty = if (docEntity.isDirty == 1) 1 else 2
                    }
                    for (categoryEntity in realm.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("isNameTemp", 1.toInt()).findAll()) {
                        categoryEntity.isDirty = 1
                        categoryEntity.categoryName = s
                        categoryEntity.updateTime = curDate
                    }
                    LogUtils.e(s)
                    categoryName=s
                    EventBus.getDefault().post(ChangeCategoryNameEvent(EventType.CHANGE_CATEGORY_NAME, s))
                    startAutoSync()
                } else {
                    ToastUtils.showShort(R.string.category_not_be_empty)
                }

            } else {
                ToastUtils.showShort(R.string.nickname_toast_symbol)
            }
        }

    }

    private fun showDeleteDialog() {
        val builder = ScanProCommonPopup.Builder(this@CategorySettingActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.confirm_delete))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            deleteCategory()
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    private fun deleteCategory() {
        val needDeleteDocs = realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).findAll()
        realm!!.transaction {
            val curDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(System.currentTimeMillis()))
            val needDeletePaths = ArrayList<String>()
            for (docEntity in needDeleteDocs) {
                docEntity.isDirty = 1
                docEntity.isDelete = 1
                docEntity.updateTime = curDate
                needDeletePaths.add(docEntity!!.processSmallImagePath!!)
                needDeletePaths.add(docEntity.processImagePath!!)
                needDeletePaths.add(docEntity.baseImagePath!!)
                needDeletePaths.add(docEntity.baseSmallImagePath!!)
                if (docEntity.originalImagePath != null) {
                    needDeletePaths.add(docEntity.originalImagePath!!)
                }
            }

            ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                override fun doInBackground(): Void? {
                    //删除sd卡上book page
                    for (needDeletePath in needDeletePaths) {
                        FileUtils.delete(needDeletePath)
                    }
                    return null
                }

                override fun onSuccess(result: Void?) {

                }
            })
            for (categoryEntity in realm!!.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).findAll()) {
                categoryEntity.isDirty = 1
                categoryEntity.isDelete = 1
                categoryEntity.updateTime = curDate
            }
        }
        startAutoSync()
        ActivityUtils.finishToActivity(IndexActivity::class.java, false)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.deleteBtn -> {
                showDeleteDialog()

            }
            else -> {
            }
        }
    }


}