package com.czur.scanpro.ui.camera;

import android.hardware.Camera;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.scanpro.utils.DeviceLevelUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by Yz on 2018/4/15
 * Email：<EMAIL>
 */

public class CameraUtil {
    private volatile static CameraUtil cameraUtil;
    private CameraUtil() {

    }

    public static CameraUtil getInstance() {
        if (cameraUtil == null) {
            synchronized (CameraUtil.class) {
                if (cameraUtil == null) {
                    cameraUtil = new CameraUtil();
                }
            }
        }
        return cameraUtil;
    }

    protected Camera.Size getCloselyPreSize(int surfaceWidth, int surfaceHeight, List<Camera.Size> preSizeList) {
        int ReqTmpWidth;
        int ReqTmpHeight;
        // 当屏幕为垂直的时候需要把宽高值进行调换，保证宽大于高
        // if (mIsPortrait) {
        ReqTmpWidth = surfaceWidth;
        ReqTmpHeight = surfaceHeight;
        // } else {
        //     ReqTmpWidth = surfaceWidth;
        //     ReqTmpHeight = surfaceHeight;
        //  }
        //先查找preview中是否存在与surfaceview相同宽高的尺寸
        for (Camera.Size size : preSizeList) {
            if ((size.width == ReqTmpWidth) && (size.height == ReqTmpHeight)) {
                return size;
            }
        }
        // 得到与传入的宽高比最接近的size
        float reqRatio = ((float) ReqTmpWidth) / ReqTmpHeight;
        float curRatio, deltaRatio;
        float deltaRatioMin = Float.MAX_VALUE;
        Camera.Size retSize = null;
        for (Camera.Size size : preSizeList) {
            curRatio = ((float) size.width) / size.height;
            deltaRatio = Math.abs(reqRatio - curRatio);
            if (deltaRatio < deltaRatioMin) {
                deltaRatioMin = deltaRatio;
                retSize = size;
            }
        }
        return retSize;
    }


    public Camera.Size getPictureSize(List<Camera.Size> list) {
        //不考虑原始数据顺序，从中找出最接近目标像素大小的Camera.Size
        int pixel;
        switch (DeviceLevelUtils.judgeDeviceLevel(Utils.getApp().getApplicationContext())) {
            case DeviceLevelUtils.DEVICE_LEVEL_LOW:
                LogUtils.d("低端机");
                pixel = 2000000;
                break;
            case DeviceLevelUtils.DEVICE_LEVEL_MID:
                LogUtils.d("中端机");
                pixel = 5000000;
                break;
            case DeviceLevelUtils.DEVICE_LEVEL_HIGH:
                LogUtils.d("高端机");
                pixel = 8000000;
                break;
            default:
                pixel = 4000000;
                break;
        }


        List<Camera.Size> list43 = new ArrayList<>();
        for (Camera.Size s : list) {
            if (is43(s.width, s.height)) {
                list43.add(s);
            }
        }
        //如果查找不到4:3的从原有支持的size里筛选
        if (list43.size() == 0) {
            if (list.size() == 1) {
                return list.get(0);
            } else {
                int minDifference = Math.abs(list.get(0).width * list.get(0).height - pixel);
                int minIndex = 0;
                for (int i = 1; i < list.size(); i++) {
                    int temp = Math.abs(list.get(i).width * list.get(i).height - pixel);
                    if (temp < minDifference) {
                        minIndex = i;
                        minDifference = temp;
                    }
                }
                return list.get(minIndex);
            }
        } else {
            //从4:3里的size集合里查找
            if (list43.size() == 1) {
                return list43.get(0);
            } else {
                int minDifference = Math.abs(list43.get(0).width * list43.get(0).height - pixel);
                int minIndex = 0;
                for (int i = 1; i < list43.size(); i++) {
                    int temp = Math.abs(list43.get(i).width * list43.get(i).height - pixel);
                    if (temp < minDifference) {
                        minIndex = i;
                        minDifference = temp;
                    }
                }
                return list43.get(minIndex);
            }
        }
    }

    private boolean is43(int width, int height) {
        return width / 4 * 3 == height;
    }

}
