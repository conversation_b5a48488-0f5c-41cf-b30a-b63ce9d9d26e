package com.czur.scanpro.ui.component;

import android.content.Context;
import androidx.appcompat.widget.AppCompatEditText;
import android.util.AttributeSet;
import android.view.View;

/**
 * Created by Yz on 2018/6/22.
 * Email：<EMAIL>
 */
public class NoHintEditText extends AppCompatEditText {

    private Context mContext;
    public NoHintEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.mContext = context;
        init();
    }

    private void init() {
      final  String hint=getHint().toString();
      setOnFocusChangeListener(new OnFocusChangeListener() {
          @Override
          public void onFocusChange(View v, boolean hasFocus) {
              if (hasFocus){
                  setHint("");
              }else {
                  setHint(hint);
              }
          }
      });
    }
}
