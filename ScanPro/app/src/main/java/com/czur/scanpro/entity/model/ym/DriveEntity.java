package com.czur.scanpro.entity.model.ym;

public class DriveEntity {


    /**
     * Name : 总裁
     * CardNo : 210602198903032515
     * Sex : 男
     * Birthday : 1989年03月03日
     * Address : 辽宁省丹东市振兴区表厂路12号楼4单JG车03室
     * IssueDate : 2015-12-30
     * Nation : 中国
     * DrivingType : C1
     * RegisterDate : 2015-12-30至2021-12-30
     */

    private String Name;
    private String CardNo;
    private String Sex;
    private String Birthday;
    private String Address;
    private String IssueDate;
    private String Nation;
    private String DrivingType;
    private String RegisterDate;

    private String ValidPeriod;
    private String FileNo;
    private String Record;
    public String getValidPeriod() {
        return ValidPeriod;
    }

    public void setValidPeriod(String validPeriod) {
        ValidPeriod = validPeriod;
    }

    public String getValidperiod() {
        return Validperiod;
    }

    public void setValidperiod(String validperiod) {
        Validperiod = validperiod;
    }

    private String Validperiod;

    public String getName() {
        return Name;
    }

    public void setName(String Name) {
        this.Name = Name;
    }

    public String getCardNo() {
        return CardNo;
    }

    public void setCardNo(String CardNo) {
        this.CardNo = CardNo;
    }

    public String getSex() {
        return Sex;
    }

    public void setSex(String Sex) {
        this.Sex = Sex;
    }

    public String getBirthday() {
        return Birthday;
    }

    public void setBirthday(String Birthday) {
        this.Birthday = Birthday;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String Address) {
        this.Address = Address;
    }

    public String getIssueDate() {
        return IssueDate;
    }

    public void setIssueDate(String IssueDate) {
        this.IssueDate = IssueDate;
    }

    public String getNation() {
        return Nation;
    }

    public void setNation(String Nation) {
        this.Nation = Nation;
    }

    public String getDrivingType() {
        return DrivingType;
    }

    public void setDrivingType(String DrivingType) {
        this.DrivingType = DrivingType;
    }

    public String getRegisterDate() {
        return RegisterDate;
    }

    public void setRegisterDate(String RegisterDate) {
        this.RegisterDate = RegisterDate;
    }
    public String getFileNo() {
        return FileNo;
    }

    public void setFileNo(String fileNo) {
        FileNo = fileNo;
    }
    public String getRecord() {
        return Record;
    }

    public void setRecord(String record) {
        Record = record;
    }

}
