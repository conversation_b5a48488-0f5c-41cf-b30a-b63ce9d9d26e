package com.czur.scanpro.network.core;

import android.app.Activity;
import android.app.Application;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.common.ChangerTimestampTypeAdapter;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.entity.model.RegisterModel;
import com.czur.scanpro.entity.realm.TagEntity;
import com.czur.scanpro.event.EventType;
import com.czur.scanpro.event.LogoutEvent;
import com.czur.scanpro.network.HttpManager;
import com.czur.scanpro.preferences.UserPreferences;
import com.czur.scanpro.ui.account.LoginActivity;
import com.czur.scanpro.utils.MD5Utils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import cn.sharesdk.sina.weibo.SinaWeibo;
import cn.sharesdk.wechat.friends.Wechat;
import io.realm.Realm;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class SyncHttpTask {

    private static final String FORMATTER = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String DATA_CODE = "code";
    private static final String DATA_MSG = "msg";
    public static final String DATA_BODY = "body";

    private static final int LOGIN_AGAIN_SUCCESS = 0;
    private static final int LOGIN_AGAIN_NOT_CONFORM = 1;

    private Gson gson = new GsonBuilder().setDateFormat(FORMATTER).registerTypeAdapter(Timestamp.class, new ChangerTimestampTypeAdapter()).create();
    private Handler handler = new Handler(Looper.getMainLooper());
    private long lastTime = 0;

    private SyncHttpTask() {
    }

    public static SyncHttpTask getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        static final SyncHttpTask instance = new SyncHttpTask();
    }

    <T> MiaoHttpEntity<T> syncGet(Application application, String url, Type type, HashMap<String, String> headers, ArrayList<String> logs, boolean isRetry) throws Exception {
        MiaoHttpEntity<T> entity = null;
        Request.Builder builder = new Request.Builder().url(url);
        if (headers.size() != 0) {
            for (HashMap.Entry<String, String> entry : headers.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        Response response = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(builder.build()).execute()
                : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(builder.build()).execute();
        if (response.isSuccessful()) {
            String json = response.body().string();
            responseLog(json, response, logs);
            entity = makeEntity(json, type);
            if (entity.getCode() == MiaoHttpManager.STATUS_TIMEOUT && LOGIN_AGAIN_SUCCESS == tokenTimeout(application)) {
                String token = UserPreferences.getInstance(application).getToken();
                builder.removeHeader("T-ID").addHeader("T-ID", token);
                Response responseAgain = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(builder.build()).execute()
                        : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(builder.build()).execute();
                if (responseAgain.isSuccessful()) {
                    String jsonAgain = responseAgain.body().string();
                    responseLog(jsonAgain, responseAgain, logs);
                    entity = makeEntity(jsonAgain, type);
                }
                responseAgain.body().close();
            }
        } else {
            responseLog("", response, logs);
        }
        response.body().close();
        return entity;
    }

    <T> MiaoHttpEntity<T> syncPost(Application application, String url, Type type, HashMap<String, String> postParam, HashMap<String, String> headers, ArrayList<String> logs, boolean isRetry) throws Exception {
        MiaoHttpEntity<T> entity = null;
        FormBody.Builder builder = new FormBody.Builder();
        if (postParam != null && postParam.size() > 0) {
            for (Map.Entry<String, String> entry : postParam.entrySet()) {
                builder.add(entry.getKey(), entry.getValue());
            }
        }
        Request.Builder requestBuilder = new Request.Builder().url(url).post(builder.build());
        if (headers.size() != 0) {
            for (HashMap.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        Response response = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(requestBuilder.build()).execute()
                : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(requestBuilder.build()).execute();
        if (response.isSuccessful()) {
            String json = response.body().string();
            responseLog(json, response, logs);
            entity = makeEntity(json, type);
            LogUtils.d(entity.getCode() + "重新登录的code");
            if (entity.getCode() == MiaoHttpManager.STATUS_TIMEOUT && LOGIN_AGAIN_SUCCESS == tokenTimeout(application)) {
                String token = UserPreferences.getInstance(application).getToken();
                requestBuilder.removeHeader("T-ID").addHeader("T-ID", token);
                Response responseAgain = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(requestBuilder.build()).execute()
                        : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(requestBuilder.build()).execute();
                if (responseAgain.isSuccessful()) {
                    try {
                        String jsonAgain = responseAgain.body().string();
                        responseLog(jsonAgain, responseAgain, logs);
                        entity = makeEntity(jsonAgain, type);
                    } catch (Exception e) {
                        LogUtils.e(e);
                    }
                }
                responseAgain.body().close();
            }
        } else {
            responseLog("", response, logs);
        }
        response.body().close();
        return entity;
    }

    private int tokenTimeout(final Application application) throws Exception {
        UserPreferences userPreferences = UserPreferences.getInstance(application);
        String loginUsername = userPreferences.getLoginUserName();
        String loginPassword = MD5Utils.md5(userPreferences.getLoginPassword());
        String channel = userPreferences.getChannel();
        String thirdPartyOpenid = userPreferences.getThirdPartyOpenid();
        final String thirdPartyPlatName = userPreferences.getThirdPartyPlatName();
        String servicePlatName = userPreferences.getServicePlatName();
        String thirdPartyToken = userPreferences.getThirdPartyToken();
        boolean thirdParty = userPreferences.isThirdParty();
        String thirdPartyRefreshToken = userPreferences.getThirdPartyRefreshToken();
        if (thirdParty) {
            // 微信微博先尝试续第三方AccessToken
            if (thirdPartyPlatName.equals(Wechat.NAME)) {
                String urlBuilder = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=wx1e22b31e63df0112&grant_type=refresh_token&refresh_token=" + thirdPartyRefreshToken;
                Request request = new Request.Builder().url(urlBuilder).get().build();
                Response response = MiaoHttpManager.getInstance().getHttpClientWeixin().newCall(request).execute();
                if (response.isSuccessful()) {
                    String resultStr = response.body().string();
                    JSONObject jsonObject = new JSONObject(resultStr);
                    String newAccessToken = null;
                    String newRefreshToken = null;
                    if (jsonObject.has("access_token") && jsonObject.has("refresh_token")) {
                        newAccessToken = jsonObject.getString("access_token");
                        newRefreshToken = jsonObject.getString("refresh_token");
                    }
                    if (newAccessToken == null) {
                        if (System.currentTimeMillis() - lastTime <= 2000) {
                            return LOGIN_AGAIN_NOT_CONFORM;
                        }
                        thirdPartGoLogin(application, thirdPartyPlatName);
                        return LOGIN_AGAIN_NOT_CONFORM;
                    } else {
                        thirdPartyToken = newAccessToken;
                        userPreferences.setThirdPartyToken(newAccessToken);
                        userPreferences.setThirdPartyRefreshToken(newRefreshToken);
                        LogUtils.d("微信刷新AccessToken成功");
                    }
                } else {
                    throw new Exception("重新登录失败或其他code");
                }
            } else if (thirdPartyPlatName.equals(SinaWeibo.NAME)) {
                RequestBody formBody = new FormBody.Builder()
                        .add("client_id", "3976849238")
                        .add("client_secret", "5d00dfea24394e08e4ac8e48f43f3baf")
                        .add("grant_type", "refresh_token")
                        .add("redirect_uri", "http://www.weibo.com")
                        .add("refresh_token", thirdPartyRefreshToken).build();
                Request request = new Request.Builder().url("https://api.weibo.com/oauth2/access_token").post(formBody).build();
                Response response = MiaoHttpManager.getInstance().getHttpClientWeibo().newCall(request).execute();
                if (response.isSuccessful()) {
                    String resultStr = response.body().string();
                    JSONObject jsonObject = new JSONObject(resultStr);
                    String newAccessToken = null;
                    String refreshToken = null;
                    if (jsonObject.has("access_token") && jsonObject.has("refresh_token")) {
                        newAccessToken = jsonObject.getString("access_token");
                        refreshToken = jsonObject.getString("refresh_token");
                    }
                    if (newAccessToken == null) {
                        if (System.currentTimeMillis() - lastTime <= 2000) {
                            return LOGIN_AGAIN_NOT_CONFORM;
                        }
                        thirdPartGoLogin(application, thirdPartyPlatName);
                        return LOGIN_AGAIN_NOT_CONFORM;
                    } else {
                        thirdPartyToken = newAccessToken;
                        userPreferences.setThirdPartyToken(thirdPartyToken);
                        userPreferences.setThirdPartyRefreshToken(refreshToken);
                        LogUtils.d("微博刷新AccessToken成功");
                    }
                    Log.d("weixin", resultStr);
                } else {
                    ResponseBody responseBody = response.body();
                    if (responseBody != null) {
                        String result = responseBody.string();
                        JSONObject resultJson = new JSONObject(result);
                        if (resultJson.getString("error_code") != null) {
                            if (System.currentTimeMillis() - lastTime <= 2000) {
                                return LOGIN_AGAIN_NOT_CONFORM;
                            }
                            thirdPartGoLogin(application, thirdPartyPlatName);
                            return LOGIN_AGAIN_NOT_CONFORM;
                        } else {
                            throw new Exception("重新登录失败或其他code");
                        }
                    } else {
                        throw new Exception("重新登录失败或其他code");
                    }

                }
            }

            MiaoHttpEntity<RegisterModel> thirdPartyLoginModel = HttpManager.getInstance().requestPassport().thirdPartyLoginSync(channel, userPreferences.getIMEI(), Constants.SCAN_PRO,
                    servicePlatName, thirdPartyToken, thirdPartyOpenid, RegisterModel.class);
            if (thirdPartyLoginModel.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.setToken(thirdPartyLoginModel.getBody().getToken());
                return LOGIN_AGAIN_SUCCESS;
            } else if (thirdPartyLoginModel.getCode() == MiaoHttpManager.STATUS_THIRD_PARTY_TIME_OUT) {
                if (System.currentTimeMillis() - lastTime <= 2000) {
                    return LOGIN_AGAIN_NOT_CONFORM;
                }

                userPreferences.setIsThirdParty(false);
                userPreferences.setIsUserLogin(false);
//                userPreferences.setUserId(Constants.NO_USER);
                userPreferences.logOutToReset();
                EventBus.getDefault().post(new LogoutEvent(EventType.LOG_OUT));
                createTag(userPreferences);
                if (!"LoginActivity".equals(ActivityUtils.getTopActivity().getClass().getSimpleName())){
                    Intent intent = new Intent(application, LoginActivity.class);
                    intent.putExtra("platName", thirdPartyPlatName);
                    intent.putExtra("isThirdPartyToken", true);
                    intent.putExtra("type", 99);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    ActivityUtils.getTopActivity().finish();
                    application.startActivity(intent);
                    handler.postDelayed(() -> {
                        ToastUtils.showShort(String.format(application.getString(R.string.third_party_token_timeout_login_again), thirdPartyPlatName));
                    },500);
                }

                return LOGIN_AGAIN_NOT_CONFORM;
            } else {
                throw new Exception("重新登录失败或其他code");
            }
        } else {
            MiaoHttpEntity<RegisterModel> loginModel = HttpManager.getInstance().requestPassport().loginSync(Constants.SCAN_PRO, userPreferences.getIMEI(), channel, loginUsername, loginPassword, RegisterModel.class);
            if (loginModel.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.setToken(loginModel.getBody().getToken());
                return LOGIN_AGAIN_SUCCESS;
            } else if (loginModel.getCode() == MiaoHttpManager.STATUS_INVALID_USER_OR_PASSWORD) {
                if (System.currentTimeMillis() - lastTime <= 2000) {
                    return LOGIN_AGAIN_NOT_CONFORM;
                }
                userPreferences.setIsUserLogin(false);
//                userPreferences.setUserId(Constants.NO_USER);

                userPreferences.logOutToReset();
                EventBus.getDefault().post(new LogoutEvent(EventType.LOG_OUT));
                createTag(userPreferences);
                if (!"LoginActivity".equals(ActivityUtils.getTopActivity().getClass().getSimpleName())){
                    Intent intent = new Intent(application, LoginActivity.class);
                    intent.putExtra("type", 99);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    ActivityUtils.getTopActivity().finish();
//                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    application.startActivity(intent);
                    handler.postDelayed(() -> {
                        lastTime = System.currentTimeMillis();
                        ToastUtils.showShort(R.string.token_login_invalid);
                    },500);
                }
                return LOGIN_AGAIN_NOT_CONFORM;
            } else {
                throw new Exception("重新登录失败或其他code");
            }
        }

    }

    private void createTag(UserPreferences userPreferences) {
        Realm realm = Realm.getDefaultInstance();
        Activity activity = ActivityUtils.getTopActivity();
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                TagEntity tag1 = realm.where(TagEntity.class).equalTo("isDelete", 0).equalTo("tagName", activity.getString(R.string.default_tag1)).findFirst();
                if (tag1 == null) {
                    TagEntity tagEntity = new TagEntity();
                    tagEntity.setTagId(UUID.randomUUID().toString());
                    tagEntity.setTagName(activity.getString(R.string.default_tag1));
                    tagEntity.setUserID(getUserIdIsLogin(userPreferences));
                    tagEntity.setCreateTime(Constants.DEFAULT_TIME1);
                    tagEntity.setDefault(1);
                    tagEntity.setFileType(0);
                    realm.copyToRealmOrUpdate(tagEntity);
                } else {
                    tag1.setUserID(getUserIdIsLogin(userPreferences));
                }
                TagEntity tag2 = realm.where(TagEntity.class).equalTo("isDelete", 0).equalTo("tagName", activity.getString(R.string.default_tag2)).findFirst();
                if (tag2 == null) {
                    TagEntity tagEntity1 = new TagEntity();
                    tagEntity1.setTagId(UUID.randomUUID().toString());
                    tagEntity1.setTagName(activity.getString(R.string.default_tag2));
                    tagEntity1.setUserID(getUserIdIsLogin(userPreferences));
                    tagEntity1.setCreateTime(Constants.DEFAULT_TIME2);
                    tagEntity1.setDefault(1);
                    tagEntity1.setFileType(1);
                    realm.copyToRealmOrUpdate(tagEntity1);
                } else {
                    tag2.setUserID(getUserIdIsLogin(userPreferences));
                }
                TagEntity tag3 = realm.where(TagEntity.class).equalTo("isDelete", 0).equalTo("tagName", activity.getString(R.string.default_tag3)).findFirst();
                if (tag3 == null) {
                    TagEntity tagEntity2 = new TagEntity();
                    tagEntity2.setTagId(UUID.randomUUID().toString());
                    tagEntity2.setTagName(activity.getString(R.string.default_tag3));
                    tagEntity2.setUserID(getUserIdIsLogin(userPreferences));
                    tagEntity2.setCreateTime(Constants.DEFAULT_TIME3);
                    tagEntity2.setDefault(1);
                    tagEntity2.setFileType(2);
                    realm.copyToRealmOrUpdate(tagEntity2);
                } else {
                    tag3.setUserID(getUserIdIsLogin(userPreferences));
                }
                TagEntity tag4 = realm.where(TagEntity.class).equalTo("isDelete", 0).equalTo("tagName", activity.getString(R.string.default_tag4)).findFirst();
                if (tag4 == null) {
                    TagEntity tagEntity3 = new TagEntity();
                    tagEntity3.setTagId(UUID.randomUUID().toString());
                    tagEntity3.setTagName(activity.getString(R.string.default_tag4));
                    tagEntity3.setUserID(getUserIdIsLogin(userPreferences));
                    tagEntity3.setCreateTime(Constants.DEFAULT_TIME4);
                    tagEntity3.setDefault(1);
                    tagEntity3.setFileType(3);
                    realm.copyToRealmOrUpdate(tagEntity3);
                } else {
                    tag4.setUserID(getUserIdIsLogin(userPreferences));
                }
            }
        });

    }

    private String getUserIdIsLogin(UserPreferences userPreferences) {
        return userPreferences.isValidUser() ? userPreferences.getUserId() : Constants.NO_USER;
    }

    private void thirdPartGoLogin(final Application application, final String thirdPartyPlatName) {

        UserPreferences userPreferences = UserPreferences.getInstance(application);
        userPreferences.setIsThirdParty(false);
        userPreferences.setIsUserLogin(false);
//        userPreferences.setUserId(Constants.NO_USER);
        userPreferences.logOutToReset();
        EventBus.getDefault().post(new LogoutEvent(EventType.LOG_OUT));
        createTag(userPreferences);
        Intent intent = new Intent(application, LoginActivity.class);
        intent.putExtra("platName", thirdPartyPlatName);
        intent.putExtra("isThirdPartyToken", true);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        ActivityUtils.getTopActivity().finish();
        application.startActivity(intent);
        handler.postDelayed(() -> {
            ToastUtils.showShort(String.format(application.getString(R.string.third_party_token_timeout_login_again), thirdPartyPlatName));
        },500);
    }

    private <T> MiaoHttpEntity<T> makeEntity(String json, Type type) throws Exception {
        JSONObject jsonObject = new JSONObject(json);
        int code = jsonObject.getInt(DATA_CODE);
        String msg = jsonObject.has(DATA_MSG) ? jsonObject.getString(DATA_MSG) : "";
        MiaoHttpEntity<T> entity = new MiaoHttpEntity<>();
        entity.setCode(code);
        entity.setMsg(msg);
        if (jsonObject.has(DATA_BODY)) {
            String bodyJson = jsonObject.getString(DATA_BODY);
            if (bodyJson.startsWith("{")) {
                T t = gson.fromJson(bodyJson, type);
                entity.setBody(t);
            } else if (bodyJson.startsWith("[")) {
                List<T> list = gson.fromJson(bodyJson, type);
                entity.setBodyList(list);
            } else {
                entity.setBody((T) bodyJson);
            }
        }
        return entity;
    }

    private void responseLog(String json, Response response, ArrayList<String> logs) {
        LogUtils.d(logs);
        Headers responseHeaders = response.headers();
//        LogUtils.d(new Gson().toJson(responseHeaders));
        LogUtils.json("返回数据:", json);
    }
}