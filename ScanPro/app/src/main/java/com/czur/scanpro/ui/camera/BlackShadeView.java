package com.czur.scanpro.ui.camera;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.util.AttributeSet;
import android.view.View;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.alg.Args;
import com.czur.scanpro.alg.CZPoint;
import com.czur.scanpro.alg.CZSaoMiao_Alg;
import com.czur.scanpro.utils.ScreenAdaptationUtils;

public class BlackShadeView extends View {

    private Paint paint;
    private CutPoints points;
    private boolean isShowing;
    private int drawWidth;
    private int drawHeight;
    private Args args;
    private float height;
    private float scale;
    private Paint paint1;
    private Context context;

    public BlackShadeView(Context context) {
        this(context, null);
    }

    public BlackShadeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context=context;
        init();
    }

    /**
     * @des: 初始化画笔
     * @params:
     * @return:
     */

    private void init() {
        drawWidth = ScreenUtils.getScreenWidth();
        drawHeight = ScreenUtils.getScreenWidth() / 3 * 4;
        paint = new Paint();
        paint.setColor(0xEE000000);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);
        paint.setAntiAlias(true);
        paint.setFlags(Paint.ANTI_ALIAS_FLAG);
        paint.setStrokeWidth(SizeUtils.px2dp(4f));

        paint1 = new Paint();
        //抗锯齿
        paint1.setAntiAlias(true);
        paint1.setFlags(Paint.ANTI_ALIAS_FLAG);
        paint1.setColor(0xEE000000);
        paint1.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.XOR));
        paint1.setStyle(Paint.Style.FILL);
        paint1.setStrokeWidth(SizeUtils.dp2px(1f));
    }

    public void show(Args args ,float height,float scale) {
        isShowing = true;
        this.args = args;
        this.scale = scale;
        this.height = height;
        invalidate();
    }


    public void hide() {
        isShowing = false;
        invalidate();
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (isShowing) {

            CZSaoMiao_Alg alg = CZSaoMiao_Alg.getInstance(context);
            CZPoint leftDown = alg.transpose_key_points(args.getLeftDownP(), (int) height);
            CZPoint rightTop = alg.transpose_key_points(args.getRightTopP(), (int) height);
            CZPoint leftTop = alg.transpose_key_points(args.getLeftTopP(), (int) height);
            CZPoint rightDown = alg.transpose_key_points(args.getRightDownP(), (int) height);

            canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);

            canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR);
            canvas.drawRect(0,0,drawWidth, drawHeight, paint1);
            Path path1 = new Path();
            float scale1 = scale;
            if(ScreenAdaptationUtils.getPhoneModel()){
                scale1 =scale/4*3;
            }
            path1.moveTo(leftDown.getX()* scale, leftDown.getY()* scale1);
            path1.lineTo(leftTop.getX()* scale, leftTop.getY()* scale1);
            path1.lineTo(rightTop.getX()* scale, rightTop.getY()* scale1);
            path1.lineTo(rightDown.getX()* scale, rightDown.getY()* scale1);
            path1.lineTo(leftDown.getX()* scale, leftDown.getY()* scale1);
            canvas.drawPath(path1, paint1);
//            //屏幕逆时针旋转90度的时候下方的梯形
//            Path bottomPath = new Path();
//            bottomPath.moveTo(0, 0);
//            bottomPath.lineTo(leftDown.getX()*scale, leftDown.getY()*scale);
//            bottomPath.lineTo(leftTop.getX(), leftTop.getY()*scale);
//            bottomPath.lineTo(drawWidth, 0);
//            bottomPath.lineTo(0, 0);
//            canvas.drawPath(bottomPath, paint);
//
//            //屏幕逆时针旋转90度的时候左方的梯形
//            Path leftPath = new Path();
//            leftPath.moveTo(0, drawHeight);
//            leftPath.lineTo(rightDown.getX()*scale, rightDown.getY()*scale);
//            leftPath.lineTo(leftDown.getX()*scale, leftDown.getY()*scale);
//            leftPath.lineTo(0, 0);
//            leftPath.lineTo(0, drawHeight);
//            canvas.drawPath(leftPath, paint);
//
//
//            //屏幕逆时针旋转90度的时候上方的梯形
//            Path topPath = new Path();
//            topPath.moveTo(drawWidth, 0);
//            topPath.lineTo(leftTop.getX()*scale, leftTop.getY()*scale);
//            topPath.lineTo(rightTop.getX()*scale, rightTop.getY()*scale);
//            topPath.lineTo(drawWidth, drawHeight);
//            topPath.lineTo(drawWidth, 0);
//            canvas.drawPath(topPath, paint);
//
//
//            //屏幕逆时针旋转90度的时候右方的梯形
//            Path rightPath = new Path();
//            rightPath.moveTo(drawWidth, drawHeight);
//            rightPath.lineTo(rightTop.getX()*scale, rightTop.getY()*scale);
//            rightPath.lineTo(rightDown.getX()*scale, rightDown.getY()*scale);
//            rightPath.lineTo(0, drawHeight);
//            rightPath.lineTo(drawWidth, drawHeight);
//            canvas.drawPath(rightPath, paint);



        }
    }
}
