package com.czur.scanpro.ui.account

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityForgetPasswordBinding
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator

/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class ForgetPasswordActivity : BaseActivity(), View.OnClickListener {
    private lateinit var binding: ActivityForgetPasswordBinding


    private var userPreferences: UserPreferences = UserPreferences.getInstance(this)
    private var timeCount: TimeCount? = null
    private var codeHasContent = false
    private var accountContent = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this,true)
        setContentView(R.layout.activity_forget_password)
        binding = ActivityForgetPasswordBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        binding.registerTopBar.normalTitle.text=getString(R.string.forget_password)
        binding.accountEdt.addTextChangedListener(accountTextWatcher)
        binding.codeEdt.addTextChangedListener(codeTextWatcher)
    }


    private fun registerEvent() {
        binding.registerTopBar.backBtn.setOnClickListener(this)
        binding.nextStepBtn.setOnClickListener(this)
        binding.getCodePswBtn.setOnClickListener(this)
    }

    /**
     * @des: 验证手机号或者邮箱
     * @params:[]
     * @return:void
     */
    private fun validatorAccount() {

        val accountStr = binding.accountEdt.text.toString()
        if (Validator.isEmpty(accountStr)) {
            showMessage(R.string.account_empty)
        } else {
            if (RegexUtils.isMobileExact(accountStr)) {
                getIdentifyCode()
            } else if (RegexUtils.isEmail(accountStr)) {
                getIdentifyCode()
            } else {
                showMessage(R.string.account_format_error)
            }

        }
    }

    private fun getIdentifyCode() {

        val locale = resources.configuration.locale
        val language = locale.toString()
        HttpManager.getInstance().requestPassport().findPwdSendCode(Constants.SCAN_PRO, userPreferences.getIMEI(), userPreferences.getChannel(), binding.accountEdt.text.toString(),
                EtUtils.getLocale(language), String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {

                LogUtils.iTag("send find password code", Gson().toJson(entity))
                timeCountBegin()
                showMessage(R.string.toast_code_send)

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {

                when (entity.code) {
                    MiaoHttpManager.STATUS_CODE_1_MIN -> showMessage(R.string.toast_code_1_min)
                    MiaoHttpManager.STATUS_COND_1_MAIL -> showMessage(R.string.toast_code_1_min)
                    MiaoHttpManager.STATUS_5_MIN_4_TIME -> showMessage(R.string.toast_5_min_4_time)
                    MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY -> showMessage(R.string.toast_5_time_in_one_day)
                    MiaoHttpManager.STATUS_NOT_USER -> showMessage(R.string.toast_user_no_exist)
                    MiaoHttpManager.STATUS_ERROR -> showMessage(R.string.toast_internal_error)
                    else -> showMessage(R.string.request_failed_alert)
                }
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })


    }

    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private fun timeCountBegin() {
        timeCount = TimeCount(60000, 1000)
        timeCount?.start()
    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    internal inner class TimeCount(millisInFuture: Long, countDownInterval: Long) : CountDownTimer(millisInFuture, countDownInterval) {

        override fun onFinish() {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.red_de4d4d)).build()
            binding.getCodePswBtn.background = drawable

            binding.getCodePswBtn.setText(R.string.gain)
            binding.getCodePswBtn.setClickable(true)
            binding.getCodePswBtn.setSelected(true)


        }

        override fun onTick(millisUntilFinished: Long) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e1)).build()
            binding.getCodePswBtn.background = drawable
            binding.getCodePswBtn.setClickable(false)
            binding.getCodePswBtn.setText((millisUntilFinished / 1000).toString() + " s")
            binding.getCodePswBtn.setSelected(false)

        }

    }

    private val codeTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            if (s.length > 0) {
                codeHasContent = true
            } else {
                codeHasContent = false
            }

            checkRegisterButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            if (s.length > 0) {
                codeHasContent = true
            } else {
                codeHasContent = false
            }
            checkRegisterButtonToClick()
        }
    }

    private val accountTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            if (s.length > 0) {
                accountContent = true
            } else {
                accountContent = false
            }

            checkRegisterButtonToClick()

        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            if (s.length > 0) {
                accountContent = true
            } else {
                accountContent = false
            }

            checkRegisterButtonToClick()
        }
    }


    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkRegisterButtonToClick() {

        val accountIsNotEmpty = Validator.isNotEmpty(binding.accountEdt.text.toString())
        val passwordIsNotEmpty = Validator.isNotEmpty(binding.codeEdt.text.toString())

        if (accountIsNotEmpty && passwordIsNotEmpty && codeHasContent && accountContent) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
            binding.nextStepBtn.background = drawable
            binding.nextStepBtn.isSelected = true
            binding.nextStepBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
            binding.nextStepBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
            binding.nextStepBtn.background = drawable
            binding.nextStepBtn.isSelected = false
            binding.nextStepBtn.setTextColor(resources.getColor(R.color.white))
            binding.nextStepBtn.isClickable = false
        }
    }


    /**
     * @des: 校验 验证码
     * @params:
     * @return:
     */

    private fun confirmIdentifyCode() {
        KeyboardUtils.hideSoftInput(this)
        HttpManager.getInstance().requestPassport().findPwdNext(Constants.SCAN_PRO, userPreferences.imei, userPreferences.channel,
            binding.accountEdt.text.toString(), binding.codeEdt.text.toString(), String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {
                showProgressDialog(false)
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                val intent = Intent(this@ForgetPasswordActivity, ResetPasswordActivity::class.java)
                intent.putExtra("account", binding.accountEdt.text.toString())
                intent.putExtra("resetCode", entity.body)
                ActivityUtils.startActivity(intent)

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()

                when {
                    entity.code == MiaoHttpManager.STATUS_INVALID_CODE -> showMessage(R.string.toast_code_error)
                    entity.code == MiaoHttpManager.STATUS_NOT_USER -> showMessage(R.string.toast_user_no_exist)
                    entity.code == MiaoHttpManager.STATUS_FAIL -> showMessage(R.string.toast_internal_error)
                    else -> showMessage(R.string.request_failed_alert)
                }
            }

            override fun onError(e: Exception) {
                hideProgressDialog()

                showMessage(R.string.request_failed_alert)
            }
        })
    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.backBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.getCodePswBtn -> {
                validatorAccount()
            }
            R.id.nextStepBtn -> {
                confirmIdentifyCode()
            }
            else -> {
            }
        }
    }


}
