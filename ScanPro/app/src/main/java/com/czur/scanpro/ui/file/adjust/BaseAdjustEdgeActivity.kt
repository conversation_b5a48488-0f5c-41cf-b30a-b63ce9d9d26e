package com.czur.scanpro.ui.file.adjust

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.PointF
import android.graphics.RectF
import android.os.Bundle
import android.os.Handler
import androidx.constraintlayout.widget.ConstraintSet
import android.util.SizeF
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.SizeUtils
import com.czur.scanpro.R
import com.czur.scanpro.alg.Args
import com.czur.scanpro.alg.CZOrientation
import com.czur.scanpro.alg.Vec4f
import com.czur.scanpro.databinding.ActivityAdjustEdgeBinding
import com.czur.scanpro.ui.base.BaseActivity
import kotlin.math.abs

@SuppressLint("ClickableViewAccessibility")
abstract class BaseAdjustEdgeActivity : BaseActivity(), View.OnTouchListener {
    val binding: ActivityAdjustEdgeBinding by lazy{
        ActivityAdjustEdgeBinding.inflate(layoutInflater)
    }

    var fileID: String? = null
    var originalImagePath: String? = null
    var bitmap: Bitmap? = null
    var imageView: ImageView? = null
    var pointEventView: View? = null
    val touchPosition: PointF = PointF()
    val prevTouchPosition: PointF = PointF()
    var imagePosition: PointF = PointF()
    val handler = Handler()
    var algInit: Args? = null

    // magnifier move
    protected val dp50 = SizeUtils.dp2px(65f - 15f)
    protected val dp64 = SizeUtils.dp2px(65f - 1f)
    protected val dp65 = SizeUtils.dp2px(65f)
    protected val dp180 = SizeUtils.dp2px(180f)
    val magnifierWidthHeight = 100

    // init points
    var imageScaleWithScreen: Float = 0f
    val rectWidth = 0.05f * ScreenUtils.getScreenWidth().toFloat()

    // move event
    val dp20 = SizeUtils.dp2px(20f)
    var differenceSize = SizeF(0f, 0f)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_adjust_edge)
        setContentView(binding.root)
        initComponent()
        getIntentInfo()
        initView()
        initEvent()
    }

    private fun initComponent() {
        fileID = intent.getStringExtra("fileID")
        originalImagePath = intent.getStringExtra("originalImagePath")

    }

    private fun initView() {
        showProgressDialog(true)
        binding.imageMaxRegion.post {
            makeBitmap(SizeF(binding.imageMaxRegion.width.toFloat(), binding.imageMaxRegion.height.toFloat())) {
                makeImageView()
                imageScaleWithScreen = bitmap!!.width.toFloat() / it

                makePointView()

//                imageView!!.setOnTouchListener(this)
                pointEventView!!.setOnTouchListener(this)

                imageView!!.post {
                    imagePosition.x = imageView!!.x
                    imagePosition.y = imageView!!.y
                }

                hideProgressDialog()
            }
        }
        binding.magnifier.z = 996f
        binding.hView.z = 997f
        binding.vView.z = 998f
    }

    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        touchPosition.run {
            x = event!!.x - dp20
            y = event.y - dp20
        }
        when (event!!.action) {
            MotionEvent.ACTION_DOWN -> {

                prevTouchPosition.run {
                    x = event.x - dp20
                    y = event.y - dp20
                    if (event.x <= 0) {
                        x= 0F
                    }
                    if ( event.y <= 0) {
                        y= 0F
                    }
                    if (event.x >= bitmap!!.width) {
                        x = bitmap!!.width.toFloat()
                    }
                    if ( event.y >= bitmap!!.height) {
                        y = bitmap!!.height.toFloat()
                    }

                }
                touchDown()
            }
            MotionEvent.ACTION_MOVE -> {
//                LogUtils.e(touchPosition.x, touchPosition.y)
                if (checkNeedResetPoints()) {
                    return false
                }
                // 当前点和之前点完全一样，什么也不做
                if (isTouchPositionNoChanged()) {
                    return true
                }
                if (!isTouchPositionInImage()) {
                    magnifierHide()
                }
                touchMove(isTouchPositionInImage())

                prevTouchPosition.run {
                    x = touchPosition.x
                    y = touchPosition.y
                }
            }
            MotionEvent.ACTION_UP -> touchUp()
        }
        return true
    }

    abstract fun getIntentInfo()

    abstract fun touchDown()
    abstract fun touchMove(isInImage: Boolean)
    abstract fun touchUp()
    abstract fun checkNeedResetPoints(): Boolean

    abstract fun makePointView()

    private fun makeBitmap(maxRegionSize: SizeF, callback: ((originalImageWidth: Float) -> Unit)) {
        Thread(Runnable {
            algInit = algInit()
            val originalImageSize = algInit?.contentImg?.width?.toFloat()?.let { algInit?.contentImg?.height?.toFloat()?.let { it1 -> SizeF(it, it1) } }
            originalImageSize?.let {
                val originalImageScale = originalImageSize.width / originalImageSize.height
                val maxRegionScale = maxRegionSize.width / maxRegionSize.height
                val isLandscape = originalImageScale >= maxRegionScale
                val bitmapSize = if (originalImageSize.width > maxRegionSize.width || originalImageSize.height > maxRegionSize.height) {
                    if (isLandscape) {
                        SizeF(maxRegionSize.width, maxRegionSize.width / originalImageScale)
                    } else {
                        SizeF(maxRegionSize.height * originalImageScale, maxRegionSize.height)
                    }
                } else {
                    originalImageSize
                }
                bitmap = Bitmap.createBitmap(bitmapSize.width.toInt(), bitmapSize.height.toInt(), Bitmap.Config.ARGB_8888)
                bitmap?.let {
                    val canvas = Canvas(bitmap!!)
                    val rect = RectF(0f, 0f, bitmapSize.width, bitmapSize.height)
                    algInit?.contentImg?.let { it1 -> canvas.drawBitmap(it1, null, rect, null) }
                }
                handler.post {
                    callback(originalImageSize.width)
                }

            }

        }).start()
    }



    private fun isTouchPositionInImage(): Boolean {
        return touchPosition.x >= 0 && touchPosition.y >= 0 && touchPosition.x <= bitmap!!.width && touchPosition.y <= bitmap!!.height
    }

    private fun isTouchPositionNoChanged(): Boolean {
        return touchPosition.x == prevTouchPosition.x && touchPosition.y == prevTouchPosition.y
    }

    abstract fun algInit(): Args

    private fun makeImageView() {
        imageView = ImageView(this)
        imageView.run {
            imageView!!.id = R.id.AdjustEdgeSimpleImage
            imageView!!.setImageBitmap(bitmap)
        }
        binding.adjustEdgeBody.addView(imageView)

        val constraintSet = ConstraintSet()
        constraintSet.run {
            connect(imageView!!.id, ConstraintSet.START, R.id.imageMaxRegion, ConstraintSet.START)
            connect(imageView!!.id, ConstraintSet.TOP, R.id.imageMaxRegion, ConstraintSet.TOP)
            connect(imageView!!.id, ConstraintSet.END, R.id.imageMaxRegion, ConstraintSet.END)
            connect(imageView!!.id, ConstraintSet.BOTTOM, R.id.imageMaxRegion, ConstraintSet.BOTTOM)
            constrainWidth(imageView!!.id, bitmap!!.width)
            constrainHeight(imageView!!.id, bitmap!!.height)
            applyTo(binding.adjustEdgeBody)
        }
    }

    private fun initEvent() {

        binding.adjustEdgeBackBtn.setOnClickListener {
            backClick()
        }
        binding.finishBtn.setOnClickListener {
            finishClick()
        }
    }

    fun magnifierShow() {
        binding.magnifier.x = touchPosition.x + imagePosition.x - dp65
        binding.magnifier.y = touchPosition.y + imagePosition.y - dp180

        binding.hView.x = binding.magnifier.x + dp50
        binding.hView.y = binding.magnifier.y + dp64

        binding.vView.x = binding.magnifier.x + dp64
        binding.vView.y = binding.magnifier.y + dp50

        binding.magnifierGroup.visibility = View.VISIBLE
        makeMagnifierImage(touchPosition.x,touchPosition.y)
    }

    fun magnifierHide() {
        binding.magnifierGroup.visibility = View.GONE
    }

    abstract fun makeMagnifierImage(x:Float,y:Float)

    fun px2Image(px: Float): Float {
        return px / imageScaleWithScreen
    }

    fun px2Screen(px: Float): Float {
        return px * imageScaleWithScreen
    }

    fun deepCopyPointList(mutableList: MutableList<PointF>): ArrayList<PointF> {
        return ArrayList(mutableList.map { PointF(it.x, it.y) })
    }

    abstract fun backClick()
    abstract fun finishClick()

    fun lineRectCrossPts(rect: RectF, point1: PointF, point2: PointF, pointOrientation: CZOrientation): PointF {
        val line1 = Vec4f(point1, point2)
        val line2 = when (pointOrientation) {
            CZOrientation.Top -> Vec4f(PointF(rect.left, rect.top), PointF(rect.right, rect.top))
            CZOrientation.Bottom -> Vec4f(PointF(rect.left, rect.bottom), PointF(rect.right, rect.bottom))
            CZOrientation.Left -> Vec4f(PointF(rect.left, rect.top), PointF(rect.left, rect.bottom))
            CZOrientation.Right -> Vec4f(PointF(rect.right, rect.top), PointF(rect.right, rect.bottom))
        }
        return lineIntersection(line1, line2)
    }

    fun lineIntersection(line1: Vec4f, line2: Vec4f): PointF {
        val l1 = Vec4f(line1)
        val l2 = Vec4f(line2)
        if (l1.point1.x > l1.point2.x) {
            l1.point1.x = l1.point2.x.also { l1.point2.x = l1.point1.x }
            l1.point1.y = l1.point2.y.also { l1.point2.y = l1.point1.y }
        }
        if (l2.point1.x > l2.point2.x) {
            l2.point1.x = l2.point2.x.also { l2.point2.x = l2.point1.x }
            l2.point1.y = l2.point2.y.also { l2.point2.y = l2.point1.y }
        }

        val denominator = 1.4e-45f + (l1.point1.y - l1.point2.y) * (l2.point2.x - l2.point1.x) -
                (l1.point2.x - l1.point1.x) * (l2.point1.y - l2.point2.y)
        var x = (l1.point1.y * l1.point2.x - l1.point1.x * l1.point2.y) * (l2.point2.x - l2.point1.x) -
                (l1.point2.x - l1.point1.x) * (l2.point1.y * l2.point2.x - l2.point1.x * l2.point2.y)
        x = abs(x / denominator)
        var y = (l1.point1.y - l1.point2.y) * (l2.point1.y * l2.point2.x - l2.point1.x * l2.point2.y) -
                (l2.point1.y - l2.point2.y) * (l1.point1.y * l1.point2.x - l1.point1.x * l1.point2.y)
        y = abs(y / denominator)
        return PointF(x, y)
    }
}