package com.czur.scanpro.ui.camera;

public class CutPoints {

    //比例
    public float scale;
    public  float[] keyPoints=new float[8];

    //左上3个点XY坐标
    public int topLeftCenterX, topLeftCenterY;
    public int topLeftOneX, topLeftOneY;
    public int topLeftTwoX, topLeftTwoY;

    //右上3个点XY坐标
    public int topRightCenterX, topRightCenterY;
    public int topRightOneX, topRightOneY;
    public int topRightTwoX, topRightTwoY;

    //左下3个点XY坐标
    public int bottomLeftCenterX, bottomLeftCenterY;
    public int bottomLeftOneX, bottomLeftOneY;
    public int bottomLeftTwoX, bottomLeftTwoY;

    //右下3个点XY坐标
    public int bottomRightCenterX, bottomRightCenterY;
    public int bottomRightOneX, bottomRightOneY;
    public int bottomRightTwoX, bottomRightTwoY;


    public CutPoints(float[] points, float scale) {
        this.scale = scale;
        //左上
        topLeftCenterX = (int) (((float) points[0]) * scale);
        topLeftCenterY = (int) (((float) points[1]) * scale);
        topLeftOneX = (int) (((float) points[2]) * scale);
        topLeftOneY = (int) (((float) points[3]) * scale);
        topLeftTwoX = (int) (((float) points[4]) * scale);
        topLeftTwoY = (int) (((float) points[5]) * scale);
        //右上
        topRightCenterX = (int) (((float) points[6]) * scale);
        topRightCenterY = (int) (((float) points[7]) * scale);
        topRightOneX = (int) (((float) points[8]) * scale);
        topRightOneY = (int) (((float) points[9]) * scale);
        topRightTwoX = (int) (((float) points[10]) * scale);
        topRightTwoY = (int) (((float) points[11]) * scale);
        //左下
        bottomLeftCenterX = (int) (((float) points[12]) * scale);
        bottomLeftCenterY = (int) (((float) points[13]) * scale);
        bottomLeftOneX = (int) (((float) points[14]) * scale);
        bottomLeftOneY = (int) (((float) points[15]) * scale);
        bottomLeftTwoX = (int) (((float) points[16]) * scale);
        bottomLeftTwoY = (int) (((float) points[17]) * scale);
        //右下
        bottomRightCenterX = (int) (((float) points[18]) * scale);
        bottomRightCenterY = (int) (((float) points[19]) * scale);
        bottomRightOneX = (int) (((float) points[20]) * scale);
        bottomRightOneY = (int) (((float) points[21]) * scale);
        bottomRightTwoX = (int) (((float) points[22]) * scale);
        bottomRightTwoY = (int) (((float) points[23]) * scale);

        for (int i = 0; i <8 ; i+=2) {
            keyPoints[i]=points[i*6/2+0];
            keyPoints[i+1]=points[i*6/2+1];
        }

    }


}
