package com.czur.scanpro.ui.file

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.alg.CZSaoMiao_Alg
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityOcrResultBinding
import com.czur.scanpro.databinding.ActivityOtherResultBinding
import com.czur.scanpro.entity.model.BusinessLicenseModel
import com.czur.scanpro.entity.model.ym.DriveEntity
import com.czur.scanpro.entity.model.ym.IDCardEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.ConsumptionEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.callback.ProgressHelper
import com.czur.scanpro.network.callback.UIProgressRequestListener
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.popup.EmptyCountPopup
import com.czur.scanpro.ui.sync.SyncService
import com.czur.scanpro.utils.AppendYmStringUtils
import com.czur.scanpro.utils.ClipboardUtils
import com.czur.scanpro.utils.FormatOcrLicenseUtil
import com.czur.scanpro.utils.SignV3Utils
import com.google.gson.Gson
import com.umeng.analytics.MobclickAgent
import com.yunmai.android.engine.OcrEngine
import io.realm.Realm
import okhttp3.*
import org.greenrobot.eventbus.EventBus
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.nio.charset.Charset
import java.util.*
import java.util.concurrent.TimeUnit


class OtherResultActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityOtherResultBinding by lazy{
        ActivityOtherResultBinding.inflate(layoutInflater)
    }

    private var resultText: String? = null
    private var position: Int = 0
    private var fileId: String? = null
    private var url: String? = null
    private var ocrType: Int = 0
    private var fromOcr: Boolean = false
    private var tempPath: String? = null
    private var currentTimeMillis: Long = 0
    private var userPreferences: UserPreferences? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_other_result)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        resultText = intent.getStringExtra("resultText")
        fromOcr = intent.getBooleanExtra("fromOcr", false)
        url = intent.getStringExtra("url")
        tempPath = intent.getStringExtra("tempPath")
        ocrType = intent.getIntExtra("ocrType", 0)
        fileId = intent.getStringExtra("fileId")
        position = intent.getIntExtra("position", 0)
        binding.handwritingResultText.text = resultText

    }

    private fun registerEvent() {
        binding.handwritingResultFinishBtn.setOnClickListener(this)
        binding.recognizeAgain.setOnClickListener(this)
        binding.handwritingResultCopyRl.setOnClickListener(this)
    }


    private fun isHasCount(): Boolean {
        return  getSp().remainCard > 0

    }

    private fun showNoCountEmptyMessage() {
        showEmptyCountDialog(this@OtherResultActivity, EmptyCountPopup.EmptyType.CARD)

    }


    /**
     * @des: 如果不到100毫秒就休眠到100毫秒
     * @params:
     * @return:
     */
    private fun threadSleepTo1000Ms(algTime: Long): Long {
        if (algTime in 1..999) {
            return (1000.0f - algTime).toLong()
        } else {
            return 0
        }
    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.handwritingResultCopyRl -> {
                ClipboardUtils.copyText(binding.handwritingResultText.text.toString())
                showMessage(R.string.copy_success)
            }
            R.id.handwritingResultFinishBtn -> ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
            R.id.recognizeAgain -> {
                Thread(Runnable {
                    requestUserInfoSync()
                    runOnUiThread {
                        if (isHasCount()) {
                            if (ocrType == 4) {
                                val idCardBitmaps = CZSaoMiao_Alg.getInstance(this).cut_idcard_from_merged_img(url)

                                val frontBitmaps = ImageUtils.bitmap2Bytes(idCardBitmaps[0], Bitmap.CompressFormat.JPEG, 90)
                                val backBitmaps = if (idCardBitmaps.size > 1) ImageUtils.bitmap2Bytes(idCardBitmaps[1], Bitmap.CompressFormat.JPEG, 90) else null
                                executeIdCardBitmap(frontBitmaps, backBitmaps)
                            } else if (ocrType == 6) {
                                val driveBitmaps = CZSaoMiao_Alg.getInstance(this).cut_idcard_from_merged_img(url);

                                val frontBitmaps = ImageUtils.bitmap2Bytes(driveBitmaps[0], Bitmap.CompressFormat.JPEG, 90)
                                val backBitmaps = if (driveBitmaps.size > 1) ImageUtils.bitmap2Bytes(driveBitmaps[1], Bitmap.CompressFormat.JPEG, 90) else null
                                executeDriveBitmap(frontBitmaps, backBitmaps)
                            } else {
                                executeBusinessLicenseBitmap()
                            }
                        } else {
                            showNoCountEmptyMessage()
                        }
                    }
                }).start()

            }

            else -> {
            }
        }
    }

    fun mobClickBusinessLicenseEvent(activity: Context?, eventId: String) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[Constants.BUSINESS_LICENSE_EVENT] = Constants.BUSINESS_LICENSE_EVENT
        MobclickAgent.onEvent(activity, eventId, map)
    }

    private fun executeBusinessLicenseBitmap() {
        showProgressDialog(true)
        Thread {
            val path = userPreferences!!.sdPath + Constants.BUSINESS_LICENSE_PATH + UUID.randomUUID().toString() + Constants.JPG
            FileUtils.copy(url, path) { _, _ -> true }
            val file = FileUtils.getFileByPath(path)

            LogUtils.i("path:" + file.absolutePath)
            try {
                //这个是ui线程回调，可直接操作UI
                val uiProgressRequestListener = object : UIProgressRequestListener() {
                    override fun onUIRequestProgress(bytesWrite: Long, contentLength: Long, done: Boolean) {
                        Log.i("czurxx", "bytesWrite:$bytesWrite")
                        Log.i("czurxx", "contentLength$contentLength")
                        Log.i("czurxx", (100 * bytesWrite / contentLength).toString() + " % done ")
                        Log.i("czurxx", "done:$done")
                        Log.i("czurxx", "================================")

                    }
                }
                val jsonObject = JSONObject()
                jsonObject.put("base64", SignV3Utils.imageToBase64(file.path))
                val syncJson = jsonObject.toString()
                val requestBody: RequestBody = RequestBody.create(SyncService.JSON, syncJson)

                val request = Request.Builder()
                    .header("Content-Type", "application/json")
                    .header("T-ID", userPreferences!!.token)
                    .header("U-ID", userPreferences!!.userId)
                    .url(Constants.BUSINESS_LICENSE_URL)
                    .post(
                        ProgressHelper.addProgressRequestListener(
                            requestBody,
                            uiProgressRequestListener
                        )
                    )
                    .build()
                val okHttpClient = OkHttpClient.Builder()
                        .connectTimeout(15, TimeUnit.SECONDS)
                        .writeTimeout(15, TimeUnit.SECONDS)
                        .readTimeout(20, TimeUnit.SECONDS)
                        .build()

                val response = okHttpClient.newCall(request).execute()
                LogUtils.i("-- 上传营业执照 start --")
                val jsonString = response.body!!.string()
                LogUtils.i(jsonString)
                val businessLicenceEntity = Gson().fromJson<BusinessLicenseModel>(jsonString, BusinessLicenseModel::class.java)
                LogUtils.i("-- 上传营业执照 end --")
                // 请求成功
                if (response.isSuccessful) {
                    val code = businessLicenceEntity.code
                    LogUtils.e(code)

                    // 上传成功
                    if (code == 1000) {
                        mobClickBusinessLicenseEvent(this@OtherResultActivity, BuildConfig.PHASE.generatePdfCount)
                        val textDetectionsBean = Gson().fromJson<BusinessLicenseModel.DataBean>(businessLicenceEntity.data, BusinessLicenseModel.DataBean::class.java)
                        businessLicenceEntity.dataBean = textDetectionsBean
                        val resultString = FormatOcrLicenseUtil.formatLicenseStr(businessLicenceEntity.dataBean,businessLicenceEntity.data)
                        consumption(resultString.toString(), "highCertificate", 5)
                    } else if (code == -9011) {
                        ocrFailed(-2)
                    } else {
                        ocrFailed(-2)
                    }
                } else {
                    ocrFailed(-2)
                }// 请求失败
            } catch (e: IOException) {
                LogUtils.e(e)
                ocrFailed(-2)
            } catch (e: JSONException) {
                LogUtils.e(e)
                ocrFailed(-2)
            } catch (e: Exception) {
                LogUtils.e(e)
                ocrFailed(-2)
            }
        }.start()


    }

    override fun onBackPressed() {
        super.onBackPressed()
        ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
    }

    override fun onDestroy() {
        super.onDestroy()
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                CleanUtils.cleanCustomDir(cacheDir.path + Constants.TEMP)
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        LogUtils.i("requestCode=$requestCode", " resultCode=$resultCode")
        if (requestCode == SHARE_SUCCESS_CODE) {

        }
    }

    companion object {
        private val SHARE_SUCCESS_CODE = 666
    }


    private fun executeIdCardBitmap(frontBitmap: ByteArray?, backBitmap: ByteArray?) {
        showProgressDialog(true)

        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<String>() {

            override fun doInBackground(): String? {
                val ocrEngine = OcrEngine()
                try {
                    val idCardFront = ocrEngine.recognize(this@OtherResultActivity, frontBitmap, "")
                    if (backBitmap != null) {
                        val idCardBack = ocrEngine.recognize(this@OtherResultActivity, backBitmap, "")

                        if (idCardFront.recogStatus == OcrEngine.RECOG_OK && idCardBack.recogStatus == OcrEngine.RECOG_OK) {
                            val res = String(idCardFront.charInfo, Charset.forName("gbk"))
                            val res1 = String(idCardBack.charInfo, Charset.forName("gbk"))
                            val idCardModel = Gson().fromJson(res.trim { it <= ' ' }.toString(), IDCardEntity::class.java)
                            val idCardModel1 = Gson().fromJson(res1.trim { it <= ' ' }.toString(), IDCardEntity::class.java)

                            val result = AppendYmStringUtils.idCardToString(idCardModel).append().append(AppendYmStringUtils.idCardToString(idCardModel1)).toString()
                            consumption(result, "highCertificate", 4)

                            return result
                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    } else {
                        if (idCardFront.recogStatus == OcrEngine.RECOG_OK) {
                            val res = String(idCardFront.charInfo, Charset.forName("gbk"))
                            val idCardModel = Gson().fromJson(res.trim { it <= ' ' }.toString(), IDCardEntity::class.java)
                            val result = AppendYmStringUtils.idCardToString(idCardModel).append().toString()
                            consumption(result, "highCertificate", 5);

                            return result
                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    }


                } catch (e: Exception) {
                    LogUtils.i(e)
                    ocrFailed(-2)
                    return null
                } finally {
                    LogUtils.i("finally")
                    ocrEngine.finalize()
                }
                return null

            }

            override fun onSuccess(result: String?) {
                LogUtils.i(result)
                hideProgressDialog()
            }

            override fun onFail(t: Throwable) {
                super.onFail(t)
                ocrFailed(-2)
            }
        })
    }

    private fun executeDriveBitmap(frontBitmap: ByteArray?, backBitmap: ByteArray?) {
        showProgressDialog(true)

        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<String>() {

            override fun doInBackground(): String? {
                val ocrEngine = com.ymjz.ocr.OcrEngine()
                try {
                    val driveFront = ocrEngine.getJZInfo(this@OtherResultActivity, frontBitmap, "", "")
                    if (backBitmap != null) {
                        val driveBack = ocrEngine.getJZInfo(this@OtherResultActivity, backBitmap, "", "")
                        if (driveFront.yMrecognState == OcrEngine.RECOG_OK && driveBack.yMrecognState == OcrEngine.RECOG_OK) {
                            val res = String(driveFront.charInfo, Charset.forName("gbk"))
                            val res1 = String(driveBack.charInfo, Charset.forName("gbk"))
                            val driveModel = Gson().fromJson(res.trim { it <= ' ' }.toString(), DriveEntity::class.java)
                            val driveModel1 = Gson().fromJson(res1.trim { it <= ' ' }.toString(), DriveEntity::class.java)
                            LogUtils.e(res)
                            LogUtils.e(res1)

                            val result = AppendYmStringUtils.driveToString(driveModel).append().append(AppendYmStringUtils.driveToString(driveModel1)).toString()
                            consumption(result, "highCertificate", 6);
                            return result

                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    } else {
                        if (driveFront.yMrecognState == OcrEngine.RECOG_OK) {
                            val res = String(driveFront.charInfo, Charset.forName("gbk"))
                            LogUtils.e(res)
                            val driveModel = Gson().fromJson(res.trim { it <= ' ' }.toString(), DriveEntity::class.java)
                            val result = AppendYmStringUtils.driveToString(driveModel).append().toString()
                            consumption(result, "highCertificate", 6);
                            return result
                        } else {
                            ocrFailed(-2)
                            return null
                        }
                    }
                } catch (e: Exception) {
                    LogUtils.i(e)
                    ocrFailed(-2)
                    return null
                } finally {
                    LogUtils.i("finally")
                    ocrEngine.finalize()
                }
                return null

            }

            override fun onSuccess(result: String?) {
                LogUtils.i(result)
                hideProgressDialog()
            }

            override fun onFail(t: Throwable) {
                super.onFail(t)
                ocrFailed(-2)
            }
        })
    }

//    private fun executeBusinessLicenseBitmap() {
//        showProgressDialog(true)
//
//        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<String>() {
//
//            override fun doInBackground(): String? {
//
//                val ocrEngine = com.ym.ocr.engine.threec.OcrEngine()
//                try {
//                    val resultInfo = ocrEngine.doImportOcr(url)
//                    if (resultInfo.ret == 1) {
//                        val res = String(resultInfo.charInfo, Charset.forName("gbk"))
//                        val result = AppendYmStringUtils.businessLicenceToString(Gson().fromJson(res.trim { it <= ' ' }.toString(), BusinessLicenceEntity::class.java)).toString()
//                        consumption(result, "certificate", 5)
//                    } else {
//                        ocrFailed(-2)
//                        return null
//                    }
//
//                } catch (e: Exception) {
//                    LogUtils.i(e)
//                    ocrFailed(-2)
//                    return null
//                } finally {
//                    LogUtils.i("finally")
//                    ocrEngine.finalize()
//                }
//                return null
//
//            }
//
//            override fun onSuccess(result: String?) {
//                LogUtils.i(result)
//                hideProgressDialog()
//
//            }
//
//            override fun onFail(t: Throwable) {
//                super.onFail(t)
//                ocrFailed(-2)
//            }
//        })
//    }

    private fun ocrFailed(code: Int) {
        runOnUiThread(Runnable {
            if (code == 2) {
                failedDelay(R.string.ocr_language_failed)
            } else if (code == 3) {
                failedDelay(R.string.ocr_blur_failed)
            } else {
                failedDelay(R.string.ocr_failed)
            }
        })
    }

    private fun failedDelay(failedText: Int) {

        runOnUiThread(Runnable {
            showMessage(failedText)
            hideProgressDialog()

        })

    }

    /**
     * @des: 请求用户信息certificate
     * @params:
     * @return:
     */

    private fun consumption(result: String?, func: String, type: Int) {
        HttpManager.getInstance().request().consumption(getSp().userId, func, String::class.java, object : MiaoHttpManager.CallbackNetwork<String> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                commonRequestUserInfoNow()
                if (type == 4) {
                    finishOcr(result, 4)
                    EventBus.getDefault().post(ConsumptionEvent(EventType.CARD_COUNT_REDUCE))
                } else {
                    finishOcr(result, 5)
                    EventBus.getDefault().post(ConsumptionEvent(EventType.CARD_COUNT_REDUCE))
                }
            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
            }

            override fun onError(e: Exception) {
                hideProgressDialog()
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    private fun finishOcr(resultText: String?, ocrType: Int) {


        val realm = Realm.getDefaultInstance()
        realm.beginTransaction()
        val docEntity = realm.where(DocEntity::class.java).equalTo("fileID", fileId).equalTo("isDelete", 0.toInt()).findFirst()
        docEntity!!.ocrResult = resultText
        realm.commitTransaction()

        binding.handwritingResultText.text = resultText


    }
}