package com.czur.scanpro.utils

import android.Manifest
import android.content.Context
import android.content.DialogInterface
import android.os.Build
import android.view.Gravity
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.czur.scanpro.common.StarryCommonPopup
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants

object PermissionUtil {
    private var checkPermissionDialog: StarryCommonPopup? = null

    // 权限设置友好提示对话框
    @JvmStatic
    fun checkPermissionWithDialog(
        context: Context,
        title: String = "",
        message: String = "",
        positiveTitle: String = "",
        negativeTitle: String = "",
        clickListener: View.OnClickListener
    ) {
        if (checkPermissionDialog != null && checkPermissionDialog?.isShowing() == true) {
            return
        }
        checkPermissionDialog = StarryCommonPopup.Builder(
            ActivityUtils.getTopActivity(),
            CloudCommonPopupConstants.COMMON_TWO_BUTTON
        )
            .setTitle(title)
            .setMessage(message)
            .setPositiveTitle(positiveTitle)
            .setNegativeTitle(negativeTitle)
            .setOnPositiveListener { dialog: DialogInterface, _: Int ->
                clickListener.onClick(View(context))
                dialog.dismiss()
            }
            .setOnNegativeListener { dialog: DialogInterface, _: Int ->
                clickListener.onClick(null)
                dialog.dismiss()
            }
//            .setTextContentGravity(Gravity.START or Gravity.CENTER_VERTICAL)
            .create()
        checkPermissionDialog?.show()
    }

    // 改动这个方法需要查看调用处,因为其他地方调用了[0]和[1]
    @JvmStatic
    fun getStoragePermission(): Array<String?> {
        val requestPermissions: Array<String?>
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissions = arrayOfNulls(2)
            requestPermissions[0] = Manifest.permission.READ_MEDIA_IMAGES
            requestPermissions[1] = Manifest.permission.READ_MEDIA_VIDEO
        } else {
            requestPermissions = arrayOfNulls(2)
            requestPermissions[0] = Manifest.permission.WRITE_EXTERNAL_STORAGE
            requestPermissions[1] = Manifest.permission.READ_EXTERNAL_STORAGE
        }
        return requestPermissions
    }

    @JvmStatic
    fun getStoragePermission(vararg addPermissions: String?): Array<String?> {
        var requestPermissions: Array<String?> =
            arrayOfNulls(addPermissions.size + getStoragePermission().size)
        requestPermissions = requestPermissions.plus(getStoragePermission()).plus(addPermissions)
        return requestPermissions
    }

}