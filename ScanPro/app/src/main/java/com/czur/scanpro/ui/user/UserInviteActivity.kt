package com.czur.scanpro.ui.user

import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import cn.sharesdk.framework.Platform
import cn.sharesdk.framework.Platform.SHARE_IMAGE
import cn.sharesdk.framework.PlatformActionListener
import cn.sharesdk.framework.ShareSDK
import cn.sharesdk.wechat.friends.Wechat
import cn.sharesdk.wechat.moments.WechatMoments
import com.blankj.utilcode.util.*
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.MediumBoldTextView
import com.umeng.analytics.MobclickAgent
import java.util.*

/**
 * Created by shaojun.
 */
class UserInviteActivity : BaseActivity(), View.OnClickListener {
    private var httpManager: HttpManager? = null
    private var shareBitmap: Bitmap? = null
    private lateinit var userPreferences: UserPreferences

    private lateinit var normalTitle: MediumBoldTextView
    private lateinit var normalBackBtn: ImageView
    private lateinit var tv_code: MediumBoldTextView
    private lateinit var invite_img: ImageView
    private lateinit var info_btn: MediumBoldTextView
    private lateinit var info_img: ImageView
    private lateinit var wechat_btn: ImageView
    private lateinit var moment_btn: ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_user_invite)
        initView()
        initComponent()
        registerEvent()
    }

    private fun initView() {
        normalTitle = findViewById(R.id.normalTitle)
        normalBackBtn = findViewById(R.id.normalBackBtn)
        tv_code = findViewById(R.id.tv_code)
        invite_img = findViewById(R.id.invite_img)
        info_btn = findViewById(R.id.info_btn)
        info_img = findViewById(R.id.info_img)
        wechat_btn = findViewById(R.id.wechat_btn)
        moment_btn = findViewById(R.id.moment_btn)
    }

    private fun initComponent() {
        userPreferences = UserPreferences.getInstance(this)
        httpManager = HttpManager.getInstance()
        normalTitle.setText(R.string.inviteTitle)
        val imageResource = R.mipmap.invite
        //用来测量居中的坐标x
        tv_code.text = userPreferences.inviteCode
        tv_code.postDelayed(Runnable {
            shareBitmap = ImageUtils.addTextWatermark(
                ImageUtils.addCornerBorder(
                    ImageUtils.getBitmap(imageResource),
                    SizeUtils.dp2px(13f).toFloat(),
                    resources.getColor(R.color.gray_dd),
                    SizeUtils.dp2px(35f).toFloat()
                ),
                userPreferences.inviteCode, SizeUtils.sp2px(80f),
                resources.getColor(R.color.black_24),
                tv_code.x,
                SizeUtils.dp2px(465f).toFloat()
            )
            invite_img.setImageBitmap(shareBitmap)
            userPreferences.inviteImage = imageResource
            val i = ScreenUtils.getScreenHeight() - SizeUtils.dp2px(300f)
            LogUtils.e(i, ScreenUtils.getScreenHeight())

            invite_img.layoutParams.height = i * 539 / 650
            invite_img.layoutParams.width = i * 303 / 650
        }, 100)
    }

    private fun registerEvent() {
        normalBackBtn.setOnClickListener(this)
        info_btn.setOnClickListener(this)
        info_img.setOnClickListener(this)
        wechat_btn.setOnClickListener(this)
        moment_btn.setOnClickListener(this)
    }

    private fun showShare(type: Int) {
        val sp = Platform.ShareParams()
        sp.imageData = shareBitmap
        sp.site = getString(R.string.app_name)
        when (type) {
            1 -> {
                sp.shareType = SHARE_IMAGE
                val weixin = ShareSDK.getPlatform(Wechat.NAME)
                mobClickShareEvent(
                    this@UserInviteActivity,
                    BuildConfig.PHASE.invitationCode,
                    Constants.WECHAT_SHARE,
                    userPreferences!!.inviteCode
                )
                weixin.platformActionListener = object : PlatformActionListener {
                    override fun onComplete(
                        platform: Platform,
                        i: Int,
                        hashMap: HashMap<String, Any>
                    ) {
                    }

                    override fun onError(platform: Platform, i: Int, throwable: Throwable) {
                        runOnUiThread(Runnable {
                            if (i == 9) {
                                showMessage(R.string.WEXIN_NOT_INSTALL)
                            } else {
                                showMessage(R.string.share_failed)
                            }
                        })
                    }

                    override fun onCancel(platform: Platform, i: Int) {
                    }
                }
                weixin.share(sp)
            }

            2 -> {
                sp.shareType = SHARE_IMAGE
                LogUtils.e("朋友圈")
                mobClickShareEvent(
                    this@UserInviteActivity,
                    BuildConfig.PHASE.invitationCode,
                    Constants.MOMENT_SHARE,
                    userPreferences!!.inviteCode
                )
                val wechatMoments = ShareSDK.getPlatform(WechatMoments.NAME)
                wechatMoments.platformActionListener = object : PlatformActionListener {
                    override fun onComplete(
                        platform: Platform,
                        i: Int,
                        hashMap: HashMap<String, Any>
                    ) {
                    }

                    override fun onError(platform: Platform, i: Int, throwable: Throwable) {
                        runOnUiThread(Runnable {
                            if (i == 9) {
                                showMessage(R.string.WEXIN_NOT_INSTALL)
                            } else {
                                showMessage(R.string.share_failed)
                            }
                        })
                    }

                    override fun onCancel(platform: Platform, i: Int) {
                    }
                }
                wechatMoments.share(sp)
            }

            else -> {
            }
        }

    }

    private fun mobClickShareEvent(
        activity: Context?,
        eventId: String,
        platName: String,
        code: String
    ) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[Constants.METHOD] = platName
        map[Constants.CODE] = "$platName-$code"
        LogUtils.e(platName, "$platName-$code")
        MobclickAgent.onEvent(activity, eventId, map)
    }

    override fun onDestroy() {
        shareBitmap!!.recycle()
        shareBitmap = null
        super.onDestroy()
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            R.id.info_btn, R.id.info_img -> ActivityUtils.startActivity(InviteInfoActivity::class.java)
            R.id.wechat_btn -> showShare(1)
            R.id.moment_btn -> showShare(2)
            else -> {
            }
        }
    }


}