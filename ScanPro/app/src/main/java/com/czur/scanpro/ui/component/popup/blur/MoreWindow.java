package com.czur.scanpro.ui.component.popup.blur;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.os.Build;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.RomUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;

import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicBoolean;

public class MoreWindow extends PopupWindow implements View.OnClickListener {

    private Activity mContext;
    private RelativeLayout layout;
    private ImageView close;
    private View bgView;
    private ImageView blurringIV;
    private int mWidth;
    private int mHeight;
    private int statusBarHeight;
    private ImageView cardBtn;
    private ImageView surfaceBtn;
    private ImageView singleBtn;
    private LinearLayout cardLl;
    private LinearLayout surfaceLl;
    private LinearLayout singleLl;
    private Handler mHandler = new Handler();
    private LinearLayout importLL;
    private View indexDecView;

    public void setCanClose(AtomicBoolean canClose) {
        this.canClose = canClose;
    }

    private AtomicBoolean canClose = new AtomicBoolean(false);

    public MoreWindow(Activity context) {
        mContext = context;
    }

    public MoreWindow(Activity context, IndexAnimFinishedListener indexAnimFinishedListener) {
        mContext = context;
        this.indexAnimFinishedListener = indexAnimFinishedListener;
    }

    /**
     * 初始化
     *
     * @param view 要显示的模糊背景View,一般选择跟布局layout
     */
    public void init(View view) {
        this.indexDecView = view;
        Rect frame = new Rect();
        mContext.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        mContext.getWindow().getDecorView().getWindowVisibleDisplayFrame(frame);
        statusBarHeight = frame.top;
        DisplayMetrics metrics = new DisplayMetrics();
        mContext.getWindowManager().getDefaultDisplay()
                .getMetrics(metrics);
        mWidth = metrics.widthPixels;
        mHeight = metrics.heightPixels;


        setWidth(ScreenUtils.getScreenWidth());

        setHeight(ScreenUtils.getScreenHeight());

        layout = (RelativeLayout) LayoutInflater.from(mContext).inflate(R.layout.more_window, null);

        setContentView(layout);


        cardLl = (LinearLayout) layout.findViewById(R.id.card_ll);
        surfaceLl = (LinearLayout) layout.findViewById(R.id.surface_ll);
        singleLl = (LinearLayout) layout.findViewById(R.id.single_ll);
        int i = ScreenUtils.getScreenHeight() - SizeUtils.dp2px(136) - (SizeUtils.getMeasuredHeight(singleLl) * 3);
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) cardLl.getLayoutParams();
        layoutParams.topMargin = RomUtils.isSmartisan() ? (i / 2 + BarUtils.getStatusBarHeight()) : (i / 2);
        cardLl.setLayoutParams(layoutParams);
        LinearLayout.LayoutParams layoutParams1 = (LinearLayout.LayoutParams) surfaceLl.getLayoutParams();
        layoutParams1.topMargin = i / 5;
        surfaceLl.setLayoutParams(layoutParams1);
        LinearLayout.LayoutParams layoutParams2 = (LinearLayout.LayoutParams) singleLl.getLayoutParams();
        layoutParams2.topMargin = i / 5;
        singleLl.setLayoutParams(layoutParams2);

        RelativeLayout closeLayout = (RelativeLayout) layout.findViewById(R.id.bottom_rl);
        RelativeLayout.LayoutParams layoutParams4 = (RelativeLayout.LayoutParams) closeLayout.getLayoutParams();
        layoutParams4.topMargin = ScreenUtils.getScreenHeight() * 8/ 9;
        closeLayout.setLayoutParams(layoutParams4);

        cardBtn = (ImageView) layout.findViewById(R.id.card_btn);
        surfaceBtn = (ImageView) layout.findViewById(R.id.surface_btn);
        singleBtn = (ImageView) layout.findViewById(R.id.single_btn);
        importLL = (LinearLayout) layout.findViewById(R.id.import_ll);
        close = (ImageView) layout.findViewById(R.id.iv_close);

        cardBtn.setEnabled(false);
        cardBtn.setClickable(false);
        surfaceBtn.setEnabled(false);
        surfaceBtn.setClickable(false);
        singleBtn.setEnabled(false);
        singleBtn.setClickable(false);
        close.setEnabled(false);
        close.setClickable(false);
        importLL.setEnabled(false);
        importLL.setClickable(false);

        importLL.setOnClickListener(this);
        cardBtn.setOnClickListener(this);
        surfaceBtn.setOnClickListener(this);
        singleBtn.setOnClickListener(this);
        close.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (isShowing() && canClose.get()) {
                    canClose.set(false);
                    LogUtils.e("点击关闭");
                    closeAnimation();
                }
            }

        });

        blurringIV = (ImageView) layout.findViewById(R.id.more_blurring_imageView);

        bgView = layout.findViewById(R.id.rel);


        setOutsideTouchable(false);
//
//        setFocusable(true);
        setClippingEnabled(false);//使popupwindow全屏显示
    }

    public static int getNavigationBarHeight(Activity context) {
        int result = 0;
        if (RomUtils.isLenovo()) {
            result = 0;
        } else if (context.getPackageManager().hasSystemFeature("com.oppo.feature.screen.heteromorphism")) {
            result = 0;
        } else {
//            LogUtils.e("xxxxx", hasNavBar(context),BarUtils.isSupportNavBar(),BarUtils.getNavBarHeight());
            if (BarUtils.isSupportNavBar()) {
                result = BarUtils.getNavBarHeight();

            }
        }
        LogUtils.e("xxxxx", result);
        return result;
    }

    /**
     * 获取是否有虚拟按键
     * 通过判断是否有物理返回键反向判断是否有虚拟按键
     *
     * @param context
     * @return
     */
    public static boolean checkDeviceHasNavigationBar(Context context) {

        boolean hasMenuKey = ViewConfiguration.get(context)
                .hasPermanentMenuKey();
        boolean hasBackKey = KeyCharacterMap
                .deviceHasKey(KeyEvent.KEYCODE_BACK);
        if (!hasMenuKey & !hasBackKey) {
            // 做任何你需要做的,这个设备有一个导航栏
            return true;
        }
        return false;
    }

    /**
     * 显示window动画
     *
     * @param anchor
     * @param blur
     */
    public void showMoreWindow(View anchor, Bitmap blur) {
        if (canClose.get()) {
            canClose.set(false);
            cardBtn.setEnabled(false);
            cardBtn.setClickable(false);
            surfaceBtn.setEnabled(false);
            surfaceBtn.setClickable(false);
            singleBtn.setEnabled(false);
            singleBtn.setClickable(false);
            close.setEnabled(false);
            close.setClickable(false);
            importLL.setEnabled(false);
            importLL.setClickable(false);
        }
        blurringIV.setImageBitmap(blur);
        showAtLocation(anchor, Gravity.TOP | Gravity.START, 0, 0);
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                ObjectAnimator animator = ObjectAnimator.ofFloat(bgView, "alpha", 0f, 1f);
                animator.setDuration(400);
                animator.start();
            }
        });

        showAnimation(layout);


    }

    private void showAnimation(ViewGroup layout) {
        try {
            LinearLayout linearLayout = layout.findViewById(R.id.lin);
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    //＋ 旋转动画
                    close.animate().rotation(90).setDuration(400);
                }
            });
            //菜单项弹出动画
            for (int i = 0; i < linearLayout.getChildCount(); i++) {
                final View child = linearLayout.getChildAt(i);
                child.setVisibility(View.INVISIBLE);
                int finalI = i;
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        child.setVisibility(View.VISIBLE);


                        if (finalI == 0) {
                            ValueAnimator fadeAnim = ObjectAnimator.ofFloat(child, "translationY", 750, 0);
                            fadeAnim.setDuration(400);
                            KickBackAnimator2 kickAnimator = new KickBackAnimator2();
                            kickAnimator.setDuration(250);
                            fadeAnim.setEvaluator(kickAnimator);
                            fadeAnim.start();
                        } else if (finalI == 1) {
                            ValueAnimator fadeAnim = ObjectAnimator.ofFloat(child, "translationY", 950, 0);
                            fadeAnim.setDuration(400);
                            KickBackAnimator1 kickAnimator = new KickBackAnimator1();
                            kickAnimator.setDuration(250);
                            fadeAnim.setEvaluator(kickAnimator);
                            fadeAnim.start();
                        } else {
                            ValueAnimator fadeAnim = ObjectAnimator.ofFloat(child, "translationY", 1150, 0);
                            fadeAnim.setDuration(400);
                            KickBackAnimator kickAnimator = new KickBackAnimator();
                            kickAnimator.setDuration(250);
                            fadeAnim.setEvaluator(kickAnimator);
                            fadeAnim.addListener(new Animator.AnimatorListener() {
                                @Override
                                public void onAnimationStart(Animator animator) {

                                }

                                @Override
                                public void onAnimationEnd(Animator animator) {

                                    new Handler().postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            cardBtn.setEnabled(true);
                                            cardBtn.setClickable(true);
                                            surfaceBtn.setEnabled(true);
                                            surfaceBtn.setClickable(true);
                                            singleBtn.setEnabled(true);
                                            singleBtn.setClickable(true);
                                            close.setEnabled(true);
                                            close.setClickable(true);
                                            importLL.setEnabled(true);
                                            importLL.setClickable(true);
                                            canClose.set(true);
                                        }
                                    }, 200);


                                }

                                @Override
                                public void onAnimationCancel(Animator animator) {

                                }

                                @Override
                                public void onAnimationRepeat(Animator animator) {

                                }
                            });
                            fadeAnim.start();
                        }


                    }
                }, i * 50 + 100);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public interface IndexAnimFinishedListener {
        void onFinish();
    }

    public IndexAnimFinishedListener indexAnimFinishedListener;

    public void setIndexAnimFinishedListener(IndexAnimFinishedListener indexAnimFinishedListener) {
        this.indexAnimFinishedListener = indexAnimFinishedListener;
    }

    /**
     * 关闭window动画
     */
    private void closeAnimation() {
        LogUtils.e("关闭动画");


        ObjectAnimator animator = ObjectAnimator.ofFloat(bgView, "alpha", 1f, 0f);
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation, boolean isReverse) {
                canClose.set(false);
            }

            @Override
            public void onAnimationEnd(Animator animation, boolean isReverse) {


            }
        });
        animator.setDuration(500);
        animator.start();
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {

                onfinish();
                dismiss();
                bgView.setAlpha(1f);
                close.setRotation(0);
            }
        }, 520);

    }

    private void onfinish() {

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                canClose.set(false);
                if (indexAnimFinishedListener != null) {
                    indexAnimFinishedListener.onFinish();
                }
            }
        }, 200);
    }

    long clickTime = 0;
    /**
     * 点击事件处理
     *
     * @param v
     */
    @Override
    public void onClick(View v) {
        if (isShowing()) {
            closeAnimation();
        }

        if (System.currentTimeMillis() - clickTime < 1000) {
            // 禁止两个连点
            return;
        }
        clickTime = System.currentTimeMillis();


        int id = v.getId();
        if (id == R.id.card_btn) {
            if (onMainDialogOnClickListener != null) {
                onMainDialogOnClickListener.onClick(R.id.card_btn);
            }
        } else if (id == R.id.surface_btn) {
            if (onMainDialogOnClickListener != null) {
                onMainDialogOnClickListener.onClick(R.id.surface_btn);
            }
        } else if (id == R.id.single_btn) {
            if (onMainDialogOnClickListener != null) {
                onMainDialogOnClickListener.onClick(R.id.single_btn);
            }
        } else if (id == R.id.import_ll) {
            if (onMainDialogOnClickListener != null) {
                onMainDialogOnClickListener.onClick(R.id.import_ll);
            }
        } else if (id == R.id.rel_close) {
        }


    }

    /**
     * 点击事件接口
     **/
    public interface OnMainDialogOnClickListener {
        /**
         * @param viewId
         */
        void onClick(int viewId);
    }

    private OnMainDialogOnClickListener onMainDialogOnClickListener;

    public void setOnMainDialogOnClickListener(OnMainDialogOnClickListener onMainDialogOnClickListener) {
        this.onMainDialogOnClickListener = onMainDialogOnClickListener;

    }

    float fromDpToPx(float dp) {
        return dp * Resources.getSystem().getDisplayMetrics().density;
    }


}