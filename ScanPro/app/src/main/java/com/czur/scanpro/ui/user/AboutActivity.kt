package com.czur.scanpro.ui.user

import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityAdjustEdgeBinding
import com.czur.scanpro.databinding.ActivityUserAboutBinding
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.UpdateEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.component.popup.UpdatePopup
import com.czur.scanpro.ui.download.DownloadApkService
import com.czur.scanpro.utils.getAppUpdateUrl
import okhttp3.OkHttpClient
import okhttp3.Request
import org.greenrobot.eventbus.EventBus
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.util.*

/**
 * Created by Yz on 2019/3/14.
 * Email：<EMAIL>
 */
class AboutActivity : BaseActivity(), View.OnClickListener {
    val binding: ActivityUserAboutBinding by lazy{
        ActivityUserAboutBinding.inflate(layoutInflater)
    }

    private var httpManager: HttpManager? = null

    private var updateUrl: String? = null
    private var notes: String? = null
    private var version: String? = null
    private var apkPath: String? = null
    private var apkName: String? = null
    private var isNotLatest: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_user_about)
        setContentView(binding.root)
        initComponent()
        registerEvent()
        if (NetworkUtils.isConnected()) {
            checkUpdate()
        }

    }

    private fun initComponent() {


        httpManager = HttpManager.getInstance()
        binding.userTitle.setText(R.string.about)
        binding.versionTv.text = String.format(getString(R.string.version), AppUtils.getAppVersionName())
    }

    private fun registerEvent() {
        binding.userBackBtn.setOnClickListener(this)
        binding.updateRl.setOnClickListener(this)
    }

    /**
     * @des: 检查更新
     * @params:
     * @return:
     */

    private fun checkUpdate() {
        Thread(Runnable {
            val clearCacheStr = "?" + UUID.randomUUID().toString()
            val checkRequest = Request.Builder().url(getString(getAppUpdateUrl()) + clearCacheStr).get().build()
            val checkCall = MiaoHttpManager.getInstance().client.newCall(checkRequest)
            try {
                val checkResponse = checkCall.execute()
                if (checkResponse.isSuccessful) {

                    val packageInfo = packageManager.getPackageInfo(packageName, 0)
                    val jsonObject = JSONObject(checkResponse.body!!.string())
                    LogUtils.i("new version : $jsonObject")
                    updateUrl = jsonObject.getString("package")
                    notes = jsonObject.getString("notes")
                    version = jsonObject.getString("version")

                    apkPath = (getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.APK_PATH
                            + getString(R.string.app_name) + "_" + version + ".apk")
                    apkName = getString(R.string.app_name) + "_" + version + ".apk"
                    LogUtils.i(packageInfo.versionCode, jsonObject.getInt("build"))
                    if (packageInfo.versionCode < jsonObject.getInt("build")) {
                        EventBus.getDefault().post(UpdateEvent(EventType.HAS_NEW_VERSION))
                        runOnUiThread {
                            if (FileUtils.isFileExists(apkPath)) {
                                LogUtils.i(apkPath, " apk is exist!")
                                showUpdatePopup(true)
                            } else {
                                showUpdatePopup(false)

                            }
                            isNotLatest = true
                            binding.newVersionTv.visibility = View.VISIBLE }

                    } else {
                        EventBus.getDefault().post(UpdateEvent(EventType.IS_LATEST_VERSION))

                        runOnUiThread {
                            isNotLatest = false
                            binding.newVersionTv.visibility = View.GONE }


                    }

                }
            } catch (e: IOException) {
                LogUtils.e(e)
            } catch (e: PackageManager.NameNotFoundException) {
                LogUtils.e(e)
            } catch (e: JSONException) {
                LogUtils.e(e)
            }
        }).start()

    }

    private fun showUpdatePopup(isFileExist: Boolean) {

        val builder = UpdatePopup.Builder(this, CloudCommonPopupConstants.UPDATE_ONE_BUTTON)
        builder.setTitle(getString(R.string.app_update_title))
        builder.setMessage(notes)
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            if (!ServiceUtils.isServiceRunning(DownloadApkService::class.java)) {
                if (isFileExist) {
                    showInstallPopup()
                } else {
                    if (NetworkUtils.isWifiConnected()) {
                        showLongMessage(R.string.wifi_download)
                        val intent = Intent(this@AboutActivity, DownloadApkService::class.java)
                        intent.putExtra("updateUrl", updateUrl)
                        intent.putExtra("notes", notes)
                        intent.putExtra("apkName", apkName)
                        startService(intent)
                    } else {
                        showLongMessage(R.string.network_download)

                    }
                }

            }
            dialog.dismiss()
        })
        val commonPopup = builder.create()

        commonPopup.show()
    }

    private fun showInstallPopup() {

        val builder = ScanProCommonPopup.Builder(this, CloudCommonPopupConstants.INSTALL_ONE_BUTTON)
        builder.setTitle(getString(R.string.download_complete))
        builder.setMessage(String.format(getString(R.string.install_now), getString(R.string.app_name) + version))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            AppUtils.installApp(apkPath)
            dialog.dismiss()
        })
        val commonPopup = builder.create()

        commonPopup.show()
    }

    override fun onClick(v: View) {
        when (v.id) {

            R.id.userBackBtn -> ActivityUtils.finishActivity(this)
            R.id.updateRl -> {
                if (!isNotLatest) {
                    showMessage(R.string.already_latest_version)
                } else {
                    if (FileUtils.isFileExists(apkPath)) {
                        LogUtils.i(apkPath, " apk is exist!")
                        showUpdatePopup(true)
                    } else {
                        showUpdatePopup(false)
                    }
                }
            }
            else -> {
            }
        }
    }


}