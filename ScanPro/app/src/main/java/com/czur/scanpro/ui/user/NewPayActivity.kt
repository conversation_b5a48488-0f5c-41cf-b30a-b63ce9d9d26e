package com.czur.scanpro.ui.user

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import androidx.recyclerview.widget.LinearLayoutManager
import android.util.Log
import android.view.View
import com.alipay.sdk.app.PayTask
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.adapter.NewPayAdapter
import com.czur.scanpro.adapter.VipDetailAdapter
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityInviteResultBinding
import com.czur.scanpro.databinding.ActivityNewPayBinding
import com.czur.scanpro.entity.model.ProductModel
import com.czur.scanpro.entity.model.UserAllInfoModel
import com.czur.scanpro.entity.model.pay.AlipayResultModel
import com.czur.scanpro.entity.model.pay.PayCheckOrderModel
import com.czur.scanpro.entity.model.pay.PayOrderModel
import com.czur.scanpro.entity.model.pay.SaoMiaoPrice
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.PayEvent
import com.czur.scanpro.event.WechatPayEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.HttpServices
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.SaomiaoApplication
import com.czur.scanpro.ui.base.WebViewActivity
import com.czur.scanpro.utils.ktExtension.fromJson
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tencent.mm.opensdk.modelpay.PayReq
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Request
import okhttp3.Response
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.IOException
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

class NewPayActivity : BaseActivity(), View.OnClickListener {


    private val binding: ActivityNewPayBinding by lazy{
        ActivityNewPayBinding.inflate(layoutInflater)
    }


    private lateinit var userPreferences: UserPreferences
    private var productList: List<ProductModel>? = null
    private lateinit var product: ProductModel
    private var isWechatPay = true
    private val handler = Handler()
    private lateinit var payOrderModel: PayOrderModel
    private var isGoWechat = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_new_pay)
        setContentView(binding.root)
        initView()
        initListener()
        initData()
    }

    private fun initView() {
        val layoutManager =
            LinearLayoutManager(this)
        layoutManager.orientation = LinearLayoutManager.HORIZONTAL
        binding.recyclerviewProduct.layoutManager = layoutManager
        binding.recyclerviewProduct.setHasFixedSize(true)
        binding.rlVip.setOnClickListener {
            if (binding.viewVipIndexp.visibility != View.VISIBLE) {
                binding.viewVipIndexp.visibility = View.VISIBLE
                binding.viewSvipIndex.visibility = View.GONE
                if (productList != null) {
                    createList(1, 0)
                } else {
                    getProductInfo(1, 0)
                }
            }
        }
        binding.rlSvip.setOnClickListener {
            if (binding.viewSvipIndex.visibility != View.VISIBLE) {
                binding.viewSvipIndex.visibility = View.VISIBLE
                binding.viewVipIndexp.visibility = View.GONE
                if (productList == null) {
                    getProductInfo(1, 1)
                } else {
                    createList(1, 1)
                }
            }
        }
        initRecyclerViews()
    }

    private fun initListener() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        binding.tvBuy.setOnClickListener(this)
        binding.tvGetFree.setOnClickListener(this)
        binding.tvRenewal.setOnClickListener(this)
        binding.userBackBtn.setOnClickListener(this)
        binding.wechatRl.setOnClickListener(this)
        binding.alipayRl.setOnClickListener(this)
        binding.callPhone.setOnClickListener(this)
        binding.tvVipAgreement.setOnClickListener(this)
    }

    private fun initRecyclerViews() {
        binding.recyclerview0.layoutManager =
            LinearLayoutManager(this)
        binding.recyclerview0.setHasFixedSize(true)
        binding.recyclerview0.isNestedScrollingEnabled = false
        binding.recyclerview1.layoutManager =
            LinearLayoutManager(this)
        binding.recyclerview1.setHasFixedSize(true)
        binding.recyclerview1.isNestedScrollingEnabled = false
        binding.recyclerview2.layoutManager =
            LinearLayoutManager(this)
        binding.recyclerview2.setHasFixedSize(true)
        binding.recyclerview2.isNestedScrollingEnabled = false
        binding.recyclerview3.layoutManager =
            LinearLayoutManager(this)
        binding.recyclerview3.setHasFixedSize(true)
        binding.recyclerview3.isNestedScrollingEnabled = false
        getVipDetail()
    }

    private fun initData() {
        userPreferences = UserPreferences.getInstance(this)
        checkLogin()
        binding.viewVipIndexp.visibility = View.VISIBLE
        binding.viewSvipIndex.visibility = View.GONE
        getProductInfo(1, 0)
    }

    private fun checkLogin() {
        if (userPreferences.isValidUser) {
            if (userPreferences.userPhoto.isNullOrEmpty()) {
                binding.payUserHeadImg.setImageResource(R.mipmap.user_login_icon)
            } else {
                binding.payUserHeadImg.setImageURI(userPreferences.userPhoto)
            }
            binding.payUserName.text = userPreferences.userName
            if (userPreferences.isVip) {
                binding.llBuy.visibility = View.GONE
                binding.tvRenewal.visibility = View.VISIBLE
                if (userPreferences.userType == "svip") {
                    binding.payUserVipImg.setImageResource(R.mipmap.img_svip)
                    val layoutParams = binding.payUserVipImg.layoutParams
                    layoutParams.width = SizeUtils.dp2px(40f)
                    binding.payUserVipImg.layoutParams = layoutParams
                    binding.payAdvanceDate.text = String.format(getString(R.string.pay_date_format), "SVIP", userPreferences.svipEndOn?.substring(0, 10))
                } else {
                    val layoutParams = binding.payUserVipImg.layoutParams
                    layoutParams.width = SizeUtils.dp2px(26f)
                    binding.payUserVipImg.layoutParams = layoutParams
                    binding.payUserVipImg.setImageResource(R.mipmap.img_vip)
                    if (userPreferences.userType == "vip") {
                        binding.payAdvanceDate.text = String.format(getString(R.string.pay_date_format), "VIP", userPreferences.vipEndOn?.substring(0, 10))
                    }else{
                        binding.payAdvanceDate.text = String.format(getString(R.string.pay_date_format), "高级权限", userPreferences.vipEndOn?.substring(0, 10))
                    }
                }
                if (userPreferences.userType == "freevip") {
                    binding.tvRenewal.text = (getString(R.string.subscribe_right_now))
                } else {
                    binding.tvRenewal.text = (getString(R.string.renewal))
                }
                binding.payUserVipImg.visibility = View.VISIBLE
                binding.tvNoVip.visibility = View.GONE
                binding.payAdvanceDate.visibility = View.VISIBLE
            } else {
                binding.llBuy.visibility = View.VISIBLE
                binding.tvRenewal.visibility = View.GONE
                binding.payAdvanceDate.visibility = View.GONE
                binding.payUserVipImg.visibility = View.GONE
                binding.tvNoVip.visibility = View.VISIBLE
            }
        } else {
            binding.payUserHeadImg.setImageResource(R.mipmap.user_not_login_icon)
        }
    }



    private fun getProductInfo(type: Int, vipType: Int) {
        val type = object : ParameterizedType {
            override fun getRawType(): Type = List::class.java
            override fun getOwnerType(): Type? = null
            override fun getActualTypeArguments(): Array<Type> = arrayOf(ProductModel::class.java)
        }
        HttpManager.getInstance().request().getProductList("com.czur.ScanPro", "android", type, object : MiaoHttpManager.CallbackNetwork<ProductModel> {
                override fun onNoNetwork() {
            }

            override fun onStart() {
            }

            override fun onResponse(entity: MiaoHttpEntity<ProductModel>) {
                if (entity.bodyList != null) {
                    productList = entity.bodyList
                    createList(1, 0)
                }
            }

            override fun onFailure(entity: MiaoHttpEntity<ProductModel>) {
                showMessage(R.string.request_failed_alert)
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    private fun getVipDetail() {
        showProgressDialog()
        val checkRequest = Request.Builder().url(getString(R.string.price_url)).get().build()
        val checkCall = MiaoHttpManager.getInstance().client.newCall(checkRequest)
        checkCall.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    hideProgressDialog()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val saoMiaoPrice = Gson().fromJson(response.body?.string(), SaoMiaoPrice::class.java)
                runOnUiThread {
                    hideProgressDialog()
                    if (saoMiaoPrice != null) {
                        val list = saoMiaoPrice.VIPVSGeneral
                        val list0 = ArrayList<String>()
                        list0.add(list[0].title)
                        list0.addAll(list[0].content)
                        binding.recyclerview0.adapter = VipDetailAdapter(this@NewPayActivity, list0)
                        val list1 = ArrayList<String>()
                        list1.add(list[1].title)
                        list1.addAll(list[1].content)
                        binding.recyclerview1.adapter = VipDetailAdapter(this@NewPayActivity, list1)

                        val list2 = ArrayList<String>()
                        list2.add(list[2].title)
                        list2.addAll(list[2].content)
                        binding.recyclerview2.adapter = VipDetailAdapter(this@NewPayActivity, list2)

                        val list3 = ArrayList<String>()
                        list3.add(list[3].title)
                        list3.addAll(list[3].content)
                        binding.recyclerview3.adapter = VipDetailAdapter(this@NewPayActivity, list3)
                    }
                }
            }
        })
    }


    private fun createList(type: Int, vipType: Int) {
        val list: MutableList<ProductModel> = ArrayList()
        productList?.forEach {
            if (it.type == type && it.vipType == vipType) {
                list.add(it)
            }
        }
        list.forEach {
            it.isSelect = list.indexOf(it) == 0
        }
        //初始化默认第一个
        product = list[0]
        setProduct()
        val adapter = NewPayAdapter(this@NewPayActivity, list)
        adapter.setOnItemClickListener(object : NewPayAdapter.OnItemClickListener {
            override fun onPayItemClick(ProductModel: ProductModel, position: Int) {
                product = ProductModel
                setProduct()
            }
        })
        binding.recyclerviewProduct.adapter = adapter
    }

    private fun setProduct() {
        if (product.showPrice == product.price) {
            if (product.vipType == 0) {
                binding.tvSubscribe.text = String.format(getString(R.string.subscribe_detail_without_discount), "VIP" + product.name)
            } else {
                binding.tvSubscribe.text = String.format(getString(R.string.subscribe_detail_without_discount), "SVIP" + product.name)
            }
        } else {
            if (product.vipType == 0) {
                binding.tvSubscribe.text = String.format(getString(R.string.subscribe_detail), "VIP" + product.name, String.format("%.1f", product.price * 10 / product.showPrice))
            } else {
                binding.tvSubscribe.text = String.format(getString(R.string.subscribe_detail), "SVIP" + product.name, String.format("%.1f", product.price * 10 / product.showPrice))
            }
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.tvBuy -> {
                pay()
            }
            R.id.tvGetFree -> {
                ActivityUtils.startActivity(InputInviteActivity::class.java)
            }
            R.id.tvRenewal -> {
                pay()
            }
            R.id.userBackBtn -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.wechatRl -> {
                isWechatPay = true
                binding.wechatImg.setImageResource(R.mipmap.red_check_selected)
                binding.alipayImg.setImageResource(R.mipmap.red_check_unselected)
            }
            R.id.alipayRl -> {
                isWechatPay = false
                binding.alipayImg.setImageResource(R.mipmap.red_check_selected)
                binding.wechatImg.setImageResource(R.mipmap.red_check_unselected)
            }
            R.id.callPhone -> {
                val intent = Intent().apply {
                    action = Intent.ACTION_DIAL
                    data = Uri.parse("tel:4008507919")
                }
                this.startActivity(intent)
            }
            R.id.tvVipAgreement ->{
                val intent = Intent(this, WebViewActivity::class.java)
                intent.putExtra("title", getString(R.string.user_agree_vip))
                intent.putExtra("url", Constants.VIP_AGREEMENT)
                ActivityUtils.startActivity(intent)
            }
        }
    }

    private fun pay() {
        product.let {
            HttpManager.getInstance().request().createPayOrder(UserPreferences.getInstance(this)!!.userId, it.productId,
                    (if (isWechatPay) HttpServices.PayWay.Wechat else HttpServices.PayWay.Alipay).payWay, PayOrderModel::class.java,
                    object : MiaoHttpManager.Callback<PayOrderModel?> {
                        override fun onFailure(entity: MiaoHttpEntity<PayOrderModel?>) {
                            hideProgressDialog()
                            showMessage(R.string.pay_defeat)
                        }

                        override fun onResponse(entity: MiaoHttpEntity<PayOrderModel?>) {
                            if (isWechatPay) {
                                wechatPay(entity.body!!)
                            } else {
                                alipay(entity.body!!)
                            }
                        }

                        override fun onError(e: java.lang.Exception?) {
                            hideProgressDialog()
                            showMessage(R.string.pay_defeat)
                        }

                        override fun onStart() {
                            showProgressDialog(false)
                        }
                    }
            )
        }
    }

    private fun alipay(order: PayOrderModel) {
        Thread(Runnable {
            val payTask = PayTask(this)
            val resultMap = payTask.payV2(order.alipayResponse, true)
            val resultStatus = resultMap["resultStatus"]
            val result = resultMap["result"]
            if ("9000" == resultStatus) {
                val resultModel: AlipayResultModel = Gson().fromJson<AlipayResultModel>(result!!)
                val isSuccess = checkPayOrder(resultModel.alipay_trade_app_pay_response.trade_no, resultModel.alipay_trade_app_pay_response.out_trade_no, HttpServices.PayWay.Alipay)
                handler.post {
                    // TODO 刷新画面中VIP时间
                    if (isSuccess) {
                        showMessage(R.string.pay_success)
                        requestUserInfoNow()
                    } else {
                        showMessage(R.string.pay_defeat)
                    }
                    hideProgressDialog()
                }
            } else {
                handler.post {
                    showMessage(R.string.pay_defeat)
                    hideProgressDialog()
                }
            }
        }).start()
    }

    private fun wechatPay(order: PayOrderModel) {
        payOrderModel = order
        val api = (application as SaomiaoApplication).wechatPayApi()
        val payRequest = PayReq()
        payRequest.appId = order.appid
        payRequest.partnerId = order.partnerid
        payRequest.prepayId = order.prepayid
        payRequest.packageValue = "Sign=WXPay"
        payRequest.nonceStr = order.noncestr
        payRequest.timeStamp = order.timestamp
        payRequest.sign = order.sign
        if (!api.sendReq(payRequest)) {
            hideProgressDialog()
            ToastUtils.showShort(R.string.WEXIN_NOT_INSTALL)
        } else {
            isGoWechat = true
        }
    }

    private fun checkPayOrder(transactionId: String, orderId: String, payWay: HttpServices.PayWay): Boolean {
        return try {
            val result: MiaoHttpEntity<PayCheckOrderModel> = HttpManager.getInstance().request().checkPayOrder(
                    UserPreferences.getInstance(this)!!.userId, transactionId, orderId, payWay.payWay, PayCheckOrderModel::class.java)
            result.code == MiaoHttpManager.STATUS_SUCCESS
        } catch (e: java.lang.Exception) {
            e.message?.let { Log.e("PayActivity", it) }
            false
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            EventType.WECHAT_PAY_CALLBACK -> {
                val wechatPayEvent = event as WechatPayEvent
                when (wechatPayEvent.errCode) {
                    // 成功
                    0 -> {
                        Thread(Runnable {
                            val checkResult = checkPayOrder(payOrderModel.prepayid, payOrderModel.orderId, HttpServices.PayWay.Wechat)
                            handler.post {
                                if (checkResult) {
                                    // TODO 刷新画面中VIP时间
                                    showMessage(R.string.pay_success)
                                    requestUserInfoNow()
                                } else {
                                    showMessage("检查订单失败")
                                }
                                hideProgressDialog()
                            }
                        }).start()
                    }
                    // 错误
                    -1 -> {
                        hideProgressDialog()
                        showMessage(R.string.pay_defeat)
                    }
                    // 取消
                    -2 -> {
                        hideProgressDialog()
                        showMessage(R.string.pay_cancel)
                    }
                }
            }
            else -> {
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (isGoWechat) {
            hideProgressDialog()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    /**
     * @des: 请求用户信息
     * @params:
     * @return:
     */

    private fun requestUserInfoNow() {
        HttpManager.getInstance().request().userAllInfo(userPreferences.userId, UserAllInfoModel::class.java, object : MiaoHttpManager.CallbackNetwork<UserAllInfoModel> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<UserAllInfoModel>) {
                hideProgressDialog()
                LogUtils.i(Gson().toJson(entity.body))
                userPreferences.usages = entity.body.usages
                userPreferences.usagesLimit = entity.body.usagesLimit
                userPreferences.photoOssKey = entity.body.photoOssKey
                userPreferences.isInvited = entity.body.isIsInvitate
                userPreferences.isVip = entity.body.isIsVip
                userPreferences.userType = entity.body.userType
                userPreferences.vipEndOn = entity.body.vipEndOn
                userPreferences.svipEndOn = entity.body.svipEndOn
                userPreferences.inviteCode = entity.body.invitationCode
                userPreferences.inviteCount = entity.body.invitationCount
                userPreferences.remainOcr = entity.body.remainingOcr
                userPreferences.remainCard = entity.body.remainingCertificate
                userPreferences.remainVip = entity.body.remainingVip
                userPreferences.remainPdf = entity.body.remainingPdf
                if (userPreferences.isVip) {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrVip
                    userPreferences.remainHandwriting = entity.body.remainingHandwriting
                } else {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrNormal
                    userPreferences.remainHandwriting = entity.body.remainingHandwritingNormal
                }
                checkLogin()
                EventBus.getDefault().post(PayEvent(EventType.PAY_SUCCESS))
            }

            override fun onFailure(entity: MiaoHttpEntity<UserAllInfoModel>) {
                showMessage(R.string.request_failed_alert)
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }
}