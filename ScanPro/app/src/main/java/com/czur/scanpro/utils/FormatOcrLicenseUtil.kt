package com.czur.scanpro.utils

import com.czur.scanpro.entity.model.BusinessLicenseModel
import org.json.JSONObject

object FormatOcrLicenseUtil {
     fun formatLicenseStr(dataBean: BusinessLicenseModel.DataBean, data: String): String {
        var resultStr = ""
        //String转为json格式
        val jsonObject = JSONObject(data)
        //判断json中是否有字段RegNum

        if (jsonObject.has("RegNum")){
            resultStr += "统一社会信用代码：${dataBean.regNum}\n"
        }
        if (jsonObject.has("Name")){
            resultStr += "名称：${dataBean.name}\n"
        }
        if (jsonObject.has("Capital")){
            resultStr += "注册资本：${dataBean.capital}\n"
        }
        if (jsonObject.has("Person")){
            resultStr += "法定代表人：${dataBean.person}\n"
        }
        if (jsonObject.has("Address")){
            resultStr += "地址：${dataBean.address}\n"
        }
        if (jsonObject.has("Business")){
            resultStr += "经营范围：${dataBean.business}\n"
        }
        if (jsonObject.has("Type")){
            resultStr += "主体类型：${dataBean.type}\n"
        }
        if (jsonObject.has("Period")){
            resultStr += "营业期限：${dataBean.period}\n"
        }
        if (jsonObject.has("ComposingForm")){
            resultStr += "组成形式：${dataBean.composingForm}\n"
        }
        if (jsonObject.has("SetDate")){
            resultStr += "成立日期：${dataBean.setDate}\n"
        }
        if (jsonObject.has("RegistrationDate")){
            resultStr += "登记日期：${dataBean.registrationDate}\n"
        }
        return resultStr
    }
}