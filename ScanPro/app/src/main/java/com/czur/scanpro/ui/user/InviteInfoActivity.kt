package com.czur.scanpro.ui.user

import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.scanpro.R
import com.czur.scanpro.databinding.ActivityInviteInfoBinding
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.ui.base.BaseActivity

/**
 * Created by <PERSON>z on 2019/3/14.
 * Email：<EMAIL>
 */
class InviteInfoActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityInviteInfoBinding by lazy {
        ActivityInviteInfoBinding.inflate(layoutInflater)
    }

    private var httpManager: HttpManager? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_invite_info)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        httpManager = HttpManager.getInstance()
        binding.registerTopBar.normalTitle.setText(R.string.rule_info)
    }

    private fun registerEvent() {
        binding.registerTopBar.normalBackBtn.setOnClickListener(this)

    }


    override fun onClick(v: View) {
        when (v.getId()) {
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)

            else -> {
            }

        }
    }


}