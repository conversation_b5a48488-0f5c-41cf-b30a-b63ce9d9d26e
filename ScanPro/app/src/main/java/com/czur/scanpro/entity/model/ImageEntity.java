package com.czur.scanpro.entity.model;

import android.os.Parcel;
import android.os.Parcelable;

public class ImageEntity implements Parcelable {
    private String id;
    private String fileId;
    private String imagePath;
    private String position;
    @Override
    public String toString() {
        return "ImageEntity{" +
                "id='" + id + '\'' +
                ", fileId='" + fileId + '\'' +
                ", imagePath='" + imagePath + '\'' +
                ", position='" + position + '\'' +
                '}';
    }



    public ImageEntity(){
    }

    public ImageEntity(String fileId,String imagePath){
        this.fileId = fileId;
        this.imagePath=imagePath;
    }

    protected ImageEntity(Parcel in) {
        id = in.readString();
        fileId = in.readString();
        imagePath = in.readString();
        position = in.readString();
    }

    public static final Creator<ImageEntity> CREATOR = new Creator<ImageEntity>() {
        @Override
        public ImageEntity createFromParcel(Parcel in) {
            ImageEntity Image = new ImageEntity();
            Image.setId(in.readString());
            Image.setFileId(in.readString());
            Image.setImagePath(in.readString());
            Image.setPosition(in.readString());
            return Image;
        }

        @Override
        public ImageEntity[] newArray(int size) {
            return new ImageEntity[size];
        }
    };

    public String getFileId() {
        return fileId;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel out, int flags) {
        out.writeString(id);
        out.writeString(fileId);
        out.writeString(imagePath);
        out.writeString(position);
    }
}
