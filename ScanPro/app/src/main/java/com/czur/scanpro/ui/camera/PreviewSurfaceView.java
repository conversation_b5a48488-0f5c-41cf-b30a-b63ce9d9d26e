package com.czur.scanpro.ui.camera;

import android.content.Context;
import android.graphics.Rect;
import android.hardware.Camera;
import android.os.Handler;
import androidx.annotation.IntDef;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.SurfaceView;

import com.blankj.utilcode.util.ScreenUtils;

import java.util.Calendar;


/**
 * SurfaceView to show LenxCameraPreview2 feed
 */
public class PreviewSurfaceView extends SurfaceView {

    public static final int FOCUS_WHITE = 0;
    public static final int FOCUS_GREEN = 1;
    public static final int FOCUS_RED = 2;
    private float x;
    private float y;

    @IntDef({FOCUS_WHITE, FOCUS_GREEN, FOCUS_RED})
    public @interface FocusColor {
    }

    private CameraPreview camPreview;
    private boolean isTouchFocus = false;
    private FocusView focusView;
    private boolean isDraw = false;
    private Handler handler = new Handler();
    private Rect touchRect;

    public PreviewSurfaceView(Context context) {
        this(context, null);
    }

    public PreviewSurfaceView(Context context, AttributeSet attrs) {
        super(context, attrs);
//        getHolder().addCallback(cameraPreview);
//        getHolder().setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), MeasureSpec.getSize(heightMeasureSpec));
    }

    /**
     * 加入防止多次点击引起native层异常
     */
    public static final int MIN_CLICK_DELAY_TIME = 1000;
    long upTime = 0;
    long downTime = 0;
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!isTouchFocus) {
            return false;
        }
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            downTime = Calendar.getInstance().getTimeInMillis();
            if (downTime -upTime >MIN_CLICK_DELAY_TIME) {
                x = event.getX();
                y = event.getY();
                float chang = ScreenUtils.getScreenDensity() * 20;
                touchRect = new Rect((int) (x - chang), (int) (y - chang), (int) (x + chang), (int) (y + chang));
                Rect targetFocusRect = new Rect(
                        touchRect.left * 2000 / this.getWidth() - 1000,
                        touchRect.top * 2000 / this.getHeight() - 1000,
                        touchRect.right * 2000 / this.getWidth() - 1000,
                        touchRect.bottom * 2000 / this.getHeight() - 1000);
                if (isDraw) {
                    focusView.show(touchRect, 0xEEFFFFFF);
                }
                camPreview.doTouchFocus(targetFocusRect, focusView, touchRect);
            }
        }
        if (event.getAction() == MotionEvent.ACTION_UP) {
            upTime = Calendar.getInstance().getTimeInMillis();
        }
        return false;
    }


    public void hideFocusView() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                focusView.hide();
            }
        }, 500);
    }

    private class CustomAutoFocusCallback implements Camera.AutoFocusCallback {

        private Rect rect;

        public CustomAutoFocusCallback(Rect rect) {
            this.rect = rect;
        }

        @Override
        public void onAutoFocus(boolean success, Camera camera) {
            if (success) {
                focusView.show(rect, 0xFF00FF00);
                camera.cancelAutoFocus();
            } else {
                focusView.show(rect, 0x7300FF00);
            }
            hideFocusView();
        }
    }


    /**
     * set CameraPreview instance for touch focus.
     *
     * @param camPreview - CameraPreview
     */
    public void setListener(CameraPreview camPreview) {
        this.camPreview = camPreview;
        isTouchFocus = true;
    }

    /**
     * set DrawingView instance for touch focus indication.
     *
     * @param dView - DrawingView
     */
    public void setFocusView(FocusView dView) {
        focusView = dView;
        isDraw = true;
    }

}