package com.czur.scanpro.ui.component.popup;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.DecelerateInterpolator;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.blankj.utilcode.util.BarUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.utils.validator.StringUtils;


/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class ChangeFileNamePopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public ChangeFileNamePopup(Context context) {
        super(context);
    }

    public ChangeFileNamePopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder {
        private Context context;
        private CloudCommonPopupConstants constants;

        private String title;
        private boolean isAddCategory;
        private LongPressDialog pressDialog;
        private View contentsView;

        private OnClickListener positiveListener;

        public Builder(Context context, CloudCommonPopupConstants constants) {
            this.context = context;
            this.constants = constants;
        }
        public Builder setPressDialog(LongPressDialog pressDialog) {
            this.pressDialog = pressDialog;
            return this;
        }
        public Builder setIsAddCategory(boolean isAddCategory) {
            this.isAddCategory = isAddCategory;
            return this;
        }
        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnPositiveListener(OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }



        public ChangeFileNamePopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final ChangeFileNamePopup dialog = new ChangeFileNamePopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            BarUtils.setStatusBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode( dialog.getWindow(), true);

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final ChangeFileNamePopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.6f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_change_file_name, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);

            startAnim(layout);

            TextView title = (TextView) layout.findViewById(R.id.title);
            ImageView backBtn = (ImageView) layout.findViewById(R.id.add_tag_back_btn);
            TextView positiveBtn = (TextView) layout.findViewById(R.id.positive_button);
            EditText editText= (EditText) layout.findViewById(R.id.edt);
            if (contentsView == null) {

                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                } else if (constants.getTitle() > 0) {
                    title.setText(context.getResources().getString(constants.getTitle()));
                }
            } else {
                title.setVisibility(View.GONE);
            }
            if (constants.getPositiveBtn() > 0) {
                positiveBtn.setText(context.getResources().getString(constants.getPositiveBtn()));
            } else {
                positiveBtn.setVisibility(View.GONE);
            }
            editText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    if (isAddCategory){
                        positiveBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rect_30_bg_black_24));
                        positiveBtn.setClickable(true);
                        positiveBtn.setEnabled(true);
                    }else{
                        if (s.length() > 0) {
                            positiveBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rect_30_bg_black_24));
                            positiveBtn.setClickable(true);
                            positiveBtn.setEnabled(true);
                        }else {
                            positiveBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rect_30_bg_gray_ec));
                            positiveBtn.setClickable(false);
                            positiveBtn.setEnabled(false);
                        }
                    }


                }

                @Override
                public void afterTextChanged(Editable s) {
                    if (isAddCategory){
                        positiveBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rect_30_bg_black_24));
                        positiveBtn.setClickable(true);
                        positiveBtn.setEnabled(true);
                    }else {
                        if (s.length() > 0) {
                            positiveBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rect_30_bg_black_24));
                            positiveBtn.setClickable(true);
                            positiveBtn.setEnabled(true);
                        } else {
                            positiveBtn.setBackground(context.getResources().getDrawable(R.drawable.btn_rect_30_bg_gray_ec));
                            positiveBtn.setClickable(false);
                            positiveBtn.setEnabled(false);
                        }
                    }
                }
            });

            if (positiveListener != null) {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, constants.getPositiveBtn());
                    }
                });
            } else {
                positiveBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                    }
                });
            }
            backBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });

            return layout;
        }

        private void startAnim(View layout) {
            AnimatorSet animatorSet1 = new AnimatorSet();
            ObjectAnimator scaleX1 = ObjectAnimator.ofFloat(layout, "scaleX", 0.0f,0.0f);
            ObjectAnimator   scaleY1 = ObjectAnimator.ofFloat(layout, "scaleY", 0.0f,0.0f);
            animatorSet1.setDuration(10);
            animatorSet1.setInterpolator(new DecelerateInterpolator());
            animatorSet1.play(scaleX1).with(scaleY1);
            animatorSet1.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animator) {

                }

                @Override
                public void onAnimationEnd(Animator animator) {
                    AnimatorSet animatorSet = new AnimatorSet();
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(layout, "scaleX", 0.1f, 1.2f, 0.9f, 1.0f);
                    ObjectAnimator   scaleY = ObjectAnimator.ofFloat(layout, "scaleY", 0.1f, 1.2f, 0.9f, 1.0f);
                    animatorSet.setDuration(600);

                    animatorSet.setInterpolator(new DecelerateInterpolator());
                    animatorSet.play(scaleX).with(scaleY);
                    animatorSet.start();
                }

                @Override
                public void onAnimationCancel(Animator animator) {

                }

                @Override
                public void onAnimationRepeat(Animator animator) {

                }
            });
            animatorSet1.start();
        }

        private boolean isChinese(char c) {
            return c >= 0x4E00 && c <= 0x9FA5;// 根据字节码判断
        }
    }
}
