package com.czur.scanpro.ui.camera;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.alg.Args;
import com.czur.scanpro.alg.CZPoint;
import com.czur.scanpro.alg.CZSaoMiao_Alg;
import com.czur.scanpro.utils.ScreenAdaptationUtils;

public class CameraBorderView extends View {

    private Paint paint;
    private Paint paint1;
    private Paint paint2;
    private Args args;
    private float scale;
    private float height;
    private boolean isShowing;
    private boolean isBorder;
    private int drawWidth;
    private int drawHeight;
    private Context context;

    public CameraBorderView(Context context) {
        this(context, null);
    }

    public CameraBorderView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        init();
    }

    /**
     * @des: 初始化画笔
     * @params:
     * @return:
     */

    private void init() {
        drawWidth = ScreenUtils.getScreenWidth();
        if(ScreenAdaptationUtils.getPhoneModel()){
            drawHeight = ScreenUtils.getScreenWidth() ;
        } else {
            drawHeight = ScreenUtils.getScreenWidth() / 3 * 4;
        }

        paint = new Paint();
        //抗锯齿
        paint.setAntiAlias(true);
        paint.setFlags(Paint.ANTI_ALIAS_FLAG);
        paint.setColor(0xEEDE4D4D);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(SizeUtils.dp2px(5f));

        paint1 = new Paint();
        //抗锯齿
        paint1.setAntiAlias(true);
        paint1.setFlags(Paint.ANTI_ALIAS_FLAG);
        paint1.setColor(0xCC202020);

        paint1.setStyle(Paint.Style.FILL);
        paint1.setStrokeWidth(SizeUtils.dp2px(1f));


        paint2 = new Paint();
        //抗锯齿
        paint2.setAntiAlias(true);
        paint2.setFlags(Paint.ANTI_ALIAS_FLAG);
        paint2.setColor(Color.TRANSPARENT);
        paint2.setStyle(Paint.Style.FILL);
        paint2.setStrokeWidth(SizeUtils.dp2px(1f));
    }

    public void show(Args args, float scale, float height, boolean isBorder) {
        isShowing = true;
        this.height = height;
        this.isBorder = isBorder;
        this.scale = scale;
        this.args = args;
        invalidate();
    }


    public void hide() {
        isShowing = false;
        invalidate();
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (isShowing) {
            CZSaoMiao_Alg alg = CZSaoMiao_Alg.getInstance(context);
            CZPoint leftDown = alg.transpose_key_points(this.args.getLeftDownP(), (int) height);
            CZPoint rightTop = alg.transpose_key_points(this.args.getRightTopP(), (int) height);
            CZPoint leftTop = alg.transpose_key_points(this.args.getLeftTopP(), (int) height);
            CZPoint rightDown = alg.transpose_key_points(this.args.getRightDownP(), (int) height);

            CZPoint leftDown0 = alg.transpose_key_points(this.args.leftDownP0, (int) height);
            CZPoint rightTop0 = alg.transpose_key_points(this.args.rightTopP0, (int) height);
            CZPoint leftTop0 = alg.transpose_key_points(this.args.leftTopP0, (int) height);
            CZPoint rightDown0 = alg.transpose_key_points(this.args.rightDownP0, (int) height);
            CZPoint leftDown1 = alg.transpose_key_points(this.args.leftDownP1, (int) height);
            CZPoint rightTop1 = alg.transpose_key_points(this.args.rightTopP1, (int) height);
            CZPoint leftTop1 = alg.transpose_key_points(this.args.leftTopP1, (int) height);
            CZPoint rightDown1 = alg.transpose_key_points(this.args.rightDownP1, (int) height);
            if (isBorder) {
                float scale1 = scale;
                if(ScreenAdaptationUtils.getPhoneModel()){
                    scale1 =scale/4*3;
                }
                canvas.drawRect(0, 0, drawWidth, drawHeight, paint1);
                paint1.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_OUT));
                Path path1 = new Path();
                path1.moveTo(leftDown.getX() * scale, leftDown.getY() * scale1);
                path1.lineTo(leftTop.getX() * scale, leftTop.getY() * scale1);
                path1.lineTo(rightTop.getX() * scale, rightTop.getY() * scale1);
                path1.lineTo(rightDown.getX() * scale, rightDown.getY() * scale1);
                path1.lineTo(leftDown.getX() * scale, leftDown.getY() * scale1);
                canvas.drawPath(path1, paint1);
                paint1.setXfermode(null);

//
                Path leftTopPath = new Path();
                Path rightTopPath = new Path();
                Path leftBottomPath = new Path();
                Path rightBottomPath = new Path();


                leftTopPath.moveTo(leftTop0.getX() * scale, leftTop0.getY() * scale1);
                leftTopPath.lineTo(leftTop.getX() * scale, leftTop.getY() * scale1);
                leftTopPath.lineTo(leftTop1.getX() * scale, leftTop1.getY() * scale1);
                canvas.drawPath(leftTopPath, paint);

                //右上
                rightTopPath.moveTo(rightTop0.getX() * scale, rightTop0.getY() * scale1);
                rightTopPath.lineTo(rightTop.getX() * scale, rightTop.getY() * scale1);
                rightTopPath.lineTo(rightTop1.getX() * scale, rightTop1.getY() * scale1);
                canvas.drawPath(rightTopPath, paint);


                //左下
                leftBottomPath.moveTo(leftDown0.getX() * scale, leftDown0.getY() * scale1);
                leftBottomPath.lineTo(leftDown.getX() * scale, (leftDown.getY()) * scale1);
                leftBottomPath.lineTo(leftDown1.getX() * scale, leftDown1.getY() * scale1);
                canvas.drawPath(leftBottomPath, paint);

                //右下
                rightBottomPath.moveTo(rightDown0.getX() * scale, rightDown0.getY() * scale1);
                rightBottomPath.lineTo(rightDown.getX() * scale, rightDown.getY() * scale1);
                rightBottomPath.lineTo(rightDown1.getX() * scale, rightDown1.getY() * scale1);
                canvas.drawPath(rightBottomPath, paint);

            } else {
                canvas.drawLine(leftTop.getX() * scale, leftTop.getY() * scale, rightTop.getX() * scale, rightTop.getY() * scale, paint);
                canvas.drawLine(leftDown.getX() * scale,leftDown.getY() * scale, rightDown.getX() * scale, rightDown.getY() * scale, paint);
            }


        }
    }
}
