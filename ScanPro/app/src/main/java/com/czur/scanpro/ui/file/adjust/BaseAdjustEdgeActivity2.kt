package com.czur.scanpro.ui.file.adjust

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.PointF
import android.graphics.RectF
import android.os.Bundle
import android.os.Handler
import androidx.annotation.Dimension
import androidx.constraintlayout.widget.ConstraintSet
import android.util.Log
import android.util.SizeF
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.SizeUtils
import com.czur.scanpro.R
import com.czur.scanpro.alg.Args
import com.czur.scanpro.alg.CZOrientation
import com.czur.scanpro.alg.CZSaoMiao_Alg
import com.czur.scanpro.alg.Vec4f
import com.czur.scanpro.databinding.ActivityAdjustEdge2Binding
import com.czur.scanpro.databinding.ActivityAdjustEdgeBinding
import com.czur.scanpro.entity.model.ImageEntity
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.MemoryUtils
import kotlinx.coroutines.*
import java.util.*
import kotlin.collections.ArrayList
import kotlin.math.abs


@SuppressLint("ClickableViewAccessibility")
abstract class BaseAdjustEdgeActivity2 : BaseActivity(), View.OnTouchListener {
    val binding: ActivityAdjustEdge2Binding by lazy{
        ActivityAdjustEdge2Binding.inflate(layoutInflater)
    }
    
    var fileID: String? = null
    var originalImagePath: String? = null
    var bitmap: Bitmap? = null
    var imageView: ImageView? = null
    var pointEventView: View? = null
    val touchPosition: PointF = PointF()
    val prevTouchPosition: PointF = PointF()
    var imagePosition: PointF = PointF()
    val handler = Handler()
    var algInit: Args? = null
     var algList = ArrayList<Args>()
     var BitmapList = ArrayList<Bitmap>()
     var imageScaleList = ArrayList<Float>()

    // magnifier move
    protected val dp50 = SizeUtils.dp2px(65f - 15f)
    protected val dp64 = SizeUtils.dp2px(65f - 1f)
    protected val dp65 = SizeUtils.dp2px(65f)
    protected val dp180 = SizeUtils.dp2px(180f)
    val magnifierWidthHeight = 100

    // init points
    var imageScaleWithScreen: Float = 0f
    val rectWidth = 0.05f * ScreenUtils.getScreenWidth().toFloat()

    // move event
    val dp20 = SizeUtils.dp2px(20f)
    var differenceSize = SizeF(0f, 0f)
    private var listImage = ArrayList<ImageEntity>()
    private var showList = ArrayList<ImageEntity>()
    private lateinit var mAdapter: ImagePagerAdapter
    private val viewMap = HashMap<Int, ImageView>()
    var currentItem = 0
    private var lastItem = 0
    var viewList = ArrayList<ImageView>()
    var width = 0f
    var height =0f
    val job = Job()
    private val scope = CoroutineScope(job)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_adjust_edge2)
        setContentView(binding.root)
        initComponent()
        getIntentInfo()
        initView()
        initEvent()
    }

    private fun initComponent() {
//        fileID = intent.getStringExtra("fileID")
//        originalImagePath = intent.getStringExtra("originalImagePath")
        listImage = intent.getParcelableArrayListExtra("imageList")!!;
        showList = listImage

    }

    private fun initView() {
            showProgressDialog(true)

        binding.viewPager2.post {
            width = binding.viewPager2.getMeasuredWidth().toFloat()
            height = binding.viewPager2.getMeasuredHeight().toFloat()
            scope.launch(Dispatchers.IO) {
                for (index in 0 until listImage.size ) {
                    getShowView(listImage.get(index).imagePath, SizeF(width, height))
                }
                scope.launch(Dispatchers.Main) {
                    initViewPager()
                    imageScaleWithScreen = imageScaleList.get(0)
                    val constraintSet = ConstraintSet()
                    var imageView = viewList.get(0)
                    bitmap = BitmapList.get(0)
                    algInit = algList.get(0)
                    constraintSet.run {
                        connect(imageView!!.id, ConstraintSet.START, R.id.imageMaxRegion2, ConstraintSet.START)
                        connect(imageView!!.id, ConstraintSet.TOP, R.id.imageMaxRegion2, ConstraintSet.TOP)
                        connect(imageView!!.id, ConstraintSet.END, R.id.imageMaxRegion2, ConstraintSet.END)
                        connect(imageView!!.id, ConstraintSet.BOTTOM, R.id.imageMaxRegion2, ConstraintSet.BOTTOM)

                        connect(binding.viewPager2!!.id, ConstraintSet.START, R.id.imageMaxRegion2, ConstraintSet.START)
                        connect(binding.viewPager2!!.id, ConstraintSet.TOP, R.id.imageMaxRegion2, ConstraintSet.TOP)
                        connect(binding.viewPager2!!.id, ConstraintSet.END, R.id.imageMaxRegion2, ConstraintSet.END)
                        connect(binding.viewPager2!!.id, ConstraintSet.BOTTOM, R.id.imageMaxRegion2, ConstraintSet.BOTTOM)

                        constrainWidth(imageView!!.id, bitmap!!.width)
                        constrainHeight(imageView!!.id, bitmap!!.height)

                        constrainWidth(binding.viewPager2!!.id, bitmap!!.width)
                        constrainHeight(binding.viewPager2!!.id, bitmap!!.height)

                        applyTo(binding.adjustEdgeBody2)
                    }

                    makePointView(currentItem)
                    pointEventView!!.setOnTouchListener(this@BaseAdjustEdgeActivity2)
                        imagePosition.x = binding.imageMaxRegion2!!.x
                        imagePosition.y = binding.imageMaxRegion2!!.y

                    hideProgressDialog()
                }

            }

        }

        binding.magnifier.z = 666f
        binding.hView.z = 667f
        binding.vView.z = 668f
    }


    @SuppressLint("StringFormatMatches")
    private fun initViewPager() {
        mAdapter = ImagePagerAdapter()
//        binding.viewPager2.pageMargin = 20 * Dimension.DP
        binding.viewPager2.adapter = mAdapter
        //预加载
        binding.viewPager2.offscreenPageLimit = 3
        binding.viewPager2.setCurrentItem(currentItem, false)
        binding.previewFileTitle.text = String.format(getString(R.string.title_format), binding.viewPager2.currentItem + 1, showList.size)
    }


    inner class ImagePagerAdapter : PagerAdapter() {
        override fun getCount(): Int {
            return showList.size
        }

        override fun isViewFromObject(view: View, `object`: Any): Boolean {
            return view === `object`
        }

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
//            var zoomImage: ImageView? = viewMap[position]
            container.addView(viewList.get(position))
            return viewList.get(position)
        }

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            container.removeView(`object` as View)
        }
    }


    private fun getShowView(imagePath: String, maxRegionSize: SizeF) {
        var bitmap: Bitmap? = null
       var algInit = CZSaoMiao_Alg.getInstance(this).before_single_manual_process(imagePath, MemoryUtils.getAvailMemory(this))
        val originalImageSize = algInit?.contentImg?.width?.toFloat()?.let { algInit?.contentImg?.height?.toFloat()?.let { it1 -> SizeF(it, it1) } }
        originalImageSize?.let {
            val originalImageScale = originalImageSize.width / originalImageSize.height
            val maxRegionScale = maxRegionSize.width / maxRegionSize.height
            val isLandscape = originalImageScale >= maxRegionScale
            val bitmapSize = if (originalImageSize.width > maxRegionSize.width || originalImageSize.height > maxRegionSize.height) {
                if (isLandscape) {
                    SizeF(maxRegionSize.width, maxRegionSize.width / originalImageScale)
                } else {
                    SizeF(maxRegionSize.height * originalImageScale, maxRegionSize.height)
                }
            } else {
                originalImageSize
            }
            bitmap = Bitmap.createBitmap(bitmapSize.width.toInt(), bitmapSize.height.toInt(), Bitmap.Config.ARGB_8888)
            bitmap?.let {
                val canvas = Canvas(bitmap!!)
                val rect = RectF(0f, 0f, bitmapSize.width, bitmapSize.height)
                algInit?.contentImg?.let { it1 -> canvas.drawBitmap(it1, null, rect, null) }
            }
        }
        var imageScale = bitmap!!.width.toFloat() / originalImageSize!!.width
        var imageView = ImageView(this)
        imageView?.setImageBitmap(bitmap)
        this.imageView = imageView
        viewList.add(imageView!!)
        algList.add(algInit!!)
        BitmapList.add(bitmap!!)
        imageScaleList.add(imageScale!!)
    }


    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        touchPosition.run {
            x = event!!.x - dp20
            y = event.y - dp20
        }
        when (event!!.action) {
            MotionEvent.ACTION_DOWN -> {

                prevTouchPosition.run {
                    x = event.x - dp20
                    y = event.y - dp20
                    if (event.x <= 0) {
                        x = 0F
                    }
                    if (event.y <= 0) {
                        y = 0F
                    }
                    if (event.x >= bitmap!!.width) {
                        x = bitmap!!.width.toFloat()
                    }
                    if (event.y >= bitmap!!.height) {
                        y = bitmap!!.height.toFloat()
                    }

                }
                if (!touchDown()) return false

            }
            MotionEvent.ACTION_MOVE -> {
//                LogUtils.e(touchPosition.x, touchPosition.y)
                if (checkNeedResetPoints()) {
                    return false
                }
                // 当前点和之前点完全一样，什么也不做
                if (isTouchPositionNoChanged()) {
                    return true
                }
                if (!isTouchPositionInImage()) {
                    magnifierHide()
                }
                touchMove(isTouchPositionInImage())

                prevTouchPosition.run {
                    x = touchPosition.x
                    y = touchPosition.y
                }
            }
            MotionEvent.ACTION_UP -> touchUp()
        }
        return true
    }

    abstract fun getIntentInfo()

    abstract fun touchDown(): Boolean
    abstract fun touchMove(isInImage: Boolean)
    abstract fun touchUp()
    abstract fun checkNeedResetPoints(): Boolean

    abstract fun makePointView(currentItem: Int)
//    abstract fun setAlgList(algList: ArrayList<Args>, imageScaleList: ArrayList<Float>, BitmapList: ArrayList<Bitmap>)


    private fun isTouchPositionInImage(): Boolean {
        return touchPosition.x >= 0 && touchPosition.y >= 0 && touchPosition.x <= bitmap!!.width && touchPosition.y <= bitmap!!.height
    }

    private fun isTouchPositionNoChanged(): Boolean {
        return touchPosition.x == prevTouchPosition.x && touchPosition.y == prevTouchPosition.y
    }

//    abstract fun algInit(): Args


    private fun initEvent() {
        binding.viewPager2!!.addOnPageChangeListener(viewPageListener)
        binding.adjustEdgeBackBtn.setOnClickListener {
            backClick()
        }
        binding.finishBtn.setOnClickListener {
            finishClick()
        }
    }

    fun magnifierShow() {
        binding.magnifier.x = touchPosition.x + imagePosition.x - dp65
        binding.magnifier.y = touchPosition.y + imagePosition.y - dp180

        binding.hView.x = binding.magnifier.x + dp50
        binding.hView.y = binding.magnifier.y + dp64

        binding.vView.x = binding.magnifier.x + dp64
        binding.vView.y = binding.magnifier.y + dp50

        binding.magnifierGroup2.visibility = View.VISIBLE
        makeMagnifierImage(touchPosition.x, touchPosition.y)
    }

    fun magnifierHide() {
        binding.magnifierGroup2.visibility = View.GONE
    }

    abstract fun makeMagnifierImage(x: Float, y: Float)

    fun px2Image(px: Float): Float {
        return px / imageScaleWithScreen
    }

    fun px2Screen(px: Float): Float {
        return px * imageScaleWithScreen
    }

    fun deepCopyPointList(mutableList: MutableList<PointF>): ArrayList<PointF> {
        return ArrayList(mutableList.map { PointF(it.x, it.y) })
    }

    abstract fun backClick()
    abstract fun finishClick()
    abstract fun removePointView()
    abstract fun savePointView(indext: Int)

    fun lineRectCrossPts(rect: RectF, point1: PointF, point2: PointF, pointOrientation: CZOrientation): PointF {
        val line1 = Vec4f(point1, point2)
        val line2 = when (pointOrientation) {
            CZOrientation.Top -> Vec4f(PointF(rect.left, rect.top), PointF(rect.right, rect.top))
            CZOrientation.Bottom -> Vec4f(PointF(rect.left, rect.bottom), PointF(rect.right, rect.bottom))
            CZOrientation.Left -> Vec4f(PointF(rect.left, rect.top), PointF(rect.left, rect.bottom))
            CZOrientation.Right -> Vec4f(PointF(rect.right, rect.top), PointF(rect.right, rect.bottom))
        }
        return lineIntersection(line1, line2)
    }

    fun lineIntersection(line1: Vec4f, line2: Vec4f): PointF {
        val l1 = Vec4f(line1)
        val l2 = Vec4f(line2)
        if (l1.point1.x > l1.point2.x) {
            l1.point1.x = l1.point2.x.also { l1.point2.x = l1.point1.x }
            l1.point1.y = l1.point2.y.also { l1.point2.y = l1.point1.y }
        }
        if (l2.point1.x > l2.point2.x) {
            l2.point1.x = l2.point2.x.also { l2.point2.x = l2.point1.x }
            l2.point1.y = l2.point2.y.also { l2.point2.y = l2.point1.y }
        }

        val denominator = 1.4e-45f + (l1.point1.y - l1.point2.y) * (l2.point2.x - l2.point1.x) -
                (l1.point2.x - l1.point1.x) * (l2.point1.y - l2.point2.y)
        var x = (l1.point1.y * l1.point2.x - l1.point1.x * l1.point2.y) * (l2.point2.x - l2.point1.x) -
                (l1.point2.x - l1.point1.x) * (l2.point1.y * l2.point2.x - l2.point1.x * l2.point2.y)
        x = abs(x / denominator)
        var y = (l1.point1.y - l1.point2.y) * (l2.point1.y * l2.point2.x - l2.point1.x * l2.point2.y) -
                (l2.point1.y - l2.point2.y) * (l1.point1.y * l1.point2.x - l1.point1.x * l1.point2.y)
        y = abs(y / denominator)
        return PointF(x, y)
    }

    private val viewPageListener = object : ViewPager.OnPageChangeListener {
        override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {
        }

        @SuppressLint("StringFormatMatches")
        override fun onPageSelected(position: Int) {
            currentItem = position
//            showProgressDialog(true)
            handler.post {
                //保存上一个图片
                savePointView(lastItem)
                lastItem = currentItem
                removePointView()
                imageScaleWithScreen = imageScaleList.get(currentItem)
                var imageView = viewList.get(currentItem)
                val constraintSet = ConstraintSet()
                bitmap = BitmapList.get(currentItem)

                algInit = algList.get(currentItem)
                imageView!!.id = R.id.AdjustEdgeSimpleImage
                constraintSet.run {
                    connect(imageView.id, ConstraintSet.START, R.id.imageMaxRegion2, ConstraintSet.START)
                    connect(imageView.id, ConstraintSet.TOP, R.id.imageMaxRegion2, ConstraintSet.TOP)
                    connect(imageView.id, ConstraintSet.END, R.id.imageMaxRegion2, ConstraintSet.END)
                    connect(imageView.id, ConstraintSet.BOTTOM, R.id.imageMaxRegion2, ConstraintSet.BOTTOM)

                    connect(binding.viewPager2!!.id, ConstraintSet.START, R.id.imageMaxRegion2, ConstraintSet.START)
                    connect(binding.viewPager2!!.id, ConstraintSet.TOP, R.id.imageMaxRegion2, ConstraintSet.TOP)
                    connect(binding.viewPager2!!.id, ConstraintSet.END, R.id.imageMaxRegion2, ConstraintSet.END)
                    connect(binding.viewPager2!!.id, ConstraintSet.BOTTOM, R.id.imageMaxRegion2, ConstraintSet.BOTTOM)

                    constrainWidth(imageView!!.id, bitmap!!.width)
                    constrainHeight(imageView!!.id, bitmap!!.height)

                    constrainWidth(binding.viewPager2!!.id, bitmap!!.width)
                    constrainHeight(binding.viewPager2!!.id, bitmap!!.height)
                    applyTo(binding.adjustEdgeBody2)
                }

                makePointView(currentItem)
                pointEventView!!.setOnTouchListener(this@BaseAdjustEdgeActivity2)

                    imagePosition.x = binding.imageMaxRegion2!!.x
                    imagePosition.y = binding.imageMaxRegion2!!.y

//                hideProgressDialog()

            }
            binding.magnifier.z = 996f
            binding.hView.z = 997f
            binding.vView.z = 998f
            binding.previewFileTitle.text = String.format(getString(R.string.title_format), binding.viewPager2!!.currentItem + 1, showList.size)
        }

        override fun onPageScrollStateChanged(p0: Int) {
        }

    }


}