package com.czur.scanpro.entity.model;

public class UserShareModel{


    /**
     * isDelete : false
     * name : uu
     * photo : https://changer-passport.oss-cn-beijing.aliyuncs.com/v60u5gst4if8zwy.png
     * shared : true
     * userId : 2544
     */

    private boolean isDelete;
    private String name;
    private String photo;
    private String shared;
    private String userId;

    public boolean isIsDelete() {
        return isDelete;
    }

    public void setIsDelete(boolean isDelete) {
        this.isDelete = isDelete;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getShared() {
        return shared;
    }

    public void setShared(String shared) {
        this.shared = shared;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
