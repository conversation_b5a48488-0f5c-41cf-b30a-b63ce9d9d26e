package com.czur.scanpro.ui.base

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.view.KeyEvent
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityIndexBinding
import com.czur.scanpro.databinding.ActivityWelcomeBinding
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.PrivacyPopup
import com.czur.scanpro.utils.MemoryUtils
import com.czur.scanpro.utils.initializeUtils
import com.czur.scanpro.utils.validator.Validator
import com.umeng.commonsdk.UMConfigure
import io.realm.Realm
import io.realm.Sort
import java.io.File
import java.util.*

/**
 * Created by Yz on 2019/1/21.
 * Email：<EMAIL>
 */
class WelcomeActivity : BaseActivity(), View.OnClickListener {
    private lateinit var binding: ActivityWelcomeBinding


    private var userPreferences: UserPreferences? = null
    private lateinit var realm: Realm
    var commonPopup: PrivacyPopup? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_welcome)
        binding = ActivityWelcomeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initComponent()
        LogUtils.i("WelcomeActivity.onCreate()")

    }

    override fun onResume() {
        super.onResume()
    }

    private fun initComponent() {


        LogUtils.i("WelcomeActivity.initComponent()")

        userPreferences = UserPreferences.getInstance(this)
        if (userPreferences!!.isFirstPrivacyPolicy) {
            if (commonPopup == null || commonPopup?.isShowing == false) {
                showPrivacyDialog()
            }
        } else {
            LogUtils.e("xxx", MemoryUtils.getAvailMemory(this))
            val tz = TimeZone.getDefault()
            val s =
                "TimeZone   " + tz.getDisplayName(false, TimeZone.SHORT) + " Timezon id :: " + tz.id
            LogUtils.e(s)
            init()
        }
    }

    //首次安装紧调用一次
    private fun showPrivacyDialog() {
//        val builder = PrivacyPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        val builder = PrivacyPopup.Builder(this, CloudCommonPopupConstants.COMMON_TWO_BUTTON)
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            userPreferences!!.setIsFirstPrivacyPolicy(false)
            dialog.dismiss()
            initializeUtils.initAll(application)
            LogUtils.e("xxx", MemoryUtils.getAvailMemory(this))
            val tz = TimeZone.getDefault()
            val s =
                "TimeZone   " + tz.getDisplayName(false, TimeZone.SHORT) + " Timezon id :: " + tz.id
            LogUtils.e(s)
            init()
        })
        builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, _ ->
            dialog.dismiss()
        })
        commonPopup = builder.create()
        commonPopup?.show()
    }

    private fun init() {
        UMConfigure.init(
            this,
            "5cf0ed994ca3575330000ba2",
            getChannel(this),
            UMConfigure.DEVICE_TYPE_PHONE,
            ""
        )
        realm = Realm.getDefaultInstance()
//        userPreferences!!.sdPath = Environment.getExternalStorageDirectory().toString() + Constants.SD_PATH

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
            getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.Q
        ) {
            userPreferences!!.sdPath =
                getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS).toString() + Constants.SD_PATH
        } else {
            userPreferences!!.sdPath =
                Environment.getExternalStorageDirectory().toString() + Constants.SD_PATH
        }
        var imei = userPreferences?.imei
        var udid = userPreferences?.udid
        val channel = userPreferences?.channel
        val endpoint = userPreferences?.endpoint
        if (Validator.isEmpty(imei)) {
            imei = UUID.randomUUID().toString()
            userPreferences!!.imei = imei
        }
        if (Validator.isEmpty(udid)) {
            udid = UUID.randomUUID().toString()
            userPreferences!!.udid = udid
        }
        if (Validator.isEmpty(channel)) {
            userPreferences!!.channel = Constants.CHANNEL
        }
        if (Validator.isEmpty(endpoint)) {
            userPreferences!!.endpoint = Constants.ENDPOINT
        }
        createTag(realm)
        resetTemp(realm)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                ResourceUtils.copyFileFromAssets(
                    "template_corner.jpg",
                    <EMAIL> + File.separator + "template_corner.jpg"
                )
                val tessPath =
                    <EMAIL> + File.separator + "tessdata"
                if (FileUtils.createOrExistsDir(tessPath)) {
                    ResourceUtils.copyFileFromAssets(
                        "eng.traineddata",
                        tessPath + File.separator + "eng.traineddata"
                    )
                    ResourceUtils.copyFileFromAssets(
                        "osd.traineddata",
                        tessPath + File.separator + "osd.traineddata"
                    )

                }

                val pdfPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.PDF_PATH
                val orExistsPdfPath = FileUtils.createOrExistsDir(pdfPath)
                val apkPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)?.absolutePath.toString() + Constants.SD_PATH + Constants.APK_PATH
                val orExistsApkPath = FileUtils.createOrExistsDir(apkPath)
                val ocrPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.OCR_PATH
                val orExistsOcrPath = FileUtils.createOrExistsDir(ocrPath)

                val handwritingPath =
                    getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.HANDWRITING_PATH
                val orExistsHandwritingPath = FileUtils.createOrExistsDir(handwritingPath)
                FileUtils.createOrExistsDir(cacheDir.path + Constants.TEMP)
                CleanUtils.cleanCustomDir(cacheDir.path + Constants.TEMP)
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })
        startSyncNow()

        Handler().postDelayed({
            finish()
            val intent = Intent(this@WelcomeActivity, IndexActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            ActivityUtils.startActivity(intent)


        }, 500)

    }


    /**
     * @des: 返回时设置temp为0
     * @params:
     * @return:
     */
    private fun resetTemp(realm: Realm) {
        realm.executeTransaction(Realm.Transaction {
            val docEntities = realm.where(DocEntity::class.java)
                .equalTo("isTemp", 1.toInt())
                .findAll().sort("takePhotoTime", Sort.ASCENDING)

            for (docEntity in docEntities) {
                docEntity.isTemp = 0
                docEntity.takePhotoTime = Constants.TAKE_PHOTO_INIT_TIME
            }

            for (docEntity in realm.where(DocEntity::class.java).equalTo("isDelete", 0.toInt())
                .equalTo("isNameTemp", 1.toInt()).findAll()) {
                docEntity.isNameTemp = 0
            }
            for (categoryEntity in realm.where(CategoryEntity::class.java)
                .equalTo("isDelete", 0.toInt()).equalTo("isNameTemp", 1.toInt()).findAll()) {
                categoryEntity.isNameTemp = 0
            }
        })

    }

    private fun registerEvent() {

    }

    override fun onClick(v: View) {

    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            //不执行父类点击事件
            true
        } else {
            super.onKeyDown(keyCode, event)
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        if (this::realm.isInitialized)
            realm.close()
    }

    fun getChannel(context: Context): String? {
        try {
            val appInfo = context.packageManager
                .getApplicationInfo(
                    context.packageName,
                    PackageManager.GET_META_DATA
                )
            return appInfo.metaData.getString("UMENG_CHANNEL")
        } catch (e: Exception) {
        }

        return "getChannelException"
    }
}
