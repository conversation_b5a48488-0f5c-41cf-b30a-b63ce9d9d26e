package com.czur.scanpro.ui.file.adjust


import android.graphics.PointF
import android.graphics.RectF
import androidx.constraintlayout.widget.ConstraintSet
import android.util.SizeF
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.R
import com.czur.scanpro.alg.*
import com.czur.scanpro.common.Constants
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.CategoryEvent
import com.czur.scanpro.event.EditEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.FileEvent
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.component.adjustEdge.AdjustEdgeSimpleView
import com.czur.scanpro.ui.home.FileActivity
import com.czur.scanpro.utils.MemoryUtils
import com.czur.scanpro.utils.ktExtension.createBitmapSquareOutOfBounds
import com.czur.scanpro.utils.validator.Validator
import io.realm.Realm
import io.realm.RealmList
import org.greenrobot.eventbus.EventBus
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.abs
import kotlin.math.min


class AdjustEdgeSimpleActivity : BaseAdjustEdgeActivity() {

    private var pointView: AdjustEdgeSimpleView? = null
    private var isEdgeRight = true
    private var initPoints = mutableListOf<PointF>()
    private var currentPointIndex = -1
    private var isLinePoint = false

    private var finalCategoryName: String? = null
    private var finalCategoryId: String? = null
    private var isImport = false
    private var tagId: String? = null
    private var tagName: String? = null
    private var categoryID: String? = null
    private var categoryName: String? = null
    private var type = 0
    private var isAlg = true

    override fun backClick() {
        finish()
    }

    override fun getIntentInfo() {
        isImport = intent.getBooleanExtra("isImport", false)
        tagId = intent.getStringExtra("tagId")
        tagName = intent.getStringExtra("tagName")
        categoryID = intent.getStringExtra("categoryID")
        categoryName = intent.getStringExtra("categoryName")
        type = intent.getIntExtra("type", 0)
        fileID = intent.getStringExtra("fileID")
        originalImagePath = intent.getStringExtra("originalImagePath")

        if (isImport) {
            binding.adjustCtrlBtn.background = resources.getDrawable(R.mipmap.all_page_icon)
            binding.adjustCtrlTv.text = getString(R.string.all_page)
            binding.adjustCtrlBtn.visibility = View.VISIBLE
            binding.adjustCtrlTv.visibility = View.VISIBLE
            binding.adjustBottomBar.visibility = View.VISIBLE
            binding.adjustBottomBar.setOnClickListener {
                if (isAlg) {
                    LogUtils.e(pointView!!.points)
                    pointView!!.points = mutableListOf(
                            PointF(0F, 0F),
                            PointF(bitmap!!.width.toFloat(), 0F),
                            PointF(bitmap!!.width.toFloat(), bitmap!!.height.toFloat()),
                            PointF(0F, bitmap!!.height.toFloat())
                    )
                } else {
                    LogUtils.e(pointView!!.points)
                    pointView!!.points = deepCopyPointList(initPoints)
                }
                makePoints()
                pointView!!.showPoints()
                binding.adjustCtrlBtn.background = if (isAlg) resources.getDrawable(R.mipmap.alg_page_icon) else resources.getDrawable(R.mipmap.all_page_icon)
                binding.adjustCtrlTv.text = if (isAlg) getString(R.string.alg_page) else getString(R.string.all_page)
                isAlg = !isAlg
            }
        } else {
           binding.adjustCtrlBtn.visibility = View.INVISIBLE
           binding.adjustCtrlTv.visibility = View.INVISIBLE
           binding.adjustBottomBar.visibility = View.INVISIBLE
        }

    }

    override fun finishClick() {
        showProgressDialog(true)
        Thread(Runnable {
            val args = Args()
            args.scanType = CZSaoMiao_Alg.ScanType.SINGLE
            args.leftTopP = CZPoint(pointView!!.points[0].x / imageScaleWithScreen, pointView!!.points[0].y / imageScaleWithScreen)
            args.rightTopP = CZPoint(pointView!!.points[1].x / imageScaleWithScreen, pointView!!.points[1].y / imageScaleWithScreen)
            args.rightDownP = CZPoint(pointView!!.points[2].x / imageScaleWithScreen, pointView!!.points[2].y / imageScaleWithScreen)
            args.leftDownP = CZPoint(pointView!!.points[3].x / imageScaleWithScreen, pointView!!.points[3].y / imageScaleWithScreen)
            val realm = Realm.getDefaultInstance()
            if (isImport) {
                createDoc(realm)
            }
            val doc = realm.where(DocEntity::class.java).equalTo("fileID", fileID).findFirst()
            val originalImagePath = doc!!.originalImagePath
            val enhanceMode = doc.enhanceMode
            val baseImagePath = doc.baseImagePath!!
            val baseSmallImagePath = doc.baseSmallImagePath!!
            val processImagePath = doc.processImagePath!!
            val processSmallImagePath = doc.processSmallImagePath!!

            args.colorType = getColorType(enhanceMode)
            if (!isAlg) {
                args.scanType = CZSaoMiao_Alg.ScanType.NOTHING
            }
            val manualResult = CZSaoMiao_Alg.getInstance(this).on_single_manual_finished(
                    args,
                    baseImagePath,
                    processImagePath,
                    baseSmallImagePath,
                    processSmallImagePath)

            realm.executeTransaction {
                if (isImport) {
                    doc.fileSize = FileUtils.getFileLength(getFilesPath() + fileID + Constants.BASE_JPG).toString()
                }
                doc.isDirty = 1
                doc.uuid = UUID.randomUUID().toString()
                doc.updateTime = getCurrentTime()
                val floats = RealmList<Float>()

                floats.add(pointView!!.points[0].x / imageScaleWithScreen)
                floats.add(pointView!!.points[0].y / imageScaleWithScreen)
                floats.add(pointView!!.points[1].x / imageScaleWithScreen)
                floats.add(pointView!!.points[1].y / imageScaleWithScreen)
                floats.add(pointView!!.points[2].x / imageScaleWithScreen)
                floats.add(pointView!!.points[2].y / imageScaleWithScreen)
                floats.add(pointView!!.points[3].x / imageScaleWithScreen)
                floats.add(pointView!!.points[3].y / imageScaleWithScreen)
                for (aFloat in floats) {
                    LogUtils.e(aFloat)
                }
                doc.rect = floats
            }
            EventBus.getDefault().post(EditEvent(EventType.ADJUST))

            handler.post {
                hideProgressDialog()
                if (isImport) {
                    if (type == 0) ActivityUtils.finishToActivity(IndexActivity::class.java, false)
                    else
                        ActivityUtils.finishToActivity(FileActivity::class.java, false)
                } else {
                    finish()

                }
            }
        }).start()
    }

    private fun getColorType(enhanceMode: Int): CZSaoMiao_Alg.ColorType {
        return when (enhanceMode) {
            0 -> CZSaoMiao_Alg.ColorType.AUTO
            1 -> CZSaoMiao_Alg.ColorType.BW
            2 -> CZSaoMiao_Alg.ColorType.BG_WHITEN
            else -> CZSaoMiao_Alg.ColorType.NO_TRANS
        }
    }

    override fun touchDown() {
        isEdgeRight = true
        checkTouchWitchPoint()
    }

    override fun touchMove(isInImage: Boolean) {
        if (currentPointIndex != -1) {
            pointMove()
        }
    }

    override fun checkNeedResetPoints(): Boolean {
        return !isEdgeRight
    }

    override fun touchUp() {
        magnifierHide()
    }

    private fun pointMove() {
        if (isLinePoint) {
            val isHorizontal = currentPointIndex == 1 || currentPointIndex == 5
            val oryVal: Float = if (isHorizontal) touchPosition.y - prevTouchPosition.y else touchPosition.x - prevTouchPosition.x

            moveLinePointMath(oryVal)

            // 吸附
            val args = Args()
            args.leftTopP = CZPoint(px2Image(pointView!!.points[0].x), px2Image(pointView!!.points[0].y))
            args.rightTopP = CZPoint(px2Image(pointView!!.points[1].x), px2Image(pointView!!.points[1].y))
            args.rightDownP = CZPoint(px2Image(pointView!!.points[2].x), px2Image(pointView!!.points[2].y))
            args.leftDownP = CZPoint(px2Image(pointView!!.points[3].x), px2Image(pointView!!.points[3].y))
            //(0:上， 1：右， 2：下， 3：左）
            val orientation = when (currentPointIndex) {
                1 -> 0
                3 -> 1
                5 -> 2
                else -> 3
            }

            val resultArgs = CZSaoMiao_Alg.getInstance(this).on_single_sorption(
                    args,
                    px2Image(touchPosition.x - differenceSize.width).toInt(),
                    px2Image(touchPosition.y - differenceSize.height).toInt(),
                    orientation)

            if (resultArgs.isHasSorption) {
                val sorptionPointList = mutableListOf<PointF>()
                sorptionPointList.add(PointF(px2Screen(resultArgs.leftTopP.x), px2Screen(resultArgs.leftTopP.y)))
                sorptionPointList.add(PointF(px2Screen(resultArgs.rightTopP.x), px2Screen(resultArgs.rightTopP.y)))
                sorptionPointList.add(PointF(px2Screen(resultArgs.rightDownP.x), px2Screen(resultArgs.rightDownP.y)))
                sorptionPointList.add(PointF(px2Screen(resultArgs.leftDownP.x), px2Screen(resultArgs.leftDownP.y)))
                pointView!!.points = sorptionPointList
            }

        } else {


            pointView!!.points[currentPointIndex / 2].run {
                x = touchPosition.x - differenceSize.width
                y = touchPosition.y - differenceSize.height
            }
        }

        if (!checkEdgeIsRight()) {
            isEdgeRight = false
            pointView!!.points = deepCopyPointList(initPoints)
        }

        if (currentPointIndex != -1) {
            if (pointView!!.points[currentPointIndex / 2].x <= 0) {
                pointView!!.points[currentPointIndex / 2].x = 0F
            }
            if (pointView!!.points[currentPointIndex / 2].y <= 0) {
                pointView!!.points[currentPointIndex / 2].y = 0F
            }
            if (pointView!!.points[currentPointIndex / 2].x >= bitmap!!.width) {
                pointView!!.points[currentPointIndex / 2].x = bitmap!!.width.toFloat()
            }
            if (pointView!!.points[currentPointIndex / 2].y >= bitmap!!.height) {
                pointView!!.points[currentPointIndex / 2].y = bitmap!!.height.toFloat()
            }
            if (!isLinePoint) {
                binding.magnifier.x = pointView!!.points[currentPointIndex / 2].x + imagePosition.x - dp65
                binding.magnifier.y = pointView!!.points[currentPointIndex / 2].y + imagePosition.y - dp180

                binding.hView.x = binding.magnifier.x + dp50
                binding.hView.y = binding.magnifier.y + dp64

                binding.vView.x = binding.magnifier.x + dp64
                binding.vView.y = binding.magnifier.y + dp50

                binding.magnifierGroup.visibility = View.VISIBLE
                makeMagnifierImage(pointView!!.points[currentPointIndex / 2].x, pointView!!.points[currentPointIndex / 2].y)
            }

        }

        makePoints()
        pointView!!.showPoints()
    }

    private fun moveLinePointMath(oryVal: Float) {
        // 0： 上  1： 右  2：下  3： 左
        val viewPoints = deepCopyPointList(pointView!!.points)
        when (currentPointIndex) {
            // 上
            1 -> {
                val newPoint0 = PointF(viewPoints[0].x, viewPoints[0].y + oryVal)
                val newPoint1 = PointF(viewPoints[1].x, viewPoints[1].y + oryVal)
                val resultPoint0 = lineIntersection(Vec4f(viewPoints[0], viewPoints[3]), Vec4f(newPoint0, newPoint1))
                val resultPoint1 = lineIntersection(Vec4f(viewPoints[1], viewPoints[2]), Vec4f(newPoint0, newPoint1))
                viewPoints[0].run {
                    x = resultPoint0.x
                    y = resultPoint0.y
                }
                viewPoints[1].run {
                    x = resultPoint1.x
                    y = resultPoint1.y
                }
                if (viewPoints[0].y <= 0F) {
                    LogUtils.e("1111111111")
                    viewPoints[0].y = 0F
                    viewPoints[1].y -= oryVal
                }
                if (viewPoints[1].y <= 0F) {
                    LogUtils.e("22222222222")
                    viewPoints[1].y = 0F
                    viewPoints[0].y -= oryVal
                }
            }
            // 右
            3 -> {
                val newPoint1 = PointF(viewPoints[1].x + oryVal, viewPoints[1].y)
                val newPoint2 = PointF(viewPoints[2].x + oryVal, viewPoints[2].y)
                val resultPoint1 = lineIntersection(Vec4f(viewPoints[0], viewPoints[1]), Vec4f(newPoint1, newPoint2))
                val resultPoint2 = lineIntersection(Vec4f(viewPoints[2], viewPoints[3]), Vec4f(newPoint1, newPoint2))
                viewPoints[1].run {
                    x = resultPoint1.x
                    y = resultPoint1.y
                }
                viewPoints[2].run {
                    x = resultPoint2.x
                    y = resultPoint2.y
                }


                if (viewPoints[2].x >= bitmap!!.width) {
                    LogUtils.e("3333333", oryVal)
                    viewPoints[2].x = bitmap!!.width.toFloat()
                    viewPoints[1].x += oryVal
                }
                if (viewPoints[1].x >= bitmap!!.width) {
                    LogUtils.e("44444")
                    viewPoints[1].x = bitmap!!.width.toFloat()
                    viewPoints[2].x += oryVal
                }
            }
            // 下
            5 -> {
                val newPoint2 = PointF(viewPoints[2].x, viewPoints[2].y + oryVal)
                val newPoint3 = PointF(viewPoints[3].x, viewPoints[3].y + oryVal)
                val resultPoint2 = lineIntersection(Vec4f(viewPoints[1], viewPoints[2]), Vec4f(newPoint2, newPoint3))
                val resultPoint3 = lineIntersection(Vec4f(viewPoints[0], viewPoints[3]), Vec4f(newPoint2, newPoint3))
                viewPoints[2].run {
                    x = resultPoint2.x
                    y = resultPoint2.y
                }
                viewPoints[3].run {
                    x = resultPoint3.x
                    y = resultPoint3.y
                }

                if (viewPoints[2].y >= bitmap!!.height) {
                    LogUtils.e("55555555", oryVal)
                    viewPoints[2].y = bitmap!!.height.toFloat()
                    viewPoints[3].y += oryVal
                }
                if (viewPoints[3].y >= bitmap!!.height) {
                    LogUtils.e("6666666", oryVal)
                    viewPoints[3].y = bitmap!!.height.toFloat()
                    viewPoints[2].y += oryVal
                }


            }
            // 左
            7 -> {
                val newPoint0 = PointF(viewPoints[0].x + oryVal, viewPoints[0].y)
                val newPoint3 = PointF(viewPoints[3].x + oryVal, viewPoints[3].y)
                val resultPoint0 = lineIntersection(Vec4f(viewPoints[0], viewPoints[1]), Vec4f(newPoint0, newPoint3))
                val resultPoint3 = lineIntersection(Vec4f(viewPoints[2], viewPoints[3]), Vec4f(newPoint0, newPoint3))
                viewPoints[0].run {
                    x = resultPoint0.x
                    y = resultPoint0.y
                }
                viewPoints[3].run {
                    x = resultPoint3.x
                    y = resultPoint3.y
                }
                if (viewPoints[3].x <= 0F) {
                    LogUtils.e("77777777", oryVal)
                    viewPoints[3].x = 0F
                    viewPoints[0].x -= oryVal
                }
                if (viewPoints[0].x <= 0F) {
                    LogUtils.e("88888888", oryVal)
                    viewPoints[0].x = 0F
                    viewPoints[3].x -= oryVal
                }
            }
        }

//        viewPoints.forEach {
//            if (it.x < 0f || it.y < 0f || it.x > bitmap!!.width.toFloat() || it.y > bitmap!!.height.toFloat()) {
//                return
//            }
//        }
        pointView!!.points = viewPoints
    }

    private fun checkEdgeIsRight(): Boolean {
        val x1 = pointView!!.points[0].x
        val y1 = pointView!!.points[0].y
        val x2 = pointView!!.points[1].x
        val y2 = pointView!!.points[1].y
        val x3 = pointView!!.points[3].x
        val y3 = pointView!!.points[3].y
        val x4 = pointView!!.points[2].x
        val y4 = pointView!!.points[2].y
        val z1 = ((x2 - x1) * (y4 - y1) - (x4 - x1) * (y2 - y1))
        val z2 = ((x4 - x1) * (y3 - y1) - (x3 - x1) * (y4 - y1))
        val z3 = ((x4 - x2) * (y3 - y2) - (x3 - x2) * (y4 - y2))
        val z4 = ((x3 - x2) * (y1 - y2) - (x1 - x2) * (y3 - y2))
        return (z1 * z2 > 0) && (z3 * z4 > 0)
    }

    private fun checkTouchWitchPoint() {
        // 集合和触摸点接近的点
        val nearPointDistance = mutableMapOf<Int, Float>()
        pointView!!.pointsWithMiddle.forEachIndexed { index, point ->
            val distanceX = abs(touchPosition.x - point.x)
            val distanceY = abs(touchPosition.y - point.y)
            if (distanceX <= dp20 && distanceY <= dp20) {
                nearPointDistance[index] = distanceX + distanceY
            }
        }

        // 整理出最近的点和是角点还是线点
        if (nearPointDistance.isEmpty()) {
            currentPointIndex = -1
        } else {
            var tempIndex = -1
            var tempMixDistance = 0f
            nearPointDistance.forEach {
                if (tempMixDistance == 0f || it.value < tempMixDistance) {
                    tempIndex = it.key
                    tempMixDistance = it.value
                }
            }

            currentPointIndex = tempIndex
            isLinePoint = tempIndex == 1 || tempIndex == 3 || tempIndex == 5 || tempIndex == 7
            differenceSize = SizeF(
                    touchPosition.x - pointView!!.pointsWithMiddle[tempIndex].x,
                    touchPosition.y - pointView!!.pointsWithMiddle[tempIndex].y
            )
        }
    }

    override fun makeMagnifierImage(x: Float, y: Float) {
        val magnifierBitmap = bitmap!!.createBitmapSquareOutOfBounds(
                x.toInt(), y.toInt(), magnifierWidthHeight)
        binding.magnifier.setImageBitmap(magnifierBitmap)
    }

    private fun makePoints() {

        makePointsWithMiddlePoint()
        makePointRect()
        makePointCrossLine()
    }

    private fun makePointsWithMiddlePoint() {
        pointView!!.pointsWithMiddle.clear()
        pointView!!.points.forEachIndexed { index: Int, point: PointF ->
            val nextPoint = if (index == pointView!!.points.size - 1) pointView!!.points[0] else pointView!!.points[index + 1]
            pointView!!.pointsWithMiddle.add(point)
            pointView!!.pointsWithMiddle.add(PointF(abs(point.x - nextPoint.x) / 2f + min(point.x, nextPoint.x), abs(point.y - nextPoint.y) / 2f + min(point.y, nextPoint.y)))
        }
    }

    private fun makePointRect() {
        pointView!!.pointRect.clear()
        pointView!!.pointsWithMiddle.forEach {
            val x = it.x - rectWidth / 2f
            val y = it.y - rectWidth / 2f
            pointView!!.pointRect.add(RectF(x, y, x + rectWidth, y + rectWidth))
        }
    }

    private fun makePointCrossLine() {
        pointView!!.pointCrossLines.clear()
        pointView!!.pointCrossLines.run {
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[0], pointView!!.pointsWithMiddle[0], pointView!!.pointsWithMiddle[1], CZOrientation.Right),
                    lineRectCrossPts(pointView!!.pointRect[1], pointView!!.pointsWithMiddle[0], pointView!!.pointsWithMiddle[1], CZOrientation.Left)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[1], pointView!!.pointsWithMiddle[1], pointView!!.pointsWithMiddle[2], CZOrientation.Right),
                    lineRectCrossPts(pointView!!.pointRect[2], pointView!!.pointsWithMiddle[1], pointView!!.pointsWithMiddle[2], CZOrientation.Left)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[2], pointView!!.pointsWithMiddle[2], pointView!!.pointsWithMiddle[3], CZOrientation.Bottom),
                    lineRectCrossPts(pointView!!.pointRect[3], pointView!!.pointsWithMiddle[2], pointView!!.pointsWithMiddle[3], CZOrientation.Top)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[3], pointView!!.pointsWithMiddle[3], pointView!!.pointsWithMiddle[4], CZOrientation.Bottom),
                    lineRectCrossPts(pointView!!.pointRect[4], pointView!!.pointsWithMiddle[3], pointView!!.pointsWithMiddle[4], CZOrientation.Top)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[4], pointView!!.pointsWithMiddle[4], pointView!!.pointsWithMiddle[5], CZOrientation.Left),
                    lineRectCrossPts(pointView!!.pointRect[5], pointView!!.pointsWithMiddle[4], pointView!!.pointsWithMiddle[5], CZOrientation.Right)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[5], pointView!!.pointsWithMiddle[5], pointView!!.pointsWithMiddle[6], CZOrientation.Left),
                    lineRectCrossPts(pointView!!.pointRect[6], pointView!!.pointsWithMiddle[5], pointView!!.pointsWithMiddle[6], CZOrientation.Right)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[6], pointView!!.pointsWithMiddle[6], pointView!!.pointsWithMiddle[7], CZOrientation.Top),
                    lineRectCrossPts(pointView!!.pointRect[7], pointView!!.pointsWithMiddle[6], pointView!!.pointsWithMiddle[7], CZOrientation.Bottom)))
            add(Vec4f(
                    lineRectCrossPts(pointView!!.pointRect[7], pointView!!.pointsWithMiddle[7], pointView!!.pointsWithMiddle[0], CZOrientation.Top),
                    lineRectCrossPts(pointView!!.pointRect[0], pointView!!.pointsWithMiddle[7], pointView!!.pointsWithMiddle[0], CZOrientation.Bottom)))
        }
    }

    override fun makePointView() {
        pointView = AdjustEdgeSimpleView(this)
        pointView!!.id = R.id.AdjustEdgeSimplePointView
        binding.adjustEdgeBody.addView(pointView)

        val constraintSet = ConstraintSet()
        constraintSet.run {
            connect(pointView!!.id, ConstraintSet.START, imageView!!.id, ConstraintSet.START)
            connect(pointView!!.id, ConstraintSet.TOP, imageView!!.id, ConstraintSet.TOP)
            connect(pointView!!.id, ConstraintSet.END, imageView!!.id, ConstraintSet.END)
            connect(pointView!!.id, ConstraintSet.BOTTOM, imageView!!.id, ConstraintSet.BOTTOM)
            constrainWidth(pointView!!.id, bitmap!!.width + dp20 * 2)
            constrainHeight(pointView!!.id, bitmap!!.height + dp20 * 2)
            applyTo(binding.adjustEdgeBody)
        }

        var offset = 0f
        val hasPointDoc = Realm.getDefaultInstance().where(DocEntity::class.java).equalTo("fileID", fileID).equalTo("isDelete", 0.toInt()).findFirst()
        if (hasPointDoc != null) {

            LogUtils.e(hasPointDoc.rect)
            if (hasPointDoc.rect.isNullOrEmpty()) {
                pointView!!.points = mutableListOf(
                        PointF(px2Screen(algInit!!.leftTopP.x + offset), px2Screen(algInit!!.leftTopP.y + offset)),
                        PointF(px2Screen(algInit!!.rightTopP.x + offset), px2Screen(algInit!!.rightTopP.y + offset)),
                        PointF(px2Screen(algInit!!.rightDownP.x + offset), px2Screen(algInit!!.rightDownP.y + offset)),
                        PointF(px2Screen(algInit!!.leftDownP.x + offset), px2Screen(algInit!!.leftDownP.y + offset))
                )
            } else {
                LogUtils.e(hasPointDoc.rect!!)
                var s = ""
                for (i in hasPointDoc.rect!!) {
                    s += "$i,"
                }
                LogUtils.e(s)
                pointView!!.points = mutableListOf(
                        PointF(px2Screen(hasPointDoc.rect!![0]!!), px2Screen(hasPointDoc.rect!![1]!!)),
                        PointF(px2Screen(hasPointDoc.rect!![2]!!), px2Screen(hasPointDoc.rect!![3]!!)),
                        PointF(px2Screen(hasPointDoc.rect!![4]!!), px2Screen(hasPointDoc.rect!![5]!!)),
                        PointF(px2Screen(hasPointDoc.rect!![6]!!), px2Screen(hasPointDoc.rect!![7]!!))
                )

            }
        } else {
            pointView!!.points = mutableListOf(
                    PointF(px2Screen(algInit!!.leftTopP.x + offset), px2Screen(algInit!!.leftTopP.y + offset)),
                    PointF(px2Screen(algInit!!.rightTopP.x + offset), px2Screen(algInit!!.rightTopP.y + offset)),
                    PointF(px2Screen(algInit!!.rightDownP.x + offset), px2Screen(algInit!!.rightDownP.y + offset)),
                    PointF(px2Screen(algInit!!.leftDownP.x + offset), px2Screen(algInit!!.leftDownP.y + offset))
            )
        }

        initPoints = deepCopyPointList(pointView!!.points)
        makePoints()
        pointView!!.showPoints()
        pointEventView = pointView
    }

    private fun createDoc(realm: Realm) {

        realm.executeTransaction {
            if (FileUtils.createOrExistsDir(getFilesPath())) {
                createCategory()
                LogUtils.e(originalImagePath, getFilesPath() + fileID + Constants.ORIGINAL_JPG)
                if (FileUtils.copy(originalImagePath, getFilesPath() + fileID + Constants.ORIGINAL_JPG)) {
                    val docEntity = DocEntity()
                    docEntity.fileID = fileID
                    docEntity.bucket = Constants.BUCKET
                    docEntity.uuid = UUID.randomUUID().toString()
                    docEntity.enhanceMode = 0
                    docEntity.userID = getUserIdIsLogin()
                    when (type) {
                        0 -> {
                            docEntity.categoryID = finalCategoryId
                            docEntity.categoryName = finalCategoryName
                        }
                        1 -> {
                            docEntity.isNameTemp = 1
                            docEntity.categoryID = categoryID
                            docEntity.categoryName = categoryName
                        }
                        2 -> {
                            docEntity.tagId = tagId
                            docEntity.tagName = tagName
                            docEntity.categoryID = finalCategoryId
                            docEntity.categoryName = finalCategoryName
                        }
                    }
                    docEntity.isNewAdd = 1
                    docEntity.bucket = Constants.SCAN_PRO
                    docEntity.fileType = 0
                    docEntity.isDirty = 1

                    docEntity.baseImagePath = getFilesPath() + fileID + Constants.BASE_JPG
                    docEntity.baseSmallImagePath = getFilesPath() + fileID + Constants.BASE_SMALL_JPG
                    docEntity.originalImagePath = getFilesPath() + fileID + Constants.ORIGINAL_JPG
                    docEntity.processSmallImagePath = getFilesPath() + fileID + Constants.SMALL_JPG
                    docEntity.processImagePath = getFilesPath() + fileID + Constants.JPG
                    val curDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(System.currentTimeMillis()))
                    docEntity.createTime = curDate
                    docEntity.updateTime = curDate
                    docEntity.takePhotoTime = curDate
                    val floats = RealmList<Float>()

                    floats.add(algInit!!.leftTopP.x)
                    floats.add(algInit!!.leftTopP.y)
                    floats.add(algInit!!.rightTopP.x)
                    floats.add(algInit!!.rightTopP.y)
                    floats.add(algInit!!.rightDownP.x)
                    floats.add(algInit!!.rightDownP.y)
                    floats.add(algInit!!.leftDownP.x)
                    floats.add(algInit!!.leftDownP.y)
                    for (aFloat in floats) {
                        LogUtils.e(aFloat)
                    }
                    docEntity.rect = floats
                    realm.copyToRealmOrUpdate(docEntity)
                }

            }
        }

    }

    private fun createCategory() {
        if (Validator.isEmpty(finalCategoryName) && (type == 0 || type == 2)) {
            EventBus.getDefault().post(CategoryEvent(EventType.ADD_CATEGORY_HIDE))
            var i = 0
            val bookName = Constants.CATEGORY_DEFAULT_NAME
            var finalName = bookName
            val realm = Realm.getDefaultInstance()
            var sameCategory = realm.where(CategoryEntity::class.java)
                    .equalTo("categoryName", Constants.CATEGORY_DEFAULT_NAME)
                    .equalTo("isDelete", 0.toInt())
                    .findFirst()
            while (Validator.isNotEmpty(sameCategory)) {
                val current = System.currentTimeMillis()
                i++
                finalName = bookName + i
                sameCategory = realm.where(CategoryEntity::class.java)
                        .equalTo("categoryName", finalName)
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                LogUtils.i(System.currentTimeMillis() - current)
            }
            finalCategoryName = finalName

            finalCategoryId = UUID.randomUUID().toString()
            val categoryEntity = realm.createObject(CategoryEntity::class.java, finalCategoryId)
            categoryEntity.userID = getUserIdIsLogin()
            categoryEntity.isDirty = 1
            categoryEntity.categoryName = finalCategoryName
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
            val curDate = formatter.format(Date(System.currentTimeMillis()))
            categoryEntity.createTime = curDate
            categoryEntity.updateTime = curDate
        }


    }

    override fun onDestroy() {
        super.onDestroy()
        if (isImport) {
            startSyncNow()
        } else {
            startAutoSync()
        }
        if (isImport && (type == 0 || type == 2)) {
            EventBus.getDefault().post(CategoryEvent(EventType.ADD_CATEGORY))
            EventBus.getDefault().post(FileEvent(EventType.ADD_FILES, 1))
        }
    }

    override fun algInit(): Args {
        return CZSaoMiao_Alg.getInstance(this).before_single_manual_process(originalImagePath, MemoryUtils.getAvailMemory(this))
    }
}