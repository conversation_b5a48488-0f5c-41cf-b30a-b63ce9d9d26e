package com.czur.scanpro.ui.camera;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.hardware.Camera;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.KeyEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.adapter.CameraPreviewAdapter;
import com.czur.scanpro.alg.Args;
import com.czur.scanpro.alg.CZSaoMiao_Alg;
import com.czur.scanpro.alg.Callbacks;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.entity.realm.CategoryEntity;
import com.czur.scanpro.entity.realm.DocEntity;
import com.czur.scanpro.event.BaseEvent;
import com.czur.scanpro.event.CardFrontEvent;
import com.czur.scanpro.event.CategoryEvent;
import com.czur.scanpro.event.DeleteEvent;
import com.czur.scanpro.event.EventType;
import com.czur.scanpro.event.FileEvent;
import com.czur.scanpro.preferences.UserPreferences;
import com.czur.scanpro.ui.base.BaseActivity;
import com.czur.scanpro.ui.component.CameraGuideView;
import com.czur.scanpro.ui.file.FilePreviewActivity;
import com.czur.scanpro.utils.NavBarUtils;
import com.czur.scanpro.utils.ScreenAdaptationUtils;
import com.czur.scanpro.utils.validator.Validator;
import com.facebook.drawee.view.SimpleDraweeView;
import com.jni.bitmap_operations.JniBitmapHolder;
import com.nshmura.snappysmoothscroller.SnapType;
import com.nshmura.snappysmoothscroller.SnappyLinearLayoutManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

import io.realm.Realm;
import io.realm.Sort;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

@SuppressWarnings("ALL")
public class CardCameraActivity extends BaseActivity implements View.OnClickListener {


    private CardFocusView focusView;
    private CardPreviewSurfaceView previewSurfaceView;
    private CardCameraPreview cameraPreview;
    private CameraBorderView cameraBorderView;
    private BlackShadeView cameraBlackShade;
    private Vibrator vibrator;
    private Realm realm;
    private UserPreferences userPreferences;


    private View animView;
    private TextView pageTv;
    private TextView cameraPromptTv;
    private TextView cameraGuideConfirmBtn;
    private ImageView previewNoIconBackBtn;
    private ImageView cameraFlashModeBtn;
    private ImageView flashBtn;
    private SimpleDraweeView cameraCutImg;
    private SimpleDraweeView cameraColorImg;
    private RelativeLayout cameraScanRl;
    private ImageView cameraFlashView;
    private RelativeLayout cameraGuideRl;
    private RelativeLayout cameraFlashModeRl;
    private RelativeLayout cameraContainer;


    private RecyclerView recyclerView;
    private CameraPreviewAdapter cameraPreviewAdapter;
    private List<DocEntity> docEntities;
    private DocEntity sameDocEntity;
    private ObjectAnimator hideShadeAndShowCutAnim;
    private ObjectAnimator blackShadeShowAnim;
    private Camera.Size size;
    private Bitmap cutBitmap;
    private Bitmap colorBitmap;
    private Handler handler = new Handler();

    //0不开启闪光灯 1自动 2长亮
    private int[] flashMode = new int[]{R.mipmap.close_flash_icon, R.mipmap.auto_flash, R.mipmap.open_flash_icon};

    private int mSgType = 1;
    private int height;
    private int finalPageNum;
    public long address;
    private float scale;

    private int[] rgba;
    private float[] keyPoints;
    private byte[] data;
    private byte[] previewData;
    private boolean isPageRecVisible = false;
    private boolean isPageNumVisible = false;
    private boolean isListAdd = false;
    private boolean isRun = true;
    private AtomicBoolean isPreview;
    private AtomicBoolean isInCamera;
    private AtomicBoolean isGetColorBitmap;
    private AtomicBoolean isWaitingColor;
    private AtomicBoolean isGetCutBitmap;
    private AtomicBoolean isWaitingCut;
    private AtomicBoolean isPrint;
    private AtomicBoolean needAlgFocus;
    private String docIdUUid;
    private String colorPath;
    private String smallPath;
    private String dirPath;
    private SimpleDateFormat formatter;
    private int docsCount = 0;
    private ObjectAnimator showWhiteViewAnim;
    private boolean isStable;
    private boolean isFocusSuccess = false;
    private long lastVibrateTime = 0;
    private String tagId;
    private String tagName;
    private String categoryID;
    private String categoryName;
    private boolean isSingle;
    private int type = 0;
    private CameraGuideView cameraGuideView;
    private CZSaoMiao_Alg scanProAlg;
    private Args args;

    private String originalPath;
    private String basePath;
    private String baseSmallPath;
    private String processPath;
    private String processSmallPah;
    private long timeMillis;
    private float startDegree = 0f;
    //1:左，2：右，3：上,4；下
    private int oritationType = 4;
    private long oritationTime;
    private float x1;
    private float y1;
    private boolean isCard = true;
    private boolean isFrontCard = true;
    private boolean isPhotoedLicense = false;
    private TextView middleToast;
    private ImageView idCardImg;
    private TextView bottomToast;
    private LinearLayout cameraTabLl;
    private LinearLayout idCardLl;
    private LinearLayout businessLl;
    private TextView idCardTv;
    private TextView businessTv;
    private View idCardTab;
    private View businessTab;
    private String finalCategoryName;
    private String finalCategoryId;
    private ArrayList<View> rotateViews = new ArrayList<>();
    private boolean needShowStatusBar = ScreenAdaptationUtils.hasLiuHaiInVivo();
    private SensorManager sensorManager;
    private RotateSensorUtil sensorUtil;
    private AtomicBoolean isStartPreview;
    private JniBitmapHolder bitmapHolder = new JniBitmapHolder();
    private boolean isSmallScreen;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_camera_card);
        initComponent();
        initRecyclerView();
        registerEvent();
        resetTemp();
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        docEntities = new ArrayList<>();
        cameraPreviewAdapter = new CameraPreviewAdapter(this, docEntities, isSmallScreen);
        cameraPreviewAdapter.setOnItemClickListener(onItemClickListener);
        recyclerView.setAdapter(cameraPreviewAdapter);
        recyclerView.setHasFixedSize(true);
        SnappyLinearLayoutManager linearLayoutManager = new SnappyLinearLayoutManager(this);
        linearLayoutManager.setSnapType(SnapType.CENTER);
        linearLayoutManager.setAutoMeasureEnabled(true);
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        recyclerView.setLayoutManager(linearLayoutManager);

    }


    private void initComponent() {
        initSensor();
        if (NavBarUtils.hasNavBar(this)) {
            hideBottomUIMenu();
        }
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        userPreferences = UserPreferences.getInstance(this);
        scanProAlg = CZSaoMiao_Alg.getInstance(this);
        scanProAlg.setCallbacks(new AlgCallBacks());
        isStartPreview = new AtomicBoolean(false);
        isInCamera = new AtomicBoolean(false);
        isPreview = new AtomicBoolean(false);
        isWaitingColor = new AtomicBoolean(false);
        isGetColorBitmap = new AtomicBoolean(false);
        isWaitingCut = new AtomicBoolean(false);
        isGetCutBitmap = new AtomicBoolean(false);
        isPrint = new AtomicBoolean(false);
        needAlgFocus = new AtomicBoolean(false);
        //震动
        vibrator = (Vibrator) this.getSystemService(VIBRATOR_SERVICE);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        realm = Realm.getDefaultInstance();
        isSingle = getIntent().getBooleanExtra("isSingle", false);
        tagId = getIntent().getStringExtra("tagId");
        tagName = getIntent().getStringExtra("tagName");
        categoryID = getIntent().getStringExtra("categoryID");
        categoryName = getIntent().getStringExtra("categoryName");
        type = getIntent().getIntExtra("type", 0);

        cameraFlashView = (ImageView) findViewById(R.id.camera_flash_view);
        cameraScanRl = (RelativeLayout) findViewById(R.id.camera_scan_rl);
        cameraCutImg = findViewById(R.id.camera_cut_img);
        cameraColorImg = findViewById(R.id.camera_color_img);
        animView = (View) findViewById(R.id.anim_view);
        middleToast = (TextView) findViewById(R.id.middle_toast);
        idCardImg = (ImageView) findViewById(R.id.id_card_img);
        bottomToast = (TextView) findViewById(R.id.bottom_toast);
        cameraTabLl = (LinearLayout) findViewById(R.id.camera_tab_ll);
        idCardLl = (LinearLayout) findViewById(R.id.id_card_ll);
        businessLl = (LinearLayout) findViewById(R.id.business_ll);
        idCardTv = (TextView) findViewById(R.id.id_card_tv);
        businessTv = (TextView) findViewById(R.id.business_tv);
        idCardTab = (View) findViewById(R.id.id_card_tab);
        businessTab = (View) findViewById(R.id.business_tab);
        businessTv.setAlpha(0.3f);

        cameraFlashModeRl = (RelativeLayout) findViewById(R.id.camera_flash_mode_rl);
        previewNoIconBackBtn = (ImageView) findViewById(R.id.preview_no_icon_back_btn);
        cameraFlashModeBtn = (ImageView) findViewById(R.id.camera_flash_mode_btn);
        recyclerView = (RecyclerView) findViewById(R.id.camera_list);
        cameraContainer = (RelativeLayout) findViewById(R.id.camera_preview_layout);
        focusView = (CardFocusView) findViewById(R.id.focus_view);
        cameraBorderView = (CameraBorderView) findViewById(R.id.preview_point_view);
        previewSurfaceView = (CardPreviewSurfaceView) findViewById(R.id.surface_view);
        flashBtn = (ImageView) findViewById(R.id.button_flash);
        cameraBlackShade = (BlackShadeView) findViewById(R.id.camera_black_shade);
        cameraPromptTv = (TextView) findViewById(R.id.camera_prompt_tv);
//        rotateViews.add(cameraFlashModeBtn);
//        rotateViews.add(previewNoIconBackBtn);
//        sensorUtil = new RotateSensorUtil(this,rotateViews);

        if(ScreenAdaptationUtils.getPhoneModel()){
            height = ScreenUtils.getScreenWidth() ;
            isSmallScreen = true;
        } else{
            height = ScreenUtils.getScreenWidth() / 3 * 4;
            isSmallScreen = false;
        }

        RelativeLayout.LayoutParams cutImgLayoutParams = (RelativeLayout.LayoutParams) cameraCutImg.getLayoutParams();
        cutImgLayoutParams.height = ScreenUtils.getScreenWidth() * 4 / 5;
        cutImgLayoutParams.width = ScreenUtils.getScreenWidth() * 4 / 5;
        cameraCutImg.setLayoutParams(cutImgLayoutParams);
        RelativeLayout.LayoutParams colorImgLayoutParams = (RelativeLayout.LayoutParams) cameraColorImg.getLayoutParams();
        colorImgLayoutParams.height = ScreenUtils.getScreenWidth() * 4 / 5;
        colorImgLayoutParams.width = ScreenUtils.getScreenWidth() * 4 / 5;
        cameraColorImg.setLayoutParams(colorImgLayoutParams);

        RelativeLayout.LayoutParams cameraContainerLayoutParams = (RelativeLayout.LayoutParams) cameraContainer.getLayoutParams();
        cameraContainerLayoutParams.height = height;
        cameraContainer.setLayoutParams(cameraContainerLayoutParams);

        cameraPreview = new CardCameraPreview(CardCameraActivity.this);
        cameraPreview.setOnCameraReadyListener(onCameraReadyListener);

        SurfaceHolder camHolder = previewSurfaceView.getHolder();
        camHolder.addCallback(cameraPreview);
        camHolder.setKeepScreenOn(true);
        camHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);


        RelativeLayout.LayoutParams cameraTabLlLayoutParams = (RelativeLayout.LayoutParams) cameraTabLl.getLayoutParams();
        cameraTabLlLayoutParams.leftMargin = ScreenUtils.getScreenWidth() / 2 - SizeUtils.getMeasuredWidth(idCardTv) / 2;

        cameraTabLl.setLayoutParams(cameraTabLlLayoutParams);


        previewSurfaceView.setListener(cameraPreview);
        previewSurfaceView.setFocusView(focusView);
        dirPath = getFilesPath();
        docIdUUid = UUID.randomUUID().toString();
        generateNewPath();

    }

    /**
     * 初始化陀螺仪
     *
     * @param: []
     * @return: []
     */

    private void initSensor() {
        sensorManager = (SensorManager) getSystemService(SENSOR_SERVICE);
        Sensor sensor = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
        Sensor sensor1 = sensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
        sensorManager.registerListener(sensorEventListener, sensor, SensorManager.SENSOR_DELAY_UI);
        sensorManager.registerListener(sensorEventListener, sensor1, SensorManager.SENSOR_DELAY_UI);
    }

    private class AlgCallBacks extends Callbacks {
        @Override
        public void on_cut_finished(boolean isSucess, Bitmap cuttedImg, String savedPath) {
            if (isSucess) {
                bitmapHolder.storeBitmap(cuttedImg);
                float scale = (float) (720.0 / cuttedImg.getWidth());
                bitmapHolder.scaleBitmap(720, (int) (cuttedImg.getHeight() * scale), JniBitmapHolder.ScaleMethod.NearestNeighbour);
                cutBitmap = bitmapHolder.getBitmapAndFree();
                timeMillis = System.currentTimeMillis();
                waitForJniToCut();
            } else {
                restartPreview(true, true);
                showMessage(R.string.take_photo_error);
            }


        }

        @Override
        public void on_color_trans_finished_with_keypoints(boolean isSucess, Bitmap dstImg, String savedPath, float leftTopX, float leftTopY, float rightTopX, float rightTopY, float leftDownX, float leftDownY, float rightDownX, float rightDownY) {
            if (isSucess) {
                bitmapHolder.storeBitmap(dstImg);
                float scale = (float) (720.0 / dstImg.getWidth());
                bitmapHolder.scaleBitmap(720, (int) (dstImg.getHeight() * scale), JniBitmapHolder.ScaleMethod.NearestNeighbour);
                colorBitmap = bitmapHolder.getBitmapAndFree();
                waitForJniToColor();
            } else {
                restartPreview(true, true);
                showMessage(R.string.take_photo_error);
            }
        }


        @Override
        public void on_notify_angle(int angle) {
            super.on_notify_angle(angle);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    float rotateAngle = 0;
                    long duration = 1;
                    if (angle == 90) {
                        rotateAngle = -90;
                        duration = 500;
                    } else if (angle == 180) {
                        rotateAngle = -180;
                        duration = 500;
                    } else if (angle == 270) {
                        rotateAngle = 90;
                        duration = 500;
                    }
                    LogUtils.e("SaoMiao_ALG", rotateAngle, duration);
                    ObjectAnimator rotationAnim = ObjectAnimator.ofFloat(cameraCutImg, "rotation", 0, rotateAngle);
                    rotationAnim.setDuration(duration);
                    rotationAnim.addListener(roateAnim);
                    rotationAnim.start();
                }
            });
        }

        @Override
        public void on_need_focus() {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (needAlgFocus.get()) {
                        LogUtils.d("算法ccc");
                        checkStaticTime();
                    }
                }
            });
        }
    }

    private void generateNewPath() {
        originalPath = dirPath + docIdUUid + Constants.ORIGINAL_JPG;
        basePath = dirPath + docIdUUid + Constants.BASE_JPG;
        baseSmallPath = dirPath + docIdUUid + Constants.BASE_SMALL_JPG;
        processPath = dirPath + docIdUUid + Constants.JPG;
        processSmallPah = dirPath + docIdUUid + Constants.SMALL_JPG;
    }

    private void initGuideView() {
        cameraGuideView = (CameraGuideView) findViewById(R.id.camera_guide_view);
        if (isSingle) {
            if (userPreferences.isBookModeGuide()) {
                cameraGuideView.setOnCameraGuideViewNextBtnOnClickListener(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        userPreferences.setIsBookModeGuide(false);
                        cameraGuideView.setVisibility(View.GONE);
                        return null;
                    }
                });
                cameraGuideView.startAnimBookMode();
            } else {
                cameraGuideView.setVisibility(View.GONE);
            }
        } else {
            if (userPreferences.isEdgeModeGuide()) {
                cameraGuideView.setOnCameraGuideViewNextBtnOnClickListener(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        userPreferences.setIsEdgeModeGuide(false);
                        cameraGuideView.setVisibility(View.GONE);
                        return null;
                    }
                });
                cameraGuideView.startAnimEdgeMode();
            } else {
                cameraGuideView.setVisibility(View.GONE);
            }
        }


    }


    private void registerEvent() {
        flashBtn.setOnClickListener(this);
        idCardLl.setOnClickListener(this);
        businessLl.setOnClickListener(this);
        cameraFlashModeRl.setOnClickListener(this);
        previewNoIconBackBtn.setOnClickListener(this);
        previewSurfaceView.setOnSlideListener(onSlideListener);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case ROTATE:
                refreshList();
                break;
            case CUT:
                if (isCard) {
                    isFrontCard = true;
                    showIdCardView();
                }
                refreshList();
                break;
            case CARD_IS_FRONT:
                if (isCard) {
                    CardFrontEvent cardFrontEvent = (CardFrontEvent) event;
                    float rotate = cardFrontEvent.getRotate();
                    boolean cut = cardFrontEvent.isCut();
                    if (!cut && (rotate == 360F || rotate == 0F) && !isFrontCard) {
                        isFrontCard = false;
                    } else {
                        isFrontCard = true;
                        showIdCardView();
                    }
                }

                break;
            case DELETE_FILE:
                if (isCard) {
                    DeleteEvent deleteEvent = (DeleteEvent) event;
                    if (deleteEvent.getDocId().equals(docIdUUid)) {
                        isFrontCard = true;
                        showIdCardView();
                    }
                }
                break;

            default:
                break;
        }
    }

    private CardPreviewSurfaceView.OnSlideListener onSlideListener = new CardPreviewSurfaceView.OnSlideListener() {
        @Override
        public void onSlide(boolean isRight) {
            if (!isInCamera.get()) {
                if (isRight) {
                    transToCard();
                } else {
                    transToBusiness();
                }
            }


        }
    };

    private void transToBusiness() {
        if (isCard && !isInCamera.get()) {
            isCard = !isCard;
            isFrontCard = true;
            transBusinessAnim();
            showBussinesView();
        }
    }

    private void transToCard() {
        if (!isCard && !isInCamera.get()) {
            isCard = !isCard;
            transIdCardAnim();
            showIdCardView();
        }
    }

    private void showBussinesView() {
        bottomToast.setVisibility(View.GONE);
        idCardImg.setVisibility(View.GONE);
        idCardImg.setAlpha(1F);
        middleToast.setVisibility(View.VISIBLE);
        businessTv.setAlpha(1f);
        idCardTv.setAlpha(0.3f);

        ObjectAnimator animator = ObjectAnimator.ofFloat(middleToast, "alpha", 1f, 1f).setDuration(800);
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                ObjectAnimator.ofFloat(middleToast, "alpha", 1f, 0f).setDuration(300).start();
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        animator.start();
    }

    private void transBusinessAnim() {
        ValueAnimator slideAnim = ValueAnimator.ofInt(ScreenUtils.getScreenWidth() / 2 - SizeUtils.getMeasuredWidth(idCardTv) / 2, ScreenUtils.getScreenWidth() / 2 - SizeUtils.dp2px(20) - SizeUtils.getMeasuredWidth(businessTv) / 2 - SizeUtils.getMeasuredWidth(idCardTv));
        slideAnim.addUpdateListener(slideAnimListener);
        slideAnim.setDuration(300);
        slideAnim.start();
        ObjectAnimator animator1 = ObjectAnimator.ofFloat(businessTab, "alpha", businessTab.getAlpha(), 1f).setDuration(300);
        animator1.start();
        ObjectAnimator animator2 = ObjectAnimator.ofFloat(idCardTab, "alpha", idCardTab.getAlpha(), 0f).setDuration(300);
        animator2.start();


    }

    private ValueAnimator.AnimatorUpdateListener slideAnimListener = new ValueAnimator.AnimatorUpdateListener() {
        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            int leftMargin = (Integer) animation.getAnimatedValue();
            RelativeLayout.LayoutParams cameraTabLlLayoutParams = (RelativeLayout.LayoutParams) cameraTabLl.getLayoutParams();
            cameraTabLlLayoutParams.leftMargin = leftMargin;
            cameraTabLl.setLayoutParams(cameraTabLlLayoutParams);
        }
    };


    private void showIdCardView() {
        idCardImg.setImageResource(isFrontCard ? R.mipmap.id_card_front : R.mipmap.id_card_back);
        LogUtils.e("where am i");
        bottomToast.setText(isFrontCard ? getString(R.string.id_card_front) : getString(R.string.id_card_back));
        bottomToast.setVisibility(View.VISIBLE);
        idCardImg.setVisibility(View.VISIBLE);
        middleToast.setVisibility(View.GONE);
        middleToast.setAlpha(1f);
        businessTv.setAlpha(0.3f);
        idCardTv.setAlpha(1f);
        showCardAnim();
    }

    private void showCardAnim() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(idCardImg, "alpha", 1f, 1f).setDuration(1000);
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {

            }

            @Override
            public void onAnimationEnd(Animator animator) {
                ObjectAnimator.ofFloat(idCardImg, "alpha", 1f, 0f).setDuration(400).start();
            }

            @Override
            public void onAnimationCancel(Animator animator) {

            }

            @Override
            public void onAnimationRepeat(Animator animator) {

            }
        });
        animator.start();
    }

    private void transIdCardAnim() {
        ValueAnimator slideAnim = ValueAnimator.ofInt(ScreenUtils.getScreenWidth() / 2 - SizeUtils.dp2px(20) - SizeUtils.getMeasuredWidth(businessTv) / 2 - SizeUtils.getMeasuredWidth(idCardTv), ScreenUtils.getScreenWidth() / 2 - SizeUtils.getMeasuredWidth(idCardTv) / 2);
        slideAnim.addUpdateListener(slideAnimListener);
        slideAnim.setDuration(400);
        slideAnim.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(idCardTab, "alpha", idCardTab.getAlpha(), 1f).setDuration(300);
        animator1.start();
        ObjectAnimator animator2 = ObjectAnimator.ofFloat(businessTab, "alpha", businessTab.getAlpha(), 0f).setDuration(300);
        animator2.start();
//
//        ObjectAnimator animator3 = ObjectAnimator.ofFloat(idCardTv, "alpha", idCardTv.getAlpha(), 1f).setDuration(300);
//        animator1.start();
//        ObjectAnimator animator4 = ObjectAnimator.ofFloat(businessTv, "alpha", businessTv.getAlpha(), 0.3f).setDuration(300);
//        animator2.start();
    }

    /**
     * @des: 相机准备监听
     * @params:
     * @return:
     */

    private CardCameraPreview.OnCameraReadyListener onCameraReadyListener = new CardCameraPreview.OnCameraReadyListener() {
        @Override
        public void cameraIsReady() {

            size = cameraPreview.getCamera().getParameters().getPreviewSize();
            rgba = new int[size.width * size.height];
            scale = ((float) ScreenUtils.getScreenWidth()) / ((float) size.height);
            cameraPreview.getCamera().setPreviewCallback(previewCallback);
            if (isCard) {
                idCardTab.setAlpha(1f);
                businessTab.setAlpha(0f);
                showIdCardView();
            } else {
                idCardTab.setAlpha(0f);
                businessTab.setAlpha(1f);
            }
            previewToDraw();
            isStartPreview.set(true);
        }
    };

    private void previewToDraw() {
        if (isPreview.get()) {
            LogUtils.i("is already preview!");
            return;
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (isRun) {
                    isPreview.set(true);
                    while (isPreview.get()) {
                        if (previewData == null) {
                            try {
                                Thread.sleep(100);
                            } catch (InterruptedException e) {
                                LogUtils.e("break_preview" + e);
                                break;
                            }
                        } else {

                            final long timeBegin = System.currentTimeMillis();
                            args = scanProAlg.miao_detect(previewData, size.width, size.height, isCard ? CZSaoMiao_Alg.ScanType.IDCARD : CZSaoMiao_Alg.ScanType.ENTERPRISE_DOC);
                            drawRedRec(args);
                            long algTime = System.currentTimeMillis() - timeBegin;
//                            LogUtils.iTag("algTime", algTime);
                            threadSleepTo100Ms(algTime);
                        }
                    }
                }

            }
        }).start();


    }


    /**
     * @des: 预览回调
     * @params:
     * @return:
     */

    private Camera.PreviewCallback previewCallback = new Camera.PreviewCallback() {
        @Override
        public void onPreviewFrame(final byte[] data, final Camera camera) {
            previewData = data;

        }
    };


    /**
     * @des: 画页码和边框
     * @params:
     * @return:
     */

    private void drawRedRec(Args args) {
        //页码边框显示
        runOnUiThread(new Runnable() {

            @Override
            public void run() {
                if (args.isHasObject()) {
                    cameraBorderView.setVisibility(View.VISIBLE);
                    cameraBorderView.show(args, scale, size.height, true);
                } else {
                    isPageRecVisible = false;
                    cameraBorderView.setVisibility(View.GONE);
                }
            }

        });
    }

    private double getDistance(Point a, Point b) {
        double x = Math.abs(a.x - b.x);
        double y = Math.abs(a.y - b.y);
        return Math.sqrt(x * x + y * y);
    }

    /**
     * @des: 对焦
     * @params:
     * @return:
     */

    private void cameraToFocus() {
        cameraPreview.getCamera().autoFocus(new Camera.AutoFocusCallback() {
            @Override
            public void onAutoFocus(boolean success, Camera camera) {
                isFocusSuccess = success;
            }
        });
    }

    /**
     * @des: 自动对焦后拍照
     * @params:
     * @return:
     */

    private void takePicture() {
        takePictureAndDoColorMode();
    }

    /**
     * @des: 拍照、裁剪、色彩模式
     * @params:
     * @return:
     */
    private void takePictureAndDoColorMode() {
        try {
            cameraPreview.getCamera().takePicture(null, null, new Camera.PictureCallback() {
                @Override
                public void onPictureTaken(final byte[] data, final Camera camera) {
                    if (data != null) {
                        cameraPreview.getCamera().cancelAutoFocus();
                        isStartPreview.set(false);
                        camera.stopPreview();
                        isPrint.set(true);
                        CardCameraActivity.this.data = data;
                        showBlackShade();

                    } else {
                        restartPreview(true, true);
                        showMessage(R.string.take_photo_error);
                    }
                }
            });
        } catch (Exception e) {
            LogUtils.e(e);
            restartPreview(true, true);
            showMessage(R.string.take_photo_error);
        }
    }


    /**
     * @des: 重新开始预览画面
     * @params:
     * @return:
     */
    private synchronized void restartPreview(final boolean isHideScanRl, boolean isError) {
        if (isCard) {
            showIdCardView();
        }
        isRun = true;
        if (isError) {
            isInCamera.set(false);
        }
        previewData = null;
        resetBitmap();
        runOnUiThread(new Runnable() {
            @Override
            public void run() {

                if (isHideScanRl) {
                    cameraScanRl.setVisibility(View.GONE);
                    //捕获异常取消正在执行的动画
                    cancelAnim();
                }

//                    cameraPrintImg.setVisibility(View.GONE);
                cameraFlashView.setVisibility(View.GONE);
                cameraFlashView.setAlpha(0);
                previewNoIconBackBtn.setVisibility(View.VISIBLE);
                cameraFlashModeBtn.setVisibility(View.VISIBLE);
                previewNoIconBackBtn.setEnabled(true);
                previewNoIconBackBtn.setClickable(true);
                cameraFlashModeRl.setEnabled(true);
                cameraFlashModeRl.setClickable(true);
                cameraBlackShade.setVisibility(View.GONE);
                cameraCutImg.setVisibility(View.GONE);
                cameraColorImg.setVisibility(View.GONE);
                flashBtn.setVisibility(View.VISIBLE);
                Camera camera = cameraPreview.getCamera();
                if (camera != null) {
                    camera.startPreview();
                    isStartPreview.set(true);
                    camera.setPreviewCallback(previewCallback);
                    previewToDraw();
                }

            }
        });

    }

    private void resetBitmap() {
        isGetColorBitmap.set(false);
        isWaitingColor.set(false);
        isGetCutBitmap.set(false);
        isWaitingCut.set(false);
        colorBitmap = null;
        previewData = null;
        cutBitmap = null;
    }

    /**
     * @des: 裁剪并作色彩模式
     * @params:
     * @return:
     */
    private void cutAndDoColorMode() {
        timeMillis = System.currentTimeMillis();
        //拍照的时候的页码为最后页码
        if (FileUtils.createOrExistsDir(dirPath)) {
            Args args = new Args();
            args.setScanType(isCard ? CZSaoMiao_Alg.ScanType.IDCARD : CZSaoMiao_Alg.ScanType.ENTERPRISE_DOC);
            args.setColorType(isCard ? CZSaoMiao_Alg.ColorType.IDCARD_DEFAULT : CZSaoMiao_Alg.ColorType.ENTERPRISE_DOC_DEFAULT);
            Args backArgs = scanProAlg.miao_single_pipeline(data, data.length, originalPath, basePath, processPath, baseSmallPath, processSmallPah, getFilesDir().getAbsolutePath() + File.separator + Constants.ID_CARD_PATH, isFrontCard, args);
        } else {
            restartPreview(true, true);
            showMessage(R.string.camera_error);
        }

    }

    /**
     * @des: 隐藏页码边框，显示黑色遮罩,然后消失
     * @params:
     * @return:
     */
    private void showBlackShade() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                cutAndDoColorMode();
            }
        }).start();

        bottomToast.setVisibility(View.GONE);
        cameraBorderView.setVisibility(View.GONE);
        cameraBlackShade.setVisibility(View.VISIBLE);
        cameraBlackShade.show(args, size.height, scale);
        blackShadeShowAnim = ObjectAnimator.ofFloat(cameraBlackShade, "alpha", 0, 1.0f);
        blackShadeShowAnim.setDuration(500);
        blackShadeShowAnim.start();
        blackShadeShowAnim.addListener(blackShadeShowAnimListener);

    }

    /**
     * @des: 隐藏黑色遮罩并且开始显示扫描画面
     * @params:
     * @return:
     */
    private void hideBlackShadeAndShowRotate() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideShadeAndShowCutAnim = ObjectAnimator.ofFloat(cameraScanRl, "alpha", 0, 1.0f);
                hideShadeAndShowCutAnim.setDuration(1000);
                hideShadeAndShowCutAnim.addListener(hideShadeAndShowRotateAnimListener);
                hideShadeAndShowCutAnim.start();
            }
        });
    }


    /**
     * @des: 拍照显示动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener showWhiteViewAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
        }

        @Override
        public void onAnimationEnd(Animator animation) {


        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };
    /**
     * @des: 黑色遮罩显示动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener blackShadeShowAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            waitForAnimToCut();
        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };
    /**
     * @des: 黑色遮罩显示动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener roateAnim = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            //根据动画设置扫描白图片高度
            ValueAnimator scanAnim = ValueAnimator.ofInt(0, height);
            scanAnim.addUpdateListener(scanUpdateListener);
            scanAnim.setDuration(1000);
            scanAnim.start();

        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };

    /**
     * @des: 扫描动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener hideShadeAndShowRotateAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
            cameraCutImg.setRotation(0);
            //重置上一次裁剪图片alpha值从0到1
            cameraCutImg.setAlpha(1.0f);
            //设置扫描的白色高为0
            ViewGroup.LayoutParams layoutParams = animView.getLayoutParams();
            layoutParams.height = 0;
            animView.setLayoutParams(layoutParams);

            cameraColorImg.setVisibility(View.GONE);
            cameraScanRl.setVisibility(View.VISIBLE);
            cameraCutImg.setVisibility(View.VISIBLE);
            cameraCutImg.setImageBitmap(cutBitmap);
        }

        @Override
        public void onAnimationEnd(Animator animation) {


        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };


    /**
     * @des: 扫描动画更新的监听
     * @params:
     * @return:
     */
    private ValueAnimator.AnimatorUpdateListener scanUpdateListener = new ValueAnimator.AnimatorUpdateListener() {

        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            //获取当前的height值
            int currentHeight = (Integer) animation.getAnimatedValue();
            ViewGroup.LayoutParams layoutParams = animView.getLayoutParams();
            layoutParams.height = currentHeight;
            animView.setLayoutParams(layoutParams);
            if (currentHeight == height) {
                waitForAnimToColor();
            }
        }
    };

    /**
     * @des: 隐藏裁剪图片显示色彩模式图片动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener hideCutAndShowColorAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
            cameraBlackShade.setVisibility(View.GONE);
            cameraColorImg.setVisibility(View.VISIBLE);
            cameraColorImg.setImageBitmap(colorBitmap);
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    ObjectAnimator hideScanToFinishAnim = ObjectAnimator.ofFloat(cameraScanRl, "alpha", 1.0f, 0);
                    hideScanToFinishAnim.setDuration(580);
                    hideScanToFinishAnim.setInterpolator(new DecelerateInterpolator());
                    hideScanToFinishAnim.addListener(hideScanToFinishAnimListener);
                    hideScanToFinishAnim.start();
                }
            }, 400);

        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };

    /**
     * @des: 隐藏裁剪图片显示色彩模式图片动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener hideScanToFinishAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
            checkCardIsFront();
            restartPreview(false, false);
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            saveToRealm();
        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }
    };

    private void checkCardIsFront() {
        if (isCard) {
            isFrontCard = !isFrontCard;
        }
        if (!isCard) {
            isFrontCard = true;
        }
    }

    /**
     * @des: 捕捉异常后取消动画
     * @params:
     * @return:
     */
    private void cancelAnim() {
        if (null != blackShadeShowAnim) {
            if (blackShadeShowAnim.isRunning()) {
                LogUtils.i("blackShadeShowAnim.cancel");
                blackShadeShowAnim.cancel();
            }
        }
        if (null != hideShadeAndShowCutAnim) {
            if (hideShadeAndShowCutAnim.isRunning()) {
                LogUtils.i("hideShadeAndShowCutAnim.cancel");
                hideShadeAndShowCutAnim.cancel();
            }
        }
    }

    /**
     * @des: 保存到数据库
     * @params:
     * @return:
     */
    private void saveToRealm() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                isListAdd = true;
                releaseBitmap();
                realm.executeTransaction(new Realm.Transaction() {
                    @Override
                    public void execute(Realm realm) {
                        createCategory();

                        DocEntity isHasFrontDoc = realm.where(DocEntity.class).equalTo("fileID", docIdUUid).equalTo("isDelete", 0).findFirst();
                        String curDate = formatter.format(new Date(System.currentTimeMillis()));
                        if (isHasFrontDoc != null) {
                            isHasFrontDoc.setDirty(1);
                            isHasFrontDoc.setCreateTime(curDate);
                            isHasFrontDoc.setUpdateTime(curDate);
                            isHasFrontDoc.setNewAdd(1);
                        } else {
                            DocEntity docEntity = realm.createObject(DocEntity.class, docIdUUid);
                            docEntity.setBucket(Constants.BUCKET);
                            docEntity.setTemp(1);
                            docEntity.setUuid(UUID.randomUUID().toString());
                            docEntity.setEnhanceMode(0);
                            docEntity.setUserID(getUserIdIsLogin());
                            if (type == 0) {
                                docEntity.setCategoryID(finalCategoryId);
                                docEntity.setCategoryName(finalCategoryName);
                            } else if (type == 1) {
                                docEntity.setNameTemp(1);
                                docEntity.setCategoryID(categoryID);
                                docEntity.setCategoryName(categoryName);
                            } else if (type == 2) {
                                docEntity.setTagId(tagId);
                                docEntity.setTagName(tagName);
                            }
                            docEntity.setNewAdd(1);
                            docEntity.setBucket(Constants.SCAN_PRO);
                            docEntity.setFileType(isCard ? 2 : 3);
                            docEntity.setDirty(1);
                            docEntity.setBaseImagePath(basePath);
                            docEntity.setBaseSmallImagePath(baseSmallPath);
                            docEntity.setOriginalImagePath(originalPath);
                            docEntity.setProcessSmallImagePath(processSmallPah);
                            docEntity.setProcessImagePath(processPath);
                            docEntity.setFileSize(FileUtils.getFileLength(basePath) + "");

                            docEntity.setCreateTime(curDate);
                            docEntity.setUpdateTime(curDate);
                            docEntity.setTakePhotoTime(curDate);
                        }
                        refreshList();
                        isInCamera.set(false);

                    }
                });

                previewToDraw();
            }
        }, 110);
    }

    /**
     * @des: 释放bitmap
     * @params:
     * @return:
     */
    private void releaseBitmap() {
        if (null != cutBitmap) {
            //释放bitmap
            if (cutBitmap.isRecycled()) {
                cutBitmap.recycle();
                cutBitmap = null;
            }
        }
        //释放色彩模式Bitmap
        if (null != colorBitmap) {
            if (colorBitmap.isRecycled()) {
                colorBitmap.recycle();
                colorBitmap = null;
            }
        }

    }

    /**
     * @des: 显示最后的色彩模式的图
     * @params:
     * @return:
     */
    private void showColorImg() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //扫描进度100的时候展示色彩模式之后的图片500ms
                ObjectAnimator hideCutImgAnim = ObjectAnimator.ofFloat(cameraCutImg, "alpha", 1.0f, 0);
                hideCutImgAnim.setDuration(350);
                hideCutImgAnim.start();
                ObjectAnimator showColorImgAnim = ObjectAnimator.ofFloat(cameraColorImg, "alpha", 0, 1.0f);
                showColorImgAnim.setDuration(600);
                showColorImgAnim.setInterpolator(new AccelerateDecelerateInterpolator());
                showColorImgAnim.addListener(hideCutAndShowColorAnimListener);
                showColorImgAnim.start();

            }
        });
    }


    /**
     * @des: 显示最后的色彩模式的图
     * @params:
     * @return:
     */
    private void showCutImage() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //扫描进度100的时候展示色彩模式之后的图片500ms
                ObjectAnimator hideCutImgAnim = ObjectAnimator.ofFloat(cameraCutImg, "alpha", 1.0f, 0);
                hideCutImgAnim.setDuration(350);
                hideCutImgAnim.start();
                ObjectAnimator showColorImgAnim = ObjectAnimator.ofFloat(cameraColorImg, "alpha", 0, 1.0f);
                showColorImgAnim.setDuration(600);
                showColorImgAnim.setInterpolator(new AccelerateDecelerateInterpolator());
                showColorImgAnim.addListener(hideCutAndShowColorAnimListener);
                showColorImgAnim.start();

            }
        });
    }


    private void refreshList() {
        docEntities = realm.where(DocEntity.class)
                .equalTo("isTemp", 1)
                .equalTo("isDelete", 0)
                .findAll()
                .sort("takePhotoTime", Sort.ASCENDING);
        docsCount = docEntities.size();
        checkPromptTvShow();
        if (docEntities.size() != 0) {
            recyclerView.smoothScrollToPosition(docEntities.size() - 1);
            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(new File(docEntities.get(docEntities.size() - 1).getProcessImagePath()))));
        }
        cameraPreviewAdapter.refreshData(docEntities, isListAdd, docEntities.size() - 1);
        isListAdd = false;
    }

    private void checkPromptTvShow() {
        if (docEntities.size() > 0) {
            cameraPromptTv.setVisibility(View.GONE);
        } else {
            cameraPromptTv.setVisibility(View.VISIBLE);
        }
    }

    /**
     * @des: 相机列表点击监听
     * @params:
     * @return:
     */

    private CameraPreviewAdapter.OnItemClickListener onItemClickListener = new CameraPreviewAdapter.OnItemClickListener() {
        @Override
        public void onItemClick(DocEntity docEntity, int position) {
            Intent intent = new Intent(CardCameraActivity.this, FilePreviewActivity.class);
            intent.putExtra("isCamera", true);
            intent.putExtra("type", 4);
            intent.putExtra("isItemTag", Validator.isNotEmpty(docEntity.getTagName()) ? 1 : 0);
            intent.putExtra("fileType", docEntity.getFileType());
            intent.putExtra("categoryName", docEntity.getCategoryName());
            intent.putExtra("categoryID", docEntity.getCategoryID());
            intent.putExtra("tagId", docEntity.getTagId());
            intent.putExtra("tagName", docEntity.getTagName());
            intent.putExtra("index", position);
            intent.putExtra("currentPageId", docEntity.getFileID());
            startActivity(intent);
        }
    };

    /**
     * @des: 返回时设置temp为0
     * @params:
     * @return:
     */
    private void resetTemp() {
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                docEntities = realm.where(DocEntity.class)
                        .equalTo("isTemp", 1)
                        .findAll().sort("takePhotoTime", Sort.ASCENDING);

                for (DocEntity docEntity : docEntities) {
                    docEntity.setTemp(0);
                    docEntity.setTakePhotoTime(Constants.TAKE_PHOTO_INIT_TIME);
                }
            }
        });

    }

    /**
     * @des: 如果不到100毫秒就休眠到100毫秒
     * @params:
     * @return:
     */
    private void threadSleepTo100Ms(long algTime) {
        if (algTime > 0 && algTime < 500) {
            try {
                long sleepTime = (long) (500.0f - algTime);
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * @des: 如果是jni色彩模式先做完就等动画做完再进行下一步
     * @params:
     * @return:
     */
    private void waitForAnimToColor() {
        if (isGetColorBitmap.get()) {
            LogUtils.i("get colorBitmap faster than anim");
            showColorImg();
        } else {
            isGetColorBitmap.set(false);
            isWaitingColor.set(true);
        }
    }


    /**
     * @des: 如果是动画先做完就等得到色彩模式的图片再去做下一步
     * @params:
     * @return:
     */
    private void waitForJniToColor() {
        if (isWaitingColor.get()) {
            LogUtils.i("get colorBitmap slowed than anim");
            showColorImg();
        } else {
            isWaitingColor.set(false);
            isGetColorBitmap.set(true);
        }
    }

    /**
     * @des: 如果是jni色彩模式先做完就等动画做完再进行下一步
     * @params:
     * @return:
     */
    private void waitForAnimToCut() {
        if (isGetCutBitmap.get()) {
            LogUtils.i("get cutBitmap faster than anim");
            hideBlackShadeAndShowRotate();
        } else {
            isGetCutBitmap.set(false);
            isWaitingCut.set(true);
        }
    }


    /**
     * @des: 如果是动画先做完就等得到色彩模式的图片再去做下一步
     * @params:
     * @return:
     */
    private void waitForJniToCut() {
        if (isWaitingCut.get()) {
            LogUtils.i("get cutBitmap slowed than anim");
            hideBlackShadeAndShowRotate();
        } else {
            isWaitingCut.set(false);
            isGetCutBitmap.set(true);
        }
    }

    /**
     * @des: 屏蔽返回键
     * @params:
     * @return:
     */
    private void shieldBack() {
        previewNoIconBackBtn.setVisibility(View.GONE);
        cameraFlashModeBtn.setVisibility(View.GONE);
    }

    /**
     * @des: 显示返回键
     * @params:
     * @return:
     */
    private void showBack() {
        previewNoIconBackBtn.setVisibility(View.VISIBLE);
        cameraFlashModeBtn.setVisibility(View.VISIBLE);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.button_flash) {
            if (!isInCamera.get()) {

                //拍照的时候把预览设成false
                previewNoIconBackBtn.setEnabled(false);
                previewNoIconBackBtn.setClickable(false);
                cameraFlashModeRl.setEnabled(false);
                cameraFlashModeRl.setClickable(false);
                if (isFrontCard) {
                    docIdUUid = UUID.randomUUID().toString();
                }
                generateNewPath();
                isPreview.set(false);
                isInCamera.set(true);
                showCameraFlashAnim();
                takePicture();

            }
        } else if (id == R.id.preview_no_icon_back_btn) {
            ActivityUtils.finishActivity(this);
        } else if (id == R.id.camera_flash_mode_rl) {
            switchFlashMode();
        } else if (id == R.id.id_card_ll) {
            transToCard();
        } else if (id == R.id.business_ll) {
            transToBusiness();
        }
    }

    /**
     * @des: 震动提醒
     * @params:
     * @return:
     */
    private void vibrateNotify() {

        if (System.currentTimeMillis() - lastVibrateTime <= 1000) {
            return;
        }
        vibrator.vibrate(200);
        lastVibrateTime = System.currentTimeMillis();
    }

    /**
     * @des: 显示照相闪光view
     * @params:
     * @return:
     */

    private void showCameraFlashAnim() {
        cameraFlashView.setVisibility(View.VISIBLE);
        cameraFlashView.setAlpha(0);
        showWhiteViewAnim = ObjectAnimator.ofFloat(cameraFlashView, "alpha", 0, 1.0f, 0);
        showWhiteViewAnim.setDuration(300);
        showWhiteViewAnim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {

            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        showWhiteViewAnim.start();
    }

    /**
     * @des: 切换闪关灯模式
     * @params:
     * @return:
     */
    private void switchFlashMode() {
        switch (mSgType) {
            case 0:
                cameraFlashModeBtn.setBackgroundResource(flashMode[0]);
                cameraPreview.setIsOpenFlashMode(Camera.Parameters.FLASH_MODE_OFF);
                mSgType = 1;
                break;
            case 1:
                cameraFlashModeBtn.setBackgroundResource(flashMode[1]);
                cameraPreview.setIsOpenFlashMode(Camera.Parameters.FLASH_MODE_AUTO);
                mSgType = 2;
                break;
            case 2:
                cameraFlashModeBtn.setBackgroundResource(flashMode[2]);
                cameraPreview.setIsOpenFlashMode(Camera.Parameters.FLASH_MODE_ON);
                mSgType = 0;
                break;

            default:
                break;
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        isRun = true;
        previewToDraw();

    }


    @Override
    protected void onPause() {
        super.onPause();
        isRun = false;
        isPreview.set(false);

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        sensorManager.unregisterListener(sensorEventListener);
        isAutoSync();
        if (docEntities.size() > 0) {
            if (type == 0) {
                createCategory();
                EventBus.getDefault().post(new CategoryEvent(EventType.ADD_CATEGORY));
                EventBus.getDefault().post(new FileEvent(EventType.ADD_FILES, docEntities.size()));
            } else {
                EventBus.getDefault().post(new FileEvent(EventType.ADD_FILES, docEntities.size()));
            }
        } else {
            EventBus.getDefault().post(new CategoryEvent(EventType.DELETE_CATEGORY));
            realm.executeTransaction(new Realm.Transaction() {
                @Override
                public void execute(Realm realm) {
                    CategoryEntity sameCategory = realm.where(CategoryEntity.class)
                            .equalTo("categoryID", finalCategoryId)
                            .equalTo("isDelete", 0)
                            .findFirst();
                    if (sameCategory != null) {
                        sameCategory.setDelete(1);
                    }
                }
            });
        }
        resetTemp();
        realm.close();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private void createCategory() {
        if (Validator.isEmpty(finalCategoryName) && (type == 0 || type == 2)) {
            EventBus.getDefault().post(new CategoryEvent(EventType.ADD_CATEGORY_HIDE));
            int i = 0;
            String bookName = Constants.CATEGORY_DEFAULT_NAME;
            String finalName = bookName;
            Realm realm = Realm.getDefaultInstance();
            CategoryEntity sameCategory = realm.where(CategoryEntity.class)
                    .equalTo("categoryName", Constants.CATEGORY_DEFAULT_NAME)
                    .equalTo("isDelete", 0)
                    .findFirst();
            while (Validator.isNotEmpty(sameCategory)) {
                long current = System.currentTimeMillis();
                i++;
                finalName = bookName + i;
                sameCategory = realm.where(CategoryEntity.class)
                        .equalTo("categoryName", finalName)
                        .equalTo("isDelete", 0)
                        .findFirst();
                LogUtils.i(System.currentTimeMillis() - current);
            }
            finalCategoryName = finalName;

            finalCategoryId = UUID.randomUUID().toString();
            CategoryEntity categoryEntity = realm.createObject(CategoryEntity.class, finalCategoryId);
            categoryEntity.setUserID(getUserIdIsLogin());
            categoryEntity.setDirty(1);
            categoryEntity.setCategoryName(finalCategoryName);
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            String curDate = formatter.format(new Date(System.currentTimeMillis()));
            categoryEntity.setCreateTime(curDate);
            categoryEntity.setUpdateTime(curDate);
        }
    }

    /**
     * @des: 是否自动同步
     * @params:
     * @return:
     */

    private void isAutoSync() {
        if (docsCount > 0) {
            startSyncNow();
        }
    }

    public static final int STATUS_NONE = 0;
    public static final int STATUS_STATIC = 1;
    public static final int STATUS_MOVE = 2;
    private int mX, mY, mZ;
    private int STATUE = STATUS_NONE;
    boolean canFocus = false;
    boolean canFocusIn = false;
    boolean isFocusing = false;
    private Calendar mCalendar;
    private final double moveIs = 1.4;
    private long lastStaticStamp = 0;
    public static final int DELAY_DURATION = 500;
    private long staticTime = 0;

    private void restParams() {
        STATUE = STATUS_NONE;
        canFocusIn = false;
        mX = 0;
        mY = 0;
        mZ = 0;
    }

    private void checkStaticTime() {
        if (System.currentTimeMillis() - staticTime > 800) {
            if (cameraPreview.getCamera() != null) {
                checkStartPreview();
            }
        }
    }


    private SensorEventListener sensorEventListener = new SensorEventListener() {
        @Override
        public void onSensorChanged(SensorEvent sensorEvent) {

            if (isFocusing) {
                restParams();
                return;
            }
            if (sensorEvent.sensor.getType() == Sensor.TYPE_ORIENTATION) {
                float yAngle = sensorEvent.values[1];
                // 获取与Z轴的夹角
                float zAngle = sensorEvent.values[2];

                if (Math.abs(zAngle - yAngle) > 40 && zAngle > 3f && zAngle <= 180f && Math.abs(yAngle) < 45) {
                    if (System.currentTimeMillis() - oritationTime > 100) {
                        if (oritationType == 3 || oritationType == 4) {
                            turnRight();
                        }
                        oritationType = 1;
                        oritationTime = System.currentTimeMillis();
//                    LogUtils.i("向左转");
                    }

                } else if (Math.abs(zAngle - yAngle) > 40 && zAngle < -3f && zAngle >= -180f && Math.abs(yAngle) < 60) {
                    if (System.currentTimeMillis() - oritationTime > 100) {
                        if (oritationType == 3 || oritationType == 4) {
                            turnLeft();
                        }
                        oritationType = 2;
                        oritationTime = System.currentTimeMillis();
//                    LogUtils.i("向右转");
                    }

                } else {
                    if (System.currentTimeMillis() - oritationTime > 100) {
                        if (oritationType == 1) {
                            turnBackLeft();
                        } else if (oritationType == 2) {
                            turnBackRight();
                        }
                        oritationType = 4;
                        oritationTime = System.currentTimeMillis();
//                    LogUtils.i("向下转");
                    }

                }
//            LogUtils.i(yAngle, zAngle);

            } else if (sensorEvent.sensor.getType() == Sensor.TYPE_ACCELEROMETER) {
                int x = (int) sensorEvent.values[0];
                int y = (int) sensorEvent.values[1];
                int z = (int) sensorEvent.values[2];

                mCalendar = Calendar.getInstance();
                long stamp = mCalendar.getTimeInMillis();
                int second = mCalendar.get(Calendar.SECOND);
                if (STATUE != STATUS_NONE) {
                    int px = Math.abs(mX - x);
                    int py = Math.abs(mY - y);
                    int pz = Math.abs(mZ - z);
                    double value = Math.sqrt(px * px + py * py + pz * pz);

                    if (value > moveIs) {
                        STATUE = STATUS_MOVE;
                    } else {
                        if (STATUE == STATUS_MOVE) {
                            lastStaticStamp = stamp;
                            canFocusIn = true;
                        }

                        if (canFocusIn) {
                            needAlgFocus.set(false);
                            if (stamp - lastStaticStamp > DELAY_DURATION) {
                                //移动后静止一段时间，可以发生对焦行为
                                if (!isFocusing) {
                                    canFocusIn = false;

                                    checkStaticTime();
                                }
                            }
                        }

                        STATUE = STATUS_STATIC;
                        needAlgFocus.set(true);
                    }
                } else {
                    lastStaticStamp = stamp;
                    STATUE = STATUS_STATIC;
                    needAlgFocus.set(true);

                }

                mX = x;
                mY = y;
                mZ = z;
            }


        }

        @Override
        public void onAccuracyChanged(Sensor sensor, int i) {
//            LogUtils.d(sensor, i);

        }
    };

    private void checkStartPreview() {
        if (isStartPreview.get()) {
            try {
                final Camera camera = cameraPreview.getCamera();
                if (camera != null) {
                    camera.autoFocus(new Camera.AutoFocusCallback() {
                        @Override
                        public void onAutoFocus(boolean b, Camera camera) {

                            staticTime = System.currentTimeMillis();
                            camera.cancelAutoFocus();

                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();

            }

        }

    }

    private void turnLeft() {
        float rotation = previewNoIconBackBtn.getRotation();
        float rotation1 = cameraFlashModeBtn.getRotation();
        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", 0, -90f);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", 0, -90f);
        animator1.setDuration(300);
        animator1.start();
    }

    private void turnBackLeft() {

        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", 90f, 0f);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", 90f, 0f);
        animator1.setDuration(300);
        animator1.start();
    }

    private void turnRight() {
        float rotation = previewNoIconBackBtn.getRotation();
        float rotation1 = cameraFlashModeBtn.getRotation();
        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", 0, 90f);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", 0, 90f);
        animator1.setDuration(300);
        animator1.start();
    }

    private void turnBackRight() {
        float rotation = previewNoIconBackBtn.getRotation();
        float rotation1 = cameraFlashModeBtn.getRotation();
        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", -90f, 0);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", -90f, 0);
        animator1.setDuration(300);
        animator1.start();
    }


    /**
     * 隐藏虚拟按键，并且全屏
     */
    protected void hideBottomUIMenu() {
        if (!needShowStatusBar) {
            //隐藏虚拟按键，并且全屏
            if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) {
                View v = this.getWindow().getDecorView();
                v.setSystemUiVisibility(View.GONE);
            } else if (Build.VERSION.SDK_INT >= 19) {
                //for new api versions.
                View decorView = getWindow().getDecorView();
                int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
                        | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
                        | View.SYSTEM_UI_FLAG_IMMERSIVE;
                decorView.setSystemUiVisibility(uiOptions);
                getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            }
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (!needShowStatusBar) {
            if (hasFocus && Build.VERSION.SDK_INT >= 19) {
                View decorView = getWindow().getDecorView();
                decorView.setSystemUiVisibility(
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                                | View.SYSTEM_UI_FLAG_FULLSCREEN
                                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
            }
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        //屏蔽back按键
        return (keyCode == KeyEvent.KEYCODE_BACK) && isInCamera.get() ? true : super.onKeyDown(keyCode, event);
    }

}