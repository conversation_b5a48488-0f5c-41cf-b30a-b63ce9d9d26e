package com.czur.scanpro.ui.sync;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.event.BaseEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by Yz on 2018/5/11
 * Email：<EMAIL>
 */


public class AutoSyncTimeCountService extends Service {


    public static final int AUTO_SYNC_TIME = 1 * 1000;
    private SimpleDateFormat formatter;
    private int i;
    private boolean isRun;


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        initNotification();
    }

    /**
     * Activity中一启动Service之后，就会调用 onStartCommand()方法
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        initComponent();
        initThreadToSync();
        return START_STICKY;
    }

    private void initComponent() {
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        isRun = true;
        i = 0;

    }

    /**
     * @des: 初始化单线程线程池
     * @params:
     * @return:
     */
    private void initThreadToSync() {

        String curDate = formatter.format(new Date(System.currentTimeMillis()));
        LogUtils.i("开始计时", curDate, isRun);
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (i <= 59) {
                    try {
                        if (!isRun) {
                            stopTimeCount();
                            return;
                        }
                        i++;
//                        LogUtils.i( "time count: " + i + " s");
                        Thread.sleep(AUTO_SYNC_TIME);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                LogUtils.i("计时执行同步", formatter.format(new Date(System.currentTimeMillis())));
                if (!ServiceUtils.isServiceRunning(SyncService.class)) {
                    Intent intent = new Intent(AutoSyncTimeCountService.this, SyncService.class);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        startForegroundService(intent);
                    }else {
                        startService(intent);
                    }
                }
                stopTimeCount();
            }

        }).start();

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case STOP_SYNC_TIME_COUNT:
                i = 0;
                isRun = false;
                LogUtils.i("TimeCount 收到 EventBus 被动停止");
                break;

            case RESET_TIME_COUNT:
                i = 0;
                isRun = true;
                LogUtils.i("TimeCount reset: " + i + " s");
                break;
            default:
                break;
        }
    }

    /**
     * @des: 停止Service
     * @params:
     * @return:
     */

    private void stopTimeCount() {
        isRun = false;
        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService.class)) {
            LogUtils.i("time count  stopped  by self");
            stopSelf();
            ServiceUtils.stopService(AutoSyncTimeCountService.class);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            stopForeground(true);
        }
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
    private void initNotification() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            // 提高进程优先级 ，就会在通知栏中出现自己的应用，如果不想提高优先级，可以把这个注释
            // 参数1：id 参数2：通知
            String channelId = "com.czur.cloud";
            String channelName = "Channel CZUR";
            NotificationChannel notificationChannel = null;

            notificationChannel = new NotificationChannel(channelId,
                    channelName, NotificationManager.IMPORTANCE_HIGH);
//            notificationChannel.enableLights(true);
//            notificationChannel.setLightColor(Color.RED);
            notificationChannel.enableVibration(false);
            notificationChannel.setSound(null,null);
            notificationChannel.setShowBadge(true);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            Notification notification = new NotificationCompat.Builder(this)
                    .setChannelId(channelId)
                    .setContentTitle(getString(R.string.sync_title))
//                .setContentText(getString(R.string.sync_books_title))
                    .setWhen(System.currentTimeMillis())
                    .setSmallIcon(R.mipmap.ic_launcher)
                    .build();
            int messageId = 2;
            startForeground(messageId, notification);
        }
    }
    public void showMessage(int resId) {
        ToastUtils.showShort(resId);
    }

    public void showMessage(String text) {
        ToastUtils.showShort(text);
    }
}

