package com.czur.scanpro.ui.file.adjust

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.scanpro.R
import com.czur.scanpro.databinding.ActivityAdjustEdgeBinding
import com.czur.scanpro.databinding.ActivityAdjustGuideBinding
import com.czur.scanpro.ui.base.BaseActivity

/**
 * Created by Yz on 2019/3/14.
 * Email：<EMAIL>
 */
class AdjustGuideActivity : BaseActivity(), View.OnClickListener {
    val binding: ActivityAdjustGuideBinding by lazy{
        ActivityAdjustGuideBinding.inflate(layoutInflater)
    }
    
    
    private var finalCategoryName: String? = null
    private var finalCategoryId: String? = null
    private var isCamera = false
    private var tagId: String? = null
    private var tagName: String? = null
    private var categoryID: String? = null
    private var categoryName: String? = null
    private var type = 0
    private var fileID: String? = null
    private var originalImagePath: String? = null
    private var isSimple: Boolean = false


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_adjust_guide)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        isCamera = intent.getBooleanExtra("isCamera", false)
        tagId = intent.getStringExtra("tagId")
        tagName = intent.getStringExtra("tagName")
        categoryID = intent.getStringExtra("categoryID")
        categoryName = intent.getStringExtra("categoryName")
        type = intent.getIntExtra("type", 0)
        fileID = intent.getStringExtra("fileID")
        isSimple = intent.getBooleanExtra("isSimple", false)

        finalCategoryId = intent.getStringExtra("finalCategoryId")
        finalCategoryName = intent.getStringExtra("finalCategoryName")

        originalImagePath = intent.getStringExtra("originalImagePath")
        if (isSimple){
            binding.adjustGuideImg.setImageResource(R.mipmap.simple_adjust_guide)
            binding.adjustGuideTv.text = getString(R.string.adjustGuideTv1)
        }else{
            binding.adjustGuideImg.setImageResource(R.mipmap.book_adjust_guide)
            binding.adjustGuideTv.text = getString(R.string.adjustGuideTv)
        }


    }

    private fun registerEvent() {

        binding.knownBtn.setOnClickListener(this)

    }


    private val ADJUST_BOOK_CODE = 999
    override fun onClick(v: View) {
        when (v.id) {

            R.id.knownBtn -> {
                if (isSimple){
                    getSp().simpleGuideFirst = true

                    val intent = Intent(this, AdjustEdgeSimpleActivity::class.java)
                    intent.putExtra("fileID",fileID)
                    intent.putExtra("originalImagePath",originalImagePath)
                    ActivityUtils.startActivity(intent)
                }else{
                    getSp().edgeGuideFirst = true
                    val intent = Intent(this@AdjustGuideActivity, AdjustEdgeBookActivity::class.java)
                    intent.putExtra("fileID", fileID)
                    intent.putExtra("originalImagePath", originalImagePath)
                    intent.putExtra("type", type)
                    intent.putExtra("finalCategoryId", finalCategoryId)
                    intent.putExtra("finalCategoryName", finalCategoryName)
                    if (type == 2) {
                        intent.putExtra("tagName", tagName)
                        intent.putExtra("tagId", tagId)
                    } else if (type == 1) {
                        intent.putExtra("categoryID", categoryID)
                        intent.putExtra("categoryName", categoryName)
                    }
                    intent.putExtra("isCamera", true)
                    startActivityForResult(intent, ADJUST_BOOK_CODE)
                }

                finish()
            }

            else -> {
            }
        }
    }


}