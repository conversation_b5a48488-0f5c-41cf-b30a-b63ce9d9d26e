package com.czur.scanpro.adapter

import android.content.Context
import android.graphics.Paint
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.R
import com.czur.scanpro.entity.model.ProductModel
import com.google.gson.Gson


/**
 * Created by Yz on 2019/1/5.
 * Email：<EMAIL>
 */
class PayAdapter
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<ProductModel>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private val ITEM_TYPE_RECT = 0
        private val ITEM_TYPE_TOP_ROUND = 1
        private val ITEM_TYPE_BOTTOM_ROUND = 2
        private val ITEM_TYPE_ONLY_ONE = 3


    }


    fun refreshData(payList: List<ProductModel>) {
        this.datas = payList
        notifyDataSetChanged()

    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        when (viewType) {
            ITEM_TYPE_TOP_ROUND -> {
                val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_top_round, parent, false)
                return PayHolder(view, viewType)
            }
            ITEM_TYPE_BOTTOM_ROUND -> {
                val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_bottom_round, parent, false)
                return PayHolder(view, viewType)
            }
            ITEM_TYPE_RECT -> {
                val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_middle, parent, false)
                return PayHolder(view, viewType)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_middle, parent, false)
                return PayHolder(view, viewType)
            }
        }

    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        if (holder is PayHolder) {
            holder.mItem = datas[position]
            if (holder.mItem!!.showPrice != holder.mItem!!.price) {
                holder.payDeleteTv!!.visibility = View.VISIBLE
                holder.payDeleteTv!!.paintFlags = holder.payDeleteTv!!.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                holder.payDeleteTv!!.text = String.format(context!!.getString(R.string.price), holder.mItem!!.showPrice.toInt().toString())

            } else {
                holder.payDeleteTv!!.visibility = View.GONE
            }

            if (holder.mItem!!.isSelect) {
                checkToSetBackground(holder, true)
            } else {

                checkToSetBackground(holder, false)
            }
            holder.payTv!!.text = String.format(context!!.getString(R.string.price), holder.mItem!!.price.toInt().toString())
            holder.payName!!.text = holder.mItem!!.name
            holder.payShowTv!!.text=holder.mItem!!.showMonthPrice
            holder.payRl!!.setOnClickListener {
                for (i in 0 until datas.size) {
                    datas[i].isSelect = (i == position)
                }
                LogUtils.e(Gson().toJson(datas))
                refreshData(datas)
                onItemClickListener!!.onPayItemClick(holder.mItem!!, position)
            }


        }
    }

    private fun checkToSetBackground(holder: PayHolder, isSelected: Boolean) {

        holder.payImg!!.setBackgroundResource(if (holder.viewType == ITEM_TYPE_BOTTOM_ROUND) R.mipmap.pay_right_round else R.mipmap.pay_right)
        holder.payImg!!.visibility = if (isSelected) View.VISIBLE else View.GONE
        holder.payRl!!.setBackgroundResource(when {
            holder.viewType == ITEM_TYPE_BOTTOM_ROUND -> if (!isSelected) R.drawable.rec_with_gray_stroke_without_top else R.drawable.btn_rec_5_bg_with_red_without_top
            holder.viewType == ITEM_TYPE_RECT -> if (!isSelected) R.drawable.rec_with_gray_stroke else R.drawable.rec_with_red_stroke
            holder.viewType == ITEM_TYPE_TOP_ROUND -> if (!isSelected) R.drawable.rec_with_gray_stroke_without_bottom else R.drawable.btn_rec_5_bg_with_red_without_bottom
            else -> if (!isSelected) R.drawable.btn_rec_10_bg_with_white_with_stroke_1 else R.drawable.btn_rec_10_bg_with_red_stroke_white
        })
    }


    override fun getItemViewType(position: Int): Int {
        return when {
            datas.size == 1 -> ITEM_TYPE_ONLY_ONE
            datas.size == 2 -> if (position == 0) ITEM_TYPE_TOP_ROUND else ITEM_TYPE_BOTTOM_ROUND
            datas.size > 2 -> when (position) {
                0 -> ITEM_TYPE_TOP_ROUND
                datas.size - 1 -> ITEM_TYPE_BOTTOM_ROUND
                else -> ITEM_TYPE_RECT
            }
            else -> ITEM_TYPE_ONLY_ONE
        }
    }


    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int = datas.size


    private inner class PayHolder internal constructor(val mView: View, internal var viewType: Int) : RecyclerView.ViewHolder(mView) {
        internal var mItem: ProductModel? = null
        internal var payRl: RelativeLayout? = null
        internal var payName: TextView? = null
        internal var payTv: TextView? = null
        internal var payDeleteTv: TextView? = null
        internal var payShowTv: TextView? = null

        internal var payImg: ImageView? = null

        init {
            payShowTv = mView.findViewById<View>(R.id.pay_show_tv) as TextView
            payRl = mView.findViewById<View>(R.id.pay_rl) as RelativeLayout
            payImg = mView.findViewById<View>(R.id.pay_img) as ImageView
            payName = mView.findViewById<View>(R.id.pay_name) as TextView
            payTv = mView.findViewById<View>(R.id.pay_tv) as TextView
            payDeleteTv = mView.findViewById<View>(R.id.pay_delete_tv) as TextView
        }
    }

    private var onItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onPayItemClick(ProductModel: ProductModel, position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }


}
