package com.czur.scanpro.ui.download;

import android.app.Service;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.IBinder;
import android.os.Looper;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.event.EventType;
import com.czur.scanpro.event.UpdateEvent;
import com.czur.scanpro.network.core.MiaoHttpManager;
import com.czur.scanpro.utils.AppUpdateUtilKt;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.UUID;

import okhttp3.Call;
import okhttp3.Request;
import okhttp3.Response;

public class CheckUpdateService extends Service {

    private AppDownloadManager downloadManager;
    private String updateUrl;
    private String notes;
    private String apkPath;
    private String apkName;
    private String version;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        downloadManager = new AppDownloadManager(CheckUpdateService.this);
        downloadManager.resume();
        check();
        return super.onStartCommand(intent, flags, startId);
    }

    private void check() {
        String clearCacheStr = "?" + UUID.randomUUID().toString();
        Request checkRequest = new Request.Builder().url(getString(AppUpdateUtilKt.getAppUpdateUrl()) + clearCacheStr).get().build();
        Call checkCall = MiaoHttpManager.getInstance().getClient().newCall(checkRequest);
        try {
            Response checkResponse = checkCall.execute();
            if (checkResponse.isSuccessful()) {
                PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
                JSONObject jsonObject = new JSONObject(checkResponse.body().string());
                LogUtils.i("new version : " + jsonObject.toString());
                updateUrl = jsonObject.getString("package");
                notes = jsonObject.getString("notes");
                version = jsonObject.getString("version");
                apkName = getString(R.string.app_name) + "_" + version + ".apk";
                downloadManager.setApkName(apkName);
                if (packageInfo.versionCode < jsonObject.getInt("build")) {
                    EventBus.getDefault().post(new UpdateEvent(EventType.HAS_NEW_VERSION));
                    //apk是否存在，存在就进行安装

                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            if (FileUtils.isFileExists(apkPath)) {
                                LogUtils.i(apkPath + "//apk is exist!");
                                AppUtils.installApp(apkPath);
                            } else {
                                if (NetworkUtils.isWifiConnected()) {
                                    Looper.prepare();
                                    ToastUtils.showShort(getString(R.string.wifi_download));
                                    Looper.loop();
                                    downloadManager.downloadApk(updateUrl, getString(R.string.app_update_title), notes);
                                    downloadManager.setUpdateListener(new AppDownloadManager.OnUpdateListener() {
                                        @Override
                                        public void update(int currentByte, int totalByte) {
                                            LogUtils.i(currentByte, totalByte, currentByte * 100 / totalByte + "%");
                                        }
                                    });
                                }
                            }
                        }
                    }).start();

                } else {
                    EventBus.getDefault().post(new UpdateEvent(EventType.IS_LATEST_VERSION));
                }

            }
        } catch (IOException | PackageManager.NameNotFoundException | JSONException e) {
            LogUtils.e(e);
        }


    }
}
