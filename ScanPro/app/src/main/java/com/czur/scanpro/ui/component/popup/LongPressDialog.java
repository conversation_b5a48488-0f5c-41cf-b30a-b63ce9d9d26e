package com.czur.scanpro.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.blankj.utilcode.util.BarUtils;
import com.czur.scanpro.R;


public class LongPressDialog extends Dialog implements View.OnClickListener {
    private Context mContext;
    private LinearLayout editLl;
    private LinearLayout deleteLl;

    public LongPressDialog(Context context, OnDialogClickListener onDialogClickListener) {
        //重点实现R.style.DialogStyle 动画效果
        this(context, R.style.LongPressStyle);
        this.onDialogClickListener = onDialogClickListener;
        mContext = context;
    }

    public LongPressDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_long_press);
        WindowManager.LayoutParams params = getWindow().getAttributes();


        editLl = (LinearLayout) getWindow().findViewById(R.id.edit_ll);
        deleteLl = (LinearLayout) getWindow().findViewById(R.id.delete_ll);

        //设置显示的位置
        params.gravity = Gravity.TOP;
        //设置dialog的宽度
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);

        editLl.setOnClickListener(this);
        deleteLl.setOnClickListener(this);

        Window window = getWindow();
        if (window != null) {
            //解决 状态栏变色的bug
            BarUtils.setStatusBarColor(window, Color.TRANSPARENT);
            BarUtils.setNavBarColor(window, Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode(window, true);

        }

    }


    public void showInLocation(int y) {
        WindowManager.LayoutParams params = getWindow().getAttributes();
        //设置dialog的宽度
        params.y = y;
        getWindow().setAttributes(params);
        show();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.edit_ll) {
            if (onDialogClickListener != null) {
                onDialogClickListener.onClick(R.id.edit_ll, this);
            }
        } else if (id == R.id.delete_ll) {
            if (onDialogClickListener != null) {
                onDialogClickListener.onClick(R.id.delete_ll, this);
            }
        }
    }


    /**
     * 点击事件接口
     **/
    public interface OnDialogClickListener {
        /**
         * @param viewId
         */
        void onClick(int viewId, LongPressDialog longPressDialog);
    }

    private OnDialogClickListener onDialogClickListener;

    private void setOnDialogClickListener(OnDialogClickListener onDialogClickListener) {
        this.onDialogClickListener = onDialogClickListener;

    }

}