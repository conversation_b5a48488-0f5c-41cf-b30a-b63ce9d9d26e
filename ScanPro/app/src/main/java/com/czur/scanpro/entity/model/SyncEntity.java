package com.czur.scanpro.entity.model;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 2018/4/27.
 * Email：<EMAIL>
 */
public class SyncEntity {


    /**
     * appFileGroupList : [{"groupId":"group1","clientId":"client123","userId":87,"groupName":"测试group1","isDelete":false,"createTime":"2017-08-20 23:05:46.000","updateTime":"2017-08-20 23:05:46.000","synchronousTime":"2017-08-20 22:27:47.000"},{"groupId":"group4","clientId":"client123","userId":87,"groupName":"测试group1","isDelete":false,"createTime":"2017-08-20 23:05:46.000","updateTime":"2017-08-20 23:05:46.000","synchronousTime":"2017-08-20 22:27:47.000"},{"groupId":"group2","clientId":"client231","userId":87,"groupName":"测试group2","isDelete":false,"createTime":"2017-08-03 23:26:44.000","updateTime":"2017-08-01 23:26:41.000","synchronousTime":"2017-08-22 23:26:47.000"},{"groupId":"group5","clientId":"client231","userId":87,"groupName":"测试group2","isDelete":false,"createTime":"2017-08-03 23:26:44.000","updateTime":"2017-08-01 23:26:41.000","synchronousTime":"2017-08-22 23:26:47.000"},{"groupId":"abc","clientId":"测试","userId":87,"groupName":"啊啊","isDelete":false,"createTime":"2017-01-01 04:12:12.000","updateTime":"2017-01-01 04:12:12.000","synchronousTime":"2017-01-01 04:12:12.000"}]
     * appScanFileList : [{"fileId":"fileid2","clientId":"client123","userId":87,"ossBucket":"http://www.czur.com","baseKey":"base2","mode1Key":"mode1.1","mode2Key":"mode1.2","mode3Key":"mode1.3","mode4Key":"mode1.4","groupClientId":"group1","isDelete":true,"createTime":"2017-08-20 23:26:00.000","updateTime":"2017-08-20 23:26:00.000","synchronousTime":"2017-08-20 23:26:00.000"},{"fileId":"fileid3","clientId":"client啊啊","userId":87,"ossBucket":"http://www.czur.com","baseKey":"base2","mode1Key":"mode1.1","mode2Key":"mode1.2","mode3Key":"mode1.3","mode4Key":"mode1.4","groupClientId":"group1","isDelete":true,"createTime":"2017-08-20 23:26:00.000","updateTime":"2017-08-20 23:26:00.000","synchronousTime":"2017-08-20 23:26:00.000"},{"fileId":"fileid1","clientId":"client123","userId":87,"ossBucket":"http://www.czur.com","baseKey":"base","mode1Key":"mode1","mode2Key":"mode2","mode3Key":"mode3","mode4Key":"mode4","groupClientId":"group1","isDelete":false,"createTime":"2017-08-20 23:25:05.000","updateTime":"2017-08-20 23:25:05.000","synchronousTime":"2017-08-20 23:25:05.000"},{"fileId":"fileid4","clientId":"client123","userId":87,"ossBucket":"http://www.czur.com","baseKey":"base","mode1Key":"mode1","mode2Key":"mode2","mode3Key":"mode3","mode4Key":"mode4","groupClientId":"group1","isDelete":false,"createTime":"2017-08-20 23:25:05.000","updateTime":"2017-08-20 23:25:05.000","synchronousTime":"2017-08-20 23:25:05.000"}]
     * appFileTagList : [{"clientId":"7DEFDBCC017343A2BF0C13846AB97525","tagId":"group1","tagName":"测试group1","userId":87,"isDelete":false,"createTime":"2017-08-20 23:05:46.000","updateTime":"2017-08-20 23:05:46.000","synchronousTime":"2017-08-20 22:27:47.000"}]
     * serverTime : 2017-08-23 16:07:15.108
     */

    private String serverTime;
    private List<ScanProCategoryBean> appFileGroupList;
    private List<ScanProDocBean> appScanFileList;
    private List<ScanProTagBean> appFileTagList;

    public String getServerTime() {
        return serverTime;
    }

    public void setServerTime(String serverTime) {
        this.serverTime = serverTime;
    }

    public List<ScanProCategoryBean> getAppFileGroupList() {
        return appFileGroupList;
    }

    public void setAppFileGroupList(List<ScanProCategoryBean> appFileGroupList) {
        this.appFileGroupList = appFileGroupList;
    }

    public List<ScanProDocBean> getAppScanFileList() {
        return appScanFileList;
    }

    public void setAppScanFileList(List<ScanProDocBean> appScanFileList) {
        this.appScanFileList = appScanFileList;
    }

    public List<ScanProTagBean> getAppFileTagList() {
        return appFileTagList;
    }

    public void setAppFileTagList(List<ScanProTagBean> appFileTagList) {
        this.appFileTagList = appFileTagList;
    }

    public static class ScanProCategoryBean {
        /**
         * groupId : group1
         * clientId : client123
         * userId : 87
         * groupName : 测试group1
         * isDelete : false
         * createTime : 2017-08-20 23:05:46.000
         * updateTime : 2017-08-20 23:05:46.000
         * synchronousTime : 2017-08-20 22:27:47.000
         */

        private String groupId;
        private String clientId;
        private int userId;
        private String groupName;
        private boolean isDelete;
        private String createTime;
        private String updateTime;
        private String synchronousTime;

        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public String getGroupName() {
            return groupName;
        }

        public void setGroupName(String groupName) {
            this.groupName = groupName;
        }

        public boolean getIsDelete() {
            return isDelete;
        }

        public void setIsDelete(boolean isDelete) {
            this.isDelete = isDelete;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getSynchronousTime() {
            return synchronousTime;
        }

        public void setSynchronousTime(String synchronousTime) {
            this.synchronousTime = synchronousTime;
        }
    }

    public static class ScanProDocBean {
        /**
         * fileId : fileid2
         * clientId : client123
         * userId : 87
         * ossBucket : http://www.czur.com
         * baseKey : base2
         * mode1Key : mode1.1
         * mode2Key : mode1.2
         * mode3Key : mode1.3
         * mode4Key : mode1.4
         * groupClientId : group1
         * isDelete : true
         * createTime : 2017-08-20 23:26:00.000
         * updateTime : 2017-08-20 23:26:00.000
         * synchronousTime : 2017-08-20 23:26:00.000
         */

        private String clientId;
        private String angleX;
        private String angleY;
        private String fileId;
        private int userId;
        private int fileSize;
        private int fileType;
        private int enhanceMode;
        private String ossBucket;
        private String tagId;
        private String tagName;
        private String uuid;
        private String groupName;
        private String groupClientId;
        private boolean isDelete;
        private String createTime;
        private String updateTime;
        private String synchronousTime;

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getAngleX() {
            return angleX;
        }

        public void setAngleX(String angleX) {
            this.angleX = angleX;
        }

        public String getAngleY() {
            return angleY;
        }

        public void setAngleY(String angleY) {
            this.angleY = angleY;
        }

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public int getFileSize() {
            return fileSize;
        }

        public void setFileSize(int fileSize) {
            this.fileSize = fileSize;
        }

        public int getFileType() {
            return fileType;
        }

        public void setFileType(int fileType) {
            this.fileType = fileType;
        }

        public int getEnhanceMode() {
            return enhanceMode;
        }

        public void setEnhanceMode(int enhanceMode) {
            this.enhanceMode = enhanceMode;
        }

        public String getOssBucket() {
            return ossBucket;
        }

        public void setOssBucket(String ossBucket) {
            this.ossBucket = ossBucket;
        }

        public String getTagId() {
            return tagId;
        }

        public void setTagId(String tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getGroupName() {
            return groupName;
        }

        public void setGroupName(String groupName) {
            this.groupName = groupName;
        }

        public String getGroupClientId() {
            return groupClientId;
        }

        public void setGroupClientId(String groupClientId) {
            this.groupClientId = groupClientId;
        }

        public boolean isDelete() {
            return isDelete;
        }

        public void setDelete(boolean delete) {
            isDelete = delete;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getSynchronousTime() {
            return synchronousTime;
        }

        public void setSynchronousTime(String synchronousTime) {
            this.synchronousTime = synchronousTime;
        }
    }

    public static class ScanProTagBean {
        /**
         * clientId : 7DEFDBCC017343A2BF0C13846AB97525
         * tagId : group1
         * tagName : 测试group1
         * userId : 87
         * isDelete : false
         * createTime : 2017-08-20 23:05:46.000
         * updateTime : 2017-08-20 23:05:46.000
         * synchronousTime : 2017-08-20 22:27:47.000
         */

        private String clientId;
        private String tagId;
        private String tagName;
        private int userId;
        private boolean isDelete;
        private String createTime;
        private String updateTime;
        private String synchronousTime;

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getTagId() {
            return tagId;
        }

        public void setTagId(String tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public boolean getIsDelete() {
            return isDelete;
        }

        public void setIsDelete(boolean isDelete) {
            this.isDelete = isDelete;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }

        public String getSynchronousTime() {
            return synchronousTime;
        }

        public void setSynchronousTime(String synchronousTime) {
            this.synchronousTime = synchronousTime;
        }
    }
}
