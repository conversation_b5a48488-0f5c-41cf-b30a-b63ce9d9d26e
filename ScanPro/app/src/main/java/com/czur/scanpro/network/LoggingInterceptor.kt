package com.czur.scanpro.network

import android.util.Log
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.utils.LogLevel
import com.czur.scanpro.utils.logLines
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
//import okhttp3.internal.headersContentLength

/**
 * Created by 陈丰尧 on 2021/7/13
 * 专注于输出网络请求相关的Log
 */
class LoggingInterceptor : Interceptor {

    companion object {
        private const val TAG = "NetLogging"
        private const val BYTE_COUNT_LIMIT = 1024L * 1024 * 1024
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val req: Request = chain.request()

        LogUtils.i("LoggingInterceptor.intercept.req=${req}")

        // 输出请求log
        logRequest(req)

        val startTime = System.currentTimeMillis()
        val resp = try {
            chain.proceed(req)
        } catch (exp: Exception) {
            // 打印错误日志
            logLines(TAG, "请求出错:", LogLevel.WARN, "path${req.url.encodedPath}")
            throw exp
        }
        val endTime = System.currentTimeMillis()
        // 计算响应时间
        val reqTime = endTime - startTime

        // 输出响应log
        logResponse(resp, reqTime)

        return resp
    }

    private fun logResponse(resp: Response, reqTime: Long) {
        val path = resp.request.url.encodedPath
        val httpCode = resp.code
        val httpMsg = resp.message

//        val headersContentLength = resp.headersContentLength()
        val headersContentLength = resp.headers.byteCount()
        val byteCount = if (headersContentLength > 0) headersContentLength else BYTE_COUNT_LIMIT

        val respBody = resp.peekBody(byteCount)//1M的数据
        val logHeader =
            """
            path:${path}-(${reqTime}ms)
            HTTP $httpCode $httpMsg
            """.trimIndent()
        if (resp.isSuccessful) {

            var str = respBody.string()
            if (str.length > 1024){
                str = str.substring(0, 1024)
            }
            LogUtils.i(logHeader, str)

        } else {
            logLines(TAG, "请求失败", LogLevel.WARN, logHeader)
        }
        respBody.close()
    }

    /**
     * 输出请求Log信息
     */
    private fun logRequest(req: Request) {
        LogUtils.i("LoggingInterceptor.logRequest.req=${req}")

        val method = req.method
        val host = req.url.host
        val path = req.url.encodedPath
        val headers = req.headers.toString()


        val logLines = mutableListOf(
            "method:$method",
            "host:$host",
            "path:$path",
            "请求头:",
            headers
        )
        LogUtils.i("LoggingInterceptor.logRequest.logLines=${logLines}")

        LogUtils.i("LoggingInterceptor.logRequest.req.url().querySize()=${req.url.querySize}")

        val urlParams = mutableListOf<String>()
        // url 中的参数
        for (i in 0 until req.url.querySize) {
            val name = req.url.queryParameterName(i)
            val value = req.url.queryParameterValue(i)
            urlParams.add(
                "$name: $value"
            )
        }
        if (urlParams.isNotEmpty()) {
            logLines.add("url参数:")
            logLines.addAll(urlParams)
        }

        val bodyParams = mutableListOf<String>()
        // body中的参数
        req.body?.let { body ->
            if (body is FormBody) {
                for (i in 0 until body.size) {
                    bodyParams.add(body.name(i) + ": " + body.value(i))
                }
            }
        }
        if (bodyParams.isNotEmpty()) {
            logLines.add("body参数:")
            logLines.addAll(bodyParams)
        }
//        logLines(
//            TAG,
//            "请求信息",
//            LogLevel.DEBUG,
//            *logLines.toTypedArray()
//        )
        LogUtils.i("请求信息", logLines)
    }


}