package com.czur.scanpro.ui.base

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.res.Resources
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import androidx.appcompat.app.AppCompatActivity
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.entity.model.UserAllInfoModel
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.TagEntity
import com.czur.scanpro.event.*
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.component.ProgressDialogFragment
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.EmptyCountPopup
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.ui.sync.AutoSyncTimeCountService
import com.czur.scanpro.ui.sync.SyncService
import com.czur.scanpro.ui.user.NewPayActivity
import com.czur.scanpro.utils.PermissionUtil
import com.google.gson.Gson
import com.noober.background.BackgroundLibrary
import com.umeng.analytics.MobclickAgent
import io.realm.Realm
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by Yz on 2019/1/3.
 * Email：<EMAIL>
 */


/**
 * 必须重写的方法
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

open class BaseActivity : AppCompatActivity() {
    private var activityDestroyed = false
    private var progressDialogRefCount = 0

    /**
     * @des: Activity是否结束Destroy
     * @params:
     * @return:
     */
    val isFinishingDestroyed: Boolean
        get() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            this.isFinishing || this.isDestroyed
        } else super.isFinishing() || activityDestroyed


    override fun onCreate(savedInstanceState: Bundle?) {
        BackgroundLibrary.inject(this);
        super.onCreate(savedInstanceState)
        LogUtils.i("life-onCreate${javaClass.simpleName}" )
    }


    /**
     * @des: 设置状态栏颜色
     * @params: resId
     * @return:
     */
    protected fun setStatusBarColor(resId: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            if (resId > 0) {
                val window = window
                window.statusBarColor = resources.getColor(resId)
            }
        }
    }

    /**
     * @des: 设置透明状态栏
     * @params:
     * @return:
     */
    protected fun setTranslucentBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = Color.TRANSPARENT
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            val localLayoutParams = window.attributes
            localLayoutParams.flags = WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or localLayoutParams.flags
        }
        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
    }

    /**
     * @des: 设置透明状态栏
     * @params:
     * @return:
     */
    protected fun setTranslucentBar(context: Activity, isLightMode: Boolean) {
        BarUtils.setStatusBarColor(this, resources.getColor(R.color.transparent))
        BarUtils.setStatusBarLightMode(context.window, isLightMode)
    }


    /**
     * @des: 设置透明状态栏亮度色模式
     * @params:
     * @return:
     */
    protected fun setStatusBarLightMode() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }


    }

    protected fun gson(`object`: Any) {
        LogUtils.i(Gson().toJson(`object`))
    }

    protected fun gson(objects: List<Any>) {
        LogUtils.i(Gson().toJson(objects))
    }

    /**
     * @des: 短Toast
     * @params:
     * @return:
     */

    fun showMessage(resId: Int) {
        Toast.makeText(this, getString(resId), Toast.LENGTH_SHORT).show()


    }

    fun showMessage(text: String) {
        Toast.makeText(this, text, Toast.LENGTH_SHORT).show()

    }

    fun showMessage(resId: Int, vararg args: Any) {
//        ToastUtils.setGravity(Gravity.CENTER, 0, 0)
        ToastUtils.showShort(resId, *args)
    }

    fun mobClickEvent(activity: Activity, eventId: String) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[eventId] = eventId
        MobclickAgent.onEvent(activity, eventId, map)
    }

    /**
     * @des: 长Toast
     * @params:
     * @return:
     */
    fun showLongMessage(resId: Int) {

        ToastUtils.showLong(resId)
    }

    fun showLongMessage(text: String) {
        ToastUtils.showLong(text)
    }

    fun showLongMessage(resId: Int, vararg args: Any) {
        ToastUtils.showLong(resId, *args)
    }

    fun showProgressDialog(isDark: Boolean) {
        showProgressDialog(true, false, null, isDark)
    }

    fun showProgressDialog(isDark: Boolean, isCancelable: Boolean) {
        showProgressDialog(isCancelable, false, null, isDark)
    }

    fun showProgressDialog(tag: String) {
        showProgressDialog(true, false, tag, false)
    }

    //    public void showProgressDialog(boolean cancelable) {
    //        showProgressDialog(cancelable, false, null);
    //    }

    /**
     * @des: 初始化一个加载dialog
     * @params:[cancelable, touchable, tag]
     * @return:void
     */

    fun showProgressDialog(cancelable: Boolean = true, touchable: Boolean = false, tag: String? = null, isDark: Boolean = false) {

        if (progressDialogRefCount == 0) {
            val fragmentTransaction = supportFragmentManager.beginTransaction()
            val prevDialog = supportFragmentManager.findFragmentByTag(ProgressDialogFragment.TAG)
            if (prevDialog != null) {
                fragmentTransaction.remove(prevDialog)
            }

            val progressDialog = ProgressDialogFragment.newInstance(tag)
            progressDialog.setIsDark(isDark)
            progressDialog.isCancelable = cancelable
            progressDialog.setTouchable(touchable)
            progressDialog.setOnCancelListener(ProgressDialogFragment.OnCancelListener { _, _ -> hideProgressDialog(true) })
            LogUtils.e(progressDialog.isCancelable)

            fragmentTransaction.add(progressDialog, ProgressDialogFragment.TAG)
            fragmentTransaction.commitAllowingStateLoss()
        }
        progressDialogRefCount++

    }

    fun getUserIdIsLogin(): String {
        return if (getSp().isValidUser) getSp().userId else Constants.NO_USER
    }

    fun getFilesPath(): String {
        return <EMAIL> + File.separator + getUserIdIsLogin() + File.separator;
    }

    protected fun getSp(): UserPreferences {
        return UserPreferences.getInstance(this)
    }

    fun transformCategory(realm: Realm) {
        showProgressDialog(false)

        realm.executeTransaction {
            for (docEntity in realm.where(DocEntity::class.java).equalTo("userID", Constants.NO_USER).equalTo("isDelete", 0.toInt()).findAll()) {
                docEntity.userID = getSp().userId
                docEntity.baseImagePath = docEntity.baseImagePath!!.replace(Constants.NO_USER, getSp().userId)
                docEntity.baseSmallImagePath = docEntity.baseSmallImagePath!!.replace(Constants.NO_USER, getSp().userId)
                docEntity.originalImagePath = docEntity.originalImagePath!!.replace(Constants.NO_USER, getSp().userId)
                docEntity.processImagePath = docEntity.processImagePath!!.replace(Constants.NO_USER, getSp().userId)
                docEntity.processSmallImagePath = docEntity.processSmallImagePath!!.replace(Constants.NO_USER, getSp().userId)
            }

            for (categoryEntity in realm.where(CategoryEntity::class.java).equalTo("userID", Constants.NO_USER).equalTo("isDelete", 0.toInt()).findAll()) {
                categoryEntity.userID = getSp().userId
            }
            for (tagEntity in realm.where(TagEntity::class.java).equalTo("userID", Constants.NO_USER).equalTo("isDelete", 0.toInt()).findAll()) {
                tagEntity.userID = getSp().userId
            }

        }
        createTag(realm)
        hideProgressDialog()
        startSyncNow()
    }

    protected fun transformRealCategory() {
        val beforePath = <EMAIL> + File.separator + Constants.NO_USER + File.separator
        val afterPath = <EMAIL> + File.separator + getSp().userId + File.separator
        FileUtils.createOrExistsDir(afterPath)

        val listFilesInDir = FileUtils.listFilesInDir(beforePath)
        if (!listFilesInDir.isNullOrEmpty()) {
            for (file in listFilesInDir) {
                LogUtils.e("aaa", file.absolutePath, file.name)
                FileUtils.move(file.absolutePath, afterPath + file.name)
                FileUtils.delete(file.absolutePath)

            }
        }

    }

    fun isValidatorUser(): Boolean {
        return UserPreferences.getInstance(this).isValidUser
    }


    /**
     * @des: 隐藏加载
     * @params:
     * @return:
     */
    @JvmOverloads
    fun hideProgressDialog(immediately: Boolean = false) {
        Handler().post(Runnable {
            progressDialogRefCount--
            if (immediately || progressDialogRefCount <= 0) {
                progressDialogRefCount = 0
                val fragment: DialogFragment =  supportFragmentManager.findFragmentByTag(ProgressDialogFragment.TAG) as DialogFragment?
                        ?: return@Runnable
                fragment.dialog?.cancel()
                val fragmentTransaction = supportFragmentManager.beginTransaction()
                fragmentTransaction.remove(fragment)
                fragmentTransaction.commitAllowingStateLoss()
            }
        })

    }

    /**
     * @des: 请求读写权限
     * @params:
     * @return:
     */

    protected fun requestPermission() {


        PermissionUtils.permission(PermissionUtil.getStoragePermission()[0],PermissionUtil.getStoragePermission()[1])
                .rationale(object : PermissionUtils.OnRationaleListener {
                    override fun rationale(
                        activity: UtilsTransActivity,
                        shouldRequest: PermissionUtils.OnRationaleListener.ShouldRequest
                    ) {
                        showMessage(R.string.denied_welcome)
                        shouldRequest?.again(true)
                    }

                })
                .callback(object : PermissionUtils.FullCallback {
                    override fun onGranted(permissionsGranted: List<String>) {
                        LogUtils.i(permissionsGranted)
                    }

                    override fun onDenied(permissionsDeniedForever: List<String>,
                                          permissionsDenied: List<String>) {
                        showMessage(R.string.denied_welcome)
                        LogUtils.i(permissionsDeniedForever, permissionsDenied)
                    }
                })
                .theme { activity -> ScreenUtils.setFullScreen(activity) }
                .request()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.i(TAG, String.format("%s.onNewIntent()", javaClass.simpleName))
    }

    override fun onStart() {
        super.onStart()
        Log.i(TAG, String.format("%s.onStart()", javaClass.simpleName))
    }

    override fun onResume() {
        super.onResume()
        MobclickAgent.onResume(this)
        LogUtils.i("life-onResume${javaClass.simpleName}" )

        Log.i(TAG, String.format("%s.onResume()", javaClass.simpleName))
        if (!isActive) {
            //app 从后台唤醒，进入前台
            isActive = true
            if (UserPreferences.getInstance(this).isValidUser) {
                startSyncNow()
            }
            LogUtils.i(javaClass.simpleName, "程序从后台唤醒")
        }
    }

    public fun getCurrentTime(): String {
        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        return formatter.format(Date(System.currentTimeMillis()))
    }

    public fun startAutoSync() {

        if (UserPreferences.getInstance(this).isUserLogin) {
            if (NetworkUtils.isConnected()) {
                if (UserPreferences.getInstance(this).isAutoSync) {
                    if (UserPreferences.getInstance(this).isSyncOnlyWifi) {
                        if (NetworkUtils.isWifiConnected()) {
                            if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
                                EventBus.getDefault().post(ResetTimeCountEvent(EventType.RESET_TIME_COUNT))
                            } else {
                                startTimeCountService()
                            }
                        }
                    } else {
                        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
                            EventBus.getDefault().post(ResetTimeCountEvent(EventType.RESET_TIME_COUNT))
                        } else {
                            startTimeCountService()
                        }
                    }

                }
            }
        }

    }


    private fun startTimeCountService() {
        val intent = Intent(this, AutoSyncTimeCountService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
    }

    protected fun startSyncNow() {
        if (NetworkUtils.isConnected()) {
            if (UserPreferences.getInstance(this).isValidUser) {
                if (!ServiceUtils.isServiceRunning(SyncService::class.java)) {
                    if (UserPreferences.getInstance(this).isAutoSync) {
                        if (UserPreferences.getInstance(this).isSyncOnlyWifi) {
                            if (NetworkUtils.isWifiConnected()) {
                                startSyncService()
                            }
                        } else {
                            startSyncService()
                        }

                    }
                }
            }
        }

    }


    /**
     * @des: 如果计时service在运行则关闭service
     * @params:
     * @return:
     */

    protected fun startSyncService() {
        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
            EventBus.getDefault().post(StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT))
        }
        val intent = Intent(this, SyncService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
    }

    override fun onPause() {
        super.onPause()
        MobclickAgent.onPause(this)
        Log.i(TAG, String.format("%s.onPause()", javaClass.simpleName))
    }

    protected fun removeStickyEvent() {
        val syncFinishEvent = EventBus.getDefault().getStickyEvent(SyncFinishEvent::class.java)
        if (syncFinishEvent != null) {
            EventBus.getDefault().removeStickyEvent(syncFinishEvent)
        }
        val synchronizingEvent = EventBus.getDefault().getStickyEvent(SynchronizingEvent::class.java)
        if (synchronizingEvent != null) {
            EventBus.getDefault().removeStickyEvent(synchronizingEvent)
        }
        val noticeServiceIsStopEvent = EventBus.getDefault().getStickyEvent(NoticeServiceIsStopEvent::class.java)
        if (noticeServiceIsStopEvent != null) {
            EventBus.getDefault().removeStickyEvent(noticeServiceIsStopEvent)
        }

        val noticeServiceEnoughStopEvent = EventBus.getDefault().getStickyEvent(NoticeServiceEnoughStopEvent::class.java)
        if (noticeServiceEnoughStopEvent != null) {
            EventBus.getDefault().removeStickyEvent(noticeServiceEnoughStopEvent)
        }

        val booksOrPagesChangedEvent = EventBus.getDefault().getStickyEvent(CategoryOrDocsChangedEvent::class.java)
        if (booksOrPagesChangedEvent != null) {
            EventBus.getDefault().removeStickyEvent(booksOrPagesChangedEvent)
        }


    }

    override fun onStop() {
        super.onStop()
        if (!AppUtils.isAppForeground()) {
            //app 进入后台
            isActive = false//记录当前已经进入后台
            LogUtils.i(javaClass.simpleName, "程序进入后台")
        }
        Log.i(TAG, String.format("%s.onStop()", javaClass.simpleName))
    }

    override fun onDestroy() {
        super.onDestroy()
        activityDestroyed = true
        Log.i(TAG, String.format("%s.onDestroy()", javaClass.simpleName))
        Realm.getDefaultConfiguration()?.let { Realm.compactRealm(it) }
    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.i(TAG, String.format("%s.onSaveInstanceState()", javaClass.simpleName))
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        Log.i(TAG, String.format("%s.onRestoreInstanceState()", javaClass.simpleName))
    }

    companion object {
        private val TAG = BaseActivity::class.java.simpleName
        var isActive = true
    }


    /**
     * @des: 请求用户信息
     * @params:
     * @return:
     */

    public fun requestUserInfo( loaded: (() -> Unit)? = null) {
        val userPreferences = UserPreferences.getInstance(this)
        HttpManager.getInstance().request().userAllInfo(userPreferences!!.userId, UserAllInfoModel::class.java, object : MiaoHttpManager.CallbackNetwork<UserAllInfoModel> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<UserAllInfoModel>) {
                hideProgressDialog()
                LogUtils.i(Gson().toJson(entity.body))
                userPreferences.usages = entity.body.usages
                userPreferences.usagesLimit = entity.body.usagesLimit
                userPreferences.photoOssKey = entity.body.photoOssKey
                userPreferences.isVip = entity.body.isIsVip
                userPreferences.userType = entity.body.userType
                userPreferences.isInvited = entity.body.isIsInvitate
                userPreferences.userMobile = entity.body.mobile
                userPreferences.inviteCount = entity.body.invitationCount
                userPreferences.vipEndOn = entity.body.vipEndOn
                userPreferences.svipEndOn = entity.body.svipEndOn
                userPreferences.inviteCode = entity.body.invitationCode

                userPreferences.remainOcr = entity.body.remainingOcr
                userPreferences.remainVip = entity.body.remainingVip
                userPreferences.remainCard = entity.body.remainingCertificate
                userPreferences.remainPdf = entity.body.remainingPdf
                if (userPreferences.isVip) {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrVip
                    userPreferences.remainHandwriting = entity.body.remainingHandwriting
                } else {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrNormal
                    userPreferences.remainHandwriting = entity.body.remainingHandwritingNormal
                }
                loaded?.invoke()
            }

            override fun onFailure(entity: MiaoHttpEntity<UserAllInfoModel>) {
                showMessage(R.string.request_failed_alert)
                loaded?.invoke()
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
                loaded?.invoke()
            }
        })
    }

    public fun requestUserInfoSync() {
        val userPreferences = UserPreferences.getInstance(this)
        try {
            val userInfo = HttpManager.getInstance().request().userAllInfoSync(
                    getSp().userId, UserAllInfoModel::class.java)
            if (userInfo.code == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.usages = userInfo.body.usages
                userPreferences.usagesLimit = userInfo.body.usagesLimit
                userPreferences.photoOssKey = userInfo.body.photoOssKey
                userPreferences.isVip = userInfo.body.isIsVip
                userPreferences.userType = userInfo.body.userType
                userPreferences.isInvited = userInfo.body.isIsInvitate

                userPreferences.inviteCount = userInfo.body.invitationCount
                userPreferences.vipEndOn = userInfo.body.vipEndOn
                userPreferences.svipEndOn = userInfo.body.svipEndOn
                userPreferences.inviteCode = userInfo.body.invitationCode

                userPreferences.remainOcr = userInfo.body.remainingOcr
                userPreferences.remainVip = userInfo.body.remainingVip
                userPreferences.remainCard = userInfo.body.remainingCertificate
                userPreferences.remainPdf = userInfo.body.remainingPdf
                if (userPreferences.isVip) {
                    userPreferences.remainCloudOcr = userInfo.body.remainingCloudocrVip
                    userPreferences.remainHandwriting = userInfo.body.remainingHandwriting
                } else {
                    userPreferences.remainCloudOcr = userInfo.body.remainingCloudocrNormal
                    userPreferences.remainHandwriting = userInfo.body.remainingHandwritingNormal
                }
            }
        } catch (e: Exception) {
            LogUtils.e(e)
            e.printStackTrace()
        }
    }

    protected fun logout() {
        getSp().setIsUserLogin(false)
        getSp().logOutToReset()
        EventBus.getDefault().post(LogoutEvent(EventType.LOG_OUT))
        createTag(Realm.getDefaultInstance())
        ActivityUtils.finishActivity(this)
    }

    protected fun createTag(realm: Realm) {
        realm.executeTransaction {
            val tag1 = realm.where(TagEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("tagName", getString(R.string.default_tag1)).findFirst()
            if (tag1 == null) {
                TagEntity().apply {
                    tagId = UUID.randomUUID().toString()
                    tagName = getString(R.string.default_tag1)
                    userID = getUserIdIsLogin()
                    createTime = Constants.DEFAULT_TIME1
                    isDefault = 1
                    fileType = 0
                    it.copyToRealmOrUpdate(this)
                }

            } else {
                tag1.userID = getUserIdIsLogin()

            }
            val tag2 = realm.where(TagEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("tagName", getString(R.string.default_tag2)).findFirst()

            if (tag2 == null) {
                TagEntity().apply {
                    tagId = UUID.randomUUID().toString()
                    tagName = getString(R.string.default_tag2)
                    userID = getUserIdIsLogin()
                    createTime = Constants.DEFAULT_TIME2
                    isDefault = 1
                    fileType = 1
                    it.copyToRealmOrUpdate(this)
                }

            } else {
                tag2.userID = getUserIdIsLogin()

            }


            val tag3 = realm.where(TagEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("tagName", getString(R.string.default_tag3)).findFirst()
            if (tag3 == null) {
                TagEntity().apply {
                    tagId = UUID.randomUUID().toString()
                    tagName = getString(R.string.default_tag3)
                    userID = getUserIdIsLogin()
                    createTime = Constants.DEFAULT_TIME3
                    isDefault = 1
                    fileType = 2
                    it.copyToRealmOrUpdate(this)
                }

            } else {
                tag3.userID = getUserIdIsLogin()

            }


            val tag4 = realm.where(TagEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("tagName", getString(R.string.default_tag4)).findFirst()
            if (tag4 == null) {
                TagEntity().apply {
                    tagId = UUID.randomUUID().toString()
                    createTime = Constants.DEFAULT_TIME4
                    userID = getUserIdIsLogin()
                    tagName = getString(R.string.default_tag4)
                    isDefault = 1
                    fileType = 3
                    it.copyToRealmOrUpdate(this)
                }

            } else {
                tag4.userID = getUserIdIsLogin()

            }

        }
    }

    /**
     * 次数不足时统一逻辑
     */
    public fun showConsumptionTip(code: Int) {
        val userPreferences = UserPreferences.getInstance(this)
        val builder = ScanProCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            if (userPreferences.isVip) {
                if (userPreferences.userType == "svip") {
                    dialog.dismiss()
                } else {
                    ActivityUtils.startActivity(NewPayActivity::class.java)
                    dialog.dismiss()
                }
            } else {
                ActivityUtils.startActivity(NewPayActivity::class.java)
                dialog.dismiss()
            }
        })
        when (code) {
            1072 -> {
                // certficate num is not enough
                if (userPreferences.isVip) {
                    if (userPreferences.userType == "svip") {
                        builder.setMessage(String.format(getString(R.string.svip_count_tip), "证件识别"))
                    } else {
                        builder.setMessage(String.format(getString(R.string.vip_count_tip), "证件识别"))
                    }
                } else {
                    builder.setMessage(String.format(getString(R.string.free_count_tip), "证件识别"))
                }
                builder.create().show()
            }
            1071 -> {
                // cloudocr num not enough
                if (userPreferences.isVip) {
                    if (userPreferences.userType == "svip") {
                        builder.setMessage(String.format(getString(R.string.svip_count_tip), "云识别"))
                    } else {
                        builder.setMessage(String.format(getString(R.string.vip_count_tip), "云识别"))
                    }
                } else {
                    builder.setMessage(String.format(getString(R.string.free_count_tip), "云识别"))
                }
                builder.create().show()
            }
            1063 -> {
                // pdf num not enough
                if (userPreferences.isVip) {
                    if (userPreferences.userType == "svip") {
                        builder.setMessage(String.format(getString(R.string.svip_count_tip), "生成PDF"))
                    } else {
                        builder.setMessage(String.format(getString(R.string.vip_count_tip), "生成PDF"))
                    }
                } else {
                    builder.setMessage(String.format(getString(R.string.free_count_tip), "生成PDF"))
                }
            }
            1062 -> {
                //ocr num not enough
                if (userPreferences.isVip) {
                    if (userPreferences.userType == "svip") {
                        builder.setMessage(String.format(getString(R.string.svip_count_tip), "文字识别"))
                    } else {
                        builder.setMessage(String.format(getString(R.string.vip_count_tip), "文字识别"))
                    }
                } else {
                    builder.setMessage(String.format(getString(R.string.free_count_tip), "文字识别"))
                }
                builder.create().show()
            }
            1061 -> {
                //handwriting num not enough
                if (userPreferences.isVip) {
                    if (userPreferences.userType == "svip") {
                        builder.setMessage(String.format(getString(R.string.svip_count_tip), "手写识别"))
                    } else {
                        builder.setMessage(String.format(getString(R.string.vip_count_tip), "手写识别"))
                    }
                } else {
                    builder.setMessage(String.format(getString(R.string.free_count_tip), "手写识别"))
                }
                builder.create().show()
            }
        }
    }


    protected fun showEmptyCountDialog(context: Context, type: EmptyCountPopup.EmptyType) {
        if (isActive) {
            val builder = EmptyCountPopup.Builder(context, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
            builder.setMessage(type)
            val emptyCountPopup = builder.create()
            emptyCountPopup.show()
        }
    }

    //静默刷新
    fun commonRequestUserInfoNow() {
        val userPreferences = UserPreferences.getInstance(this)
        HttpManager.getInstance().request().userAllInfo(userPreferences.userId, UserAllInfoModel::class.java, object : MiaoHttpManager.CallbackNetwork<UserAllInfoModel> {
            override fun onNoNetwork() {
            }

            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<UserAllInfoModel>) {
                userPreferences.usages = entity.body.usages
                userPreferences.usagesLimit = entity.body.usagesLimit
                userPreferences.photoOssKey = entity.body.photoOssKey
                userPreferences.isInvited = entity.body.isIsInvitate
                userPreferences.isVip = entity.body.isIsVip
                userPreferences.userType = entity.body.userType
                userPreferences.inviteCount = entity.body.invitationCount
                userPreferences.vipEndOn = entity.body.vipEndOn
                userPreferences.svipEndOn = entity.body.svipEndOn
                userPreferences.inviteCode = entity.body.invitationCode
                userPreferences.remainOcr = entity.body.remainingOcr
                userPreferences.remainVip = entity.body.remainingVip
                userPreferences.remainCard = entity.body.remainingCertificate
                userPreferences.remainPdf = entity.body.remainingPdf
                if (userPreferences.isVip) {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrVip
                    userPreferences.remainHandwriting = entity.body.remainingHandwriting
                } else {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrNormal
                    userPreferences.remainHandwriting = entity.body.remainingHandwritingNormal
                }
            }

            override fun onFailure(entity: MiaoHttpEntity<UserAllInfoModel>) {
            }

            override fun onError(e: Exception) {
            }
        })
    }


    /**
     * 全局添加防止快速点击
     */
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            // 判断连续点击事件时间差
            if (isFastClick()) {
                return true
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    private val MIN_DELAY_TIME = 200

    // 两次点击间隔不能少于200ms
    private var lastClickTime: Long = 0
    open fun isFastClick(): Boolean {
        var flag = true
        val currentClickTime = System.currentTimeMillis()
        if (currentClickTime - lastClickTime >= MIN_DELAY_TIME) {
            flag = false
        }
        lastClickTime = currentClickTime
        return flag
    }


    override fun getResources(): Resources { //还原字体大小
        val res = super.getResources()
        val configuration = res.configuration
        if (configuration.fontScale != 1f) { //fontScale要缩放的比例
            configuration.fontScale = 1f
            res.updateConfiguration(configuration, res.displayMetrics)
        }
        return res
    }


}

