package com.czur.scanpro.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.czur.scanpro.R

class InviteDetailAdapter(val context: Context, val datas: MutableList<String>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    override fun onCreateViewHolder(viewGroup: ViewGroup, position: Int): RecyclerView.ViewHolder {
        return DetailHolder(LayoutInflater.from(viewGroup.context).inflate(R.layout.item_invite_result, viewGroup, false))
    }

    override fun getItemCount(): Int {
        return datas.size
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val detailHolder: DetailHolder = holder as DetailHolder
        detailHolder.tv.text = datas[position]
    }

    private inner class DetailHolder internal constructor(mView: View) : RecyclerView.ViewHolder(mView) {
        internal var tv: TextView = mView.findViewById(R.id.tv_invite)
    }
}