package com.czur.scanpro.utils

import android.content.Context
import android.content.Intent
import android.net.Uri

/**
 * 跳转淘宝详情页
 */
fun openTaobao(context: Context,id: String) {
    //url:淘宝商品详情
    val url = "taobao://item.taobao.com/item.htm?id="+id
//            val url = "https://item.taobao.com/item.htm?spm=a230r.1.14.181.f47e65b8200K1n&id=687963831394&ns=1&abbucket=12#detail"
    val intent = Intent()
    intent.action = "android.intent.action.VIEW"
    val uri = Uri.parse(url)
    intent.data = uri
    intent.setClassName(
        "com.taobao.taobao",
        "com.taobao.tao.detail.activity.DetailActivity"
    )
    context.startActivity(intent)

}

/**
 * 跳转京东详情页
 */
fun openJD(context: Context,id: String) {
//            String url = "https://item.jd.com/231023.html";//这是京东商品详情页
    //需要提取商品id，添加到下面url，不能单独将商品详情页作为url传入
    val url =
        "openapp.jdmobile://virtual?params=%7B%22sourceValue%22:%220_productDetail_97%22,%22des%22:%22productDetail%22,%22skuId%22:%22" +
                "${id}" +
                "%22,%22category%22:%22jump%22,%22sourceType%22:%22PCUBE_CHANNEL%22%7D"
    val intent = Intent()
    intent.action = "android.intent.action.VIEW"
    val uri = Uri.parse(url)
    intent.data = uri
    context.startActivity(intent)
}

/**
 * 跳转到天猫
 * @param url 天猫商品链接地址
 */
 fun openTianMao(context: Context, id: String) {
    val intent = Intent()
    intent.action = "android.intent.action.VIEW"
    val tmall_url = "tmall://tmallclient/?{\"action\":\"item:id=$id\"}"
    val uri = Uri.parse(tmall_url)
    intent.data = uri
    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
    context.startActivity(intent)
}


fun openBrowser(context: Context, url: String) {
    val intent = Intent()
    intent.action = "android.intent.action.VIEW"
    val uri = Uri.parse(url)
    intent.data = uri
    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
    context.startActivity(intent)
}

fun openPdd(context: Context, id: String) {
    val content = "pinduoduo://com.xunmeng.pinduoduo/duo_coupon_landing.html?goods_id=$id";
    val intent= Intent(Intent.ACTION_VIEW, Uri.parse(content));
    context.startActivity(intent);
}

fun openXiaoMiYouPin(context: Context,id:String){
    val uri = Uri.parse("youpin://m.xiaomiyoupin.com/detail?gid=${id}")
    val intent = Intent().apply {
        action = Intent.ACTION_VIEW
        data = uri
    }
    context.startActivity(intent)
}
