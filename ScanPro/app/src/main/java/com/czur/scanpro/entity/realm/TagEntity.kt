package com.czur.scanpro.entity.realm

import io.realm.RealmObject
import io.realm.annotations.PrimaryKey
import io.realm.annotations.RealmClass

/**
 * Created by Yz on 2019/1/7.
 * Email：<EMAIL>
 */
@RealmClass
open class TagEntity : RealmObject() {


    @PrimaryKey
    var tagId: String? = null
    var tagName: String? = null
    var createTime: String? = null
    var updateTime: String? = null
    var isDelete: Int = 0
    var userID: String? = null


    var fileType: Int = 0
    var isDirty: Int = 0
    var isDefault:Int=0;
    var isSelf:Int=0;

}
