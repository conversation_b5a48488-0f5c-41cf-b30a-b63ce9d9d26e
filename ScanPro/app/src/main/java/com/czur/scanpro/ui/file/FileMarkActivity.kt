package com.czur.scanpro.ui.file

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import cn.hzw.doodle.*
import cn.hzw.doodle.DoodleOnTouchGestureListener.ISelectionListener
import cn.hzw.doodle.core.*
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.databinding.ActivityEditBinding
import com.czur.scanpro.databinding.ActivityFileMarkBinding
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.EditEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.launch
import io.realm.Realm
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.util.*


class FileMarkActivity : BaseActivity(), View.OnClickListener, PropertiesBSFragment.Properties {
    private val binding: ActivityFileMarkBinding by lazy{
        ActivityFileMarkBinding.inflate(layoutInflater)
    }
    
    private var fileId: String? = null
    private var realm: Realm? = null
    private var docEntity: DocEntity? = null

    private var bigImagePath: String? = null
    private var smallImagePath: String? = null
    private var baseImagePath: String? = null
    private var smallBasePath: String? = null
    private var mDoodle: IDoodle? = null
    private var mDoodleView: DoodleView? = null

    private var mPropertiesBSFragment: PropertiesBSFragment? = null
    private var textEditorDialogFragment: TextEditorDialogFragment? = null
    private var mTouchGestureListener: DoodleOnTouchGestureListener? = null
    private var detector: IDoodleTouchDetector? = null
    private var mColorCode: Int = ColorUtils.getColor(R.color.red_de4d4d)
    private var mBrushColorCode: Int = ColorUtils.getColor(R.color.red_de4d4d)
    private var mBrushOpacity: Int = 50 // 0到100 对应 100% 至 10%
    private var mBrushSize: Float = 50f // 0到100 对应 4dp 至 12dp
    private var oldSelect: IDoodleSelectableItem? = null
    private var currentPen: IDoodlePen = DoodlePen.TEXT
    private var canSave: Boolean = false
    private var userPreferences: UserPreferences? = null
    private var dirPath: String? = null
    private lateinit var initBitmap: Bitmap
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_24)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_file_mark)
        setContentView(binding.root)
        initData()
        initView()
    }

    private fun initView() {
       binding.llMarker.setOnClickListener(this)
       binding.llWaterMark.setOnClickListener(this)
       binding.llBack.setOnClickListener(this)
       binding.tvCancel.setOnClickListener(this)
       binding.tvFinish.setOnClickListener(this)
        initBitmap = ImageUtils.getBitmap(bigImagePath, 2048, 2048)
        setDoodleView(initBitmap)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setDoodleView(bitmap: Bitmap) {
        if (mDoodleView != null) {
            binding.photoView.removeView(mDoodleView)
        }
        oldSelect = null
        mDoodleView = DoodleView(this, bitmap, true, object : IDoodleListener {
            /*
            保存涂鸦图像时调用
             */
            override fun onSaved(doodle: IDoodle?, bitmap: Bitmap?, callback: Runnable?) {
                bitmap?.let {
                    setDoodleView(it)
                    if (canSave) {
                        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                            override fun doInBackground(): Void? {
                                ImageUtils.save(bitmap, bigImagePath, Bitmap.CompressFormat.JPEG)
                                ImageUtils.save(bitmap, baseImagePath, Bitmap.CompressFormat.JPEG)
                                val smallBitmap = ImageUtils.compressBySampleSize(bitmap, 300, 300)
                                ImageUtils.save(smallBitmap, smallImagePath, Bitmap.CompressFormat.JPEG)
                                ImageUtils.save(smallBitmap, smallBasePath, Bitmap.CompressFormat.JPEG)
                                return null
                            }

                            override fun onSuccess(path: Void?) {
                                Handler(Looper.getMainLooper()).postDelayed({
                                    realm!!.executeTransaction {
                                        docEntity!!.isDirty = 1
                                        docEntity!!.uuid = UUID.randomUUID().toString()
                                        docEntity!!.updateTime = getCurrentTime()
                                    }
                                    startAutoSync()
                                    hideProgressDialog()
                                    ActivityUtils.finishActivity(this@FileMarkActivity)
                                },500)
                                EventBus.getDefault().post(EditEvent(EventType.CUT))

                            }

                            override fun onFail(t: Throwable?) {
                                super.onFail(t)
                                LogUtils.e(t)
                                hideProgressDialog()
                            }
                        })
                    }
                }

            }

            /*
             此时view已经测量完成，涂鸦前的准备工作已经完成，在这里可以设置大小、颜色、画笔、形状等。
             */
            override fun onReady(doodle: IDoodle) {
                if (currentPen == DoodlePen.BRUSH) {
                    mDoodle?.size = (4 + mBrushSize * 0.08f) * mDoodle?.unitSize!!
                    mDoodle?.color = DoodleColor(mBrushColorCode)
                    mDoodle?.pen = DoodlePen.BRUSH
                    mDoodle?.opacity = mBrushOpacity
                } else {
                    mDoodle?.size = 25 * mDoodle?.unitSize!!
                    mDoodle?.color = DoodleColor(mColorCode)
                    mDoodle?.pen = DoodlePen.TEXT
                }
            }
        })
        mDoodle = mDoodleView
        mTouchGestureListener = DoodleOnTouchGestureListener(this, mDoodleView, object : ISelectionListener {
            var mLastPen: IDoodlePen? = null
            var mLastColor: IDoodleColor? = null
            var mSize: Float? = null
            var mIDoodleItemListener = IDoodleItemListener { property ->
                if (mTouchGestureListener?.selectedItem == null) {
                    return@IDoodleItemListener
                }
                if (property == IDoodleItemListener.PROPERTY_SCALE) {
                }
            }

            /*
            item（如文字，贴图）被选中或取消选中时回调
            */
            override fun onSelectedItem(doodle: IDoodle, selectableItem: IDoodleSelectableItem, selected: Boolean) {
                if (selected) {
                    if (mLastPen == null) {
                        mLastPen = mDoodle?.pen
                    }
                    if (mLastColor == null) {
                        mLastColor = mDoodle?.color
                    }
                    if (mSize == null) {
                        mSize = mDoodle?.size
                    }
                    mDoodleView?.isEditMode = true
                    mDoodle?.pen = selectableItem.pen
                    mDoodle?.color = selectableItem.color
                    mDoodle?.size = selectableItem.size
                    selectableItem.addItemListener(mIDoodleItemListener)
                    if (oldSelect != null && oldSelect == selectableItem && mTouchGestureListener?.selectedItem is DoodleText) {
                        createText(mTouchGestureListener?.selectedItem as DoodleText)
                    }
                    oldSelect = selectableItem
                } else {
                    selectableItem.removeItemListener(mIDoodleItemListener)
                    if (mTouchGestureListener?.selectedItem == null) { // nothing is selected. 当前没有选中任何一个item
                        if (mLastPen != null) {
                            mDoodle?.pen = mLastPen
                            mLastPen = null
                        }
                        if (mLastColor != null) {
                            mDoodle?.color = mLastColor
                            mLastColor = null
                        }
                        if (mSize != null) {
                            mDoodle?.size = mSize!!
                            mSize = null
                        }
                        oldSelect = null
                    }
                }
            }

            override fun onDeleteItem(doodle: IDoodle?) {
                showCancelAndSave()
            }

            override fun onCreateItem(doodle: IDoodle?) {
                binding.tvFinish.visibility = View.VISIBLE
                binding.tvCancel.visibility = View.VISIBLE
                binding.llBack.visibility = View.GONE
            }
        })
        detector = DoodleTouchDetector(applicationContext, mTouchGestureListener)
        mDoodleView?.defaultTouchDetector = detector
        binding.photoView.addView(mDoodleView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        mDoodleView?.setOnTouchListener { _, event ->
            if (mDoodle?.pen == DoodlePen.BRUSH) {
                when (event.action and MotionEvent.ACTION_MASK) {
                    MotionEvent.ACTION_DOWN -> {
                        mPropertiesBSFragment?.dialog?.dismiss()
                    }
                    MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP -> {
                        //离开屏幕超过一定时间才判断为需要显示设置面板
                        //  mDoodleView?.postDelayed(Runnable { createBrush() }, 300)
                    }
                }
            }
            false
        }
    }


    private fun initData() {
        userPreferences = UserPreferences.getInstance(this)
        fileId = intent.getStringExtra("fileId")
        binding.tvTitle.text = intent.getStringExtra("title")
        realm = Realm.getDefaultInstance()
        docEntity = realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("fileID", fileId).findFirst()
        dirPath = filesDir.absolutePath + File.separator + userPreferences!!.userId + File.separator
        baseImagePath = docEntity!!.baseImagePath
        smallBasePath = docEntity!!.baseSmallImagePath
        bigImagePath = docEntity!!.processImagePath
        smallImagePath = docEntity!!.processSmallImagePath
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.llMarker -> {
                createBrush()
            }
            R.id.llWaterMark -> {
                createText(mTouchGestureListener?.selectedItem as DoodleText?)
            }
            R.id.llBack -> {
                ActivityUtils.finishActivity(this)
            }
            R.id.tvCancel -> {
//                tvFinish.visibility = View.GONE
//                tvCancel.visibility = View.GONE
//                llBack.visibility = View.VISIBLE
//                setDoodleView(initBitmap)
                ActivityUtils.finishActivity(this)
            }

            R.id.tvFinish -> {
                mDoodle?.save()
                canSave = true
                showProgressDialog(true)
            }

        }
    }

    private fun showCancelAndSave() {
        if (mDoodle?.itemCount!! > 0) {
           binding.tvFinish.visibility = View.VISIBLE
           binding.tvCancel.visibility = View.VISIBLE
           binding.llBack.visibility = View.GONE
        } else {
            binding.tvFinish.visibility = View.GONE
            binding.tvCancel.visibility = View.GONE
            binding.llBack.visibility = View.VISIBLE
        }
    }

    override fun onColorChanged(colorCode: Int) {
        mDoodle?.pen = DoodlePen.BRUSH
        mBrushColorCode = colorCode
        mDoodle?.color = DoodleColor(mBrushColorCode)
    }

    override fun onOpacityChanged(opacity: Int) {
        mDoodle?.pen = DoodlePen.BRUSH
        mBrushOpacity = opacity
        mDoodle?.opacity = mBrushOpacity
    }

    override fun onBrushSizeChanged(brushSize: Int) {
        mDoodle?.pen = DoodlePen.BRUSH
        mBrushSize = brushSize.toFloat()
        mDoodle?.size = (4 + mBrushSize * 0.08f) * mDoodle?.unitSize!!
    }

    // 添加画笔
    private fun createBrush() {
        if (isFinishing) {
            return
        }
        mDoodleView?.isEditMode = false
        if (currentPen == DoodlePen.TEXT && mDoodle?.itemCount!! > 0) {
            mDoodle?.save()
        } else {
            mDoodle?.size = (4 + mBrushSize * 0.08f) * mDoodle?.unitSize!!
            mDoodle?.color = DoodleColor(mBrushColorCode)
            mDoodle?.opacity = mBrushOpacity
        }
        mDoodle?.pen = DoodlePen.BRUSH
        currentPen = DoodlePen.BRUSH
        if (mPropertiesBSFragment == null) {
            mPropertiesBSFragment = PropertiesBSFragment()
            mPropertiesBSFragment?.setPropertiesChangeListener(this)
        }
        if (mPropertiesBSFragment?.isAdded!!) {
            mPropertiesBSFragment?.dialog?.show()
        } else {
            mPropertiesBSFragment?.show(supportFragmentManager, mPropertiesBSFragment?.tag)
        }
    }

    override fun onBackPressed() {
        if (mPropertiesBSFragment != null && mPropertiesBSFragment?.dialog != null && mPropertiesBSFragment?.dialog?.isShowing!!) {
            mPropertiesBSFragment?.dialog?.dismiss()
        } else {
//            if (mDoodle?.itemCount!! > 0) {
//                mDoodle?.clear()
//                llBack.visibility = View.VISIBLE
//                tvCancel.visibility = View.GONE
//                tvFinish.visibility = View.GONE
//            } else {
            super.onBackPressed()
            //           }
        }
    }

    // 添加文字
    private fun createText(doodleText: DoodleText?) {
        if (isFinishing) {
            return
        }
        if (currentPen == DoodlePen.BRUSH && mDoodle?.itemCount!! > 0) {
            mDoodle?.save()
        } else {
            mDoodle?.size = 25 * mDoodle?.unitSize!!
            mDoodle?.color = DoodleColor(mColorCode)
        }
        mDoodle?.pen = DoodlePen.TEXT
        currentPen = DoodlePen.TEXT
        textEditorDialogFragment = if (doodleText != null && !TextUtils.isEmpty(doodleText.text)) {
            TextEditorDialogFragment.show(this, doodleText.text, mColorCode)
        } else {
            TextEditorDialogFragment.show(this, "", mColorCode)
        }

        textEditorDialogFragment?.setOnTextEditorListener(object : TextEditorDialogFragment.TextEditor {
            override fun onDone(inputText: String?, colorCode: Int) {
                if (TextUtils.isEmpty(inputText)) {
                    return
                }
                mColorCode = colorCode
                mDoodle?.color = DoodleColor(mColorCode)
                if (doodleText == null) {
                    mDoodleView?.isEditMode = true
                    val x: Float = (0.5 * binding.photoView?.width!!).toFloat()
                    val y: Float = (0.5 * binding.photoView?.height!!).toFloat()
                    val item: IDoodleSelectableItem = DoodleText(this@FileMarkActivity, mDoodle, inputText, mDoodle?.size!!, mDoodle?.color, mDoodleView?.toX(x)!!, mDoodleView?.toY(y)!!)
                    mDoodle?.addItem(item)
                    mTouchGestureListener?.selectedItem = item
                    showCancelAndSave()
                } else {
                    doodleText.text = inputText
                    doodleText.color = DoodleColor(colorCode)
                    doodleText.refresh()
                    mDoodle?.refresh()
                }
            }
        })

        launch {
            delay(300)
            textEditorDialogFragment?.requestFocus()
        }
    }

}