package com.czur.scanpro.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.scanpro.R;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class FileBottomDialogPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;


    public FileBottomDialogPopup(Context context) {
        super(context);
    }

    public FileBottomDialogPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder implements View.OnClickListener {
        private Context mContext;
        private boolean isMove;
        private RelativeLayout fileBottomSheetSaveBtn;
        private RelativeLayout fileBottomSheetDeleteBtn;
        private RelativeLayout fileBottomSheetCancelBtn;
        private RelativeLayout fileBottomSheetMoveBtn;

        public Builder(Context mContext, boolean isMove, OnBottomSheetClickListener onBottomSheetClickListener) {
            this.mContext = mContext;
            this.isMove = isMove;
            this.onBottomSheetClickListener = onBottomSheetClickListener;
        }


        /**
         * 点击事件接口
         **/
        public interface OnBottomSheetClickListener {
            /**
             * @param viewId
             */
            void onClick(int viewId);
        }

        private OnBottomSheetClickListener onBottomSheetClickListener;

        private void setOnBottomSheetClickListener(OnBottomSheetClickListener onBottomSheetClickListener) {
            this.onBottomSheetClickListener = onBottomSheetClickListener;

        }

        @Override
        public void onClick(View v) {
            int id = v.getId();
            if (id == R.id.file_bottom_sheet_delete_btn) {
                if (onBottomSheetClickListener != null) {
                    onBottomSheetClickListener.onClick(R.id.file_bottom_sheet_delete_btn);
                }
            } else if (id == R.id.file_bottom_sheet_save_btn) {
                if (onBottomSheetClickListener != null) {
                    onBottomSheetClickListener.onClick(R.id.file_bottom_sheet_save_btn);
                }
            } else if (id == R.id.file_bottom_sheet_cancel_btn) {
                if (onBottomSheetClickListener != null) {
                    onBottomSheetClickListener.onClick(R.id.file_bottom_sheet_cancel_btn);
                }
            } else if (id == R.id.file_bottom_sheet_move_btn) {
                if (onBottomSheetClickListener != null) {
                    onBottomSheetClickListener.onClick(R.id.file_bottom_sheet_move_btn);
                }
            }
        }


        public FileBottomDialogPopup create() {
            LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final FileBottomDialogPopup dialog = new FileBottomDialogPopup(mContext, R.style.SocialAccountDialogStyle);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            params.gravity = Gravity.BOTTOM;
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = SizeUtils.dp2px(isMove ? 210 : 160);
            dialog.getWindow().setAttributes(params);
            BarUtils.setStatusBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setNavBarColor( dialog.getWindow(), Color.TRANSPARENT);
            BarUtils.setStatusBarLightMode( dialog.getWindow(), true);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final FileBottomDialogPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_file_bottom_sheet, null, false);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            dialog.addContentView(layout, params);


            fileBottomSheetSaveBtn = (RelativeLayout) layout.findViewById(R.id.file_bottom_sheet_save_btn);
            fileBottomSheetDeleteBtn = (RelativeLayout) layout.findViewById(R.id.file_bottom_sheet_delete_btn);
            fileBottomSheetCancelBtn = (RelativeLayout) layout.findViewById(R.id.file_bottom_sheet_cancel_btn);
            fileBottomSheetMoveBtn = (RelativeLayout) layout.findViewById(R.id.file_bottom_sheet_move_btn);

            fileBottomSheetMoveBtn.setVisibility(isMove ? View.VISIBLE : View.GONE);
            fileBottomSheetMoveBtn.setOnClickListener(this);
            fileBottomSheetSaveBtn.setOnClickListener(this);
            fileBottomSheetDeleteBtn.setOnClickListener(this);
            fileBottomSheetCancelBtn.setOnClickListener(this);

            return layout;
        }
    }
}
