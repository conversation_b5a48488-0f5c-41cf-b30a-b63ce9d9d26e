package com.czur.scanpro.ui.base

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.entity.model.UserAllInfoModel
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.ResetTimeCountEvent
import com.czur.scanpro.event.StopSyncTimeCountEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.EmptyCountPopup
import com.czur.scanpro.ui.sync.AutoSyncTimeCountService
import com.czur.scanpro.ui.sync.SyncService
import com.google.gson.Gson
import com.umeng.analytics.MobclickAgent
import org.greenrobot.eventbus.EventBus
import java.util.*

/**
 * Created by Yz on 2019/1/3.
 * Email：<EMAIL>
 */
abstract class BaseFragment : Fragment() {
    var TAG = BaseActivity::class.java.simpleName
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    fun showProgressDialog(isDark: Boolean) {
        showProgressDialog(true, false, null, isDark)
    }

    fun showProgressDialog(isDark: Boolean, isCancelable: Boolean) {
        showProgressDialog(isCancelable, false, null, isDark)
    }

    fun showProgressDialog(tag: String) {
        showProgressDialog(true, false, tag, false)
    }


    fun showProgressDialog(cancelable: Boolean = true, touchable: Boolean = false, tag: String? = null, isDark: Boolean = false) {
        val activity = activity
        if (activity is BaseActivity) {
            activity.showProgressDialog(cancelable, touchable, tag, isDark)
        }
    }

    fun hideProgressDialog() {
        val activity = activity
        if (activity is BaseActivity) {
            activity.hideProgressDialog()
        }
    }

    fun hideProgressDialog(immediately: Boolean) {
        val activity = activity
        if (activity is BaseActivity) {
            activity.hideProgressDialog(immediately)
        }
    }


    /**
     * @des: 短Toast
     * @params:
     * @return:
     */

    fun showMessage(resId: Int) {
        Toast.makeText(activity,getString(resId),Toast.LENGTH_SHORT).show()
//        ToastUtils.setGravity(Gravity.CENTER, 0, 0)
//
//        ToastUtils.showShort(resId)
    }

    fun showMessage(text: String) {
        Toast.makeText(activity,text,Toast.LENGTH_SHORT).show()

//        ToastUtils.setGravity(Gravity.CENTER, 0, 0)
//
//        ToastUtils.showShort(text)
    }

    fun showMessage(resId: Int, vararg args: Any) {
//        ToastUtils.setGravity(Gravity.CENTER, 0, 0)
        ToastUtils.showShort(resId, *args)
    }

    fun getUserIdIsLogin(): String {
        return if (getSp().isUserLogin) getSp().userId else Constants.NO_USER
    }

    /**
     * @des: 长Toast
     * @params:
     * @return:
     */
    fun showLongMessage(resId: Int) {
        ToastUtils.showLong(resId)
    }

    fun showLongMessage(text: String) {
        ToastUtils.showLong(text)
    }

    fun showLongMessage(resId: Int, vararg args: Any) {
        ToastUtils.showLong(resId, *args)
    }

    fun isValidatorUser(): Boolean {
        return UserPreferences.getInstance(context).isValidUser
    }

    fun getSp(): UserPreferences {
        return UserPreferences.getInstance(context)

    }

    override fun onDetach() {
        super.onDetach()
        hideProgressDialog()
    }

    public fun startAutoSync() {
        val application = ActivityUtils.getTopActivity().application
        if (UserPreferences.getInstance(application).isUserLogin) {
            if (NetworkUtils.isConnected()) {
                if (UserPreferences.getInstance(application).isAutoSync) {
                    if (UserPreferences.getInstance(application).isSyncOnlyWifi) {
                        if (NetworkUtils.isWifiConnected()) {
                            if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
                                EventBus.getDefault().post(ResetTimeCountEvent(EventType.RESET_TIME_COUNT))
                            } else {
                                startTimeCountService()
                            }
                        }
                    } else {
                        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
                            EventBus.getDefault().post(ResetTimeCountEvent(EventType.RESET_TIME_COUNT))
                        } else {
                            startTimeCountService()
                        }
                    }

                }
            }
        }

    }


    private fun startTimeCountService() {
        val topActivity = ActivityUtils.getTopActivity()
        val intent = Intent(topActivity, AutoSyncTimeCountService::class.java)
        topActivity.startService(intent)
    }

    protected fun startSyncNow() {
        val application = ActivityUtils.getTopActivity().application
        if (NetworkUtils.isConnected()) {
            if (UserPreferences.getInstance(application).isUserLogin) {
                if (!ServiceUtils.isServiceRunning(SyncService::class.java)) {
                    if (UserPreferences.getInstance(application).isAutoSync) {
                        if (UserPreferences.getInstance(application).isSyncOnlyWifi) {
                            if (NetworkUtils.isWifiConnected()) {
                                startSyncService()
                            }
                        } else {
                            startSyncService()
                        }

                    }
                }
            }
        }

    }


    /**
     * @des: 如果计时service在运行则关闭service
     * @params:
     * @return:
     */

    protected fun startSyncService() {
        val topActivity = ActivityUtils.getTopActivity()
        if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService::class.java)) {
            EventBus.getDefault().post(StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT))
        }
        val intent = Intent(topActivity, SyncService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            topActivity.startForegroundService(intent)
        } else {
            topActivity. startService(intent)
        }

    }
    protected fun showEmptyCountDialog(context: Context, type: EmptyCountPopup.EmptyType) {
        val builder = EmptyCountPopup.Builder(ActivityUtils.getTopActivity(), CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setMessage(type)
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->

            dialog.dismiss()
        })
        val emptyCountPopup = builder.create()
        emptyCountPopup.show()
    }
    fun mobClickEvent(activity: Context?, eventId: String) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[eventId] = eventId
        MobclickAgent.onEvent(activity, eventId, map)
    }
    /**
     * @des: 请求用户信息
     * @params:
     * @return:
     */

    public fun requestUserInfo() {
        val userPreferences = getSp()
        HttpManager.getInstance().request().userAllInfo(userPreferences!!.userId, UserAllInfoModel::class.java, object : MiaoHttpManager.CallbackNetwork<UserAllInfoModel> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<UserAllInfoModel>) {
                hideProgressDialog()
                LogUtils.i(Gson().toJson(entity.body))
                userPreferences.usages = entity.body.usages
                userPreferences.usagesLimit = entity.body.usagesLimit
                userPreferences.photoOssKey = entity.body.photoOssKey
                userPreferences.isVip=entity.body.isIsVip
                userPreferences.userType = entity.body.userType
                userPreferences.isInvited = entity.body.isIsInvitate

                userPreferences.inviteCount = entity.body.invitationCount
                userPreferences.vipEndOn = entity.body.vipEndOn
                userPreferences.svipEndOn = entity.body.svipEndOn
                userPreferences.inviteCode = entity.body.invitationCode

                userPreferences.remainOcr = entity.body.remainingOcr
                userPreferences.remainVip = entity.body.remainingVip
                userPreferences.remainCard = entity.body.remainingCertificate
                userPreferences.remainPdf = entity.body.remainingPdf
                if (userPreferences.isVip) {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrVip
                    userPreferences.remainHandwriting = entity.body.remainingHandwriting
                } else {
                    userPreferences.remainCloudOcr = entity.body.remainingCloudocrNormal
                    userPreferences.remainHandwriting = entity.body.remainingHandwritingNormal
                }

            }

            override fun onFailure(entity: MiaoHttpEntity<UserAllInfoModel>) {
                showMessage(R.string.request_failed_alert)

            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)

            }
        })
    }
    public fun requestUserInfoSync() {

        val userPreferences = UserPreferences.getInstance(activity)

        try {
            val userInfo = HttpManager.getInstance().request().userAllInfoSync(
                    getSp().userId, UserAllInfoModel::class.java)
            if (userInfo.code == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.usages = userInfo.body.usages
                userPreferences.usagesLimit = userInfo.body.usagesLimit
                userPreferences.photoOssKey = userInfo.body.photoOssKey
                userPreferences.isVip = userInfo.body.isIsVip
                userPreferences.userType = userInfo.body.userType
                userPreferences.isInvited = userInfo.body.isIsInvitate

                userPreferences.inviteCount = userInfo.body.invitationCount
                userPreferences.vipEndOn = userInfo.body.vipEndOn
                userPreferences.svipEndOn = userInfo.body.svipEndOn
                userPreferences.inviteCode = userInfo.body.invitationCode

                userPreferences.remainOcr = userInfo.body.remainingOcr
                userPreferences.remainCard = userInfo.body.remainingCertificate
                userPreferences.remainVip = userInfo.body.remainingVip
                userPreferences.remainPdf = userInfo.body.remainingPdf
                if (userPreferences.isVip) {
                    userPreferences.remainCloudOcr = userInfo.body.remainingCloudocrVip
                    userPreferences.remainHandwriting = userInfo.body.remainingHandwriting
                } else {
                    userPreferences.remainCloudOcr = userInfo.body.remainingCloudocrNormal
                    userPreferences.remainHandwriting = userInfo.body.remainingHandwritingNormal
                }
//                    return when (emptyType) {
//                        EmptyType.PDF -> userPreferences!!.remainPdf > 0
//                        EmptyType.CLOUD -> userPreferences!!.remainPdf > 0
//                        EmptyType.OCR -> userPreferences!!.remainPdf > 0
//                        EmptyType.HANDWRTING -> userPreferences!!.remainPdf > 0
//                        EmptyType.CARD -> userPreferences!!.remainPdf > 0
//                        else -> false
//                    }
            }
        } catch (e: Exception) {
            LogUtils.e(e)
            e.printStackTrace()
        }




    }
}