package com.czur.scanpro.alg

import android.graphics.PointF

class Vec4f {

    var point1: PointF
    var point2: PointF

    constructor(point1: PointF, point2: PointF) {
        this.point1 = PointF(point1.x, point1.y)
        this.point2 = PointF(point2.x, point2.y)
    }

    constructor(vec4f: Vec4f) {
        this.point1 = PointF(vec4f.point1.x, vec4f.point1.y)
        this.point2 = PointF(vec4f.point2.x, vec4f.point2.y)
    }
}