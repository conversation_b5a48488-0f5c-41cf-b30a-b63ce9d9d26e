package com.czur.scanpro.utils

import android.app.Application
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.os.Looper
import android.util.Log
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.Utils
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.alg.CZSaoMiao_Alg
import com.czur.scanpro.common.Constants
import com.czur.scanpro.common.OSSInstance
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.MyMigration
import com.czur.scanpro.ui.base.SaomiaoApplication
import com.czur.scanpro.utils.cockroach.ExceptionHandler
import com.czur.scanpro.utils.sharesdk.ShareSDKUtils
import com.facebook.common.memory.MemoryTrimType
import com.facebook.common.memory.NoOpMemoryTrimmableRegistry
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.imagepipeline.core.ImagePipelineConfig
import com.facebook.imagepipeline.core.ImagePipelineFactory
import com.facebook.imagepipeline.nativecode.ImagePipelineNativeLoader
import com.mob.MobSDK
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.umeng.commonsdk.UMConfigure
import io.realm.Realm
import io.realm.RealmConfiguration
import java.io.File
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.system.exitProcess

object initializeUtils {
    private lateinit var executorService: ExecutorService;
    private val CPU_COUNT = Runtime.getRuntime().availableProcessors()
    private val CORE_POOL_SIZE = 2.coerceAtLeast((CPU_COUNT - 1).coerceAtMost(4))
    private var context: Application? = null
    private var wechatPayApi: IWXAPI? = null

    fun initAll(context: Application) {
        this.context = context
        executorService = Executors.newFixedThreadPool(CORE_POOL_SIZE)
//        CZSaoMiao_Alg.getInstance(context).init_tess(context.filesDir.absolutePath + File.separator + "tessdata")
        executorService = Executors.newFixedThreadPool(CORE_POOL_SIZE)
        //初始化工具类
        Utils.init(context)
        //初始化网络请求
        MiaoHttpManager.getInstance().init(context)
        //初始化图片加载库
        initFresco()
        //初始化数据库
        initRealm()
        executorService.execute {
            //初始化shareSdk
//            MobSDK.init(context)
//            Log.i("Jason", "initializeUtils.ShareSDKUtils.init")
            ShareSDKUtils.init(context)

            //提前加载so库
            CZSaoMiao_Alg.getInstance(context)
        }
        executorService.execute {
            //初始化Log
            initLog()
        }
        executorService.execute {
            //初始化微信支付
            initWechatPay()
        }

        executorService.execute {
            //初始化统计
            initBugly()
            initOSSInstance()
            initUMeng()
        }
    }

    private fun initFresco() {
        //解决oom和so库加载失败
        val memoryTrimmableRegistry = NoOpMemoryTrimmableRegistry.getInstance()
        memoryTrimmableRegistry.registerMemoryTrimmable { trimType ->
            val suggestedTrimRatio = trimType.suggestedTrimRatio;
            if (MemoryTrimType.OnCloseToDalvikHeapLimit.suggestedTrimRatio == suggestedTrimRatio
                    || MemoryTrimType.OnSystemLowMemoryWhileAppInBackground.suggestedTrimRatio == suggestedTrimRatio
                    || MemoryTrimType.OnSystemLowMemoryWhileAppInForeground.suggestedTrimRatio == suggestedTrimRatio) {
                //清空内存缓存
                ImagePipelineFactory.getInstance().imagePipeline.clearMemoryCaches()
            }
        };
        val builder = ImagePipelineConfig.newBuilder(context)
        var imagePipelineConfig = builder.setDownsampleEnabled(true)
                .setResizeAndRotateEnabledForNetwork(true)
                .setBitmapsConfig(Bitmap.Config.RGB_565)
                .setMemoryTrimmableRegistry(memoryTrimmableRegistry)
                .build()
        Fresco.initialize(context, imagePipelineConfig)
        try {
            ImagePipelineNativeLoader.load()
        } catch (error: UnsatisfiedLinkError) {
            Fresco.shutDown()
            builder.experiment().setNativeCodeDisabled(true)
            imagePipelineConfig = builder.build()
            Fresco.initialize(context, imagePipelineConfig)
            error.printStackTrace()
        }
    }

    private fun initRealm() {
        Realm.init(context!!)
        val config = RealmConfiguration.Builder()
                //文件名
                .name("saomiao.realm")
                //版本号
                .schemaVersion(1)
                .compactOnLaunch()
                .migration(MyMigration())
                .allowWritesOnUiThread(true)
                .allowQueriesOnUiThread(true)
                .build()
        Realm.setDefaultConfiguration(config)

    }

    /**
     * 初始化全局Log配置
     */
    private fun initLog() {
        val strDir: String = Constants.SAOMIAO_SD_PATH.toString()
        LogUtils.getConfig()
            // 设置 log 总开关，包括输出到控制台和文件，默认开
            .setLogSwitch(true)//TODO logUtils总开关
            // 设置是否输出到控制台开关，默认开
            .setConsoleSwitch(true)
            // 设置 log 文件开关
            .setLog2FileSwitch(Constants.IS_DEBUG_FLAG)
            // 设置 log 文件存储目录
            .setDir(strDir)
            .setGlobalTag(SaomiaoApplication.LOG_TAG)
            // 设置 log 头信息开关，默认为开
            .setLogHeadSwitch(true)
            // 输出日志是否带边框开关，默认开
            .setBorderSwitch(false)
            // log 的控制台过滤器，和 logcat 过滤器同理，默认 Verbose
            .setConsoleFilter(LogUtils.V)
            // log 文件过滤器，和 logcat 过滤器同理，默认 Verbose
            .setFileFilter(LogUtils.V)
            .stackDeep = 1
        // 日志保留天数
        LogUtils.getConfig().saveDays = 7
        LogUtils.i("initializeUtils.initLog.strDir=" + LogUtils.getConfig().dir)
    }


    private fun initWechatPay() {
        wechatPayApi = WXAPIFactory.createWXAPI(context, null)
    }

    fun wechatPayApi(): IWXAPI {
        if (wechatPayApi == null) {
            wechatPayApi = WXAPIFactory.createWXAPI(context, null)
            wechatPayApi!!.registerApp("wx1e22b31e63df0112")
            return wechatPayApi!!
        } else {
            return wechatPayApi!!
        }
    }


    private fun initBugly() {
        CrashReport.initCrashReport(context, "e4740051ed", true)
        CrashReport.setIsDevelopmentDevice(context, BuildConfig.DEBUG);
    }

    private fun initOSSInstance() {
        OSSInstance.instance.init(context!!)
    }

    private fun initUMeng() {
        LogUtils.e(getChannel(context!!))
        UMConfigure.preInit(context, "5cf0ed994ca3575330000ba2", getChannel(context!!))
        UMConfigure.init(context, "5cf0ed994ca3575330000ba2", getChannel(context!!), UMConfigure.DEVICE_TYPE_PHONE, "")
    }

    fun getChannel(context: Context): String? {
        try {
            val appInfo = context.packageManager
                    .getApplicationInfo(context.packageName,
                            PackageManager.GET_META_DATA)
            return appInfo.metaData.getString("UMENG_CHANNEL")
        } catch (e: Exception) {
        }

        return "getChannelException"
    }
}