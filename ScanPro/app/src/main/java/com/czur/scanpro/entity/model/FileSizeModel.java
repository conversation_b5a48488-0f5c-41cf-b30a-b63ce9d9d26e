package com.czur.scanpro.entity.model;

import java.util.List;

/**
 * Created by <PERSON>z on 2018/4/27.
 * Email：<EMAIL>
 */
public class FileSizeModel {


    /**
     * code : 1000
     * msg : Success
     * body : {"keyList":[{"ossKey":"108/7F2B8D9F-7075-4925-8D75-DD2908CAB48A_BA18542D-2648-4A7B-8529-787173AA9ADB.base.jpg","fileSize":"850842"},{"ossKey":"00459e26-263c-4f29-b124-6672b75c51d4.jpg","fileSize":"4222274"}]}
     */

    private int code;
    private String msg;
    private BodyBean body;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public BodyBean getBody() {
        return body;
    }

    public void setBody(BodyBean body) {
        this.body = body;
    }

    public static class BodyBean {
        private List<KeyListBean> keyList;

        public List<KeyListBean> getKeyList() {
            return keyList;
        }

        public void setKeyList(List<KeyListBean> keyList) {
            this.keyList = keyList;
        }

        public static class KeyListBean {
            /**
             * ossKey : 108/7F2B8D9F-7075-4925-8D75-DD2908CAB48A_BA18542D-2648-4A7B-8529-787173AA9ADB.base.jpg
             * fileSize : 850842
             */

            private String ossKey;
            private String fileSize;

            public String getOssKey() {
                return ossKey;
            }

            public void setOssKey(String ossKey) {
                this.ossKey = ossKey;
            }

            public String getFileSize() {
                return fileSize;
            }

            public void setFileSize(String fileSize) {
                this.fileSize = fileSize;
            }
        }
    }
}
