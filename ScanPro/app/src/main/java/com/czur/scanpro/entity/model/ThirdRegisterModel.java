package com.czur.scanpro.entity.model;

public class ThirdRegisterModel {


    /**
     * id : “414
     * openId : **********
     * unionId : **********
     * platformId : 3
     * isActive : true
     * createOn : *************
     * bindOn : null
     * accountId : null
     * nickname : 我好饿Zz
     * imageUrl : https://tvax1.sinaimg.cn//crop.192.147.600.600.1024/9b49f5bbly8fykciaro7wj20u00u0dgd.jpg
     */

    private String id;
    private String openId;
    private String unionId;
    private int platformId;
    private boolean isActive;
    private long createOn;
    private Object bindOn;
    private Object accountId;
    private String nickname;
    private String imageUrl;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public boolean isIsActive() {
        return isActive;
    }

    public void setIsActive(boolean isActive) {
        this.isActive = isActive;
    }

    public long getCreateOn() {
        return createOn;
    }

    public void setCreateOn(long createOn) {
        this.createOn = createOn;
    }

    public Object getBindOn() {
        return bindOn;
    }

    public void setBindOn(Object bindOn) {
        this.bindOn = bindOn;
    }

    public Object getAccountId() {
        return accountId;
    }

    public void setAccountId(Object accountId) {
        this.accountId = accountId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
}
