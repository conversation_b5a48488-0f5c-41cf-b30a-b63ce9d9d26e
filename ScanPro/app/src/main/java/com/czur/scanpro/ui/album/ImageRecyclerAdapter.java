package com.czur.scanpro.ui.album;

import android.app.Activity;
import android.graphics.Point;
import android.net.Uri;
import android.os.Build;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.czur.scanpro.R;
import com.czur.scanpro.entity.model.ImageEntity;
import com.czur.scanpro.utils.EtUtils;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.backends.pipeline.PipelineDraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.common.ResizeOptions;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.UUID;

/**
 * Created by Yz on 2018/3/15
 * Email：<EMAIL>
 */


public class ImageRecyclerAdapter extends RecyclerView.Adapter<ViewHolder> {


    private ImagePicker imagePicker;
    private Activity mActivity;
    private ArrayList<ImageItem> images;       //当前需要显示的所有的图片数据
    private ArrayList<ImageItem> mSelectedImages; //全局保存的已经选中的图片数据
    private int mImageSize;               //每个条目的大小
    private LayoutInflater mInflater;
    private OnImageItemClickListener listener;   //图片被点击的监听
    private  Boolean isSelected = false;
    private ArrayList<String> list = new ArrayList<String>();
    private ArrayList<ImageEntity> listImage =  new ArrayList<ImageEntity>();
    private LinkedHashMap<Integer, String> isCheckedMap =new LinkedHashMap();
    private  int PIC_TOTAL_SIZE = 30;

    public void setOnImageItemClickListener(OnImageItemClickListener listener) {
        this.listener = listener;
    }

    public interface OnImageItemClickListener {
        void onImageItemClick(View view, ImageItem imageItem, int position,Integer size,ArrayList<ImageEntity> listImage);
    }

    public void refreshData(ArrayList<ImageItem> images) {
        if (images == null || images.size() == 0) this.images = new ArrayList<>();
        else this.images = images;
        list.clear();
        isCheckedMap.clear();
        notifyDataSetChanged();
    }

    /**
     * 构造方法
     */
    public ImageRecyclerAdapter(Activity activity, ArrayList<ImageItem> images) {
        this.mActivity = activity;
        if (images == null || images.size() == 0) this.images = new ArrayList<>();
        else this.images = images;

        mImageSize = EtUtils.getImageItemWidth(mActivity);
//        imagePicker = ImagePicker.getInstance();
//        mSelectedImages = imagePicker.getSelectedImages();
        mInflater = LayoutInflater.from(activity);
        String version =  Build.VERSION.RELEASE.trim();
        if(version.contains(".")){
            version = version.substring(0,version.indexOf("."));
        }
        if(Float.parseFloat(version) <= 8f){
            PIC_TOTAL_SIZE = 10;
        }else{
            PIC_TOTAL_SIZE = 30;
        }
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new ImageViewHolder(mInflater.inflate(R.layout.item_image_list_item, parent, false));
//        return new IVmageHolder(mInflater.inflate(R.layout.item_image_list_item, parent, false));
    }

    private class IVmageHolder extends ViewHolder{
        SimpleDraweeView ivThumb;
        TextView tvIndex;
        RelativeLayout fileInnerItem;
        public IVmageHolder(@NonNull View itemView) {
            super(itemView);

            fileInnerItem =(RelativeLayout)itemView.findViewById(R.id.file_inner_item);
            ivThumb = (SimpleDraweeView) itemView.findViewById(R.id.iv_thumb);
            tvIndex= (TextView) itemView.findViewById(R.id.tv_index);

        }
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        if (holder instanceof ImageViewHolder) {
            ((ImageViewHolder) holder).bind(holder,position);
        }
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return images.size();
    }

    public ImageItem getItem(int position) {

        return images.get(position);

    }

    private class ImageViewHolder extends ViewHolder {
        ImageEntity mItem;
        View rootView;
        SimpleDraweeView ivThumb;
        SimpleDraweeView bgImg;
        TextView tvIndex;
        TextView flag;
        RelativeLayout fileInnerItem;
        ImageViewHolder(View itemView) {
            super(itemView);
            rootView = itemView;
            fileInnerItem =(RelativeLayout)itemView.findViewById(R.id.image_inner_item);
            ivThumb = (SimpleDraweeView) itemView.findViewById(R.id.iv_thumb);
            bgImg = (SimpleDraweeView) itemView.findViewById(R.id.bg_img);
            tvIndex= (TextView) itemView.findViewById(R.id.tv_index);
            flag= (TextView) itemView.findViewById(R.id.circle_flag);
            itemView.setLayoutParams(new AbsListView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, mImageSize)); //让图片是个正方形
        }

        void bind(final ViewHolder holder, final int position) {
            final ImageItem imageItem = getItem(position);
            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams )ivThumb.getLayoutParams();
            layoutParams.height=layoutParams.width*15/11;
            ivThumb.setLayoutParams(layoutParams);
            ivThumb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(tvIndex.isEnabled()){
                        tvIndex.setEnabled(false);
                        bgImg.setVisibility(View.GONE);
                        flag.setVisibility(View.VISIBLE);
                        tvIndex.setText("");
                        if(list!=null){
                            Iterator<String> ite = list.iterator();
                            while(ite.hasNext()) {
                                if((isCheckedMap.get(position)).equals(ite.next())) {
                                    ite.remove();
                                }
                            }
                        }
                        if(listImage!=null){
                            Iterator<ImageEntity> ite = listImage.iterator();
                            while(ite.hasNext()) {
                                if((isCheckedMap.get(position)).equals(ite.next().getFileId())) {
                                    ite.remove();
                                }
                            }
                        }
                        if (listener != null) listener.onImageItemClick(rootView, imageItem, position,list.size(),listImage);
                        notifyItemChanged(position);
//                        notifyDataSetChanged();
                    } else {
                        if (list.size() < PIC_TOTAL_SIZE) {
                            list.add(isCheckedMap.get(position));
                            listImage.add(new ImageEntity(isCheckedMap.get(position), imageItem.path));
                            if (listener != null) listener.onImageItemClick(rootView, imageItem, position,list.size(),listImage);
                            notifyItemChanged(position);
//                            notifyDataSetChanged();
                        }
                    }

                }
            });

            if(!isCheckedMap.containsKey(position))
            isCheckedMap.put(position,UUID.randomUUID().toString());

            if(list!=null){
                for(int i=0;i<list.size();i++){
                    if(list.get(i).equals(isCheckedMap.get(position))){

                         tvIndex.setEnabled(true);
                        bgImg.setVisibility(View.VISIBLE);
                        flag.setVisibility(View.GONE);
                        tvIndex.setText(String.valueOf(i + 1));
                    }
                }
            }
//            listImage.add(image);
            Uri uri = Uri.parse("file://" + imageItem.path);
            Point size = new Point(mImageSize, mImageSize);
            ImageRequest request = ImageRequestBuilder
                    .newBuilderWithSource(uri)
                    .setResizeOptions(new ResizeOptions(size.x, size.y))
                    .build();
            PipelineDraweeController controller = (PipelineDraweeController) Fresco.newDraweeControllerBuilder().setOldController(ivThumb.getController()).setImageRequest(request).build();
            ivThumb.setController(controller);
        }

    }




}
