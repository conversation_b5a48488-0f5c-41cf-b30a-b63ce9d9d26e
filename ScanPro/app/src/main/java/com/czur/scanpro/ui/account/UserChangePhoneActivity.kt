package com.czur.scanpro.ui.account

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityResetPasswordBinding
import com.czur.scanpro.databinding.ActivityUserChangePhoneBinding
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator
class UserChangePhoneActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityUserChangePhoneBinding by lazy{
        ActivityUserChangePhoneBinding.inflate(layoutInflater)
    }

    private var userPreferences: UserPreferences? = null
    private var timeCount: TimeCount? = null
    private var httpManager: HttpManager? = null
    private var codeHasContent = false


    private val codeTextWatcher = object : TextWatcher {


        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            // 输入的内容变化的监听
            codeHasContent = s.isNotEmpty()

            checkChangePhoneButtonToClick()
        }

        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int,
                                       after: Int) {
        }

        override fun afterTextChanged(s: Editable) {
            // 输入后的监听
            codeHasContent = s.isNotEmpty()
            checkChangePhoneButtonToClick()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_user_change_phone)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {

        binding.topBar.normalTitle.setText(R.string.user_change_phone)
        httpManager = HttpManager.getInstance()
        userPreferences = UserPreferences.getInstance(this)
        formatString()

    }

    private fun registerEvent() {
        binding.getCodePswBtn.setOnClickListener(this)
        binding.topBar.normalBackBtn.setOnClickListener(this)

        binding.nextStepBtn.setOnClickListener(this)
        binding.nextStepBtn.isSelected = false
        binding.nextStepBtn.isClickable = false
        binding.codeEdt.addTextChangedListener(codeTextWatcher)

    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            R.id.getCodePswBtn -> getMobileCode(userPreferences!!.userMobile)
            R.id.nextStepBtn -> checkCodeToNextStep()
            else -> {
            }
        }
    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private fun getMobileCode(mobile: String) {
        httpManager!!.requestPassport().mobileCode(mobile, String::class.java, object : MiaoHttpManager.Callback<String> {
            override fun onStart() {

            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                LogUtils.iTag("send mobile code", Gson().toJson(entity))
                timeCountBegin()
                showMessage(R.string.toast_code_send)

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                when {
                    entity.code == MiaoHttpManager.STATUS_CODE_1_MIN -> showMessage(R.string.toast_code_1_min)
                    entity.code == MiaoHttpManager.STATUS_5_MIN_4_TIME -> showMessage(R.string.toast_5_min_4_time)
                    entity.code == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY -> showMessage(R.string.toast_5_time_in_one_day)
                    else -> showMessage(R.string.request_failed_alert)
                }

            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    private fun checkCodeToNextStep() {
        val code = binding.codeEdt!!.text.toString()
        if (code.length <= 5) {
            showMessage(R.string.edit_text_code_length)
        } else {
            KeyboardUtils.hideSoftInput(this)
            httpManager!!.requestPassport().updateMobileFirst(userPreferences!!.imei, Constants.SCAN_PRO,
                    userPreferences!!.channel, userPreferences!!.userId, userPreferences!!.token, userPreferences!!.userId, code, String::class.java,
                    object : MiaoHttpManager.Callback<String> {
                        override fun onStart() {
                            showProgressDialog(false)
                        }

                        override fun onResponse(entity: MiaoHttpEntity<String>) {
                            LogUtils.i(Gson().toJson(entity))
                            hideProgressDialog()
                            userPreferences!!.ukey=entity.body
                            val intent = Intent(this@UserChangePhoneActivity, UserBindPhoneActivity::class.java)
                            intent.putExtra("changePhone", true)
                            ActivityUtils.startActivity(intent)
                        }

                        override fun onFailure(entity: MiaoHttpEntity<String>) {
                            hideProgressDialog()

                            if (entity.code == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                showMessage(R.string.mail_code_expired)
                            } else {
                                showMessage(R.string.mail_toast_testing_fail)
                            }
                        }

                        override fun onError(e: Exception) {
                            hideProgressDialog()
                            showMessage(R.string.mail_toast_testing_fail)
                        }
                    })
        }
    }


    /**
     * @des: 修改手机信息字体部分颜色
     * @params:
     * @return:
     */

    private fun formatString() {
        val finalStr = String.format(getString(R.string.user_change_phone_first_text), userPreferences!!.getUserMobile())
        val spannableString = SpannableString(finalStr)
        spannableString.setSpan(ForegroundColorSpan(this.resources.getColor(R.color.red_de4d4d)), 20, 31, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.userChangePhoneInfoTv.text = spannableString
    }


    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private fun checkChangePhoneButtonToClick() {

        if (codeHasContent) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
           binding.nextStepBtn.background = drawable
           binding.nextStepBtn.isSelected = true
           binding.nextStepBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
           binding.nextStepBtn.isClickable = true
        } else {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
            binding.nextStepBtn.background = drawable
            binding.nextStepBtn.isSelected = false
            binding.nextStepBtn.setTextColor(resources.getColor(R.color.white))
            binding.nextStepBtn.isClickable = false
        }
    }

    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private fun timeCountBegin() {
        timeCount = TimeCount(60000, 1000)
        timeCount!!.start()

    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    internal inner class TimeCount(millisInFuture: Long, countDownInterval: Long) : CountDownTimer(millisInFuture, countDownInterval) {

        override fun onFinish() {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.red_de4d4d)).build()
            binding.getCodePswBtn.background = drawable
            binding.getCodePswBtn.setText(R.string.gain)
            binding.getCodePswBtn.isClickable = true
            binding.getCodePswBtn.isSelected = true


        }

        override fun onTick(millisUntilFinished: Long) {
            val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(23f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e1)).build()
            binding.getCodePswBtn.background = drawable
            binding.getCodePswBtn.isClickable = false
            binding.getCodePswBtn.text = (millisUntilFinished / 1000).toString() + " s"
            binding.getCodePswBtn.isSelected = false

        }

    }

    override fun onDestroy() {
        super.onDestroy()
        if (Validator.isNotEmpty(timeCount)) {
            timeCount!!.cancel()
        }
    }
}