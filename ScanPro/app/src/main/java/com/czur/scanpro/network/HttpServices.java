package com.czur.scanpro.network;


import com.czur.scanpro.entity.model.*;
import com.czur.scanpro.entity.model.pay.PayCheckOrderModel;
import com.czur.scanpro.entity.model.pay.PayOrderModel;
import com.czur.scanpro.network.core.MiaoHttpEntity;
import com.czur.scanpro.network.core.MiaoHttpGet;
import com.czur.scanpro.network.core.MiaoHttpManager;
import com.czur.scanpro.network.core.MiaoHttpParam;
import com.czur.scanpro.network.core.MiaoHttpPost;

import java.lang.reflect.Type;

public interface HttpServices {


    //订阅商品列表接口
    @MiaoHttpGet("v3/User/products")
    void getProducts(@MiaoHttpParam("bundleId") String bundleId, @MiaoHttpParam("os") String os, Type type, MiaoHttpManager.Callback<ProductModel> callback);

    //订阅商品列表接口
    @MiaoHttpGet("v3/User/product/list")
    void getProductList(@MiaoHttpParam("bundleId") String bundleId, @MiaoHttpParam("os") String os, Type type, MiaoHttpManager.Callback<ProductModel> callback);

    //获取app上传oss token
    @MiaoHttpPost("v3/saomiao/apptoken")
    MiaoHttpEntity<OssModel> getOssInfo(@MiaoHttpParam("clientId") String clientId, Class<OssModel> clazz);

    @MiaoHttpPost("v3/saomiao/apptoken")
    void getOssInfo(@MiaoHttpParam("clientId") String clientId, Class<OssModel> clazz, MiaoHttpManager.Callback<OssModel> callback);

    //按时间（可选）获取文件列表
    @MiaoHttpPost("v3/saomiao/getByTime")
    MiaoHttpEntity<SyncEntity> getFileByTime(@MiaoHttpParam("u_id") String userId, @MiaoHttpParam("synTime") String synTime, Class<SyncEntity> clazz);

    //获取服务器时间
    @MiaoHttpPost("v3/saomiao/serverTime")
    MiaoHttpEntity<String> getServerTime(@MiaoHttpParam("userId") String userId, Class<String> clazz);


    //验证分享次数
    @MiaoHttpGet("v3/public/preValidateShareFileLink")
    void validatorShareLink(@MiaoHttpParam("groupId") String groupId,Class<String> clazz, MiaoHttpManager.Callback<String> callback);


    //消耗功能次数
    //ocr,pdf,handwriting,cloudocr,highCertificate
    @MiaoHttpGet("v3/saomiao/consumption")
    void consumption(@MiaoHttpParam("userId") String userId, @MiaoHttpParam("function") String function, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取channel
    @MiaoHttpGet("v3/endpoint")
    void channel(Class<ChannelModel> clazz, MiaoHttpManager.Callback<ChannelModel> callback);

    @MiaoHttpGet("v1/user/info")
    MiaoHttpEntity<UserInfoModel> userInfo(@MiaoHttpParam("u_id") String uId2, Class<UserInfoModel> clazz);

    @MiaoHttpGet("v1/user/info")
    void userInfo(@MiaoHttpParam("u_id") String uId2, Class<UserInfoModel> clazz, MiaoHttpManager.Callback<UserInfoModel> callback);

    @MiaoHttpGet("v3/User/info")
    void userAllInfo(@MiaoHttpParam("userId") String uId2, Class<UserAllInfoModel> clazz, MiaoHttpManager.Callback<UserAllInfoModel> callback);
    @MiaoHttpGet("v3/User/info")
    MiaoHttpEntity<UserAllInfoModel> userAllInfoSync(@MiaoHttpParam("userId") String uId2, Class<UserAllInfoModel> clazz);

    @MiaoHttpGet("v3/User/invitation")
    void invitation(@MiaoHttpParam("userId") String userId,@MiaoHttpParam("code") String code, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    enum PayWay {
        Alipay("Alipay"),
        Wechat("Wechat");

        private String payWay;

        PayWay(String payWay) {
            this.payWay = payWay;
        }

        public String getPayWay() {
            return payWay;
        }
    }

    @MiaoHttpPost("v3/User/order")
    void createPayOrder(@MiaoHttpParam("u_id") String userId, @MiaoHttpParam("productId") String productId, @MiaoHttpParam("payWay") String payWay,
                        Class<PayOrderModel> clazz, MiaoHttpManager.Callback<PayOrderModel> callback);

    @MiaoHttpPost("v3/User/payCallback")
    MiaoHttpEntity<PayCheckOrderModel> checkPayOrder(@MiaoHttpParam("u_id") String userId, @MiaoHttpParam("transactionId") String transactionId,
                                                     @MiaoHttpParam("orderId") String orderId, @MiaoHttpParam("payWay") String payWay, Class<PayCheckOrderModel> clazz);
}
