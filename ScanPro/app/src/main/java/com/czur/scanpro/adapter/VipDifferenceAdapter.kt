package com.czur.scanpro.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.czur.scanpro.R
import com.czur.scanpro.entity.model.VipDifferenceModel
import io.realm.Realm


/**
 * Created by Yz on 2019/1/5.
 * Email：<EMAIL>
 */
class VipDifferenceAdapter
/**
 * 构造方法
 */
(private val context: Context?, //当前需要显示的所有的图片数据
 private var datas: List<VipDifferenceModel.VIPVSGeneralBean>?) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    val realm = Realm.getDefaultInstance()

    companion object {
        private const val ITEM_TYPE_NORMAL = 0
    }


    fun refreshData(vipsCons: List<VipDifferenceModel.VIPVSGeneralBean>) {
        this.datas = vipsCons
        notifyDataSetChanged()

    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_user_advanced, parent, false)
        return VipDifferenceHolder(view)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        if (holder is VipDifferenceHolder) {
            holder.mItem = datas!![position]

            holder.tv1.text = holder.mItem!!.col1
            holder.tv2.text = holder.mItem!!.col2
            holder.tv3.text = holder.mItem!!.col3

            if (position % 2 == 0) {
                holder.bg.background=context!!.resources.getDrawable(R.color.gray_f9)
            } else {
                holder.bg.background=context!!.resources.getDrawable(R.color.white)

            }

        }

    }

    override fun getItemViewType(position: Int): Int = ITEM_TYPE_NORMAL

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount(): Int {
        return datas!!.size
    }


    private inner class VipDifferenceHolder internal constructor(val mView: View) : RecyclerView.ViewHolder(mView) {
        internal var mItem: VipDifferenceModel.VIPVSGeneralBean? = null
        internal var tv1: TextView
        internal var tv2: TextView
        internal var tv3: TextView
        internal var bg: LinearLayout


        init {

            tv1 = mView.findViewById<View>(R.id.tv_1) as TextView
            tv2 = mView.findViewById<View>(R.id.tv_2) as TextView
            tv3 = mView.findViewById<View>(R.id.tv_3) as TextView
            bg = mView.findViewById<View>(R.id.bg) as LinearLayout

        }


    }


}
