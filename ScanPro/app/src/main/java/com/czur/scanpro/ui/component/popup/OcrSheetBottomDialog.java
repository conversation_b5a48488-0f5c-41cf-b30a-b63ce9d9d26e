package com.czur.scanpro.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.BarUtils;
import com.czur.scanpro.R;


public class OcrSheetBottomDialog extends Dialog implements View.OnClickListener {
    private Context mContext;
    private RelativeLayout ocrChineseBtn;
    private TextView ocrChineseTv;
    private ImageView ocrChineseImg;
    private RelativeLayout ocrChineseTaiwanBtn;
    private TextView ocrChineseTaiwanTv;
    private ImageView ocrChineseTaiwanImg;
    private RelativeLayout ocrEnglishBtn;
    private TextView ocrEnglishTv;
    private ImageView ocrEnglishImg;
    private RelativeLayout ocrFrenchBtn;
    private TextView ocrFrenchTv;
    private ImageView ocrFrenchImg;
    private RelativeLayout ocrItalianBtn;
    private TextView ocrItalianTv;
    private ImageView ocrItalianImg;
    private RelativeLayout ocrSpanishBtn;
    private TextView ocrSpanishTv;
    private ImageView ocrSpanishImg;
    private RelativeLayout ocrPortugueseBtn;
    private TextView ocrPortugueseTv;
    private ImageView ocrPortugueseImg;
    private RelativeLayout ocrSwedishBtn;
    private TextView ocrSwedishTv;
    private ImageView ocrSwedishImg;
    private RelativeLayout ocrDanishBtn;
    private TextView ocrDanishTv;
    private ImageView ocrDanishImg;
    private RelativeLayout ocrRussianBtn;
    private TextView ocrRussianTv;
    private ImageView ocrRussianImg;
    private RelativeLayout ocrJapanesesBtn;
    private TextView ocrJapanesesTv;
    private ImageView ocrJapanesesImg;
    private RelativeLayout ocrCancelBtn;
    private RelativeLayout ocrConfirmBtn;


    public OcrSheetBottomDialog(Context context, OnOcrLanguageClickListener onOcrLanguageClickListener) {
        //重点实现R.style.DialogStyle 动画效果
        this(context, R.style.SocialAccountDialogStyle);
        this.onOcrLanguageClickListener = onOcrLanguageClickListener;
        mContext = context;
    }

    public OcrSheetBottomDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_et_ocr_bottom_sheet);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        setCancelable(false);
        setCanceledOnTouchOutside(false);

        ocrChineseBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_chinese_btn);
        ocrChineseTv = (TextView) getWindow().findViewById(R.id.ocr_chinese_tv);
        ocrChineseImg = (ImageView) getWindow().findViewById(R.id.ocr_chinese_img);
        ocrChineseTaiwanBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_chinese_taiwan_btn);
        ocrChineseTaiwanTv = (TextView) getWindow().findViewById(R.id.ocr_chinese_taiwan_tv);
        ocrChineseTaiwanImg = (ImageView) getWindow().findViewById(R.id.ocr_chinese_taiwan_img);
        ocrEnglishBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_english_btn);
        ocrEnglishTv = (TextView) getWindow().findViewById(R.id.ocr_english_tv);
        ocrEnglishImg = (ImageView) getWindow().findViewById(R.id.ocr_english_img);
        ocrFrenchBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_french_btn);
        ocrFrenchTv = (TextView) getWindow().findViewById(R.id.ocr_french_tv);
        ocrFrenchImg = (ImageView) getWindow().findViewById(R.id.ocr_french_img);
        ocrItalianBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_italian_btn);
        ocrItalianTv = (TextView) getWindow().findViewById(R.id.ocr_italian_tv);
        ocrItalianImg = (ImageView) getWindow().findViewById(R.id.ocr_italian_img);
        ocrSpanishBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_spanish_btn);
        ocrSpanishTv = (TextView) getWindow().findViewById(R.id.ocr_spanish_tv);
        ocrSpanishImg = (ImageView) getWindow().findViewById(R.id.ocr_spanish_img);
        ocrPortugueseBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_portuguese_btn);
        ocrPortugueseTv = (TextView) getWindow().findViewById(R.id.ocr_portuguese_tv);
        ocrPortugueseImg = (ImageView) getWindow().findViewById(R.id.ocr_portuguese_img);
        ocrSwedishBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_swedish_btn);
        ocrSwedishTv = (TextView) getWindow().findViewById(R.id.ocr_swedish_tv);
        ocrSwedishImg = (ImageView) getWindow().findViewById(R.id.ocr_swedish_img);
        ocrDanishBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_danish_btn);
        ocrDanishTv = (TextView) getWindow().findViewById(R.id.ocr_danish_tv);
        ocrDanishImg = (ImageView) getWindow().findViewById(R.id.ocr_danish_img);
        ocrRussianBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_russian_btn);
        ocrRussianTv = (TextView) getWindow().findViewById(R.id.ocr_russian_tv);
        ocrRussianImg = (ImageView) getWindow().findViewById(R.id.ocr_russian_img);
        ocrJapanesesBtn = (RelativeLayout) getWindow().findViewById(R.id.ocr_japaneses_btn);
        ocrJapanesesTv = (TextView) getWindow().findViewById(R.id.ocr_japaneses_tv);
        ocrJapanesesImg = (ImageView) getWindow().findViewById(R.id.ocr_japaneses_img);


        ocrCancelBtn = (RelativeLayout) findViewById(R.id.ocr_cancel_btn);
        ocrConfirmBtn = (RelativeLayout) findViewById(R.id.ocr_confirm_btn);


        //设置显示的位置
        params.gravity = Gravity.BOTTOM;
        //设置dialog的宽度
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);

        BarUtils.setStatusBarColor(getWindow(), Color.TRANSPARENT);
        BarUtils.setNavBarColor(getWindow(), Color.TRANSPARENT);
        BarUtils.setStatusBarLightMode(getWindow(), true);
        ocrChineseBtn.setOnClickListener(this);
        ocrChineseTaiwanBtn.setOnClickListener(this);
        ocrEnglishBtn.setOnClickListener(this);
        ocrFrenchBtn.setOnClickListener(this);
        ocrItalianBtn.setOnClickListener(this);
        ocrSpanishBtn.setOnClickListener(this);
        ocrPortugueseBtn.setOnClickListener(this);
        ocrSwedishBtn.setOnClickListener(this);
        ocrDanishBtn.setOnClickListener(this);
        ocrRussianBtn.setOnClickListener(this);
        ocrJapanesesBtn.setOnClickListener(this);
        ocrCancelBtn.setOnClickListener(this);
        ocrConfirmBtn.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (onOcrLanguageClickListener != null) {
            onOcrLanguageClickListener.onItemClick(v.getId());
        }
        int id = v.getId();
        if (id == R.id.ocr_chinese_btn) {
            setChinese();
        } else if (id == R.id.ocr_chinese_taiwan_btn) {
            setChineseTaiwan();
        } else if (id == R.id.ocr_english_btn) {
            setEnglish();
        } else if (id == R.id.ocr_french_btn) {
            setFrench();
        } else if (id == R.id.ocr_italian_btn) {
            setItalian();
        } else if (id == R.id.ocr_spanish_btn) {
            setSpanish();
        } else if (id == R.id.ocr_portuguese_btn) {
            setPortuguese();
        } else if (id == R.id.ocr_swedish_btn) {
            setSwedish();
        } else if (id == R.id.ocr_danish_btn) {
            setDanish();
        } else if (id == R.id.ocr_russian_btn) {
            setRussian();
        } else if (id == R.id.ocr_japaneses_btn) {
            setJapanese();
        } else if (id == R.id.ocr_confirm_btn || id == R.id.ocr_cancel_btn) {
        }
    }

    public void setJapanese() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrJapanesesImg.setVisibility(View.VISIBLE);
    }

    public void setRussian() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrRussianImg.setVisibility(View.VISIBLE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setDanish() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrDanishImg.setVisibility(View.VISIBLE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setSwedish() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrSwedishImg.setVisibility(View.VISIBLE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setPortuguese() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrPortugueseImg.setVisibility(View.VISIBLE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setSpanish() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrSpanishImg.setVisibility(View.VISIBLE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setItalian() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrItalianImg.setVisibility(View.VISIBLE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setFrench() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrFrenchImg.setVisibility(View.VISIBLE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setEnglish() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrEnglishImg.setVisibility(View.VISIBLE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setChinese() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrChineseImg.setVisibility(View.VISIBLE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseTaiwanImg.setVisibility(View.GONE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }

    public void setChineseTaiwan() {
        ocrChineseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrChineseImg.setVisibility(View.GONE);
        ocrChineseTaiwanTv.setTextColor(mContext.getResources().getColor(R.color.red_de4d4d));
        ocrChineseTaiwanImg.setVisibility(View.VISIBLE);
        ocrEnglishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrEnglishImg.setVisibility(View.GONE);
        ocrFrenchTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrFrenchImg.setVisibility(View.GONE);
        ocrItalianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrItalianImg.setVisibility(View.GONE);
        ocrSpanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSpanishImg.setVisibility(View.GONE);
        ocrPortugueseTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrPortugueseImg.setVisibility(View.GONE);
        ocrSwedishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrSwedishImg.setVisibility(View.GONE);
        ocrDanishTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrDanishImg.setVisibility(View.GONE);
        ocrRussianTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrRussianImg.setVisibility(View.GONE);
        ocrJapanesesTv.setTextColor(mContext.getResources().getColor(R.color.black_24));
        ocrJapanesesImg.setVisibility(View.GONE);
    }


    /**
     * 点击事件接口
     **/
    public interface OnOcrLanguageClickListener {
        /**
         * @param viewId
         */
        void onItemClick(int viewId);
    }

    private OnOcrLanguageClickListener onOcrLanguageClickListener;

    private void setOnOcrLanguageClickListener(OnOcrLanguageClickListener onOcrLanguageClickListener) {
        this.onOcrLanguageClickListener = onOcrLanguageClickListener;

    }


}