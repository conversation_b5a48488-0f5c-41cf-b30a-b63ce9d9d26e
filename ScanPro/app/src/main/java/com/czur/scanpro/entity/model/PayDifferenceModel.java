package com.czur.scanpro.entity.model;

import java.util.List;

public class PayDifferenceModel {


    /**
     * VIPVSGeneral : [{"col1":"功能","col2":"免费用户","col3":"高级VIP"},{"col1":"云存储空间","col2":"500MB","col3":"10G"},{"col1":"生成PDF次数","col2":"6次/天","col3":"无限制"},{"col1":"文字识别","col2":"6次/天","col3":"无限制"},{"col1":"证件识别","col2":"1次/天","col3":"无限制"},{"col1":"手写识别","col2":"30次","col3":"50次/月"},{"col1":"分享加密","col2":"-","col3":"√"},{"col1":"云识别","col2":"30次","col3":"100次/月"}]
     * payTitle : ["10G超大云空间","识别无限制","PDF无限制","手写识别50次/月","分享加密","云识别100次/月"]
     * version : 3
     */

    private int version;
    private List<VIPVSGeneralBean> VIPVSGeneral;
    private List<String> payTitle;

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public List<VIPVSGeneralBean> getVIPVSGeneral() {
        return VIPVSGeneral;
    }

    public void setVIPVSGeneral(List<VIPVSGeneralBean> VIPVSGeneral) {
        this.VIPVSGeneral = VIPVSGeneral;
    }

    public List<String> getPayTitle() {
        return payTitle;
    }

    public void setPayTitle(List<String> payTitle) {
        this.payTitle = payTitle;
    }

    public static class VIPVSGeneralBean {
        /**
         * col1 : 功能
         * col2 : 免费用户
         * col3 : 高级VIP
         */

        private String col1;
        private String col2;
        private String col3;

        public String getCol1() {
            return col1;
        }

        public void setCol1(String col1) {
            this.col1 = col1;
        }

        public String getCol2() {
            return col2;
        }

        public void setCol2(String col2) {
            this.col2 = col2;
        }

        public String getCol3() {
            return col3;
        }

        public void setCol3(String col3) {
            this.col3 = col3;
        }
    }
}
