package com.czur.scanpro.ui.file

import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.view.View
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityOcrBinding
import com.czur.scanpro.databinding.ActivityOcrResultBinding
import com.czur.scanpro.entity.model.BaiduTokenModel
import com.czur.scanpro.entity.model.BaiduWordModel
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.event.ConsumptionEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.EmptyCountPopup
import com.czur.scanpro.utils.ClipboardUtils
import com.google.gson.Gson
import io.realm.Realm
import okhttp3.*
import org.greenrobot.eventbus.EventBus
import java.io.IOException
import java.util.concurrent.TimeUnit


class OcrResultActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityOcrResultBinding by lazy{
        ActivityOcrResultBinding.inflate(layoutInflater)
    }

    private var resultText: String? = null
    private var position: Int = 0
    private var fileId: String? = null
    private var url: String? = null
    private var ocrType: Int = 0
    private var fromOcr: Boolean = false
    private var tempPath: String? = null
    private var currentTimeMillis: Long = 0
    private var okHttpClient: OkHttpClient? = null
    private var realm: Realm? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.black_2a)
        BarUtils.setStatusBarLightMode(this, false)
        setContentView(R.layout.activity_ocr_result)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {
        realm = Realm.getDefaultInstance()
        resultText = intent.getStringExtra("resultText")
        fromOcr = intent.getBooleanExtra("fromOcr", false)
        url = intent.getStringExtra("url")
        tempPath = intent.getStringExtra("tempPath")
        ocrType = intent.getIntExtra("ocrType", 0)
        if (ocrType == 3) {//云识别和文字识别绑定
            ocrType = 1
        }
        fileId = intent.getStringExtra("fileId")
        position = intent.getIntExtra("position", 0)

        binding.handwritingResultText.setMovementMethod(ScrollingMovementMethod.getInstance());
        binding.handwritingResultText.text = resultText
        binding.resultRl.requestFocus()
        binding.handwritingResultText.requestFocus()
        binding.cloudOcrTv.visibility = if (ocrType == 2) View.GONE else View.VISIBLE
        okHttpClient = OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .build()
        checkPromptToShow()
    }

    private fun registerEvent() {
        binding.fileHideTv.setOnClickListener(this)
        binding.cloudOcrTv.setOnClickListener(this)
        binding.recognizeAgain.setOnClickListener(this)
        binding.handwritingResultFinishBtn.setOnClickListener(this)
        binding.handwritingResultCopyRl.setOnClickListener(this)
    }

    private fun checkPromptToShow() {
        if (getSp().isFirstCloudOcrGuide && ocrType != 2) {
            binding.fileGuideTv.visibility = View.VISIBLE
            binding.fileGuideImg.visibility = View.VISIBLE
            binding.fileHideTv.visibility = View.VISIBLE

        } else {
            binding.fileGuideTv.visibility = View.GONE
            binding.fileGuideImg.visibility = View.GONE
            binding.fileHideTv.visibility = View.GONE
        }
    }

    private fun isHasCount(ocrType: Int): Boolean {
        return if (getSp().isVip) {
            when (ocrType) {
                1 -> true
                2 -> getSp().remainHandwriting > 0
                3 -> getSp().remainCloudOcr > 0
                else -> getSp().remainCard > 0
            }
        } else {
            when (ocrType) {
                1 -> getSp().remainOcr > 0
                2 -> getSp().remainHandwriting > 0
                3 -> getSp().remainCloudOcr > 0
                else -> getSp().remainCard > 0
            }
        }
    }

    private fun showNoCountEmptyMessage(ocrType: Int) {
        hideProgressDialog()
        return when (ocrType) {
            1 -> showOcrEmptyCountDialog()

            2 -> showEmptyCountDialog(this@OcrResultActivity, EmptyCountPopup.EmptyType.HANDWRTING)

            3 -> showEmptyCountDialog(this@OcrResultActivity, EmptyCountPopup.EmptyType.CLOUD)

            else -> showEmptyCountDialog(this@OcrResultActivity, EmptyCountPopup.EmptyType.CARD)

        }

    }

    private fun requestCloudOcrToken(bitmap: Bitmap?) {
        currentTimeMillis = System.currentTimeMillis()
        val formBody = FormBody.Builder()
                .add("grant_type", Constants.BAIDU_GRANT_TYPE)
                .add("client_id", Constants.BAIDU_CLIENT_ID)
                .add("client_secret", Constants.BAIDU_CLIENT_SECRET)
                .build()
        val request = Request.Builder().url(Constants.BAIDU_TOKEN_URL)
                .post(formBody).build()

        okHttpClient!!.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                LogUtils.d(e)
                runOnUiThread {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)
                }

            }

            override fun onResponse(call: Call, response: Response) {
                if (response.code == 200) {
                    val json = Gson().fromJson(response.body!!.string(), BaiduTokenModel::class.java)
                    LogUtils.e(json.access_token)
                    val urlEncode = EncodeUtils.base64Encode2String(ImageUtils.bitmap2Bytes(bitmap, Bitmap.CompressFormat.JPEG, 90))
                    cloudOcr(json.access_token, urlEncode)
                } else {
                    runOnUiThread {
                        hideProgressDialog()
                        showMessage(R.string.request_failed_alert)
                    }
                }
            }
        })


    }

    private fun cloudOcr(access_token: String, image: String) {
        val formBody = FormBody.Builder()
                .add("access_token", access_token)
                .add("image", image)
                .build()
        val request = Request.Builder().url(Constants.BAIDU_OCR_URL)
                .post(formBody).build()

        okHttpClient!!.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    hideProgressDialog()
                    showMessage(R.string.request_failed_alert)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                LogUtils.d(response)
                if (response.code == 200) {
                    val body = response.body!!.string()
                    val baiduWordModel = Gson().fromJson(body, BaiduWordModel::class.java)
                    val stringSpan = SpanUtils()
                    val words_result = baiduWordModel.words_result
                    if (words_result != null) {
                        for (wordsResultBean in words_result) {
                            LogUtils.d(wordsResultBean.words)
                            stringSpan.appendLine(wordsResultBean.words)
                        }
                        runOnUiThread {
                            hideProgressDialog()
                            val string = stringSpan.create().toString()
                            realm!!.executeTransaction {
                                val docEntity = realm!!.where(DocEntity::class.java)
                                        .equalTo("fileID", fileId)
                                        .equalTo("isDelete", 0.toInt())
                                        .findFirst()
                                docEntity!!.ocrResult = string
                            }
                           binding.handwritingResultText.text = string
                           binding.resultRl.requestFocus()
                           binding.handwritingResultText.requestFocus()
                            consumption()
                        }
                    } else {
                        runOnUiThread {
                            hideProgressDialog()
                            showMessage(R.string.recognition_defeat)
                        }
                    }
                } else {
                    runOnUiThread {
                        hideProgressDialog()
                        showMessage(R.string.recognition_defeat)
                    }
                }
            }
        })
    }

    /**
     * @des: 如果不到100毫秒就休眠到100毫秒
     * @params:
     * @return:
     */
    private fun threadSleepTo1000Ms(algTime: Long): Long {
        if (algTime in 1..999) {
            return (1000.0f - algTime).toLong()
        } else {
            return 0
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.fileHideTv -> {
                binding.fileGuideTv.visibility = View.GONE
                binding.fileGuideImg.visibility = View.GONE
                binding.fileHideTv.visibility = View.GONE
                getSp().setIsFirstCloudOcrGuide(false)
            }
            R.id.handwritingResultCopyRl -> {
                ClipboardUtils.copyText(binding.handwritingResultText.text.toString())
                showMessage(R.string.copy_success)
            }
            R.id.handwritingResultFinishBtn -> ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
            R.id.recognizeAgain -> {
                Thread(Runnable {
                    requestUserInfoSync()
                    runOnUiThread {
                        if (isHasCount(ocrType)) {
                            val intent1 = Intent(this@OcrResultActivity, OcrActivity::class.java)
                            intent1.putExtra("fileId", fileId)
                            intent1.putExtra("position", position)
                            intent1.putExtra("ocrType", ocrType)
                            intent1.putExtra("url", url)
                            intent1.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                            startActivity(intent1)
                        } else {
                            showNoCountEmptyMessage(ocrType)
                        }
                    }
                }).start()
            }
            R.id.cloudOcrTv -> {
                cloudOcr()
            }
            else -> {
            }
        }
    }

    private fun cloudOcr() {
        showProgressDialog(true)
        Thread(Runnable {
            requestUserInfoSync()
            runOnUiThread {
                if (fromOcr) {
                    if (isHasCount(3)) {
                        requestCloudOcrToken(ImageUtils.getBitmap(tempPath, 2048, 2048))
                    } else {
                        showNoCountEmptyMessage(3)
                    }
                } else {
                    if (isHasCount(3)) {
                        hideProgressDialog()
                        val intent1 = Intent(this@OcrResultActivity, OcrActivity::class.java)
                        intent1.putExtra("fileId", fileId)
                        intent1.putExtra("position", position)
                        intent1.putExtra("ocrType", 3)
                        intent1.putExtra("url", url)
                        intent1.putExtra("isCloud", true)
                        intent1.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                        startActivity(intent1)
                    } else {
                        showNoCountEmptyMessage(3)
                    }
                }
            }
        }).start()
    }

    private fun showOcrEmptyCountDialog() {
        if (isActive) {
            val builder = EmptyCountPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
            builder.setMessage(EmptyCountPopup.EmptyType.OCR)
            builder.setOnPositiveListener { dialog, _ ->
                dialog.cancel()
                cloudOcr()
            }
            val emptyCountPopup = builder.create()
            emptyCountPopup.show()
        }
    }

    /**
     * @des: 请求用户信息certificate
     * @params:
     * @return:
     */


    private fun consumption() {
        HttpManager.getInstance().request().consumption(getSp().userId, "cloudocr", String::class.java, object : MiaoHttpManager.CallbackNetwork<String> {
            override fun onNoNetwork() {
            }

            override fun onStart() {
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                EventBus.getDefault().post(ConsumptionEvent(EventType.BAIDU_OCR_COUNT_REDUCE))
                commonRequestUserInfoNow()
                WeakHandler().postDelayed({
                    hideProgressDialog()
                }, 800)

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {

            }

            override fun onError(e: Exception) {

            }
        })
    }

    override fun onBackPressed() {
        super.onBackPressed()
        ActivityUtils.finishToActivity(FilePreviewActivity::class.java, false)
    }

    override fun onDestroy() {
        super.onDestroy()
        Thread(Runnable {
            CleanUtils.cleanCustomDir(cacheDir.path + Constants.TEMP)
        }).start()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        LogUtils.i("requestCode=$requestCode", " resultCode=$resultCode")
        if (requestCode == SHARE_SUCCESS_CODE) {

        }
    }

    companion object {
        private val SHARE_SUCCESS_CODE = 666
    }
}