package com.czur.scanpro.ui.component.popup;


import com.czur.scanpro.R;

/**
 * Created by <PERSON><PERSON> on 2018/3/10
 * Email：<EMAIL>
 */

public enum CloudCommonPopupConstants {
    COMMON_ONE_BUTTON(-1, -1, R.string.ok, -1, -1),
    OK_ONE_BUTTON(-1, -1, R.string.okay, -1, -1),
    CONFIRM_ONE_BUTTON(-1, -1, R.string.confirm, -1, -1),
    COMMON_TWO_BUTTON(-1, -1, R.string.ok, R.string.cancel, -1),
    OCR_TWO_BUTTON(-1, -1, R.string.ok, R.string.cancel, -1),
    GPS_TWO_BUTTON(-1, -1, R.string.ok, R.string.cancel, -1),
    PRIVACY_BUTTON(-1, -1, R.string.agree, R.string.reject, -1),
    CANCEL_ACCOUNT_BUTTON(-1, -1, R.string.user_remove_account_btn, R.string.user_remove_account_btn_cancel, -1),

    EDT_TWO_BUTTON(-1, -1, R.string.ok, R.string.cancel, 1),
    INSTALL_ONE_BUTTON(-1, -1, R.string.install, -1, -1),

    UPDATE_ONE_BUTTON(-1, -1, R.string.update, -1, -1);


    private final int editText;
    private final int title;
    private final int message;
    private final int positiveBtn;
    private final int negativeBtn;

    CloudCommonPopupConstants(int title, int message, int positiveBtn, int negativeBtn, int editText) {
        this.editText = editText;
        this.title = title;
        this.message = message;
        this.positiveBtn = positiveBtn;
        this.negativeBtn = negativeBtn;
    }

    public int getMessage() {
        return message;
    }

    public int getPositiveBtn() {
        return positiveBtn;
    }

    public int getNegativeBtn() {
        return negativeBtn;
    }

    public int getEditText() {
        return editText;
    }

    public int getTitle() {
        return title;
    }
}
