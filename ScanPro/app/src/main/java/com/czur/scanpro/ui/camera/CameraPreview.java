package com.czur.scanpro.ui.camera;

import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.hardware.Camera;
import android.hardware.Camera.AutoFocusCallback;
import android.os.Handler;
import android.util.Log;
import android.view.SurfaceHolder;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class CameraPreview implements SurfaceHolder.Callback {
    private Context context;
    private final static String TAG = "CameraPreview";
    private Camera mCamera = null;
    private int mCameraId = 0;
    private Camera.Parameters mCameraParameters;

    public CameraPreview(Context context) {
        this.context = context;
    }

    /**
     * 开启camera并且预览
     *
     * @return
     */
    private void safeOpenCamera() {
        try {
            releaseCamera();
            mCamera = Camera.open(mCameraId);
        } catch (Exception e) {
            LogUtils.e("failed to open Camera");
            e.printStackTrace();
        }
    }

    /**
     * 预览相机
     */
    private void startPreview(Camera camera, SurfaceHolder holder) {
        try {
            setupCamera(camera);
            camera.setPreviewDisplay(holder);
            camera.setDisplayOrientation(90);
            camera.startPreview();
            camera.cancelAutoFocus();
            if (listener != null) {
                listener.cameraIsReady();
            }
            mCameraParameters = mCamera.getParameters();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private Handler mHandler = new Handler();

    private void setupCamera(Camera camera) {
        if (mCamera != null) {
            Camera.Parameters parameters = mCamera.getParameters();
            Camera.Size previewSize = CameraUtil.getInstance().getCloselyPreSize(800, 600, parameters.getSupportedPreviewSizes());
            parameters.setPreviewSize(previewSize.width, previewSize.height);
            //设置pictureSize
            Camera.Size pictureSize = CameraUtil.getInstance().getPictureSize(parameters.getSupportedPictureSizes());
            LogUtils.d("参数", pictureSize.width, pictureSize.height);
            parameters.setPictureSize(pictureSize.width, pictureSize.height);
            parameters.setPictureFormat(ImageFormat.JPEG);
            //设置对焦模式，低端机型可能不支持快速对焦，当然现在大部分机型支持，但是也得适配啊
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
            parameters.setJpegQuality(90); // 设置照片质量
            mCamera.setDisplayOrientation(90);
            mCamera.setParameters(parameters);
        }
    }


    /**
     * 释放相机资源
     */
    private void releaseCamera() {
        if (mCamera != null) {
            mCamera.stopPreview();
            mCamera.setPreviewCallback(null);
            mCamera.release();
            mCamera = null;
        }
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        safeOpenCamera();
        if (mCamera != null) {
            startPreview(mCamera, holder);
        }
    }

    @Override
    public void surfaceChanged(final SurfaceHolder holder, int format, int width, int height) {
        //实现自动对焦
        mCamera.autoFocus(new AutoFocusCallback() {
            @Override
            public void onAutoFocus(boolean success, Camera camera) {
                if (success) {
                    startPreview(camera, holder);
                }
            }
        });
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        LogUtils.e("aaa", "releaseCamera");
        releaseCamera();
    }

    //设置开启闪光灯(重新预览)
    public void setIsOpenFlashMode(String mIsOpenFlashMode) {
        Camera.Parameters mParameters = mCamera.getParameters();
        //设置闪光灯模式
        mParameters.setFlashMode(mIsOpenFlashMode);
        try {
            mCamera.setParameters(mParameters);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @des: 手动对焦
     * @params:
     * @return:
     */

    public void doTouchFocus(final Rect tfocusRect, FocusView focusView, Rect touchRect) {
        if (mCameraParameters == null) {
            return;
        }
        try {
            final List<Camera.Area> focusList = new ArrayList<>();
            Camera.Area focusArea = new Camera.Area(tfocusRect, 1000);
            focusList.add(focusArea);
            mCameraParameters.setFocusAreas(focusList);
            mCameraParameters.setMeteringAreas(focusList);
            mCamera.setParameters(mCameraParameters);
            this.touchRect = touchRect;
            this.focusView = focusView;
            mCamera.autoFocus(autoFocusCallbackByHand);
        } catch (Exception e) {
            e.printStackTrace();
            Log.i(TAG, "Unable to autofocus");
        }
    }

    private Rect touchRect;
    private FocusView focusView;
    private AutoFocusCallback autoFocusCallbackByHand = new AutoFocusCallback() {


        @Override
        public void onAutoFocus(boolean success, Camera camera) {
            if (success) {
                focusView.show(touchRect, 0xFF00FF00);
            } else {
                focusView.show(touchRect, 0x7300FF00);
            }
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    //一秒之后才能再次对焦
                    focusView.hide();
                }
            }, 500);

        }
    };

    public Camera getCamera() {
        return mCamera;
    }

    private OnCameraReadyListener listener;

    public void setOnCameraReadyListener(OnCameraReadyListener listener) {
        this.listener = listener;
    }

    public interface OnCameraReadyListener {
        void cameraIsReady();
    }


}