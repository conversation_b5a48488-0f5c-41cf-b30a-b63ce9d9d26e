package com.czur.scanpro.ui.home

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.RecyclerView
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.*
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.util.*
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.adapter.MainFileAdapter2
import com.czur.scanpro.common.Constants
import com.czur.scanpro.entity.model.BeanDoEntity
import com.czur.scanpro.entity.model.OssModel
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.PdfEntity
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.ConsumptionEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.FileEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.account.LoginActivity
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.base.LazyLoadBaseFragment
import com.czur.scanpro.ui.component.NoScrollViewPager
import com.czur.scanpro.ui.component.popup.*
import com.czur.scanpro.ui.file.EditTagActivity
import com.czur.scanpro.ui.file.FilePreviewActivity
import com.czur.scanpro.ui.file.PdfPreviewActivity
import com.czur.scanpro.ui.file.ShareActivity
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.GeneratePdfUtils
import com.czur.scanpro.utils.SaveUtils
import com.czur.scanpro.utils.share.FileUtil
import com.czur.scanpro.utils.share.ShareContentType
import com.czur.scanpro.utils.share.ShareUtils
import com.czur.scanpro.utils.validator.Validator
import com.google.gson.Gson
import com.truizlop.sectionedrecyclerview.SectionedRecyclerViewAdapter
import com.truizlop.sectionedrecyclerview.SectionedSpanSizeLookup
import com.umeng.analytics.MobclickAgent
import io.realm.Realm
import io.realm.Sort
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.collections.ArrayList


/**
 * Created by Yz on 2019/1/3.
 * Email：<EMAIL>
 */
class FileFragment : LazyLoadBaseFragment(), View.OnClickListener {

    private var realm: Realm? = null
    private var fileEntities: List<DocEntity> = ArrayList<DocEntity>()
    private var fileRecyclerview: RecyclerView? = null
    private var mainFileAdapter: MainFileAdapter2? = null
    private var multiBtn: ImageView? = null
    private var selectAllBtn: TextView? = null
    private var cancelBtn: TextView? = null
    private var fileTitle: TextView? = null
    private var viewpager: NoScrollViewPager? = null
    private var emptyRL: RelativeLayout? = null

    private var multiCheckBtn: ImageView? = null

    private var fileBottomLl: LinearLayout? = null
    private var fileShareRl: RelativeLayout? = null
    private var fileFileTagRl: RelativeLayout? = null
    private var fileShareImg: ImageView? = null
    private var fileShareTv: TextView? = null

    private var fileTagImg: ImageView? = null
    private var fileTagTv: TextView? = null

    private var multiRl: RelativeLayout? = null
    private var formatter: SimpleDateFormat? = null
    private var userPreferences: UserPreferences? = null
    private val pageIds = ArrayList<String>()
    private var isMultiImages = false
    private var isCheckedMap: LinkedHashMap<String, Boolean>? = null


    private var filePdfRl: RelativeLayout? = null
    private var fileMoreRl: RelativeLayout? = null

    private var filePdfImg: ImageView? = null
    private var filePdfTv: TextView? = null

    private var fileMoreImg: ImageView? = null
    private var fileMoreTv: TextView? = null
    private var fileBottomDialogPopup: FileBottomDialogPopup? = null
    private var pdfType = 0
    private var pdfQuality = 1
    private var pdfHorizontal = 0
    private var position = 0
    private var indexScanBtn: RelativeLayout? = null
    private var dialogEdt: EditText? = null
    private var savePdfPopup: SavePdfPopup? = null
    private var paths: ArrayList<String>? = arrayListOf<String>()
    private var sharePaths: ArrayList<String>? = arrayListOf<String>()


    private var progressTitle: TextView? = null
    private var commonPopup: PDFSettingPopup? = null
    private var shareBottomDialog: ShareBottomDialogPopup? = null
    private var isAnim: AtomicBoolean = AtomicBoolean(false)

    companion object {

        fun newInstance(): FileFragment {
            return FileFragment()
        }
    }

    override fun getLayoutRes(): Int = R.layout.fragment_file


    override fun initView(rootView: View?) {
        fileRecyclerview = rootView!!.findViewById(R.id.file_recyclerview)
        emptyRL = rootView.findViewById<RelativeLayout>(R.id.file_empty_layout)

    }

    private fun getFileEntities() {
        fileEntities = realm!!.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).sort("createTime", Sort.DESCENDING).equalTo("isTemp", 0.toInt()).equalTo("userID", getUserIdIsLogin()).findAll()
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {

        super.onActivityCreated(savedInstanceState)
        realm = Realm.getDefaultInstance()
        EventBus.getDefault().register(this)
        createBottomSheetDialog()
        userPreferences = UserPreferences.getInstance(context)
        formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        multiBtn = activity!!.findViewById(R.id.multiCheckBtn)
        selectAllBtn = activity!!.findViewById<com.czur.scanpro.ui.component.MediumBoldTextView>(R.id.fileSelectAllBtn)
        cancelBtn = activity!!.findViewById<com.czur.scanpro.ui.component.MediumBoldTextView>(R.id.fileCancelBtn)
        fileTitle = activity!!.findViewById<com.czur.scanpro.ui.component.MediumBoldTextView>(R.id.fileTitle)
        viewpager = activity!!.findViewById<NoScrollViewPager>(R.id.viewpager)
        multiCheckBtn = activity!!.findViewById<ImageView>(R.id.multiCheckBtn)

        fileBottomLl = activity!!.findViewById(R.id.fileBottomLl) as LinearLayout
        fileShareRl = activity!!.findViewById(R.id.fileShareRl) as RelativeLayout
        fileFileTagRl = activity!!.findViewById(R.id.fileTagRl) as RelativeLayout

        fileTagImg = activity!!.findViewById(R.id.fileTagImg) as ImageView
        fileTagTv = activity!!.findViewById(R.id.fileTagTv) as TextView
        multiRl = activity!!.findViewById(R.id.multiRl) as RelativeLayout

        fileShareImg = activity!!.findViewById(R.id.fileShareImg) as ImageView
        fileShareTv = activity!!.findViewById(R.id.fileShareTv) as TextView

        filePdfRl = activity!!.findViewById(R.id.filePdfRl) as RelativeLayout
        fileMoreRl = activity!!.findViewById(R.id.fileMoreRl) as RelativeLayout
        filePdfImg = activity!!.findViewById(R.id.filePdfImg) as ImageView
        filePdfTv = activity!!.findViewById(R.id.filePdfTv) as TextView
        fileMoreImg = activity!!.findViewById(R.id.fileMoreImg) as ImageView
        fileMoreTv = activity!!.findViewById(R.id.fileMoreTv) as TextView
        indexScanBtn = activity!!.findViewById(R.id.indexScanBtn) as RelativeLayout
        getFileEntities()
        initRecyclerView()
        isShowEmptyPrompt()

        realm!!.addChangeListener {
            getFileEntities()
            isShowEmptyPrompt()
            mainFileAdapter!!.refreshData(getlistData(fileEntities), isMultiSelect, isCheckedMap)

        }
        registerEvent()
    }

    /**
     *
     * 初始化列表
     * @param: []
     * @return: []
     */

    private fun initRecyclerView() {

        isCheckedMap = LinkedHashMap()
        mainFileAdapter = MainFileAdapter2(this.context, getlistData(fileEntities), false)
//        fileRecyclerview!!.adapter = mainFileAdapter
        fileRecyclerview!!.setHasFixedSize(true)
        mainFileAdapter!!.setOnItemCheckListener(onItemCheckListener)
        mainFileAdapter!!.setOnItemClickListener(onItemClickListener)

        var layoutManager = GridLayoutManager(
            <EMAIL>,
            3
        )
//        fileRecyclerview!!.addItemDecoration(GridDividerItemDecoration(<EMAIL>, SizeUtils.dp2px(16f), true))
        //设置header占据空间
        val lookup = SectionedSpanSizeLookup(mainFileAdapter, layoutManager)
        layoutManager.spanSizeLookup = lookup
        fileRecyclerview!!.layoutManager = layoutManager

        fileRecyclerview!!.setItemViewCacheSize(100)
        fileRecyclerview!!.adapter = mainFileAdapter


    }

    var lastTime = "0"
    var listHeader = ArrayList<BeanDoEntity>()

    //    var headerDocEntity = HeaderDocEntity()
    fun getlistData(datas: List<DocEntity>): ArrayList<BeanDoEntity> {
        listHeader.clear()
        var headerDocEntity = BeanDoEntity()
        for (index in 0..datas!!.size - 1) {

            if (index == 0 || datas!!.get(index).createTime!!.substring(0, 7).replace("-", "") == headerDocEntity.header) {
                if (headerDocEntity.header == null) {
                    headerDocEntity.header = datas!!.get(index).createTime!!.substring(0, 7).replace("-", "")
                }

                headerDocEntity.listDoc?.add(datas!!.get(index))
                if (index == datas.size - 1) {
                    listHeader.add(headerDocEntity)
                }
            } else {
                if (headerDocEntity.header != null) {
                    listHeader.add(headerDocEntity)
                }

                headerDocEntity = BeanDoEntity()
                headerDocEntity.header = datas!!.get(index).createTime!!.substring(0, 7).replace("-", "")
                headerDocEntity.listDoc?.add(datas!!.get(index))
                if (index == datas.size - 1) {
                    listHeader.add(headerDocEntity)
                }
            }

        }
        return listHeader
    }

    private fun registerEvent() {
        fileShareRl!!.setOnClickListener(this)
        fileFileTagRl!!.setOnClickListener(this)
        filePdfRl!!.setOnClickListener(this)
        fileMoreRl!!.setOnClickListener(this)


        cancelBtn!!.setOnClickListener(this)
        selectAllBtn!!.setOnClickListener(this)
        multiBtn!!.setOnClickListener(this)

    }

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private fun isShowEmptyPrompt() {
        if (fileEntities.isEmpty()) {
            fileRecyclerview!!.visibility = View.GONE
            emptyRL!!.visibility = View.VISIBLE
        } else {
            fileRecyclerview!!.visibility = View.VISIBLE
            emptyRL!!.visibility = View.GONE
        }
    }

    /**
     * @des: item选择监听
     * @params:
     * @return:
     */
    private val onItemCheckListener = object : MainFileAdapter2.OnItemCheckListener {
        override fun onItemCheck(section: Int, position: Int, docEntity: DocEntity, isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int) {
            <EMAIL> = isCheckedMap
            LogUtils.i(Gson().toJson(isCheckedMap))
            //如果选中一个 文案变为已选中1个
            if (isCheckedMap.size == 1) {
                fileTitle!!.setText(R.string.select_one)
                isMultiImages = false
                showAll()
            } else if (isCheckedMap.size > 1) {
                fileTitle!!.text = String.format(getString(R.string.select_num), isCheckedMap.size.toString() + "")
                isMultiImages = true
                showAll()

            } else {
                isMultiImages = false
                darkAll()
                if (isMultiSelect) {
                    fileTitle!!.text = String.format(getString(R.string.select_num), isCheckedMap.size.toString() + "")
                }

            }
            //如果选择不是全部Item  text变为取消全选
            checkSize(isCheckedMap, totalSize)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        val realm = Realm.getDefaultInstance()
        when (event.eventType) {
            EventType.LOG_IN,
            EventType.THIRD_PARTY_LOGIN_IN,
            EventType.REGISTER_SUCCESS,
            EventType.THIRD_PARTY_REGISTER_SUCCESS,
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
            EventType.CHANGE_PASSWORD,
            EventType.SYNC_IS_STOP,
            EventType.LOG_OUT, EventType.SYNC_IS_FINISH -> {
                getFileEntities()
                isShowEmptyPrompt()
                mainFileAdapter!!.refreshData(getlistData(fileEntities))
            }
            EventType.ADD_FILES -> {
                isAnim.set(true)
                val fileEvent = event as FileEvent
                getFileEntities()
                isShowEmptyPrompt()
                if (fileEntities.isNotEmpty()) {
                    mainFileAdapter!!.refreshData(getlistData(fileEntities), true, fileEvent.count)
                    isAnim.set(false)
                }
            }
            EventType.ROTATE, EventType.COLOR, EventType.CUT, EventType.ADJUST -> {
                mainFileAdapter!!.refreshData(getlistData(fileEntities))

            }
            EventType.SHARE, EventType.ADD_TAG, EventType.ADD_TAGS -> {
                cancelEvent()
            }
            else -> {
            }
        }
    }

    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */

    private val onItemClickListener = object : MainFileAdapter2.OnItemClickListener {

        override fun onDocEntityClick(docEntity: DocEntity, section: Int, position: Int, tvIndex: TextView) {
            if (isMultiSelect) {
                tvIndex.isEnabled = !tvIndex.isEnabled
            } else {
                var position2 = 0
                for (i in fileEntities.indices) {
                    if (fileEntities.get(i).fileID == docEntity.fileID) {
                        position2 = i
                    }
                }
                val intent = Intent(activity, FilePreviewActivity::class.java)
                intent.putExtra("type", 0)
                intent.putExtra("isItemTag", if (Validator.isNotEmpty(docEntity.tagName)) 1 else 0)
                intent.putExtra("fileType", docEntity.fileType)
                intent.putExtra("categoryName", docEntity.categoryName)
                intent.putExtra("tagId", docEntity.tagId)
                intent.putExtra("tagName", docEntity.tagName)
                intent.putExtra("categoryID", docEntity.categoryID)
                intent.putExtra("index", position2)
                intent.putExtra("currentPageId", docEntity.fileID)
                startActivity(intent)
            }
        }
    }
    private var isMultiSelect = false
    private var isSelectAll = false

    /**
     * @des: 判断检查状态
     * @params:
     * @return:
     */

    private fun checkSize(isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int) {
        if (isCheckedMap.size < totalSize) {
            selectAllBtn!!.setText(R.string.select_all)
            isSelectAll = false
        } else {
            selectAllBtn!!.setText(R.string.not_select_all)
            isSelectAll = true
        }
    }

    private fun resetStatus() {
        pageIds.clear()
        fileTitle!!.text = ""
        isMultiSelect = false
        isSelectAll = false
        isCheckedMap!!.clear()
        isCheckedMap = LinkedHashMap()
        mainFileAdapter!!.refreshData(false)
        hideSelectTopBar()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == SHARE_SUCCESS_CODE) {
        }
    }

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private fun multiSelect() {
        viewpager!!.setScroll(false)
        isMultiSelect = !isMultiSelect
        mainFileAdapter!!.refreshData(isMultiSelect)
        if (isMultiSelect) {
            showSelectTopBar()
        } else {
            hideSelectTopBar()
        }
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private fun selectAll() {
        var list = getlistData(fileEntities)
        if (!isSelectAll) {
            for (i in list.indices) {
                for (j in (list.get(i).listDoc.indices)) {
                    if (!isCheckedMap!!.containsKey(list.get(i).listDoc[j].fileID)) {
                        isCheckedMap!![list.get(i).listDoc[j].fileID!!] = true
                    }
                }

            }
            selectAllBtn!!.setText(R.string.not_select_all)
            isSelectAll = true
        } else {
            isCheckedMap!!.clear()
            isCheckedMap = LinkedHashMap()
            selectAllBtn!!.setText(R.string.select_all)
            isSelectAll = false
        }
        if (isCheckedMap?.size!! > 0) {
            showAll()
        } else {
            darkAll()
        }
        fileTitle!!.text = String.format(getString(R.string.select_num), isCheckedMap!!.size.toString() + "")
        LogUtils.iTag("select all", Gson().toJson(isCheckedMap))
        mainFileAdapter!!.refreshData(list, true, isCheckedMap)
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private fun cancelEvent() {
        resetStatus()
        fileTitle!!.text = ""
        mainFileAdapter!!.refreshData(getlistData(fileEntities), false, isCheckedMap)
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private fun showSelectTopBar() {
        darkAll()
        indexScanBtn!!.isEnabled = false
        indexScanBtn!!.isClickable = false
        multiRl!!.visibility = View.VISIBLE
        fileBottomLl!!.visibility = View.VISIBLE
        cancelBtn!!.visibility = View.VISIBLE
        selectAllBtn!!.visibility = View.VISIBLE
        cancelBtn!!.setText(R.string.quit)
        selectAllBtn!!.setText(R.string.select_all)
        fileTitle!!.text = String.format(getString(R.string.select_num), isCheckedMap!!.size.toString() + "")
    }

    public val SHARE_SUCCESS_CODE = 666
    private fun share() {
        val copyList = ArrayList<String>()
        copyList.addAll(isCheckedMap!!.keys)
        val albumDirPath = userPreferences!!.sdPath
        if (copyList.size > 0) {
            if (!isMultiImages) {
                val page = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", copyList[0])
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                val picUrl = page!!.processImagePath
                val sdPicPath = albumDirPath + UUID.randomUUID() + Constants.JPG
                ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                    override fun doInBackground(): Void? {
                        FileUtils.copy(picUrl, sdPicPath) { _, _ -> true }
                        return null
                    }

                    override fun onSuccess(result: Void?) {
                        WeakHandler().post(Runnable {
                            context!!.sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://$sdPicPath")))
                            ShareUtils.Builder(activity)
                                    .setOnActivityResult(SHARE_SUCCESS_CODE)
                                    // 指定分享的文件类型
                                    .setContentType(ShareContentType.IMAGE)

                                    // 设置分享选择器的标题
                                    .setMulti(isMultiImages)
                                    .setTitle(getString(R.string.share_to))
                                    .setShareFileUri(UriUtils.file2Uri(File(sdPicPath ?: ""))
//                                        FileUtil.getFileUri(activity, ShareContentType.FILE, File(sdPicPath))
                                    )
                                    .build()
                                    .shareBySystem()


                        })
                    }
                })
            } else {
                val files = ArrayList<File>()
                val sdPicPaths = ArrayList<String>()
                ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                    override fun doInBackground(): Void? {
                        val realm = Realm.getDefaultInstance()
                        if (FileUtils.createOrExistsDir(albumDirPath)) {
                            for (id in copyList) {
                                LogUtils.i("copyListId", id)
                                val page = realm.where(DocEntity::class.java)
                                        .equalTo("fileID", id)
                                        .equalTo("isDelete", 0.toInt())
                                        .findFirst()
                                val picUrl = page!!.processImagePath
                                val sdPicPath = albumDirPath + UUID.randomUUID() + Constants.JPG
                                sdPicPaths.add(sdPicPath)
                                FileUtils.copy(picUrl, sdPicPath) { _, _ -> true }
                                activity!!.sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://$sdPicPath")))
                            }
                        }
                        return null
                    }

                    override fun onSuccess(result: Void?) {
                        WeakHandler().post(Runnable {
                            val fileUris = arrayListOf<Uri>()
                            for (sdPicPath in sdPicPaths) {
                                fileUris.add(UriUtils.file2Uri(File(sdPicPath)))
                                files.add(FileUtils.getFileByPath(sdPicPath))
                            }
                            EtUtils.originalShareImage(<EMAIL>(), fileUris)
                        })
                    }
                })
            }
        }

    }

    private fun showDeleteDialog() {
        val builder = ScanProCommonPopup.Builder(<EMAIL>, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.confirm_delete))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            deleteFiles()
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    /**
     * @des: 删除books
     * @params:
     * @return:
     */

    private fun deleteFiles() {

        val needDeletePaths = ArrayList<String>()
        for (fileID in isCheckedMap!!.keys) {
            //得到待删除pdf paths
            val deleteFileEntity = realm!!.where(DocEntity::class.java)
                    .equalTo("fileID", fileID)
                    .equalTo("isDelete", 0.toInt()).findFirst()
            needDeletePaths.add(deleteFileEntity!!.processImagePath!!)
            needDeletePaths.add(deleteFileEntity.processSmallImagePath!!)
            needDeletePaths.add(deleteFileEntity.baseImagePath!!)
            needDeletePaths.add(deleteFileEntity.baseSmallImagePath!!)
            if (deleteFileEntity.originalImagePath != null) {
                needDeletePaths.add(deleteFileEntity.originalImagePath!!)
            }
        }
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                //删除sd卡上book page
                for (needDeletePath in needDeletePaths) {
                    FileUtils.delete(needDeletePath)
                }
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })

        realm!!.executeTransaction(Realm.Transaction {
            for (finalId in isCheckedMap!!.keys) {
                //先查找后得到PageEntity对象
                val deletePageEntity = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", finalId)
                        .equalTo("isDelete", 0.toInt()).findFirst()

                deletePageEntity!!.isDelete = 1
                deletePageEntity.isDirty = 1
                val curDate = formatter!!.format(Date(System.currentTimeMillis()))
                deletePageEntity.updateTime = curDate
            }
            resetStatus()
            startAutoSync()
        })
    }

    private fun saveToAlbum() {
        showProgressDialog()
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val realm = Realm.getDefaultInstance()
                realm.use { realm ->
                    val albumDirPath = userPreferences!!.sdPath
                    val copyList = ArrayList<String>()
                    copyList.addAll(isCheckedMap!!.keys)
                    if (FileUtils.createOrExistsDir(albumDirPath)) {
                        for (id in copyList) {
                            LogUtils.i("copyListId", id)
                            val page = realm.where(DocEntity::class.java)
                                    .equalTo("fileID", id)
                                    .equalTo("isDelete", 0.toInt())
                                    .findFirst()
                            val picUrl = page!!.processImagePath
                            val sdPicPath = albumDirPath + UUID.randomUUID() + Constants.JPG

                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
                                context?.getApplicationInfo()?.targetSdkVersion!! >= Build.VERSION_CODES.Q
                            ) {
                                SaveUtils.saveImgToAlbum(context,picUrl)
                            }else{
                                FileUtils.copy(picUrl, sdPicPath) { _, _ -> true }
                            }

                            context!!.sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://$sdPicPath")))
                            requireActivity().runOnUiThread(Runnable {
                                noticeAlbumUpdate(sdPicPath)
                            })
                        }
                    }
                }
                return null
            }

            override fun onSuccess(result: Void?) {
                hideProgressDialog(true)
                val builder = BlackRightPopup.Builder(context)
                builder.setTitle(getString(R.string.saved))
                val blackRightPopup = builder.create()
                blackRightPopup.show()
                resetStatus()
                WeakHandler().postDelayed(Runnable { blackRightPopup.cancel() }, 900)
            }
        }, 10)


    }

    private fun noticeAlbumUpdate(sdPicPath: String) {
        //通知系统相册更新
        val sdPicFile = FileUtils.getFileByPath(sdPicPath)
        val contentUri = Uri.fromFile(sdPicFile)
        MediaScannerConnection.scanFile(context,
            arrayOf(sdPicFile.absolutePath), arrayOf("image/jpeg"),
            MediaScannerConnection.OnScanCompletedListener { path, uri -> LogUtils.i("file $path", " scanned seccessfully: $uri") })
    }

    private fun editTag() {
        for (finalId in isCheckedMap!!.keys) {
            pageIds.add(finalId)
        }
        val intent = Intent(activity, EditTagActivity::class.java)
        intent.putExtra("isPreview", false)
        intent.putStringArrayListExtra("pageIds", pageIds)
        ActivityUtils.startActivity(intent)
    }


    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private fun hideSelectTopBar() {
        indexScanBtn!!.isEnabled = true
        indexScanBtn!!.isClickable = true
        viewpager!!.setScroll(true)

        multiRl!!.visibility = View.GONE

        fileBottomLl!!.visibility = View.GONE
        cancelBtn!!.visibility = View.GONE
        selectAllBtn!!.visibility = View.GONE
    }

    private var clickTime = 0L
    private fun canClick(): Boolean {
        if (System.currentTimeMillis() - clickTime > 600) {
            clickTime = System.currentTimeMillis()
            return true
        }
        return false
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.multiCheckBtn -> multiSelect()
            R.id.fileSelectAllBtn -> selectAll()
            R.id.fileCancelBtn -> cancelEvent()
            R.id.fileShareRl -> showShareDialog()
            R.id.fileTagRl -> editTag()
            R.id.filePdfRl -> {
                if (checkLogin()) {
                    Thread(Runnable {
                        requestUserInfoSync()
                        activity!!.runOnUiThread {
                            if (userPreferences!!.isVip || userPreferences!!.remainPdf > 0) {
                                if (isCheckedMap!!.size < 101) {
                                    if (canClick()) {
                                        createPDF()

                                    }
                                } else {
                                    showMessage(R.string.out_of_pdf_size)
                                }

                            } else {
                                showEmptyCountDialog(activity!!.application, EmptyCountPopup.EmptyType.PDF)
                            }
                        }
                    }).start()
                }
            }
            R.id.fileMoreRl -> showMoreDialog()
            else -> {
            }
        }
    }

    private fun showLoginDialog() {
        val builder = ScanProCommonPopup.Builder(<EMAIL>, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.please_login))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            val intent = Intent(activity, LoginActivity::class.java)
            intent.putExtra("type", 2)
            ActivityUtils.startActivity(intent)
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    private fun checkLogin(): Boolean {
        if (!userPreferences!!.isUserLogin) {
            showLoginDialog()
            return false
        }
        return true
    }

    private fun createPDF() {
        val builder = PDFSettingPopup.Builder(activity, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setOnPdfClickListener { type, quality, isHorizontal, dialog ->
            pdfType = type
            pdfQuality = quality
            pdfHorizontal = isHorizontal
            if (Validator.isNotEmpty(dialogEdt!!.text.toString())) {
                //不能含有表情
                if (EtUtils.containsEmoji(dialogEdt!!.text.toString())) {
                    val builder = ScanProCommonPopup.Builder(<EMAIL>, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
                    builder.setTitle(resources.getString(R.string.prompt))
                    builder.setMessage(resources.getString(R.string.nickname_toast_symbol))
                    builder.setOnPositiveListener { dialog1, _ -> dialog1.dismiss() }
                    val commonPopup = builder.create()
                    commonPopup.show()
                } else {
                    //文件名字重复添加括号
                    var i = 0
                    val pdfName = dialogEdt!!.text.toString()
                    var finalName = pdfName
                    var sameNamePdf: PdfEntity? = realm!!.where(PdfEntity::class.java)
                            .equalTo("pdfName", pdfName)
                            .findFirst()
                    while (Validator.isNotEmpty(sameNamePdf)) {
                        i++
                        finalName = pdfName + String.format(getString(R.string.tag_counts), i.toString() + "")
                        sameNamePdf = realm!!.where(PdfEntity::class.java)
                                .equalTo("pdfName", finalName)
                                .findFirst()
                    }
                    generatePdf(finalName)
                    resetStatus()
                    dialog.dismiss()

                }

            } else {
                showMessage(R.string.pdf_name_length_toast)
            }
        }

        builder.setOnNegativeListener { dialog, which -> dialog.dismiss() }
        commonPopup = builder.create()
        dialogEdt = commonPopup!!.window?.findViewById(R.id.create_pdf_edt) as EditText
        commonPopup!!.show()
    }

    private fun showMoreDialog() {
        fileBottomDialogPopup!!.show()
    }

    private fun generatePdf(pdfName: String) {
        commonPopup!!.dismiss()
        paths = arrayListOf<String>()
        realm!!.executeTransaction(Realm.Transaction {
            for (id in isCheckedMap!!.keys) {
                //先查找后得到PageEntity对象
                val page = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", id)
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                val picUrl = page!!.processImagePath
                paths!!.add(picUrl!!)
            }
        })
        mobClickPdfEvent(context, BuildConfig.PHASE.generatePdfCount)

        val path = activity?.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath.toString() + Constants.SD_PATH + Constants.PDF_PATH
        LogUtils.i("pdf path: $path", "paths$paths")
        val generatePdfUtils = GeneratePdfUtils(activity, path, paths, pdfName)
        generatePdfUtils.setOnGeneratePdfListener(onGeneratePdfListener)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                generatePdfUtils.createPdf()
                return null
            }

            override fun onSuccess(result: Void?) {
                consumption()
            }
        })
    }

    fun mobClickPdfEvent(activity: Context?, eventId: String) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[Constants.PDF_EVENT] = Constants.PDF_EVENT
        MobclickAgent.onEvent(activity, eventId, map)
    }


    private fun consumption() {
        HttpManager.getInstance().request().consumption(userPreferences!!.userId, "pdf", String::class.java, object : MiaoHttpManager.CallbackNetwork<String> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                LogUtils.i(Gson().toJson(entity.body))
                val activity = activity as BaseActivity
                activity.commonRequestUserInfoNow()
                EventBus.getDefault().post(ConsumptionEvent(EventType.PDF_COUNT_REDUCE))

            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {

            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)

            }
        })
    }

    private val onGeneratePdfListener = object : GeneratePdfUtils.OnGeneratePdfListener {
        override fun onStart() {
            LogUtils.i("onStart")
            val builder = SavePdfPopup.Builder(activity)
            builder.setTitle("")
            savePdfPopup = builder.create()
            progressTitle = savePdfPopup!!.window?.findViewById(R.id.title) as TextView
            if (!savePdfPopup!!.isShowing) {
                savePdfPopup!!.show()
            }

        }

        override fun onGenerate(progress: Int, time: Long) {
            LogUtils.i(progress, "耗时" + time + "毫秒")
            progressTitle!!.text = String.format(getString(R.string.have_been_generated), progress.toString(), paths!!.size.toString())
        }

        override fun onFinish(totalTime: Long, pdfPath: String, pdfName: String) {
            savePdfPopup!!.dismiss()
            realm!!.executeTransaction(Realm.Transaction { realm ->
                val pdfEntity = realm.createObject(PdfEntity::class.java, pdfName)
                pdfEntity.pdfPath = pdfPath

                val curDate = formatter!!.format(Date(System.currentTimeMillis()))
                pdfEntity.createTime = curDate
            })
            showMessage(R.string.saved_pdf)
            val intent = Intent(activity, PdfPreviewActivity::class.java)
            intent.putExtra("pdfPath", pdfPath)
            intent.putExtra("pdfName", pdfName)
            ActivityUtils.startActivity(intent)
//            val builder = BlackRightPopup.Builder(activity)
//            builder.setTitle(getString(R.string.saved_pdf))
//            val blackRightPopup = builder.create()
//            blackRightPopup.show()
//           Handler().postDelayed(Runnable {
//                blackRightPopup.dismiss()
//
//            }, 700)


        }
    }

    private fun createBottomSheetDialog() {
        val builder = FileBottomDialogPopup.Builder(activity, false, onBottomSheetClickListener)
        fileBottomDialogPopup = builder.create()

        val builder1 = ShareBottomDialogPopup.Builder(activity, onBottomSheetClickListener1)
        shareBottomDialog = builder1.create()
    }

    private fun showShareDialog() {
        if (userPreferences!!.isVip) {
            shareBottomDialog!!.show()
        } else {
            share()
        }

    }


    private fun getOssClient() {
        HttpManager.getInstance().request().getOssInfo(userPreferences!!.imei.substring(0, 18), OssModel::class.java, object : MiaoHttpManager.CallbackNetwork<OssModel> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {
                showProgressDialog(cancelable = false, touchable = false, tag = "", isDark = false)
            }

            override fun onResponse(entity: MiaoHttpEntity<OssModel>) {

                val credentialProvider = OSSStsTokenCredentialProvider(entity.body.accessKeyId,
                        entity.body.accessKeySecret,
                        entity.body.securityToken)
                val conf = ClientConfiguration()
                // 连接超时，默认15秒
                conf.connectionTimeout = 15 * 1000
                // socket超时，默认15秒
                conf.socketTimeout = 15 * 1000
                // 最大并发请求数，默认5个
                conf.maxConcurrentRequest = 5
                // 失败后最大重试次数，默认2次
                conf.maxErrorRetry = 2
                val ossClient = OSSClient(activity, userPreferences!!.endpoint, credentialProvider, conf)
                Thread(Runnable {
                    if (doesOssHasFile(ossClient)) {
                        activity!!.runOnUiThread {
                            hideProgressDialog()
                            var intent = Intent(activity, ShareActivity::class.java)
                            intent.putExtra("sharePaths", sharePaths)
                            ActivityUtils.startActivity(intent)
                        }

                    } else {
                        cancelEvent()
                        hideProgressDialog()
                        activity!!.runOnUiThread {
                            showMessage(R.string.share_defeat)
                        }
                    }
                }).start()


            }

            override fun onFailure(entity: MiaoHttpEntity<OssModel>) {
                hideProgressDialog()
                showMessage(R.string.request_failed_alert)

            }

            override fun onError(e: Exception) {
                hideProgressDialog()
                showMessage(R.string.request_failed_alert)

            }
        });

    }

    private fun doesOssHasFile(ossClient: OSSClient): Boolean {
        val realm = Realm.getDefaultInstance()
        sharePaths = arrayListOf<String>()
        var isSuccess = true
        realm!!.executeTransaction(Realm.Transaction {
            for (id in isCheckedMap!!.keys) {
                //先查找后得到PageEntity对象
                val page = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", id)
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                val path = page!!.fileID + "_" + page.uuid + Constants.BASE_JPG
                val ossPath = userPreferences!!.userId + File.separator + path
                val sharePath = "share/$ossPath"
                sharePaths!!.add(sharePath)
                try {
                    if (ossClient.doesObjectExist(Constants.BUCKET, sharePath)) {
                        LogUtils.d("doesObjectExist", "object exist.", sharePath);
                    } else {
                        LogUtils.d("doesObjectExist", "object does not exist.", sharePath);

                        val uploadFiles = uploadFiles(page.baseImagePath!!, sharePath, ossClient)
                        if (!uploadFiles) isSuccess = false
                    }

                } catch (e: ClientException) {
                    isSuccess = false
                    // 本地异常如网络异常等
                    LogUtils.e(e)
                } catch (e: ServiceException) {
                    isSuccess = false
                    // 服务异常
                    LogUtils.e("ErrorCode", e.errorCode);
                    LogUtils.e("RequestId", e.requestId);
                    LogUtils.e("HostId", e.hostId);
                    LogUtils.e("RawMessage", e.rawMessage);
                }
            }
        })
        return isSuccess

    }

    private fun uploadFiles(sourcePath: String, sharePath: String, ossClient: OSSClient): Boolean {
        var isUploadSuccess = false
        LogUtils.i("upload", sourcePath, sharePath)
        // 构造上传请求
        val put = PutObjectRequest(Constants.BUCKET, sharePath, sourcePath)

        try {
            val putResult = ossClient.putObject(put)
            isUploadSuccess = true
            LogUtils.i("PutObject: UploadSuccess", "ETag :" + putResult.eTag, "RequestId: " + putResult.getRequestId())
        } catch (e: ClientException) {
            isUploadSuccess = false
            // 本地异常如网络异常等
            e.printStackTrace()
        } catch (e: ServiceException) {
            isUploadSuccess = false
            // 服务异常
            LogUtils.e("RequestId", e.requestId)
            LogUtils.e("ErrorCode", e.errorCode)
            LogUtils.e("HostId", e.hostId)
            LogUtils.e("RawMessage", e.rawMessage)
        }

        return isUploadSuccess
    }

    /**
     * @des: 切换模式dialog监听
     * @params:
     * @return:
     */
    private val onBottomSheetClickListener = FileBottomDialogPopup.Builder.OnBottomSheetClickListener { viewId ->
        when (viewId) {
            R.id.file_bottom_sheet_delete_btn -> {
                showDeleteDialog()
                fileBottomDialogPopup!!.dismiss()
            }
            R.id.file_bottom_sheet_save_btn -> {
                saveToAlbum()
                fileBottomDialogPopup!!.dismiss()
            }
            R.id.file_bottom_sheet_cancel_btn -> fileBottomDialogPopup!!.dismiss()
            else -> {
            }
        }
    }

    private val onBottomSheetClickListener1 = ShareBottomDialogPopup.Builder.OnBottomSheetClickListener { viewId ->
        when (viewId) {
            R.id.share_system_btn -> {
                share()
                shareBottomDialog!!.dismiss()
            }
            R.id.share_vip_btn -> {
                getOssClient()
                shareBottomDialog!!.dismiss()
            }

            R.id.share_cancel_btn -> shareBottomDialog!!.dismiss()
            else -> {
            }
        }
    }

    private fun showAll() {
        showShare()
        showPDF()
        showMore()
        showTag()
    }

    private fun darkAll() {
        darkMore()
        darkPDF()
        darkShare()
        darkTag()
    }

    private fun showPDF() {
        filePdfRl!!.isClickable = true
        filePdfRl!!.isEnabled = true

        filePdfImg!!.isSelected = true
        filePdfTv!!.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkPDF() {
        filePdfRl!!.isClickable = false
        filePdfRl!!.isEnabled = false

        filePdfImg!!.isSelected = false
        filePdfTv!!.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun showMore() {
        fileMoreRl!!.isClickable = true
        fileMoreRl!!.isEnabled = true

        fileMoreImg!!.isSelected = true
        fileMoreTv!!.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkMore() {
        fileMoreRl!!.isClickable = false
        fileMoreRl!!.isEnabled = false


        fileMoreImg!!.isSelected = false
        fileMoreTv!!.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun showShare() {
        fileShareRl!!.isClickable = true
        fileShareRl!!.isEnabled = true


        fileShareImg!!.isSelected = true
        fileShareTv!!.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkShare() {
        fileShareRl!!.isClickable = false
        fileShareRl!!.isEnabled = false

        fileShareImg!!.isSelected = false
        fileShareTv!!.setTextColor(resources.getColor(R.color.dark_text))
    }


    private fun showTag() {
        fileFileTagRl!!.isClickable = true
        fileFileTagRl!!.isEnabled = true


        fileTagImg!!.isSelected = true
        fileTagTv!!.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkTag() {
        fileFileTagRl!!.isClickable = false
        fileFileTagRl!!.isEnabled = false

        fileTagImg!!.isSelected = false
        fileTagTv!!.setTextColor(resources.getColor(R.color.dark_text))
    }


    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    /*class SectionedSpanSizeLookup(adapter: SectionedRecyclerViewAdapter<*, *, *>?, layoutManager: GridLayoutManager?) : SpanSizeLookup() {
        protected var adapter: SectionedRecyclerViewAdapter<*, *, *>? = null
        protected var layoutManager: GridLayoutManager? = null
        override fun getSpanSize(position: Int): Int {
            Log.d("getSpanSize","======1233=="+position)
            Log.d("getSpanSize","======adapter!!.isSectionHeaderPosition(position)=="+adapter!!.isSectionHeaderPosition(position))
            //header和footer占据的是全屏
            return if (adapter!!.isSectionHeaderPosition(position) ) {
                2
            } else {
                1 //其他默认1
            }
        }

        init {
            this.adapter = adapter
            this.layoutManager = layoutManager
        }
    }*/

}







