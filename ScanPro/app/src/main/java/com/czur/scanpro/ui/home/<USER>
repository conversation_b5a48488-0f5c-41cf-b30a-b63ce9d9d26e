package com.czur.scanpro.ui.home

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SizeUtils
import com.czur.scanpro.R
import com.czur.scanpro.adapter.MainTagAdapter
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.TagEntity
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.ui.base.IndexActivity
import com.czur.scanpro.ui.base.LazyLoadBaseFragment
import com.czur.scanpro.ui.component.GridDividerItemDecoration
import com.czur.scanpro.ui.component.popup.AddTagPopup
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import io.realm.Realm
import io.realm.RealmChangeListener
import io.realm.Sort
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by Yz on 2019/1/3.
 * Email：<EMAIL>
 */
class TagFragment : LazyLoadBaseFragment(), View.OnClickListener {

    private var realm: Realm? = null
    private var dialogEdt: EditText? = null
    private var tagEntities: List<TagEntity> = ArrayList<TagEntity>()
    private var tagRecyclerview: RecyclerView? = null
    private var maintagAdapter: MainTagAdapter? = null
    private var tagEditBtn: TextView? = null
    private var isSelected: Boolean = false
    private var addBtn: ImageView? = null

    companion object {

        fun newInstance(): TagFragment {
            return TagFragment()
        }
    }

    override fun getLayoutRes(): Int = R.layout.fragment_tag


    override fun initView(rootView: View?) {

        tagEditBtn = rootView!!.findViewById<com.czur.scanpro.ui.component.MediumBoldTextView>(R.id.tag_edit_btn)
        tagRecyclerview = rootView.findViewById<RecyclerView>(R.id.tag_recyclerview)
        realm = Realm.getDefaultInstance()
        tagEntities = realm!!.where(TagEntity::class.java).equalTo("isDelete", 0.toInt()).distinct("tagName").sort("createTime", Sort.ASCENDING).equalTo("userID", getUserIdIsLogin()).findAll()

        initRecyclerView()
    }

    /**
     *
     * 初始化列表
     * @param: []
     * @return: []
     */

    private fun initRecyclerView() {
        maintagAdapter = MainTagAdapter(<EMAIL>, tagEntities, false).apply {
            setOnItemClickListener(clickListener)
            setOnDeleteClickListener(deleteListener)
        }
        tagRecyclerview!!.run {
            adapter = maintagAdapter
            setHasFixedSize(true)
            layoutManager =
                GridLayoutManager(activity, 2)
        }
    }


    private fun registerEvent() {
        tagEditBtn!!.setOnClickListener(this)
        realm!!.addChangeListener(RealmChangeListener { maintagAdapter!!.refreshData(isSelected) })
        val indexActivity = activity as IndexActivity
        indexActivity.setOnIndexFragmentChangeListener(object : IndexActivity.OnIndexFragmentChangeListener {
            override fun onChange(position: Int) {
                if (position == 2) {
                    tagEditBtn!!.text = resources.getString(R.string.edit)
                    maintagAdapter!!.refreshData(false)
                } else {
                    isSelected = false
                }
            }
        })

    }

    /**
     * @des:显示新建标签弹窗
     * @params:[]
     * @return:[]
     */
    private fun showAddTagDialog(isAdd: Boolean, tagEntity: TagEntity?) {
        val builder = AddTagPopup.Builder(activity, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        if (isAdd) {
            builder.setTitle(getString(R.string.add_tag))
        } else {
            builder.setTitle(getString(R.string.rename_tag))
        }
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            val newTagName = dialogEdt!!.text.toString()
            realm!!.executeTransaction(Realm.Transaction {
                if (!EtUtils.containsEmoji(newTagName)) {
                    if (Validator.isNotEmpty(newTagName)) {
                        val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                        val curDate = formatter.format(Date(System.currentTimeMillis()))

                        if (realm!!.where(TagEntity::class.java).equalTo("tagName", newTagName).equalTo("isDelete", 0.toInt()).equalTo("userID", getUserIdIsLogin()).findFirst() == null) {
                            if (isAdd) {
                                TagEntity().apply {
                                    isSelf = 1
                                    userID = getUserIdIsLogin()
                                    tagId = UUID.randomUUID().toString()
                                    tagName = newTagName
                                    isDirty = 1
                                    createTime = curDate
                                    updateTime = curDate
                                    realm!!.copyToRealmOrUpdate(this)

                                }

                            } else {
                                val docEntities = realm!!.where(DocEntity::class.java).equalTo("tagName", tagEntity!!.tagName).findAll()
                                for (docEntity in docEntities) {
                                    docEntity.tagName = newTagName
                                    docEntity.updateTime = curDate
                                    docEntity.isDirty = 2
                                }
                                //重命名tag
                                for (tagEntity1 in realm!!.where(TagEntity::class.java).equalTo("tagName", tagEntity.tagName).equalTo("isDelete", 0.toInt()).findAll()) {
                                    tagEntity1!!.isDirty = 1
                                    tagEntity1.updateTime = curDate
                                    tagEntity1.tagName = newTagName
                                }


                                maintagAdapter!!.refreshData(true)
                            }
                            startAutoSync()
                            dialog.dismiss()
                        } else {
                            showMessage(R.string.tag_is_exist)
                        }

                    } else {
                        showMessage(R.string.tag_should_not_be_empty)
                    }
                } else {
                    showMessage(R.string.nickname_toast_symbol)
                }
            })

        })
        val tagPopup = builder.create()
        dialogEdt = tagPopup.window?.findViewById(R.id.edt) as EditText
        tagPopup.show()
    }

    override fun onFragmentPause() {

        super.onFragmentPause()
        tagEditBtn!!.text = resources.getString(R.string.edit)
        maintagAdapter!!.refreshData(false)
        isSelected = false
    }
    /*
     *
     * 标签点击listener
     * @param: []
     * @return: [MainTagAdapter.OnItemClickListener]
     */

    private val clickListener = object : MainTagAdapter.OnItemClickListener {
        override fun onTagClick(tagEntity: TagEntity, position: Int) {
            if (isSelected) {
                if (tagEntity.isDefault == 0) {
                    showAddTagDialog(false, tagEntity)
                }
            } else {
                val intent = Intent(activity, FileActivity::class.java)
                intent.putExtra("isTag", true)
                intent.putExtra("isDefault", tagEntity.isDefault)
                intent.putExtra("fileType", tagEntity.fileType)
                intent.putExtra("tagName", tagEntity.tagName)
                intent.putExtra("tagId", tagEntity.tagId)
                ActivityUtils.startActivity(intent)

            }

        }
    }

    /**
     *
     * 增加标签listener
     * @param: []
     * @return: [MainTagAdapter.OnAddItemClickListener]
     */

    private
    val addListener = object : MainTagAdapter.OnAddItemClickListener {
        override fun onClick() {
            showAddTagDialog(true, null)
        }
    }

    /**
     *
     * 删除标签listener
     * @param: []
     * @return: [MainTagAdapter.OnDeleteClickListener]
     */
    private val deleteListener = object : MainTagAdapter.OnDeleteClickListener {
        override fun onTagDeleteClick(tagEntity: TagEntity?, position: Int) {
            showDeleteDialog(tagEntity)
        }
    }

    private fun deleteTag(tagEntity: TagEntity?) {
        realm!!.executeTransaction(Realm.Transaction { realm ->
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
            val curDate = formatter.format(Date(System.currentTimeMillis()))
            tagEntity!!.updateTime = curDate
            tagEntity.isDirty = 1
            tagEntity.isDelete = 1

            val docEntities = realm.where(DocEntity::class.java).equalTo("tagName", tagEntity.tagName).equalTo("isDelete", 0.toInt()).findAll()
            for (docEntity in docEntities) {
                docEntity.updateTime = curDate
                docEntity.isDirty = (if (docEntity.isDirty === 1) 1 else 2)
                docEntity.tagName = ""
                docEntity.tagId = ""
            }
            startAutoSync()
            maintagAdapter!!.refreshData(true)
        })
    }

    private fun showDeleteDialog(tagEntity: TagEntity?) {
        val builder = ScanProCommonPopup.Builder(<EMAIL>, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.confirm_delete))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            deleteTag(tagEntity)
            dialog.dismiss()
        })
        builder.setOnNegativeListener(DialogInterface.OnClickListener { dialog, _ -> dialog.dismiss() })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {

        super.onActivityCreated(savedInstanceState)
        EventBus.getDefault().register(this)

        addBtn = activity!!.findViewById<ImageView>(R.id.indexAddBtn)
        registerEvent()

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        val realm = Realm.getDefaultInstance()
        when (event.eventType) {
            EventType.LOG_IN,
            EventType.THIRD_PARTY_LOGIN_IN,
            EventType.REGISTER_SUCCESS,
            EventType.THIRD_PARTY_REGISTER_SUCCESS,
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
            EventType.CHANGE_PASSWORD,
            EventType.LOG_OUT -> {
                tagEntities = realm!!.where(TagEntity::class.java).equalTo("isDelete", 0.toInt()).distinct("tagName").equalTo("userID", getUserIdIsLogin()).sort("createTime", Sort.ASCENDING).findAll()
                maintagAdapter!!.refreshData(tagEntities, false)

            }

            else -> {
            }
        }
    }

    override fun onClick(v: View) {
        val id = v.id
        when (id) {
            R.id.tag_edit_btn -> {
                isSelected = !isSelected
                if (isSelected) tagEditBtn!!.text = resources.getString(R.string.finish) else tagEditBtn!!.text = resources.getString(R.string.edit)
                maintagAdapter!!.refreshData(isSelected)
            }
            R.id.indexAddBtn -> {
                if (<EMAIL>) {
                    showAddTagDialog(true, null)

                }
            }
            else -> {
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}







