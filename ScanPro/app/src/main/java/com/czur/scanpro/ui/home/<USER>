package com.czur.scanpro.ui.home

import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.widget.RelativeLayout
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.LogUtils
import com.czur.scanpro.R
import com.czur.scanpro.adapter.MainCategoryAdapter
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.event.BaseEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.ui.base.LazyLoadBaseFragment
import io.realm.Realm
import io.realm.Sort
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*

/**
 * Created by Yz on 2019/1/3.
 * Email：<EMAIL>
 */
class CategoryFragment : LazyLoadBaseFragment(), View.OnClickListener {
    private var realm: Realm? = null
    private var categoryEntities: List<CategoryEntity> = ArrayList<CategoryEntity>()
    private var categoryRecyclerview: RecyclerView? = null
    private var mainCategoryAdapter: MainCategoryAdapter? = null
    private var isCategoryAdd: Boolean = false
    private var emptyRL: RelativeLayout? = null
    private var isHide: Boolean = false


    companion object {

        fun newInstance(): CategoryFragment {
            return CategoryFragment()
        }
    }

    override fun getLayoutRes(): Int = R.layout.fragment_category


    override fun initView(rootView: View?) {
        categoryRecyclerview = rootView!!.findViewById<RecyclerView>(R.id.category_recyclerview)
        realm = Realm.getDefaultInstance()
        getCategoryEntities()
    }


    /**
     *
     * 初始化列表
     * @param: []
     * @return: []
     */

    private fun initRecyclerView() {
        mainCategoryAdapter = MainCategoryAdapter(<EMAIL>, categoryEntities)
        mainCategoryAdapter?.setOnItemClickListener(object : MainCategoryAdapter.OnItemClickListener {
            override fun onCategoryClick(categoryEntity: CategoryEntity?, position: Int) {

                val intent = Intent(activity, FileActivity::class.java).apply {
                    putExtra("isTag", false)
                    putExtra("categoryName", categoryEntity!!.categoryName)
                    putExtra("categoryID", categoryEntity.categoryID)
                }
                ActivityUtils.startActivity(intent)
            }
        })

        categoryRecyclerview!!.adapter = mainCategoryAdapter
        categoryRecyclerview!!.setHasFixedSize(true)
        categoryRecyclerview!!.layoutManager =
            LinearLayoutManager(activity)
    }


    private fun registerEvent() {

    }


    private fun isShowEmptyPrompt() {
        emptyRL!!.visibility = if (categoryEntities.isEmpty()) View.VISIBLE else View.GONE
        categoryRecyclerview!!.visibility = if (categoryEntities.isEmpty()) View.GONE else View.VISIBLE

    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {

        super.onActivityCreated(savedInstanceState)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        emptyRL = activity!!.findViewById<RelativeLayout>(R.id.category_empty_layout)

        registerEvent()
        initRecyclerView()

        isShowEmptyPrompt()
        realm!!.addChangeListener {
            getCategoryEntities()
            isShowEmptyPrompt()
            mainCategoryAdapter!!.refreshData(categoryEntities)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        val realm = Realm.getDefaultInstance()
        when (event.eventType) {

            EventType.ADD_CATEGORY -> {
                LogUtils.d("AAA", EventType.ADD_CATEGORY)
                checkAnimChange(true, false)
            }

            EventType.ADD_CATEGORY_HIDE -> {
                LogUtils.d("AAA", EventType.ADD_CATEGORY_HIDE)

                checkAnimChange(true, true)
            }
            EventType.DELETE_CATEGORY -> {
                LogUtils.d("AAA", EventType.DELETE_CATEGORY)
                getCategoryEntities()
                isShowEmptyPrompt()
                mainCategoryAdapter!!.refreshData(categoryEntities, true)
            }
            EventType.LOG_IN,
            EventType.THIRD_PARTY_LOGIN_IN,
            EventType.REGISTER_SUCCESS,
            EventType.THIRD_PARTY_REGISTER_SUCCESS,
            EventType.THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
            EventType.CHANGE_PASSWORD,
            EventType.LOG_OUT,
            EventType.SYNC_IS_STOP,
            EventType.SYNC_IS_FINISH -> {
                if (isCategoryAdd) {
                    LogUtils.d("AAA", "置反")

                    isCategoryAdd = false;
                } else {
                    LogUtils.d("AAA", "SYNC_IS_FINISH")
                    getCategoryEntities()
                    isShowEmptyPrompt()
                    mainCategoryAdapter!!.refreshData(categoryEntities)
                }

            }
            else -> {
            }
        }
    }

    private fun checkAnimChange(isAnim: Boolean, isHide: Boolean) {
        if (isHide) {
            isCategoryAdd = true
        }
        getCategoryEntities()
        isShowEmptyPrompt()
        if (categoryEntities.isNotEmpty()) {
            mainCategoryAdapter!!.refreshData(categoryEntities, isAnim, isHide, 0)
            smoothMoveToPosition(categoryRecyclerview!!, 0)

        }
        if (!isHide) {
            isCategoryAdd = false
        }
    }

    private fun getCategoryEntities() {
//        LogUtils.e(getUserIdIsLogin(),"ccc")
        categoryEntities = realm!!.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).sort("createTime", Sort.DESCENDING).equalTo("userID", getUserIdIsLogin()).distinct("categoryName").findAll()
    }

    override fun onClick(v: View) {
        val id = v.id
        when (id) {

            else -> {
            }
        }
    }

    /**
     * @des: 滑动到指定位置
     * @params:[mRecyclerView, position]
     * @return:void
     */

    private fun smoothMoveToPosition(mRecyclerView: RecyclerView, position: Int) {
        // 第一个可见位置
        val firstItem = mRecyclerView.getChildLayoutPosition(mRecyclerView.getChildAt(0))
        // 最后一个可见位置
        val lastItem = mRecyclerView.getChildLayoutPosition(mRecyclerView.getChildAt(mRecyclerView.childCount - 1))

        if (position < firstItem) {
            // 如果跳转位置在第一个可见位置之前，就smoothScrollToPosition可以直接跳转
            mRecyclerView.smoothScrollToPosition(position)
        } else if (position <= lastItem) {
            // 跳转位置在第一个可见项之后，最后一个可见项之前
            // smoothScrollToPosition根本不会动，此时调用smoothScrollBy来滑动到指定位置
            val movePosition = position - firstItem
            if (movePosition >= 0 && movePosition < mRecyclerView.childCount) {
                val top = mRecyclerView.getChildAt(movePosition).top
                mRecyclerView.smoothScrollBy(0, top)
            }
        } else {
            // 如果要跳转的位置在最后可见项之后，则先调用smoothScrollToPosition将要跳转的位置滚动到可见位置
            // 再通过onScrollStateChanged控制再次调用smoothMoveToPosition，执行上一个判断中的方法
            mRecyclerView.smoothScrollToPosition(position)

        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}







