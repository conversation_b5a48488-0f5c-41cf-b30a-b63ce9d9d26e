package com.czur.scanpro.ui.home

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.KeyEvent
import android.view.View
import android.widget.EditText
import android.widget.RelativeLayout
import android.widget.TextView
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.*
import com.czur.scanpro.BuildConfig
import com.czur.scanpro.R
import com.czur.scanpro.adapter.MainFileAdapter
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityFileBinding
import com.czur.scanpro.entity.model.OssModel
import com.czur.scanpro.entity.realm.CategoryEntity
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.PdfEntity
import com.czur.scanpro.event.*
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.account.LoginActivity
import com.czur.scanpro.ui.album.ImageGridActivity
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.camera.CameraActivity
import com.czur.scanpro.ui.camera.CardCameraActivity
import com.czur.scanpro.ui.component.GridDividerItemDecoration
import com.czur.scanpro.ui.component.popup.*
import com.czur.scanpro.ui.component.popup.blur.MoreWindow
import com.czur.scanpro.ui.file.*
import com.czur.scanpro.utils.BitmapUtils
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.GeneratePdfUtils
import com.czur.scanpro.utils.PermissionUtil
import com.czur.scanpro.utils.PermissionUtil.getStoragePermission
import com.czur.scanpro.utils.SaveUtils
import com.czur.scanpro.utils.launch
import com.czur.scanpro.utils.share.ShareContentType
import com.czur.scanpro.utils.share.ShareUtils
import com.czur.scanpro.utils.validator.Validator
import com.google.android.renderscript.Toolkit
import com.google.gson.Gson
import com.umeng.analytics.MobclickAgent
import io.realm.Realm
import io.realm.Sort
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by Yz on 2019/1/3.
 * Email：<EMAIL>
 */
class FileActivity : BaseActivity(), View.OnClickListener {

    private var realm: Realm? = null
    private var fileEntities: List<DocEntity> = ArrayList<DocEntity>()
    private var fileRecyclerview: RecyclerView? = null
    private var mainFileAdapter: MainFileAdapter? = null
    private var formatter: SimpleDateFormat? = null
    private var userPreferences: UserPreferences? = null
    private val pageIds = ArrayList<String>()
    private var isCheckedMap: LinkedHashMap<String, Boolean>? = null
    private var moreWindow: MoreWindow? = null
    private var blurPop: RelativeLayout? = null

    private var tagId: String? = null
    private var tagName: String? = null
    private var categoryID: String? = null
    private var categoryName: String? = null
    private var isDefault: Int? = null
    private var fileType: Int? = null
    private var isTag: Boolean = false
    private var isMultiImages = false
    private var fileBottomDialogPopup: FileBottomDialogPopup? = null
    private var shareBottomDialog: ShareBottomDialogPopup? = null
    private var pdfType = 0
    private var pdfQuality = 1
    private var pdfHorizontal = 0
    private var dialogEdt: EditText? = null
    private var savePdfPopup: SavePdfPopup? = null
    private var paths: ArrayList<String>? = arrayListOf<String>()
    private var sharePaths: ArrayList<String>? = arrayListOf<String>()
    private var progressTitle: TextView? = null
    private var commonPopup: PDFSettingPopup? = null
    private var isRealmRefresh = false
    private var isMove = false
    private var canTouch: Boolean = true
    private var canBack: Boolean = true

    private lateinit var binding: ActivityFileBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_file)
        binding = ActivityFileBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initComponent()
        registerEvent()

    }


    private fun initComponent() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        tagId = intent.getStringExtra("tagId")
        tagName = intent.getStringExtra("tagName")
        categoryID = intent.getStringExtra("categoryID")
        categoryName = intent.getStringExtra("categoryName")
        isTag = intent.getBooleanExtra("isTag", false)
        isDefault = intent.getIntExtra("isDefault", 0)
        fileType = intent.getIntExtra("fileType", 0)
        fileRecyclerview = findViewById(R.id.file_recyclerview)
        realm = Realm.getDefaultInstance()

        userPreferences = UserPreferences.getInstance(this)
        formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        moreWindow = MoreWindow(this)
        moreWindow!!.setIndexAnimFinishedListener {
            LogUtils.e("可以点击再次开启")
            canTouch = true
        }

        moreWindow!!.setOnMainDialogOnClickListener { viewId: Int ->
            when (viewId) {
                R.id.card_btn -> cardMode()
                R.id.single_btn -> singleMode()
                R.id.surface_btn -> surfaceMode()
                R.id.import_ll -> import()
            }
        }
        if (isTag) {
            isMove = false
            if (isDefault == 1) {
                binding.fileMoreRl.visibility = View.GONE
                binding.fileDeleteRl.visibility = View.VISIBLE
                binding.fileSaveRl.visibility = View.VISIBLE
                binding.fileTagRl.visibility = View.GONE
                binding.cancelFileTagRl.visibility = View.GONE
            } else {
                binding.fileMoreRl.visibility = View.VISIBLE
                binding.fileDeleteRl.visibility = View.GONE
                binding.fileSaveRl.visibility = View.GONE
                binding.fileTagRl.visibility = View.GONE
                binding.cancelFileTagRl.visibility = View.VISIBLE
            }
        } else {
            isMove = true
            binding.fileMoreRl.visibility = View.VISIBLE
            binding.fileDeleteRl.visibility = View.GONE
            binding.fileSaveRl.visibility = View.GONE
            binding.fileTagRl.visibility = View.VISIBLE
            binding.cancelFileTagRl.visibility = View.GONE
        }
        createBottomSheetDialog()
        moreWindow!!.init(<EMAIL>)
        getFiles()
        initRecyclerView()
        realm!!.addChangeListener {
            isRealmRefresh = true
            getSimpleFiles()
            mainFileAdapter!!.refreshData(fileEntities, isMultiSelect, isCheckedMap)
            isShowEmptyPrompt()
        }
        isShowEmptyPrompt()
        checkPromptToShow()

    }

    private fun getSimpleFiles() {
        if (isTag) {
            if (isDefault == 1) {
                fileEntities = realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("fileType", fileType).equalTo("userID", getUserIdIsLogin()).sort("createTime", Sort.DESCENDING).findAll()
            } else {
                fileEntities = realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("tagName", tagName).equalTo("userID", getUserIdIsLogin()).sort("createTime", Sort.DESCENDING).findAll()
            }
        } else {
            fileEntities = realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).equalTo("userID", getUserIdIsLogin()).sort("createTime", Sort.ASCENDING).findAll()
        }
    }

    private fun getFiles() {
        if (isTag) {
            binding.fileTitleTv.text = tagName
            if (isDefault == 1) {
                binding.fileScanBtn.visibility = View.GONE
                fileEntities = realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("fileType", fileType).equalTo("userID", getUserIdIsLogin()).sort("createTime", Sort.DESCENDING).findAll()
            } else {
                binding.fileScanBtn.visibility = View.VISIBLE
                fileEntities = realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("tagName", tagName).equalTo("userID", getUserIdIsLogin()).sort("createTime", Sort.DESCENDING).findAll()
            }
            binding.fileSettingBtn.visibility = View.GONE
            binding.fileGuideTv.text = getText(R.string.file_prompt_without_move)
            val layoutParams = binding.fileGuideTv.layoutParams as RelativeLayout.LayoutParams
            layoutParams.marginEnd = 0
            binding.fileGuideTv.layoutParams = layoutParams
        } else {
            binding.fileTitleTv.text = categoryName
            binding.fileSettingBtn.visibility = View.VISIBLE
            binding.fileGuideTv.text = getText(R.string.file_prompt)
            binding.fileScanBtn.visibility = View.VISIBLE
            fileEntities = realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).equalTo("userID", getUserIdIsLogin()).sort("createTime", Sort.ASCENDING).findAll()
            realm!!.executeTransaction {
                if (fileEntities.isNotEmpty()) {
                    for (fileEntity in fileEntities) {
                        fileEntity.isNameTemp = 1
                    }
                }
                for (categoryEntity in realm!!.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("categoryName", categoryName).equalTo("userID", getUserIdIsLogin()).findAll()) {
                    categoryEntity.isNameTemp = 1
                }
            }
        }
    }

    private fun registerEvent() {
        binding.fileDeleteRl.setOnClickListener(this)
        binding.fileSaveRl.setOnClickListener(this)
        binding.cancelFileTagRl.setOnClickListener(this)
        binding.fileHideTv.setOnClickListener(this)
        binding.fileSettingBtn.setOnClickListener(this)
        binding.fileScanBtn.setOnClickListener(this)
        binding.fileBackBtn.setOnClickListener(this)
        binding.fileMultiSelectBtn.setOnClickListener(this)
        binding.fileShareRl.setOnClickListener(this)
        binding.fileTagRl.setOnClickListener(this)
        binding.fileMoreRl.setOnClickListener(this)
        binding.filePdfRl.setOnClickListener(this)
        binding.fileCancelBtn.setOnClickListener(this)
        binding.fileSelectAllBtn.setOnClickListener(this)
    }

    private fun checkPromptToShow() {
        if (userPreferences!!.isFirstCategoryrGuide) {
            binding.fileGuideTv.visibility = View.VISIBLE
            binding.fileGuideImg.visibility = View.VISIBLE
            binding.fileHideTv.visibility = View.VISIBLE

        } else {
            binding.fileGuideTv.visibility = View.GONE
            binding.fileGuideImg.visibility = View.GONE
            binding.fileHideTv.visibility = View.GONE
        }
    }

    /**
     *
     * 初始化列表
     * @param: []
     * @return: []
     */

    private fun initRecyclerView() {
        isCheckedMap = LinkedHashMap()
        mainFileAdapter = MainFileAdapter(this@FileActivity, fileEntities, !isTag)
        fileRecyclerview!!.adapter = mainFileAdapter
        fileRecyclerview!!.setHasFixedSize(true)
        mainFileAdapter!!.setOnItemCheckListener(onItemCheckListener)
        mainFileAdapter!!.setOnItemClickListener(onItemClickListener)
        fileRecyclerview!!.layoutManager =
            GridLayoutManager(this@FileActivity, 3)
        fileRecyclerview!!.addItemDecoration(GridDividerItemDecoration(this, SizeUtils.dp2px(16f), true))
    }


    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private fun isShowEmptyPrompt() {
        if (fileEntities.isEmpty()) {
            fileRecyclerview!!.visibility = View.GONE
            binding.emptyLayout.visibility = View.VISIBLE
        } else {
            fileRecyclerview!!.visibility = View.VISIBLE
            binding.emptyLayout.visibility = View.GONE
        }
    }

    private fun singleMode() {
//        openCamera(0)
        requestPermissionDialog(0)

    }

    private fun surfaceMode() {
//        openCamera(1)
        requestPermissionDialog(1)

    }

    private fun cardMode() {
//        openCamera(2)
        requestPermissionDialog(2)

    }
    private fun requestPermissionDialog(cameraType: Int) {
        if (PermissionUtils.isGranted(*getStoragePermission())
            && PermissionUtils.isGranted(PermissionConstants.CAMERA)
        ) {
            openCamera(cameraType)
        } else {
            PermissionUtil.checkPermissionWithDialog(
                this,
                getString(R.string.dialog_tips),
                getString(R.string.scan_power_tips),
                getString(R.string.dialog_setting),
                getString(R.string.dialog_cancel)
            ) {
                if (it != null) {
                    openCamera(cameraType)
                }
            }
        }


    }
    private fun import() {
        if (PermissionUtils.isGranted(*getStoragePermission())){

//        val intent = Intent(this@FileActivity, SelectAlbumPhotoActivity::class.java)
            val intent = Intent(this@FileActivity, ImageGridActivity::class.java)
            intent.putExtra("isImport", true)
            intent.putExtra("type", if (isTag) 2 else 1)
            intent.putExtra("isTag", isTag)
            if (isTag) {
                intent.putExtra("tagName", tagName)
                intent.putExtra("tagId", tagId)
            } else {
                intent.putExtra("categoryID", categoryID)
                intent.putExtra("categoryName", categoryName)
            }
            ActivityUtils.startActivity(intent)
        }else{
            PermissionUtil.checkPermissionWithDialog(
                this,
                getString(R.string.dialog_tips),
                getString(R.string.import_tips),
                getString(R.string.dialog_setting),
                getString(R.string.dialog_cancel)
            ) {
                if (it != null) {

//        val intent = Intent(this@FileActivity, SelectAlbumPhotoActivity::class.java)
                    val intent = Intent(this@FileActivity, ImageGridActivity::class.java)
                    intent.putExtra("isImport", true)
                    intent.putExtra("type", if (isTag) 2 else 1)
                    intent.putExtra("isTag", isTag)
                    if (isTag) {
                        intent.putExtra("tagName", tagName)
                        intent.putExtra("tagId", tagId)
                    } else {
                        intent.putExtra("categoryID", categoryID)
                        intent.putExtra("categoryName", categoryName)
                    }
                    ActivityUtils.startActivity(intent)
                }
            }
        }


    }


    /**
     * @des: item选择监听
     * @params:
     * @return:
     */
    private val onItemCheckListener = object : MainFileAdapter.OnItemCheckListener {
        override fun onItemCheck(position: Int, docEntity: DocEntity, isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int) {
            <EMAIL> = isCheckedMap
            LogUtils.i(Gson().toJson(isCheckedMap))
            //如果选中一个 文案变为已选中1个
            if (isCheckedMap.size == 1) {
                isMultiImages = false
                binding.fileTitle!!.setText(R.string.select_one)
                showAll()
            } else if (isCheckedMap.size > 1) {
                isMultiImages = true
                binding.fileTitle!!.text = String.format(getString(R.string.select_num), isCheckedMap.size.toString())

                showAll()
            } else {
                isMultiImages = false
                darkAll()
                if (isMultiSelect) {
                    binding.fileTitle!!.text = String.format(getString(R.string.select_num), isCheckedMap.size.toString())
                }
            }
            //如果选择不是全部Item  text变为取消全选
            checkSize(isCheckedMap, totalSize)
        }
    }


    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */

    private val onItemClickListener = object : MainFileAdapter.OnItemClickListener {

        override fun onDocEntityClick(docEntity: DocEntity, position: Int, tvIndex: TextView) {
            if (isMultiSelect) {
                tvIndex.isEnabled = !tvIndex.isEnabled
            } else {
                val intent = Intent(this@FileActivity, FilePreviewActivity::class.java)
                if (!isTag) {
                    intent.putExtra("type", 1)
                    intent.putExtra("fileType", docEntity.fileType)
                } else {
                    if (isDefault == 0) {
                        intent.putExtra("type", 2)
                        intent.putExtra("fileType", docEntity.fileType)
                    } else {
                        intent.putExtra("type", 3)
                        intent.putExtra("fileType", fileType)
                    }
                }
                intent.putExtra("isItemTag", if (Validator.isNotEmpty(docEntity.tagName)) 1 else 0)
                intent.putExtra("categoryName", docEntity.categoryName)
                intent.putExtra("tagId", docEntity.tagId)
                intent.putExtra("tagName", docEntity.tagName)
                intent.putExtra("categoryID", docEntity.categoryID)
                intent.putExtra("index", position)
                intent.putExtra("currentPageId", docEntity.fileID)
                startActivity(intent)
            }
        }
    }

    private fun generatePdf(pdfName: String) {
        commonPopup!!.dismiss()
        paths = arrayListOf<String>()
        realm!!.executeTransaction(Realm.Transaction {
            for (id in isCheckedMap!!.keys) {
                //先查找后得到PageEntity对象
                val page = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", id)
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                val picUrl = page!!.processImagePath
                paths!!.add(picUrl!!)
            }
        })
        mobClickPdfEvent(this@FileActivity, BuildConfig.PHASE.generatePdfCount)

        val path = getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absoluteFile.toString() + "/" + Constants.PDF_PATH
//        val path = Environment.getExternalStorageDirectory().toString() + Constants.SD_PATH + Constants.PDF_PATH
        LogUtils.i("pdf path: $path", "paths$paths")
        val generatePdfUtils = GeneratePdfUtils(this, path, paths, pdfName)
        generatePdfUtils.setOnGeneratePdfListener(onGeneratePdfListener)
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                generatePdfUtils.createPdf()
                return null
            }

            override fun onSuccess(result: Void?) {
                consumption()
            }
        })
    }

    private fun mobClickPdfEvent(activity: Context?, eventId: String) {
        LogUtils.d("友盟_计数事件_$eventId")
        val map = HashMap<String, String>()
        map[Constants.PDF_EVENT] = Constants.PDF_EVENT
        MobclickAgent.onEvent(activity, eventId, map)
    }

    private fun consumption() {
        HttpManager.getInstance().request().consumption(userPreferences!!.userId, "pdf", String::class.java, object : MiaoHttpManager.CallbackNetwork<String> {
            override fun onNoNetwork() {
                hideProgressDialog()
            }

            override fun onStart() {
            }

            override fun onResponse(entity: MiaoHttpEntity<String>) {
                hideProgressDialog()
                LogUtils.i(Gson().toJson(entity.body))
                commonRequestUserInfoNow()
                EventBus.getDefault().post(ConsumptionEvent(EventType.PDF_COUNT_REDUCE))
            }

            override fun onFailure(entity: MiaoHttpEntity<String>) {
                showMessage(R.string.request_failed_alert)
            }

            override fun onError(e: Exception) {
                showMessage(R.string.request_failed_alert)
            }
        })
    }

    private val onGeneratePdfListener = object : GeneratePdfUtils.OnGeneratePdfListener {
        override fun onStart() {
            LogUtils.i("onStart")
            val builder = SavePdfPopup.Builder(this@FileActivity)
            builder.setTitle("")
            savePdfPopup = builder.create()
            progressTitle = savePdfPopup!!.window?.findViewById(R.id.title) as TextView
            if (!savePdfPopup!!.isShowing) {
                savePdfPopup!!.show()
            }

        }

        override fun onGenerate(progress: Int, time: Long) {
            LogUtils.i(progress, "耗时" + time + "毫秒")
            progressTitle!!.text = String.format(getString(R.string.have_been_generated), progress.toString(), paths!!.size.toString())
        }

        override fun onFinish(totalTime: Long, pdfPath: String, pdfName: String) {
            savePdfPopup!!.dismiss()
            realm!!.executeTransaction(Realm.Transaction { realm ->
                val pdfEntity = realm.createObject(PdfEntity::class.java, pdfName)
                pdfEntity.pdfPath = pdfPath

                val curDate = formatter!!.format(Date(System.currentTimeMillis()))
                pdfEntity.createTime = curDate
            })

            showMessage(R.string.saved_pdf)
            val intent = Intent(this@FileActivity, PdfPreviewActivity::class.java)
            intent.putExtra("pdfPath", pdfPath)
            intent.putExtra("pdfName", pdfName)
            ActivityUtils.startActivity(intent)

        }
    }
    private var isMultiSelect = false
    private var isSelectAll = false

    /**
     * @des: 判断检查状态
     * @params:
     * @return:
     */

    private fun checkSize(isCheckedMap: LinkedHashMap<String, Boolean>, totalSize: Int) {
        if (isCheckedMap.size < totalSize) {
            binding.fileSelectAllBtn!!.setText(R.string.select_all)
            isSelectAll = false
        } else {
            binding.fileSelectAllBtn!!.setText(R.string.not_select_all)
            isSelectAll = true
        }
    }

    private fun resetStatus() {
        pageIds.clear()
        binding.fileTitle!!.text = ""
        isMultiSelect = false
        isSelectAll = false
        isCheckedMap!!.clear()
        isCheckedMap = LinkedHashMap()
        mainFileAdapter!!.refreshData(false)
        hideSelectTopBar()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == SHARE_SUCCESS_CODE) {
            cancelEvent()
        }
    }

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private fun multiSelect() {
        isMultiSelect = !isMultiSelect
        mainFileAdapter!!.refreshData(isMultiSelect)
        if (isMultiSelect) {
            showSelectTopBar()
        } else {
            hideSelectTopBar()
        }
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private fun selectAll() {
        if (!isSelectAll) {
            for (i in fileEntities.indices) {
                if (!isCheckedMap!!.containsKey(fileEntities[i].fileID)) {
                    isCheckedMap!![fileEntities[i].fileID!!] = true
                }
            }
            binding.fileSelectAllBtn!!.setText(R.string.not_select_all)
            isSelectAll = true
        } else {
            isCheckedMap!!.clear()
            isCheckedMap = LinkedHashMap()
            binding.fileSelectAllBtn!!.setText(R.string.select_all)
            isSelectAll = false
        }
        if (isCheckedMap?.size!! > 0) {
            showAll()
        } else {
            darkAll()
        }
        binding.fileTitle.text = String.format(getString(R.string.select_num), isCheckedMap!!.size.toString() + "")
        LogUtils.iTag("select all", Gson().toJson(isCheckedMap))
        mainFileAdapter!!.refreshData(fileEntities, true, isCheckedMap)
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private fun cancelEvent() {
        resetStatus()
        binding.fileTitle.text = ""
        mainFileAdapter!!.refreshData(fileEntities, false, isCheckedMap)
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private fun showSelectTopBar() {
        darkAll()
        binding.fileScanBtn.isEnabled = false
        binding.fileScanBtn.isClickable = false
        binding.fileGuideTv.visibility = View.GONE
        binding.fileGuideImg.visibility = View.GONE
        binding.fileHideTv.visibility = View.GONE

        binding.multiRl.visibility = View.VISIBLE
        binding.fileBottomLl.visibility = View.VISIBLE
        binding.fileMultiSelectBtn.visibility = View.GONE
        binding.fileCancelBtn.visibility = View.VISIBLE
        binding.fileSelectAllBtn.visibility = View.VISIBLE
        binding.fileCancelBtn.setText(R.string.quit)
        binding.fileSelectAllBtn.setText(R.string.select_all)
        binding.fileTitle.text = String.format(getString(R.string.select_num), isCheckedMap!!.size.toString() + "")
    }

    public val SHARE_SUCCESS_CODE = 666
    private fun share() {
        val copyList = ArrayList<String>()
        copyList.addAll(isCheckedMap!!.keys)
        val albumDirPath = userPreferences!!.sdPath
        if (!isMultiImages) {
            if (copyList.size > 0) {
                val page = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", copyList[0])
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                val picUrl = page!!.processImagePath
                val sdPicPath = albumDirPath + UUID.randomUUID() + Constants.JPG
                ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                    override fun doInBackground(): Void? {
                        FileUtils.copy(picUrl, sdPicPath) { _, _ -> true }
                        return null
                    }

                    override fun onSuccess(result: Void?) {
                        WeakHandler().post(Runnable {
                            sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://$sdPicPath")))
                            ShareUtils.Builder(this@FileActivity)
                                    .setOnActivityResult(SHARE_SUCCESS_CODE)
                                    // 指定分享的文件类型
                                    .setContentType(ShareContentType.IMAGE)
                                    // 设置分享选择器的标题
                                    .setMulti(isMultiImages)
                                    .setTitle(getString(R.string.share_to))
                                    .setShareFileUri(UriUtils.file2Uri(File(sdPicPath ?: ""))
//                                        FileUtil.getFileUri(this@FileActivity, ShareContentType.FILE, File(sdPicPath))
                                    )
                                    .build()
                                    .shareBySystem()
                        })
                    }
                })
            }

        } else {
            val files = ArrayList<File>()
            val sdPicPaths = ArrayList<String>()
            ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
                override fun doInBackground(): Void? {
                    val realm = Realm.getDefaultInstance()
                    if (FileUtils.createOrExistsDir(albumDirPath)) {
                        for (id in copyList) {
                            LogUtils.i("copyListId", id)
                            val page = realm.where(DocEntity::class.java)
                                    .equalTo("fileID", id)
                                    .equalTo("isDelete", 0.toInt())
                                    .findFirst()
                            val picUrl = page!!.processImagePath
                            val sdPicPath = albumDirPath + UUID.randomUUID() + Constants.JPG
                            FileUtils.copy(picUrl, sdPicPath) { _, _ -> true }
                            sdPicPaths.add(sdPicPath)
                            sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://$sdPicPath")))
                        }
                    }
                    return null
                }

                override fun onSuccess(result: Void?) {
                    WeakHandler().post(Runnable {
                        val fileUris = arrayListOf<Uri>()
                        for (sdPicPath in sdPicPaths) {
                            fileUris.add(UriUtils.file2Uri(File(sdPicPath)))
                            files.add(FileUtils.getFileByPath(sdPicPath))
                        }
                        EtUtils.originalShareImage(this@FileActivity, fileUris)
                    })
                }
            })
        }

    }

    private fun move() {
        val fileIds = ArrayList<String>()
        for (fileID in isCheckedMap!!.keys) {
            fileIds.add(fileID)
        }
        val intent = Intent(this@FileActivity, FileMoveActivity::class.java)
        intent.putExtra("fileIds", fileIds)
        intent.putExtra("categoryName", categoryName)
        ActivityUtils.startActivity(intent)
        resetStatus()
    }

    private fun showDeleteDialog() {
        val builder = ScanProCommonPopup.Builder(this@FileActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.confirm_delete))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            deleteFiles()
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    private fun showLoginDialog() {
        val builder = ScanProCommonPopup.Builder(this@FileActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
        builder.setTitle(resources.getString(R.string.prompt))
        builder.setMessage(resources.getString(R.string.please_login))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            val intent = Intent(this@FileActivity, LoginActivity::class.java)
            intent.putExtra("type", 3)
            ActivityUtils.startActivity(intent)
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        commonPopup.show()
    }

    private fun checkLogin(): Boolean {
        if (!userPreferences!!.isUserLogin) {
            showLoginDialog()
            return false
        }
        return true
    }

    /**
     * @des: 删除books
     * @params:
     * @return:
     */

    private fun deleteFiles() {
        val needDeletePaths = ArrayList<String>()
        for (fileID in isCheckedMap!!.keys) {
            //得到待删除pdf paths
            val deleteFileEntity = realm!!.where(DocEntity::class.java)
                    .equalTo("fileID", fileID)
                    .equalTo("isDelete", 0.toInt()).findFirst()
            needDeletePaths.add(deleteFileEntity!!.processSmallImagePath!!)
            needDeletePaths.add(deleteFileEntity.processImagePath!!)
            needDeletePaths.add(deleteFileEntity.baseImagePath!!)
            needDeletePaths.add(deleteFileEntity.baseSmallImagePath!!)
            if (deleteFileEntity.originalImagePath != null) {
                needDeletePaths.add(deleteFileEntity.originalImagePath!!)
            }

        }
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                //删除sd卡上book page
                for (needDeletePath in needDeletePaths) {
                    FileUtils.delete(needDeletePath)
                }
                return null
            }

            override fun onSuccess(result: Void?) {

            }
        })

        realm!!.executeTransaction(Realm.Transaction {
            for (finalId in isCheckedMap!!.keys) {
                //先查找后得到PageEntity对象
                val deletePageEntity = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", finalId)
                        .equalTo("isDelete", 0.toInt()).findFirst()

                deletePageEntity!!.isDelete = 1
                deletePageEntity.isDirty = 1
                val curDate = formatter!!.format(Date(System.currentTimeMillis()))
                deletePageEntity.updateTime = curDate
            }
            resetStatus()
            startAutoSync()
        })
    }
//    private fun copyToSd() {
//        val sdPicPath = sdPath + UUID.randomUUID() + Constants.JPG
//        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
//            override fun doInBackground(): Void? {
//
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R &&
//                    getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.R
//                ) {
//                    SaveUtils.saveImgToAlbum(this@FilePreviewActivity,showList[viewPager!!.currentItem].processImagePath)
//                }else{
//                    FileUtils.copy(showList[viewPager!!.currentItem].processImagePath, sdPicPath) { _, _ -> true }
//                }
//
//                runOnUiThread(Runnable {
//                    whiteRightPopup!!.show()
//                    noticeAlbumUpdate(sdPicPath)
//                })
//                return null
//            }
//
//            override fun onSuccess(result: Void?) {
//                handler!!.postDelayed({ whiteRightPopup!!.dismiss() }, 900)
//            }
//        })
//
//
//    }
    private fun saveToAlbum() {
        showProgressDialog()
        ThreadUtils.executeByIo(object : ThreadUtils.SimpleTask<Void>() {
            override fun doInBackground(): Void? {
                val realm = Realm.getDefaultInstance()
                realm.use { realm ->
                    val albumDirPath = userPreferences!!.sdPath
                    val copyList = ArrayList<String>()
                    copyList.addAll(isCheckedMap!!.keys)
                    if (FileUtils.createOrExistsDir(albumDirPath)) {
                        for (id in copyList) {
                            LogUtils.i("copyListId", id)
                            val page = realm.where(DocEntity::class.java)
                                    .equalTo("fileID", id)
                                    .equalTo("isDelete", 0.toInt())
                                    .findFirst()
                            val picUrl = page!!.processImagePath
                            val sdPicPath = albumDirPath + UUID.randomUUID() + Constants.JPG

                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
                                getApplicationInfo().targetSdkVersion >= Build.VERSION_CODES.Q
                            ) {
                                SaveUtils.saveImgToAlbum(this@FileActivity,picUrl)
                            }else{
                                FileUtils.copy(picUrl, sdPicPath) { _, _ -> true }
                            }


                            sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://$sdPicPath")))
                            runOnUiThread(Runnable {
                                noticeAlbumUpdate(sdPicPath)
                            })
                        }
                    }
                }
                return null
            }

            override fun onSuccess(result: Void?) {
                hideProgressDialog()
                val builder = BlackRightPopup.Builder(this@FileActivity)
                builder.setTitle(getString(R.string.saved))
                val blackRightPopup = builder.create()
                blackRightPopup.show()
                WeakHandler().postDelayed(Runnable { blackRightPopup.cancel() }, 900)
                resetStatus()
            }

        })


    }


    private fun noticeAlbumUpdate(sdPicPath: String) {
        //通知系统相册更新
        val sdPicFile = FileUtils.getFileByPath(sdPicPath)
        val contentUri = Uri.fromFile(sdPicFile)
        MediaScannerConnection.scanFile(this@FileActivity,
            arrayOf(sdPicFile.absolutePath), arrayOf("image/jpeg"),
            MediaScannerConnection.OnScanCompletedListener { path, uri -> LogUtils.i("file $path", " scanned seccessfully: $uri") })
    }


    private fun editTag() {
        if (isTag and (isDefault == 0)) {
            realm!!.executeTransaction {
                for (fileID in isCheckedMap!!.keys) {
                    val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                    val curDate = formatter.format(Date(System.currentTimeMillis()))
                    val docEntity = realm!!.where(DocEntity::class.java).equalTo("tagName", tagName).equalTo("isDelete", 0.toInt()).equalTo("fileID", fileID).findFirst()
                    docEntity!!.tagName = ""
                    docEntity.tagId = ""
                    docEntity.updateTime = curDate
                    docEntity.isDirty = (if (docEntity.isDirty == 1) 1 else 2)
                }
                EventBus.getDefault().post(ChangeTagEvent(EventType.DELETE_TAGS))
                startAutoSync()
            }
            cancelEvent()

        } else {
            for (finalId in isCheckedMap!!.keys) {
                pageIds.add(finalId)
            }
            val intent = Intent(this, EditTagActivity::class.java)
            intent.putExtra("isPreview", false)
            intent.putStringArrayListExtra("pageIds", pageIds)
            ActivityUtils.startActivity(intent)
        }

    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private fun hideSelectTopBar() {
        checkPromptToShow()
        binding.fileScanBtn.isEnabled = true
        binding.fileScanBtn.isClickable = true
        binding.multiRl.visibility = View.GONE

        binding.fileBottomLl.visibility = View.GONE
        binding.fileMultiSelectBtn.visibility = View.VISIBLE
        binding.fileCancelBtn.visibility = View.GONE
        binding.fileSelectAllBtn.visibility = View.GONE
    }

    private var clickTime = 0L
    private fun canClick(): Boolean {
        if (System.currentTimeMillis() - clickTime > 600) {
            clickTime = System.currentTimeMillis()
            return true
        }
        return false
    }

    override fun onClick(v: View) {
        val id = v.id
        when (id) {
            R.id.fileScanBtn -> {
                if (canTouch) {
                    canTouch = false
                    LogUtils.e(canTouch)
                    launch {
                        val screenShot = BitmapUtils.getScreenShot(<EMAIL>)
                        var blur: Bitmap = screenShot
                        blur = Toolkit.blur(blur, 25)
                        moreWindow!!.showMoreWindow(binding.blurView, blur)
                    }
                }

            }
            R.id.fileHideTv -> {
                binding.fileGuideTv.visibility = View.GONE
                binding.fileGuideImg.visibility = View.GONE
                binding.fileHideTv.visibility = View.GONE
                userPreferences!!.setIsFirstCategoryGuide(false)
            }
            R.id.fileSaveRl -> saveToAlbum()
            R.id.fileDeleteRl -> showDeleteDialog()
            R.id.fileSettingBtn -> fileSetting()
            R.id.fileMultiSelectBtn -> multiSelect()
            R.id.fileSelectAllBtn -> selectAll()
            R.id.fileCancelBtn -> cancelEvent()
            R.id.fileMoreRl -> showMoreDialog()
            R.id.filePdfRl -> if (checkLogin()) {
                Thread(Runnable {
                    requestUserInfoSync()
                    runOnUiThread {
                        LogUtils.e(userPreferences!!.remainPdf)
                        if (userPreferences!!.isVip || userPreferences!!.remainPdf > 0) {
                            if (isCheckedMap!!.size < 101) {
                                if (canClick()) {
                                    createPDF()

                                }
                            } else {
                                showMessage(R.string.out_of_pdf_size)
                            }
                        } else {
                            showEmptyCountDialog(this@FileActivity, EmptyCountPopup.EmptyType.PDF)
                        }
                    }
                }).start()

            }
            R.id.fileShareRl -> showShareDialog()
            R.id.fileTagRl, R.id.cancelFileTagRl -> editTag()
            R.id.fileBackBtn -> ActivityUtils.finishActivity(this)
            else -> {
            }
        }
    }

    private fun createPDF() {

        val builder = PDFSettingPopup.Builder(this, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setOnPdfClickListener { type, quality, isHorizontal, dialog ->
            LogUtils.e(type, quality, isHorizontal)
            pdfType = type
            pdfQuality = quality
            pdfHorizontal = isHorizontal
            if (Validator.isNotEmpty(dialogEdt!!.text.toString())) {
                //不能含有表情
                if (EtUtils.containsEmoji(dialogEdt!!.text.toString())) {
                    val builder = ScanProCommonPopup.Builder(this@FileActivity, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
                    builder.setTitle(resources.getString(R.string.prompt))
                    builder.setMessage(resources.getString(R.string.nickname_toast_symbol))
                    builder.setOnPositiveListener { dialog1, _ -> dialog1.dismiss() }
                    val commonPopup = builder.create()
                    commonPopup.show()
                } else {
                    //文件名字重复添加括号
                    var i = 0
                    val pdfName = dialogEdt!!.text.toString()
                    var finalName = pdfName
                    var sameNamePdf: PdfEntity? = realm!!.where(PdfEntity::class.java)
                            .equalTo("pdfName", pdfName)
                            .findFirst()
                    while (Validator.isNotEmpty(sameNamePdf)) {
                        i++
                        finalName = pdfName + String.format(getString(R.string.tag_counts), i.toString() + "")
                        sameNamePdf = realm!!.where(PdfEntity::class.java)
                                .equalTo("pdfName", finalName)
                                .findFirst()
                    }
                    generatePdf(finalName)
                    resetStatus()
                    dialog.dismiss()
                }

            } else {
                showMessage(R.string.pdf_name_length_toast)
            }
        }

        builder.setOnNegativeListener { dialog, which -> dialog.dismiss() }
        commonPopup = builder.create()
        dialogEdt = commonPopup!!.window?.findViewById(R.id.create_pdf_edt) as EditText
        if (ActivityUtils.getTopActivity() is FileActivity && !isFinishing) {
            commonPopup!!.show()
        }

    }

    private fun showMoreDialog() {
        if (isActive) {
            fileBottomDialogPopup!!.show()
        }

    }

    private fun showShareDialog() {
        if (userPreferences!!.isVip) {
            if (isActive) {
                shareBottomDialog!!.show()
            }
        } else {
            share()
        }

    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event!!.keyCode == KeyEvent.KEYCODE_BACK && !canBack) {
            //do something.

            return true
        } else {
            return super.dispatchKeyEvent(event);
        }
    }


    private fun getOssClient() {
        HttpManager.getInstance().request().getOssInfo(userPreferences!!.imei.substring(0, 18), OssModel::class.java, object : MiaoHttpManager.CallbackNetwork<OssModel> {
            override fun onNoNetwork() {
                canBack = true
                hideProgressDialog()
            }

            override fun onStart() {
                canBack = false

                showProgressDialog(cancelable = false, touchable = false, tag = "", isDark = false)
            }

            override fun onResponse(entity: MiaoHttpEntity<OssModel>) {

                val credentialProvider = OSSStsTokenCredentialProvider(entity.body.accessKeyId,
                        entity.body.accessKeySecret,
                        entity.body.securityToken)
                val conf = ClientConfiguration()
                // 连接超时，默认15秒
                conf.connectionTimeout = 15 * 1000
                // socket超时，默认15秒
                conf.socketTimeout = 15 * 1000
                // 最大并发请求数，默认5个
                conf.maxConcurrentRequest = 5
                // 失败后最大重试次数，默认2次
                conf.maxErrorRetry = 2
                val ossClient = OSSClient(this@FileActivity, userPreferences!!.endpoint, credentialProvider, conf)
                Thread(Runnable {
                    if (doesOssHasFile(ossClient)) {
                        runOnUiThread {
                            hideProgressDialog()
                            var intent = Intent(this@FileActivity, ShareActivity::class.java)
                            intent.putExtra("sharePaths", sharePaths)
                            ActivityUtils.startActivity(intent)
                        }

                    } else {
                        runOnUiThread {
                            hideProgressDialog()
                            cancelEvent()
                            showMessage(R.string.share_defeat)
                        }
                    }
                }).start()

            }

            override fun onFailure(entity: MiaoHttpEntity<OssModel>) {
                canBack = true

                hideProgressDialog()
                showMessage(R.string.request_failed_alert)

            }

            override fun onError(e: Exception) {
                canBack = true

                hideProgressDialog()
                showMessage(R.string.request_failed_alert)

            }
        });

    }

    private fun doesOssHasFile(ossClient: OSSClient): Boolean {
        val realm = Realm.getDefaultInstance()

        sharePaths = arrayListOf<String>()
        var isSuccess = true
        realm!!.executeTransaction(Realm.Transaction {
            for (id in isCheckedMap!!.keys) {
                //先查找后得到PageEntity对象
                val page = realm!!.where(DocEntity::class.java)
                        .equalTo("fileID", id)
                        .equalTo("isDelete", 0.toInt())
                        .findFirst()
                val path = page!!.fileID + "_" + page.uuid + Constants.BASE_JPG
                val ossPath = userPreferences!!.userId + File.separator + path
                val sharePath = "share/$ossPath"
                sharePaths!!.add(sharePath)
                try {
                    if (ossClient.doesObjectExist(Constants.BUCKET, sharePath)) {
                        LogUtils.d("doesObjectExist", "object exist.", sharePath);
                    } else {
                        LogUtils.d("doesObjectExist", "object does not exist.", sharePath);

                        val uploadFiles = uploadFiles(page.baseImagePath!!, sharePath, ossClient)
                        if (!uploadFiles) isSuccess = false
                    }

                } catch (e: ClientException) {
                    isSuccess = false
                    // 本地异常如网络异常等
                    LogUtils.e(e)
                } catch (e: ServiceException) {
                    isSuccess = false
                    // 服务异常
                    LogUtils.e("ErrorCode", e.errorCode);
                    LogUtils.e("RequestId", e.requestId);
                    LogUtils.e("HostId", e.hostId);
                    LogUtils.e("RawMessage", e.rawMessage);
                }
            }
        })
        return isSuccess

    }

    private fun uploadFiles(sourcePath: String, sharePath: String, ossClient: OSSClient): Boolean {
        var isUploadSuccess = false
        LogUtils.i("upload", sourcePath, sharePath)
        // 构造上传请求
        val put = PutObjectRequest(Constants.BUCKET, sharePath, sourcePath)

        try {
            val putResult = ossClient.putObject(put)
            isUploadSuccess = true
            LogUtils.i("PutObject: UploadSuccess", "ETag :" + putResult.eTag, "RequestId: " + putResult.getRequestId())
        } catch (e: ClientException) {
            isUploadSuccess = false
            // 本地异常如网络异常等
            e.printStackTrace()
        } catch (e: ServiceException) {
            isUploadSuccess = false
            // 服务异常
            LogUtils.e("RequestId", e.requestId)
            LogUtils.e("ErrorCode", e.errorCode)
            LogUtils.e("HostId", e.hostId)
            LogUtils.e("RawMessage", e.rawMessage)
        }

        return isUploadSuccess
    }


    private val onBottomSheetClickListener1 = ShareBottomDialogPopup.Builder.OnBottomSheetClickListener { viewId ->
        when (viewId) {
            R.id.share_system_btn -> {
                share()
                shareBottomDialog!!.dismiss()
            }
            R.id.share_vip_btn -> {
                getOssClient()
                shareBottomDialog!!.dismiss()
            }

            R.id.share_cancel_btn -> shareBottomDialog!!.dismiss()
            else -> {
            }
        }
    }

    private fun createBottomSheetDialog() {
        val builder = FileBottomDialogPopup.Builder(this@FileActivity, isMove, onBottomSheetClickListener)
        fileBottomDialogPopup = builder.create()
        val builder1 = ShareBottomDialogPopup.Builder(this@FileActivity, onBottomSheetClickListener1)
        shareBottomDialog = builder1.create()
    }

    /**
     * @des: 切换模式dialog监听
     * @params:
     * @return:
     */
    private val onBottomSheetClickListener = FileBottomDialogPopup.Builder.OnBottomSheetClickListener { viewId ->
        when (viewId) {
            R.id.file_bottom_sheet_delete_btn -> {
                showDeleteDialog()
                fileBottomDialogPopup!!.dismiss()
            }
            R.id.file_bottom_sheet_save_btn -> {
                saveToAlbum()
                fileBottomDialogPopup!!.dismiss()
            }
            R.id.file_bottom_sheet_move_btn -> {
                move()
                fileBottomDialogPopup!!.dismiss()
            }
            R.id.file_bottom_sheet_cancel_btn -> fileBottomDialogPopup!!.dismiss()
            else -> {
            }
        }
    }


    private fun fileSetting() {
        val intent = Intent(this@FileActivity, CategorySettingActivity::class.java)
        intent.putExtra("categoryName", categoryName)
        ActivityUtils.startActivity(intent)

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {
            //登录注册
            EventType.CHANGE_CATEGORY_NAME -> {
                if (event is ChangeCategoryNameEvent) {
                    val changeCategoryNameEvent = event as ChangeCategoryNameEvent
                    LogUtils.e(changeCategoryNameEvent.categoryName)
                    fileEntities = if (isValidatorUser())
                        realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("categoryName", changeCategoryNameEvent.categoryName).notEqualTo("userID", Constants.NO_USER).sort("createTime", Sort.DESCENDING).findAll()
                    else
                        realm!!.where(DocEntity::class.java).equalTo("isTemp", 0.toInt()).equalTo("isDelete", 0.toInt()).equalTo("categoryName", changeCategoryNameEvent.categoryName).equalTo("userID", Constants.NO_USER).sort("createTime", Sort.DESCENDING).findAll()

                    mainFileAdapter!!.refreshData(fileEntities)
                    binding.fileTitleTv.text = changeCategoryNameEvent.categoryName
                    categoryName = changeCategoryNameEvent.categoryName
                }
            }
            EventType.ADD_FILES -> {
                val fileEvent = event as FileEvent
                getSimpleFiles()
                isShowEmptyPrompt()
                if (fileEntities.isNotEmpty()) {
                    mainFileAdapter!!.refreshData(fileEntities, true, fileEvent.count)
                }
            }
            EventType.ROTATE, EventType.COLOR, EventType.CUT, EventType.ADJUST -> {
                mainFileAdapter!!.refreshData(fileEntities)

            }
            EventType.SHARE, EventType.ADD_TAG, EventType.ADD_TAGS -> {
                cancelEvent()
            }
            else -> {
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        realm!!.executeTransaction { realm ->
            for (docEntity in realm.where(DocEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("isNameTemp", 1.toInt()).findAll()) {
                docEntity.isNameTemp = 0
            }
            for (categoryEntity in realm.where(CategoryEntity::class.java).equalTo("isDelete", 0.toInt()).equalTo("isNameTemp", 1.toInt()).findAll()) {
                categoryEntity.isNameTemp = 0
            }
        }
    }

    private fun showAll() {
        showSave()
        showDelete()
        showShare()
        showPDF()
        showMore()
        showTag()
        showCancelTag()
    }

    private fun darkAll() {
        darkDelete()
        darkSave()
        darkPDF()
        darkMore()
        darkShare()
        darkTag()
        darkCancelTag()

    }

    private fun showPDF() {
        binding.filePdfRl.isClickable = true
        binding.filePdfRl.isEnabled = true

        binding.filePdfImg.isSelected = true
        binding.filePdfTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkSave() {
        binding.fileSaveRl.isClickable = false
        binding.fileSaveRl.isEnabled = false

        binding.fileSaveImg.isSelected = false
        binding.fileSaveTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun showSave() {
        binding.fileSaveRl.isClickable = true
        binding.fileSaveRl.isEnabled = true

        binding.fileSaveImg.isSelected = true
        binding.fileSaveTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkDelete() {
       binding.fileDeleteRl.isClickable = false
       binding.fileDeleteRl.isEnabled = false

        binding.fileDeleteImg.isSelected = false
        binding.fileDeleteTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun showDelete() {
        binding.fileDeleteRl.isClickable = true
        binding.fileDeleteRl.isEnabled = true

        binding.fileDeleteImg.isSelected = true
        binding.fileDeleteTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkPDF() {
        binding.filePdfRl.isClickable = false
        binding.filePdfRl.isEnabled = false

        binding.filePdfImg.isSelected = false
        binding.filePdfTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun showMore() {
        binding.fileMoreRl.isClickable = true
        binding.fileMoreRl.isEnabled = true

        binding.fileMoreImg.isSelected = true
        binding.fileMoreTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkMore() {
        binding.fileMoreRl.isClickable = false
        binding.fileMoreRl.isEnabled = false


        binding.fileMoreImg.isSelected = false
        binding.fileMoreTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun showShare() {
        binding.fileShareRl.isClickable = true
        binding.fileShareRl.isEnabled = true


        binding.fileShareImg.isSelected = true
        binding.fileShareTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkShare() {
        binding.fileShareRl.isClickable = false
        binding.fileShareRl.isEnabled = false

        binding.fileShareImg.isSelected = false
        binding.fileShareTv.setTextColor(resources.getColor(R.color.dark_text))
    }


    private fun showTag() {
        binding.fileTagRl.isClickable = true
        binding.fileTagRl.isEnabled = true


        binding.fileTagImg.isSelected = true
        binding.fileTagTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkTag() {
        binding.fileTagRl.isClickable = false
        binding.fileTagRl.isEnabled = false

        binding.fileTagImg.isSelected = false
        binding.fileTagTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    private fun showCancelTag() {
        binding.cancelFileTagRl.isClickable = true
        binding.cancelFileTagRl.isEnabled = true
        binding.cancelFileTagImg.isSelected = true
        binding.cancelFileTagTv.setTextColor(resources.getColor(R.color.white))
    }

    private fun darkCancelTag() {
        binding.cancelFileTagRl.isClickable = false
        binding.cancelFileTagRl.isEnabled = false
        binding.cancelFileTagImg.isSelected = false
        binding.cancelFileTagTv.setTextColor(resources.getColor(R.color.dark_text))
    }

    /**
     * @des: 申请权限并且打开相机页
     * @params:
     * @return:
     */

    private fun openCamera(cameraType: Int) {
        LogUtils.i("FileActivity.openCamera.cameraType=${cameraType}")
        PermissionUtils.permission(PermissionUtil.getStoragePermission()[0],PermissionUtil.getStoragePermission()[1], PermissionConstants.CAMERA)
                .rationale { _, shouldRequest ->
                    showMessage(R.string.denied_camera)
                    shouldRequest.again(true)
                }
                .callback(object : PermissionUtils.FullCallback {
                    override fun onGranted(permissionsGranted: List<String>) {
                        LogUtils.i(permissionsGranted)
                        if (cameraType == 2) {
                            val intent = Intent(this@FileActivity, CardCameraActivity::class.java)
                            intent.putExtra("type", if (isTag) 2 else 1)
                            intent.putExtra("isTag", isTag)
                            if (isTag) {
                                intent.putExtra("tagName", tagName)
                                intent.putExtra("tagId", tagId)
                            } else {
                                intent.putExtra("categoryID", categoryID)
                                intent.putExtra("categoryName", categoryName)
                            }
                            ActivityUtils.startActivity(intent)
                        } else {
                            val intent = Intent(this@FileActivity, CameraActivity::class.java)
                            intent.putExtra("isSingle", cameraType == 0)
                            intent.putExtra("type", if (isTag) 2 else 1)
                            intent.putExtra("isTag", isTag)
                            if (isTag) {
                                intent.putExtra("tagName", tagName)
                                intent.putExtra("tagId", tagId)
                            } else {
                                intent.putExtra("categoryID", categoryID)
                                intent.putExtra("categoryName", categoryName)
                            }
                            ActivityUtils.startActivity(intent)
                        }
                    }

                    override fun onDenied(permissionsDeniedForever: List<String>,
                                          permissionsDenied: List<String>) {
                        showMessage(R.string.denied_camera)
                        LogUtils.i(permissionsDeniedForever, permissionsDenied)
                    }
                })
                .theme { activity -> ScreenUtils.setFullScreen(activity) }
                .request()
    }


}







