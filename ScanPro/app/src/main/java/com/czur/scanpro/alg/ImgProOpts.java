package com.czur.scanpro.alg;

public class ImgProOpts {
    ImgProOpts()
    {
        nMinHoleRadius = 10;			// default = 10;
        nMaxHoleRadius = 300;			// default = 300;
        nAdjustGrade = 0;			//梯度调节value
        nTypePB = 1;				//1--->国内红章;2--->海外蓝章
        nResolutionW = 0;			//0--->默认分辨率;其他：自定义分辨率
        nTypeColorConvert = 1;		//色彩模式转换类型:1-->彩色模式;2-->证件底纹;3-->白纸印章;4-->灰度模式;5-->黑白模式;6-->古籍模式;7-->无优化;8-->自动优化(通用底色净化)
        nTypeFingerRemove = 0;		//去手指类型：0--->不去手指;1--->普通去手指;2--->快速去手指;3--->无指套去手指
        nJpegEncodeQuality = 70;		//图像压缩质量
        nModeUnfold = 1;			//展平速度模式：1--->普通模式;2--->TBB快速模式
        wExtendPix = 0;				//处理结果边界自动调整宽度
        hExtendPix = 0;				//处理结果边界自动调整高度
        nCornerWhiteByCut = 0;		//单页裁剪圆角留白：0--->保留黑底方角;1--->圆角留白
        nTypeFingerGlove = 0;		//指套种类：0--->普通指套；1--->指压件
        isRotateByOrientation = 0;	//1--->开启基于文字方向的自动旋转
        isPageFilling = 0;			//1--->开启页面内容补全填充
        nHalfScanCentLine = 0;		//半页扫描中缝指导线位置
        nHalfPage2Reserve = 0;		//半页扫描指定要保留的页面：0--->自动识别;1--->保留左页;2---->保留右页
        nAddMarginWidth = 0;		//曲面展平后是需要添加的边距宽度(单位像素):0--->无需添加;其他--->像素宽度
        nModeWhitePageSeal = 0;		//白纸印章处理模式：0--->标准模式(适用旧版固件);1--->快速模式(适用20190107IQ优化后的FW)
        nDeviceType =0; 	//设备型号：0--->ET及M系列;1--->Aura 14系列;2--->高拍仪系列;3--->Aura 16系列(Max/Plus)
    }

    public int getnMinHoleRadius() {
        return nMinHoleRadius;
    }

    public void setnMinHoleRadius(int nMinHoleRadius) {
        this.nMinHoleRadius = nMinHoleRadius;
    }

    public int getnMaxHoleRadius() {
        return nMaxHoleRadius;
    }

    public void setnMaxHoleRadius(int nMaxHoleRadius) {
        this.nMaxHoleRadius = nMaxHoleRadius;
    }

    public int getnAdjustGrade() {
        return nAdjustGrade;
    }

    public void setnAdjustGrade(int nAdjustGrade) {
        this.nAdjustGrade = nAdjustGrade;
    }

    public int getnTypePB() {
        return nTypePB;
    }

    public void setnTypePB(int nTypePB) {
        this.nTypePB = nTypePB;
    }

    public int getnResolutionW() {
        return nResolutionW;
    }

    public void setnResolutionW(int nResolutionW) {
        this.nResolutionW = nResolutionW;
    }

    public int getnTypeColorConvert() {
        return nTypeColorConvert;
    }

    public void setnTypeColorConvert(int nTypeColorConvert) {
        this.nTypeColorConvert = nTypeColorConvert;
    }

    public int getnTypeFingerRemove() {
        return nTypeFingerRemove;
    }

    public void setnTypeFingerRemove(int nTypeFingerRemove) {
        this.nTypeFingerRemove = nTypeFingerRemove;
    }

    public int getnJpegEncodeQuality() {
        return nJpegEncodeQuality;
    }

    public void setnJpegEncodeQuality(int nJpegEncodeQuality) {
        this.nJpegEncodeQuality = nJpegEncodeQuality;
    }

    public int getnModeUnfold() {
        return nModeUnfold;
    }

    public void setnModeUnfold(int nModeUnfold) {
        this.nModeUnfold = nModeUnfold;
    }

    public int getwExtendPix() {
        return wExtendPix;
    }

    public void setwExtendPix(int wExtendPix) {
        this.wExtendPix = wExtendPix;
    }

    public int gethExtendPix() {
        return hExtendPix;
    }

    public void sethExtendPix(int hExtendPix) {
        this.hExtendPix = hExtendPix;
    }

    public int getnCornerWhiteByCut() {
        return nCornerWhiteByCut;
    }

    public void setnCornerWhiteByCut(int nCornerWhiteByCut) {
        this.nCornerWhiteByCut = nCornerWhiteByCut;
    }

    public int getnTypeFingerGlove() {
        return nTypeFingerGlove;
    }

    public void setnTypeFingerGlove(int nTypeFingerGlove) {
        this.nTypeFingerGlove = nTypeFingerGlove;
    }

    public int getIsRotateByOrientation() {
        return isRotateByOrientation;
    }

    public void setIsRotateByOrientation(int isRotateByOrientation) {
        this.isRotateByOrientation = isRotateByOrientation;
    }

    public int getIsPageFilling() {
        return isPageFilling;
    }

    public void setIsPageFilling(int isPageFilling) {
        this.isPageFilling = isPageFilling;
    }

    public int getnHalfScanCentLine() {
        return nHalfScanCentLine;
    }

    public void setnHalfScanCentLine(int nHalfScanCentLine) {
        this.nHalfScanCentLine = nHalfScanCentLine;
    }

    public int getnHalfPage2Reserve() {
        return nHalfPage2Reserve;
    }

    public void setnHalfPage2Reserve(int nHalfPage2Reserve) {
        this.nHalfPage2Reserve = nHalfPage2Reserve;
    }

    public int getnAddMarginWidth() {
        return nAddMarginWidth;
    }

    public void setnAddMarginWidth(int nAddMarginWidth) {
        this.nAddMarginWidth = nAddMarginWidth;
    }

    public int getnModeWhitePageSeal() {
        return nModeWhitePageSeal;
    }

    public void setnModeWhitePageSeal(int nModeWhitePageSeal) {
        this.nModeWhitePageSeal = nModeWhitePageSeal;
    }

    public int getnDeviceType() {
        return nDeviceType;
    }

    public void setnDeviceType(int nDeviceType) {
        this.nDeviceType = nDeviceType;
    }

    private int				nMinHoleRadius;			// default = 10;
    private int				nMaxHoleRadius;			// default = 300;
    private int				nAdjustGrade;			//梯度调节value
    private int				nTypePB;				//1--->国内红章;2--->海外蓝章
    private int				nResolutionW;			//0--->默认分辨率;其他：自定义分辨率
    private int				nTypeColorConvert;		//色彩模式转换类型:1-->彩色模式;2-->证件底纹;3-->白纸印章;4-->灰度模式;5-->黑白模式;6-->古籍模式;7-->无优化;8-->自动优化(通用底色净化)
    private int				nTypeFingerRemove;		//去手指类型：0--->不去手指;1--->普通去手指;2--->快速去手指;3--->无指套去手指
    private int				nJpegEncodeQuality;		//图像压缩质量
    private int				nModeUnfold;			//展平速度模式：1--->普通模式;2--->TBB快速模式
    private int				wExtendPix;				//处理结果边界自动调整宽度
    private int				hExtendPix;				//处理结果边界自动调整高度
    private int				nCornerWhiteByCut;		//单页裁剪圆角留白：0--->保留黑底方角;1--->圆角留白
    private int				nTypeFingerGlove;		//指套种类：0--->普通指套；1--->指压件
    private int				isRotateByOrientation;	//1--->开启基于文字方向的自动旋转
    private int				isPageFilling;			//1--->开启页面内容补全填充
    private int				nHalfScanCentLine;		//半页扫描中缝指导线位置
    private int				nHalfPage2Reserve;		//半页扫描指定要保留的页面：0--->自动识别;1--->保留左页;2---->保留右页
    private int				nAddMarginWidth;		//曲面展平后是需要添加的边距宽度(单位像素):0--->无需添加;其他--->像素宽度
    private int				nModeWhitePageSeal;		//白纸印章处理模式：0--->标准模式(适用旧版固件);1--->快速模式(适用20190107IQ优化后的FW)
    private int				nDeviceType;			//设备型号：0--->ET及M系列;1--->Aura 14系列;2--->高拍仪系列;3--->Aura 16系列(Max/Plus)  
}
