package com.czur.scanpro.entity.model;

import java.util.List;

public class VipDifferenceModel {


    /**
     * VIPVSGeneral : [{"col1":"功能","col2":"免费用户","col3":"高级VIP"},{"col1":"云存储空间","col2":"500MB","col3":"10G"},{"col1":"生成PDF次数","col2":"6次/天","col3":"无限制"},{"col1":"文字识别","col2":"6次/天","col3":"无限制"},{"col1":"分享加密","col2":"-","col3":"√"},{"col1":"云识别","col2":"30次","col3":"100次/月"}]
     * payTitle : ["10G","识别无制","PD限制","手写识别500次/月","分享加密","云识别100次/月"]
     * invitationCodeResult : {"title":"您已获得高级功能","daysSuffix":"天","vipFunction":["无限制文字/证件识别次数","分享加密,保护隐私","无限制生成PDF次数","每月100次云识别","每月50次手写识别"],"buttonText":"您已获得高级功能","promptText":"每推荐1位好友获1个月高级功能（最多6个月）"}
     * version : 3
     */

    private InvitationCodeResultBean invitationCodeResult;
    private int version;
    private List<VIPVSGeneralBean> VIPVSGeneral;
    private List<String> payTitle;

    public InvitationCodeResultBean getInvitationCodeResult() {
        return invitationCodeResult;
    }

    public void setInvitationCodeResult(InvitationCodeResultBean invitationCodeResult) {
        this.invitationCodeResult = invitationCodeResult;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public List<VIPVSGeneralBean> getVIPVSGeneral() {
        return VIPVSGeneral;
    }

    public void setVIPVSGeneral(List<VIPVSGeneralBean> VIPVSGeneral) {
        this.VIPVSGeneral = VIPVSGeneral;
    }

    public List<String> getPayTitle() {
        return payTitle;
    }

    public void setPayTitle(List<String> payTitle) {
        this.payTitle = payTitle;
    }

    public static class InvitationCodeResultBean {
        /**
         * title : 您已获得高级功能
         * daysSuffix : 天
         * vipFunction : ["无限制文字/证件识别次数","分享加密,保护隐私","无限制生成PDF次数","每月100次云识别","每月50次手写识别"]
         * buttonText : 您已获得高级功能
         * promptText : 每推荐1位好友获1个月高级功能（最多6个月）
         */

        private String title;
        private String daysSuffix;
        private String buttonText;
        private String promptText;
        private List<String> vipFunction;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDaysSuffix() {
            return daysSuffix;
        }

        public void setDaysSuffix(String daysSuffix) {
            this.daysSuffix = daysSuffix;
        }

        public String getButtonText() {
            return buttonText;
        }

        public void setButtonText(String buttonText) {
            this.buttonText = buttonText;
        }

        public String getPromptText() {
            return promptText;
        }

        public void setPromptText(String promptText) {
            this.promptText = promptText;
        }

        public List<String> getVipFunction() {
            return vipFunction;
        }

        public void setVipFunction(List<String> vipFunction) {
            this.vipFunction = vipFunction;
        }
    }

    public static class VIPVSGeneralBean {
        /**
         * col1 : 功能
         * col2 : 免费用户
         * col3 : 高级VIP
         */

        private String col1;
        private String col2;
        private String col3;

        public String getCol1() {
            return col1;
        }

        public void setCol1(String col1) {
            this.col1 = col1;
        }

        public String getCol2() {
            return col2;
        }

        public void setCol2(String col2) {
            this.col2 = col2;
        }

        public String getCol3() {
            return col3;
        }

        public void setCol3(String col3) {
            this.col3 = col3;
        }
    }
}
