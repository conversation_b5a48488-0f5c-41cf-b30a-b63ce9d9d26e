package com.czur.scanpro.entity.model;

public class ProductModel {


    /**
     * id : 3
     * productId : com.czur.ScanPro_subscription_produce_0
     * name : 1个月
     * type : 1
     * showPrice : null
     * price : 18.0
     * duration : 31
     * durationUnit : d
     * bundleId : com.czur.ScanPro
     * os : ios
     * createTime : 1546065772000
     * updateTime : 1546065772000
     * delete : false
     * vipType:1
     */

    private int id;
    private String productId;
    private String name;
    private int type;
    private double showPrice;
    private double price;
    private int duration;
    private String durationUnit;
    private String bundleId;
    private String os;
    private String showMonthPrice;
    private long createTime;
    private long updateTime;
    private boolean delete;
    private boolean isSelect;

    public int getVipType() {
        return vipType;
    }

    public void setVipType(int vipType) {
        this.vipType = vipType;
    }

    private int vipType;



    public String getShowMonthPrice() {
        return showMonthPrice;
    }

    public void setShowMonthPrice(String showMonthPrice) {
        this.showMonthPrice = showMonthPrice;
    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public double getShowPrice() {
        return showPrice;
    }

    public void setShowPrice(double showPrice) {
        this.showPrice = showPrice;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getDurationUnit() {
        return durationUnit;
    }

    public void setDurationUnit(String durationUnit) {
        this.durationUnit = durationUnit;
    }

    public String getBundleId() {
        return bundleId;
    }

    public void setBundleId(String bundleId) {
        this.bundleId = bundleId;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isDelete() {
        return delete;
    }

    public void setDelete(boolean delete) {
        this.delete = delete;
    }
    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

}
