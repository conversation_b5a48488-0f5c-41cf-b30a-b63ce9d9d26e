package com.czur.scanpro.event;

public enum EventType {
    //登录、注册、修改密码
    LOG_IN,
    THIRD_PARTY_LOGIN_IN,
    REGISTER_SUCCESS,
    THIRD_PARTY_REGISTER_SUCCESS,
    THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
    RESET_PASSWORD,

    //登出
    TOKEN_TIME_OUT_TO_LOGIN,
    THIRD_TOKEN_TIME_OUT_TO_LOGIN,
    LOG_OUT,

    //个人中心
    BIND_PHONE,
    CHANGE_PHONE,
    BIND_EMAIL,
    CHANGE_EMAIL,
    EDIT_USER_IMAGE,
    CHANGE_PASSWORD,
    USER_EDIT_NAME,
    UPDATE_CACHE,
    INPUT_INVITE_CODE,

    BECAME_VIP,
    //编辑
    ROTATE,
    ADJUST,
    CUT,
    COLOR,
    CARD_IS_FRONT,
    ADD_CATEGORY_HIDE,
    ADD_CATEGORY,
    DELETE_CATEGORY,
    ADD_FILES,
    DELETE_FILE,
    SHARE,
    ED<PERSON>_ADJUST_IN_CAMERA,


    //Tag
    ADD_TAG,
    ADD_TAGS,
    CHANGE_TAG,
    DELETE_TAG,
    DELETE_TAGS,
    //次数
    HANDWRITING_COUNT_REDUCE,
    OCR_COUNT_REDUCE,
    BAIDU_OCR_COUNT_REDUCE,
    PDF_COUNT_REDUCE,
    CARD_COUNT_REDUCE,

    CHANGE_CATEGORY_NAME,

    //更新
    HAS_NEW_VERSION,
    IS_LATEST_VERSION,
    FIRST_CLICK_NEW,


    //同步
    STOP_SYNC,
    SYNC_IS_STOP,
    SYNC_SPACE_IS_NOT_ENOUGH,
    SYNC_IS_FINISH,
    SYNC_FINISH_FRESH,
    SYNC_ANIM_FINISH,
    CATEGORY_OR_DOCS_CHANGED,
    IS_SYNCHRONIZING,
    STOP_SYNC_TIME_COUNT,
    RESET_TIME_COUNT,
    REMOVE_BOOK,

    // 支付
    WECHAT_PAY_CALLBACK,
    PAY_SUCCESS,

}

