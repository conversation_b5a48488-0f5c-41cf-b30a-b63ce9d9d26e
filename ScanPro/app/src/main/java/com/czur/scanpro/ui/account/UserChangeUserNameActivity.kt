package com.czur.scanpro.ui.account

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.View
import com.blankj.utilcode.util.*
import com.czur.scanpro.R
import com.czur.scanpro.common.Constants
import com.czur.scanpro.databinding.ActivityResetNameBinding
import com.czur.scanpro.databinding.ActivityUserChangeEmailBinding
import com.czur.scanpro.entity.model.UserSettingModel
import com.czur.scanpro.event.EventType
import com.czur.scanpro.event.UserInfoEvent
import com.czur.scanpro.network.HttpManager
import com.czur.scanpro.network.core.MiaoHttpEntity
import com.czur.scanpro.network.core.MiaoHttpManager
import com.czur.scanpro.preferences.UserPreferences
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.utils.EtUtils
import com.google.gson.Gson
import com.noober.background.drawable.DrawableCreator
import org.greenrobot.eventbus.EventBus

class UserChangeUserNameActivity : BaseActivity(), View.OnClickListener {
    private val binding: ActivityResetNameBinding by lazy{
        ActivityResetNameBinding.inflate(layoutInflater)
    }

    private var userPreferences: UserPreferences? = null
    private var httpManager: HttpManager? = null
    private var currentLoginTime: Long = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_reset_name)
        setContentView(binding.root)
        initComponent()
        registerEvent()
    }

    private fun initComponent() {


        binding.registerTopBar.normalTitle.setText(R.string.change_user_name)
        userPreferences = UserPreferences.getInstance(this)
        httpManager = HttpManager.getInstance()

    }

    private fun registerEvent() {


        binding.confirmBtn.setOnClickListener(this)
        binding.registerTopBar.normalBackBtn.setOnClickListener(this)
        binding.confirmBtn.isSelected = false
        binding.confirmBtn.isClickable = false

        binding.userNameEdt.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                if (s.length > 0) {
                    val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.black_24)).build()
                    binding.confirmBtn.background = drawable
                    binding.confirmBtn.isSelected = true
                    binding.confirmBtn.setTextColor(resources.getColor(R.color.red_de4d4d))
                    binding.confirmBtn.isClickable = true
                } else {
                    val drawable = DrawableCreator.Builder().setCornersRadius(SizeUtils.dp2px(36f).toFloat()).setSolidColor(resources.getColor(R.color.gray_e4)).build()
                    binding.confirmBtn.background = drawable
                    binding.confirmBtn.isSelected = false
                    binding.confirmBtn.setTextColor(resources.getColor(R.color.white))
                    binding.confirmBtn.isClickable = false
                }
            }

            override fun afterTextChanged(s: Editable) {

            }
        })

        binding.userNameEdt.setOnEditorActionListener { v, actionId, event -> event.keyCode == KeyEvent.KEYCODE_ENTER }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.confirmBtn -> {
                KeyboardUtils.hideSoftInput(this)
                saveNickname()
            }
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            else -> {
            }
        }
    }

    private fun saveNickname() {
        val nickName = binding.userNameEdt.text.toString()
        if (nickName == userPreferences!!.getUserName()) {
            showMessage(R.string.nickname_toast_same)
        } else if (EtUtils.containsEmoji(nickName)) {
            showMessage(R.string.nickname_toast_symbol)
        } else {
            currentLoginTime = System.currentTimeMillis()
            KeyboardUtils.hideSoftInput(this)
            httpManager!!.requestPassport().updateNickname(userPreferences!!.getIMEI(), Constants.SCAN_PRO,
                    userPreferences!!.getChannel(), userPreferences!!.getUserId(), userPreferences!!.getToken(),
                    userPreferences!!.getUserId(), nickName, UserSettingModel::class.java, object : MiaoHttpManager.Callback<UserSettingModel> {
                override fun onStart() {
                    showProgressDialog(false)
                }

                override fun onResponse(entity: MiaoHttpEntity<UserSettingModel>) {
                    LogUtils.e(Gson().toJson(entity))
                    userPreferences!!.setUserName(nickName)
                    EventBus.getDefault().post(UserInfoEvent(EventType.USER_EDIT_NAME))
                    showMessage(R.string.nickname_toast_update_success)
                    ActivityUtils.finishActivity(this@UserChangeUserNameActivity)

                }

                override fun onFailure(entity: MiaoHttpEntity<UserSettingModel>) {
                    showMessage(R.string.nickname_toast_update_fail)
                }

                override fun onError(e: Exception) {
                    showMessage(R.string.request_failed_alert)

                }
            })
        }
    }


}
