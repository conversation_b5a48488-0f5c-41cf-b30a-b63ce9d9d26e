package com.czur.scanpro.utils;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.DisplayCutout;
import android.view.View;
import android.view.Window;
import android.view.WindowInsets;
import android.view.WindowManager;

import com.czur.scanpro.common.Constants;

public class ScreenAdaptationUtils {

    public static boolean hasLiuHaiInVivo() {
        try {
            Class clazz = Class.forName("android.util.FtFeature");
            Method method = clazz.getDeclaredMethod("isFeatureSupport", int.class);
            return (Boolean) method.invoke(clazz, 0x00000020);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

/*    public static int isOldSmallPhone(Context context){
        DisplayMetrics metric =  context.getResources().getDisplayMetrics();
        int densityDpi = metric.densityDpi;  // 屏幕密度DPI（320/480/560,小米4的是480）
        Log.d("densityDpi","======="+densityDpi);
//        return densityDpi <= 240;
        return densityDpi;
    }*/

    public static boolean getPhoneModel(){
        String brand = android.os.Build.BRAND;//手机品牌
        String model = android.os.Build.MODEL;//手机型号
        String name = brand+model;
        switch (name){
            case Constants.GOOGLE_Pixel_2:
            case Constants.GOOGLE_Nexus_5:
            case Constants.GOOGLE_Nexus_6:
                Log.d("phoneModel","==case=="+name);
                return true;
            default:
                Log.d("phoneModel","==default=="+name);
                return false;
        }

    }
}
