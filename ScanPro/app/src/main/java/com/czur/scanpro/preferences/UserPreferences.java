package com.czur.scanpro.preferences;

import android.content.Context;

import com.czur.scanpro.R;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.entity.model.RegisterModel;
import com.czur.scanpro.entity.model.VipDifferenceModel;
import com.czur.scanpro.utils.validator.StringUtils;
import com.czur.scanpro.utils.validator.Validator;
import com.google.gson.Gson;


public class UserPreferences extends BasePreferences {

    private static final String PREF = UserPreferences.class.getSimpleName();
    //基础变量
    private static final String USER_INFO_PREF = "scan_pro_user_info_pref";
    private static final String IS_USER_LOGIN = "scan_pro_is_user_login";
    private static final String IS_AUTO_SYNC = "scan_pro_is_auto_sync";
    private static final String IS_SYNC_ONLY_WIFI = "scan_pro_sync_wifi";
    private static final String SYNC_TIME = "scan_pro_sync_time";
    private static final String IMEI = "scan_pro_imei";
    private static final String CHANNEL = "scan_pro_channel";
    private static final String ENDPOINT = "scan_pro_endpoint";
    private static final String UDID = "scan_pro_udid";
    private static final String LAST_USER_ID = "scan_pro_last_user_id";


    private static final String IS_THIRD_PARTY = "scan_pro_is_third_party";
    private static final String THIRD_PARTY_TOKEN = "scan_pro_third_token";
    private static final String THIRD_PARTY_REFRESH_TOKEN = "czur_cloud_third_refresh_token";
    private static final String THIRD_PARTY_OPENID = "scan_pro_third_openId";
    private static final String THIRD_PARTY_PLAT_NAME = "scan_pro_third_plat_name";
    private static final String SERVICE_PLAT_NAME = "scan_pro_service_plat_name";

    private static final String LOGIN_USER_NAME = "scan_pro_login_user_name";
    private static final String LOGIN_PASSWORD = "scan_pro_login_password";
    private static final String USAGES = "scan_pro_usages";
    private static final String USAGES_LIMIT = "scan_pro_usages_limit";
    private static final String PHOTO_OSS_KEY = "scan_pro_photo_oss_key";
    private static final String U_KEY = "scan_pro_u_key";

    private static final String APK_ID = "scan_pro_apk_id";
    private static final String PDF_PATH = "scan_pro_pdf_path";
    private static final String TEMP_PATH = "scan_pro_temp_path";
    private static final String SD_PATH = "scan_pro_sd_path";

    private static final String GO_ADVANCED_FIRST = "scan_pro_go_advanced_first";
    private static final String GO_INVITE_FIRST = "scan_pro_go_invite_first";
    private static final String INDEX_GUIDE_FIRST = "scan_pro_index_guide_first";
    private static final String ADJUST_SIMPLE_GUIDE_FIRST = "scan_pro_adjust_simple_guide_first";
    private static final String ADJUST_EDGE_GUIDE_FIRST = "scan_pro_adjust_edge_guide_first";

    private static final String IS_VIP = "scan_pro_is_vip";
    private static final String USER_TYPE = "scan_pro_user_type";
    private static final String VIP_END_ON = "scan_pro_vip_end";
    private static final String SVIP_END_ON = "scan_pro_svip_end";
    private static final String INVITE_IMAGE = "scan_pro_go_invite_image";
    private static final String INVITE_CODE = "scan_pro_go_invite_code";
    private static final String INVITE_COUNT = "scan_pro_invite_count";
    private static final String IS_INVITED = "scan_pro_is_invited";

    private static final String REMAIN_OCR = "scan_pro_remain_cor";
    private static final String REMAIN_VIP = "scan_pro_remain_vip";
    private static final String REMAIN_CLOUD_OCR = "scan_pro_remain_cloud";
    private static final String REMAIN_CARD = "scan_pro_remain_card";
    private static final String REMAIN_HANDWRITING = "scan_pro_remain_handwriting";
    private static final String REMAIN_PDF = "scan_pro_remain_PDF";

    private static final String IS_HANDWRITING_GUIDE = "scan_pro_is_hand_writing_guide";
    private static final String OCR_LANGUAGE = "scan_pro_ocr_language";
    private static final String IS_FIRST_CATEGORY_GUIDE = "scan_pro_is_first_category_guide";
    private static final String IS_FIRST_CLOUD_OCR_GUIDE = "scan_pro_is_first_cloud_ocr_guide";
    private static final String IS_BOOK_MODE_GUIDE = "scan_pro_is_book_mode_guide";
    private static final String IS_EDGE_MODE_GUIDE = "scan_pro_is_edge_mode_guide";
    private static final String IS_FIRST_PRIVACY_POLICY= "scan_pro_is_first_privacy_policy";
    private static final String IS_FIRST_FAST_MODE_TIP= "scan_pro_is_first_fast_mode_tip";
    private static final String IS_PUSH_ORIGINAL_PHOTO= "scan_pro_is_push_original_photo";



    private static final String PDF_TYPE = "scan_pro_pdf_size";
    private static final String PDF_IS_HORIZONTAL = "scan_pro_pdf_is_horizontal";
    private static final String PDF_QUALITY = "scan_pro_pdf_quality";

    private static final String AD_PLAY_RECORD = "scan_pro_ad_play_record";// 最后的广告播放时间(一天只放一次)



    private static UserPreferences instance;

    public static UserPreferences getInstance(Context context) {
        if (instance == null) {
            instance = new UserPreferences(context, PREF);
        }
        return instance;
    }

    public UserPreferences(Context context, String prefsName) {
        super(context, prefsName);
    }

    public void setUser(RegisterModel userData) {
        if (Validator.isNotEmpty(userData)) {
            String json = new Gson().toJson(userData);
            put(USER_INFO_PREF, json);
        }
    }

    public RegisterModel getUser() {
        String json = (String) get(USER_INFO_PREF);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return new Gson().fromJson(json, RegisterModel.class);
    }

    public void setIsUserLogin(boolean isLogin) {
        put(IS_USER_LOGIN, isLogin);
    }

    public boolean isUserLogin() {
        Object obj = get(IS_USER_LOGIN);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public boolean isValidUser() {
        RegisterModel userData = getUser();
        if (userData != null && Validator.isNotEmpty(userData.getId()) && !userData.getId().equals(Constants.NO_USER)) {
            return true;
        }
        return false;
    }

    public boolean isInValidUser() {
        return !isValidUser();
    }

    public void resetUser() {
        logOutToReset();
        setSyncTime(0);
    }


    public void logOutToReset() {
        put(USER_INFO_PREF, StringUtils.EMPTY);
        setIsThirdParty(false);
        setThirdPartyToken(StringUtils.EMPTY);
        setThirdPartyOpenid(StringUtils.EMPTY);
        setThirdPartyPlatName(StringUtils.EMPTY);
        setServicePlatName(StringUtils.EMPTY);

        setLoginPassword(StringUtils.EMPTY);
        setLoginUserName(StringUtils.EMPTY);
        setUserPhoto(StringUtils.EMPTY);
        setPhotoOssKey(StringUtils.EMPTY);
        setUkey(StringUtils.EMPTY);
        setIsSyncOnlyWifi(true);
        setIsAutoSync(true);
        setUsages(0);
        setUsagesLimit(0);
        setSyncTime(0);

        setRemainCard(0);
        setRemainCloudOcr(0);
        setRemainHandwriting(0);
        setRemainOcr(0);
        setRemainVip(0);
        setRemainPdf(0);
        setIsVip(false);


    }
    public String getUserId() {
        if (isValidUser()) {
            return getUser().getId();
        }
        return "";
    }

    public long getLongUserId() {
        if (isValidUser()) {
            return Long.parseLong(getUser().getId());
        }
        return 0;
    }

    public void setUserId(String id) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setId(id);
            setUser(userData);
        }
    }

    public String getUserName() {
        if (isValidUser()) {
            return getUser().getName();
        }
        return "";
    }

    public void setUserName(String name) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setName(name);
            setUser(userData);
        }
    }

    public String getUserMobile() {
        if (isValidUser()) {
            return getUser().getMobile();
        }
        return "";
    }

    public void setUserMobile(String mobile) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setMobile(mobile);
            setUser(userData);
        }
    }

    public String getUserEmail() {
        if (isValidUser()) {
            return getUser().getEmail();
        }
        return "";
    }

    public void setUserEmail(String email) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setEmail(email);
            setUser(userData);
        }
    }

    public String getUserPhoto() {
        if (isValidUser()) {
            return getUser().getPhoto();
        }
        return "";
    }

    public void setUserPhoto(String photo) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setPhoto(photo);
            setUser(userData);
        }
    }

    public boolean isUserActive() {
        if (isValidUser()) {
            return getUser().isActive();
        }
        return false;
    }

    public void setUserActive(boolean active) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setActive(active);
            setUser(userData);
        }
    }


    public void setToken(String token) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setToken(token);
            setUser(userData);
        }
    }

    public String getToken() {
        if (isValidUser()) {
            return getUser().getToken();
        }
        return "";
    }

    public void resetToken() {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setToken(StringUtils.EMPTY);
            setUser(userData);
        }
    }

    public void setLastUserId(String userId) {
        put(LAST_USER_ID, userId);
    }

    public String getLastUserId() {
        Object obj = get(LAST_USER_ID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setIMEI(String imei) {
        put(IMEI, imei);
    }

    public String getIMEI() {
        Object obj = get(IMEI);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setInviteCode(String imei) {
        put(INVITE_CODE, imei);
    }

    public String getInviteCode() {
        Object obj = get(INVITE_CODE);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setEndpoint(String endpoint) {
        put(ENDPOINT, endpoint);
    }

    public String getEndpoint() {
        Object obj = get(ENDPOINT);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setInviteImage(int resource) {
        put(INVITE_IMAGE, resource);
    }

    public int getInviteImage() {
        Object obj = get(INVITE_IMAGE);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setUsages(long usages) {
        put(USAGES, usages);
    }

    public long getUsages() {
        Object obj = get(USAGES);
        if (Validator.isEmpty(obj) || (long) obj < 0) {
            return 0;
        } else {
            return (long) obj;
        }
    }
    public void setRemainVip(int usages) {
        put(REMAIN_VIP, usages);
    }

    public int getRemainVip() {
        Object obj = get(REMAIN_VIP);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setRemainOcr(int usages) {
        put(REMAIN_OCR, usages);
    }

    public int getRemainOcr() {
        Object obj = get(REMAIN_OCR);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setRemainCard(int usages) {
        put(REMAIN_CARD, usages);
    }

    public int getRemainCard() {
        Object obj = get(REMAIN_CARD);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setRemainHandwriting(int usages) {
        put(REMAIN_HANDWRITING, usages);
    }

    public int getRemainHandwriting() {
        Object obj = get(REMAIN_HANDWRITING);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setRemainPdf(int usages) {
        put(REMAIN_PDF, usages);
    }

    public int getRemainPdf() {
        Object obj = get(REMAIN_PDF);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setRemainCloudOcr(int usages) {
        put(REMAIN_CLOUD_OCR, usages);
    }

    public int getRemainCloudOcr() {
        Object obj = get(REMAIN_CLOUD_OCR);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }


    public void setUsagesLimit(long usagesLimit) {
        put(USAGES_LIMIT, usagesLimit);
    }

    public long getUsagesLimit() {
        Object obj = get(USAGES_LIMIT);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setSyncTime(long syncTime) {
        put(SYNC_TIME, syncTime);
    }

    public long getSyncTime() {
        Object obj = get(SYNC_TIME);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setPhotoOssKey(String photoOssKey) {
        put(PHOTO_OSS_KEY, photoOssKey);
    }

    public String getPhotoOssKey() {
        Object obj = get(PHOTO_OSS_KEY);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setApkId(long apkId) {
        put(APK_ID, apkId);
    }

    public long getApkId() {
        Object obj = get(APK_ID);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setGoAdvancedFirst(boolean isLogin) {
        put(GO_ADVANCED_FIRST, isLogin);
    }

    public boolean getGoAdvancedFirst() {
        Object obj = get(GO_ADVANCED_FIRST);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setGoInviteFirst(boolean isLogin) {
        put(GO_INVITE_FIRST, isLogin);
    }

    public boolean getGoInviteFirst() {
        Object obj = get(GO_INVITE_FIRST);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setEdgeGuideFirst(boolean isLogin) {
        put(ADJUST_EDGE_GUIDE_FIRST, isLogin);
    }

    public boolean getEdgeGuideFirst() {
        Object obj = get(ADJUST_EDGE_GUIDE_FIRST);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }
    public void setSimpleGuideFirst(boolean isLogin) {
        put(ADJUST_SIMPLE_GUIDE_FIRST, isLogin);
    }

    public boolean getSimpleGuideFirst() {
        Object obj = get(ADJUST_SIMPLE_GUIDE_FIRST);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }
    public void setIndexGuideFirst(boolean isLogin) {
        put(INDEX_GUIDE_FIRST, isLogin);
    }

    public boolean getIndexGuideFirst() {
        Object obj = get(INDEX_GUIDE_FIRST);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setIsVip(boolean isLogin) {
        put(IS_VIP, isLogin);
    }

    public boolean getIsVip() {
        Object obj = get(IS_VIP);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setUserType(String userType) {
        put(USER_TYPE, userType);
    }


    public String getUserType() {
        Object obj = get(USER_TYPE);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }



    public String getVipEndOn() {
        Object obj = get(VIP_END_ON);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setSvipEndOn(String svipEndOn) {
        put(SVIP_END_ON, svipEndOn);
    }


    public String getSvipEndOn() {
        Object obj = get(SVIP_END_ON);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setVipEndOn(String vipEndOn) {
        put(VIP_END_ON, vipEndOn);
    }


    public void setIsThirdParty(boolean isThirdParty) {
        put(IS_THIRD_PARTY, isThirdParty);
    }

    public boolean isThirdParty() {
        Object obj = get(IS_THIRD_PARTY);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setThirdPartyToken(String thirdPartyToken) {
        put(THIRD_PARTY_TOKEN, thirdPartyToken);
    }

    public String getThirdPartyToken() {
        Object obj = get(THIRD_PARTY_TOKEN);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setThirdPartyRefreshToken(String thirdPartyRefreshToken) {
        put(THIRD_PARTY_REFRESH_TOKEN, thirdPartyRefreshToken);
    }

    public String getThirdPartyRefreshToken() {
        Object obj = get(THIRD_PARTY_REFRESH_TOKEN);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setThirdPartyOpenid(String thirdPartyOpenid) {
        put(THIRD_PARTY_OPENID, thirdPartyOpenid);
    }

    public String getThirdPartyOpenid() {
        Object obj = get(THIRD_PARTY_OPENID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setThirdPartyPlatName(String thirdPartyPlatName) {
        put(THIRD_PARTY_PLAT_NAME, thirdPartyPlatName);
    }

    public String getThirdPartyPlatName() {
        Object obj = get(THIRD_PARTY_PLAT_NAME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setServicePlatName(String thirdPartyPlatName) {
        put(SERVICE_PLAT_NAME, thirdPartyPlatName);
    }

    public String getServicePlatName() {
        Object obj = get(SERVICE_PLAT_NAME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setChannel(String channel) {
        put(CHANNEL, channel);
    }

    public String getChannel() {
        Object obj = get(CHANNEL);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setTempPath(String tempPath) {
        put(TEMP_PATH, tempPath);
    }

    public String getTempPath() {
        Object obj = get(TEMP_PATH);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setPdfPath(String pdfPath) {
        put(PDF_PATH, pdfPath);
    }

    public String getPdfPath() {
        Object obj = get(PDF_PATH);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setSdPath(String phoneRootPath) {
        put(SD_PATH, phoneRootPath);
    }

    public String getSdPath() {
        Object obj = get(SD_PATH);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setLoginUserName(String loginUserName) {
        put(LOGIN_USER_NAME, loginUserName);
    }

    public String getLoginUserName() {
        Object obj = get(LOGIN_USER_NAME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setLoginPassword(String loginPassword) {
        put(LOGIN_PASSWORD, loginPassword);
    }

    public String getLoginPassword() {
        Object obj = get(LOGIN_PASSWORD);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setUkey(String uKey) {
        put(U_KEY, uKey);
    }

    public String getUkey() {
        Object obj = get(U_KEY);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setIsInvited(boolean isInvited) {
        put(IS_INVITED, isInvited);
    }

    public boolean getIsInvited() {
        Object obj = get(IS_INVITED);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsAutoSync(boolean isAutoSync) {
        put(IS_AUTO_SYNC, isAutoSync);
    }

    public boolean getIsAutoSync() {
        Object obj = get(IS_AUTO_SYNC);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsSyncOnlyWifi(boolean isSyncOnlyWifi) {
        put(IS_SYNC_ONLY_WIFI, isSyncOnlyWifi);
    }

    public boolean getIsSyncOnlyWifi() {
        Object obj = get(IS_SYNC_ONLY_WIFI);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setUdid(String udid) {
        put(UDID, udid);
    }

    public String getUdid() {
        Object obj = get(UDID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setIsHandwritingGuide(boolean notFirst) {
        put(IS_HANDWRITING_GUIDE, notFirst);
    }

    public boolean isHandwritingGuide() {
        Object obj = get(IS_HANDWRITING_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setLastOcrViewId(int viewId) {
        put(OCR_LANGUAGE, viewId);
    }

    public int getLastOcrViewId() {
        Object obj = get(OCR_LANGUAGE);
        if (Validator.isEmpty(obj)) {
            return R.id.ocr_chinese_btn;
        } else {
            return (int) obj;
        }
    }

    public void setIsFirstCategoryGuide(boolean notFirst) {
        put(IS_FIRST_CATEGORY_GUIDE, notFirst);
    }

    public boolean isFirstCategoryrGuide() {
        Object obj = get(IS_FIRST_CATEGORY_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }


    public void setIsFirstCloudOcrGuide(boolean notFirst) {
        put(IS_FIRST_CLOUD_OCR_GUIDE, notFirst);
    }

    public boolean isFirstCloudOcrGuide() {
        Object obj = get(IS_FIRST_CLOUD_OCR_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsBookModeGuide(boolean notFirst) {
        put(IS_BOOK_MODE_GUIDE, notFirst);
    }

    public boolean isBookModeGuide() {
        Object obj = get(IS_BOOK_MODE_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsEdgeModeGuide(boolean notFirst) {
        put(IS_EDGE_MODE_GUIDE, notFirst);
    }

    public boolean isEdgeModeGuide() {
        Object obj = get(IS_EDGE_MODE_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }
    public void setIsFirstPrivacyPolicy(boolean notFirst) {
        put(IS_FIRST_PRIVACY_POLICY, notFirst);
    }

    public boolean isFirstPrivacyPolicy() {
        Object obj = get(IS_FIRST_PRIVACY_POLICY);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setPushOriPhoto(boolean ispush) {
        put(IS_PUSH_ORIGINAL_PHOTO, ispush);
    }

    public boolean isPushOriPhoto() {
        Object obj = get(IS_PUSH_ORIGINAL_PHOTO);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return false;
    }

    public void setIsFirstFastModeTip(boolean notFirst) {
        put(IS_FIRST_FAST_MODE_TIP, notFirst);
    }

    public boolean isFirstFastModeTip() {
        Object obj = get(IS_FIRST_FAST_MODE_TIP);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }


    public void setInviteCount(int pdfType) {
        put(INVITE_COUNT, pdfType);
    }

    public int getInviteCount() {
        Object obj = get(INVITE_COUNT);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setPdfType(int pdfType) {
        put(PDF_TYPE, pdfType);
    }

    public int getPdfType() {
        Object obj = get(PDF_TYPE);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setPdfIsHorizontal(int isHorizontal) {
        put(PDF_IS_HORIZONTAL, isHorizontal);
    }

    public int getPdfIsHorizontal() {
        Object obj = get(PDF_IS_HORIZONTAL);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setPdfQuality(int pdfQuality) {
        put(PDF_QUALITY, pdfQuality);
    }

    public int getPdfQuality() {
        Object obj = get(PDF_QUALITY);
        if (Validator.isEmpty(obj)) {
            return 1;
        } else {
            return (int) obj;
        }
    }

    public void setAdPlayRecord(String adPlayRecord) {
        put(AD_PLAY_RECORD, adPlayRecord);
    }

    public String getAdPlayRecord() {
        Object obj = get(AD_PLAY_RECORD);
        if (Validator.isEmpty(obj)) {
            return "1";
        } else {
            return (String) obj;
        }
    }


}
