package com.czur.scanpro.ui.file

import android.content.DialogInterface
import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.czur.scanpro.R
import com.czur.scanpro.adapter.EditTagAdapter
import com.czur.scanpro.entity.realm.DocEntity
import com.czur.scanpro.entity.realm.TagEntity
import com.czur.scanpro.event.ChangeTagEvent
import com.czur.scanpro.event.EventType
import com.czur.scanpro.ui.base.BaseActivity
import com.czur.scanpro.ui.component.popup.AddTagPopup
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants
import com.czur.scanpro.utils.EtUtils
import com.czur.scanpro.utils.validator.Validator
import io.realm.Realm
import io.realm.RealmResults
import io.realm.Sort
import org.greenrobot.eventbus.EventBus
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by Yz on 2019/3/26.
 * Email：<EMAIL>
 */
class EditTagActivity : BaseActivity(), View.OnClickListener {


    private var normalBackBtn: ImageView? = null
    private var normalTitle: TextView? = null
    private var tagRecyclerView: RecyclerView? = null
    private var cancelTagBtn: TextView? = null
    private var realm: Realm? = null
    private var tagAdapter: EditTagAdapter? = null
    private var dialogEdt: EditText? = null
    private var isPreview: Boolean = false
    private var fileId: String? = null
    private var tagEntities: RealmResults<TagEntity>? = null
    private var pageIds: ArrayList<String>? = null

    private val addTagListener = EditTagAdapter.AddTagListener { showAddTagDialog(true) }


    private val onTagItemClickListener = EditTagAdapter.OnTagItemClickListener { tagEntity, position ->
        realm!!.executeTransaction { realm ->
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
            val curDate = formatter.format(Date(System.currentTimeMillis()))
            if (isPreview) {
                val docEntity = realm.where(DocEntity::class.java).equalTo("fileID", <EMAIL>).findFirst()
                docEntity!!.isDirty = (if (docEntity.isDirty == 1) 1 else 2)
                docEntity.updateTime = curDate
                docEntity.tagId = tagEntity.tagId
                docEntity.tagName = tagEntity.tagName

                EventBus.getDefault().post(ChangeTagEvent(EventType.ADD_TAG))
                ActivityUtils.finishActivity(this@EditTagActivity)

            } else {
                //给多选的书页添加标签
                for (fileId in pageIds!!) {
                    val docEntity = realm.where(DocEntity::class.java).equalTo("fileID", fileId).findFirst()
                    docEntity!!.isDirty = (if (docEntity.isDirty == 1) 1 else 2)
                    docEntity.updateTime = curDate
                    docEntity.tagId = tagEntity.tagId
                    docEntity.tagName = tagEntity.tagName
                }
                EventBus.getDefault().post(ChangeTagEvent(EventType.ADD_TAGS))
                ActivityUtils.finishActivity(this@EditTagActivity)
            }
            startAutoSync()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gray_f9)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.activity_edit_tag)
        initComponent()
        registerEvent()
    }


    private fun initComponent() {
        realm = Realm.getDefaultInstance()
        normalBackBtn = findViewById(R.id.normalBackBtn)
        normalTitle = findViewById(R.id.normalTitle)
        tagRecyclerView = findViewById(R.id.tag_recyclerView)
        cancelTagBtn = findViewById(R.id.cancel_tag_btn)
        normalTitle!!.text = getString(R.string.choose_tag)
        isPreview = intent.getBooleanExtra("isPreview", false)
        fileId = intent.getStringExtra("fileId")
        pageIds = intent.getStringArrayListExtra("pageIds")
        refreshUI()
        tagRecyclerView!!.setHasFixedSize(true)
        tagRecyclerView!!.layoutManager =
            GridLayoutManager(this, 2)

    }


    private fun registerEvent() {
        normalBackBtn!!.setOnClickListener(this)
        cancelTagBtn!!.setOnClickListener(this)
    }

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private fun showAddTagDialog(isAdd: Boolean) {
        val builder = AddTagPopup.Builder(this@EditTagActivity, CloudCommonPopupConstants.CONFIRM_ONE_BUTTON)
        builder.setTitle(getString(R.string.create_tag))
        builder.setOnPositiveListener(DialogInterface.OnClickListener { dialog, _ ->
            realm!!.executeTransaction { realm ->
                val newTagName = dialogEdt!!.text.toString()
                if (!EtUtils.containsEmoji(newTagName)) {
                    if (Validator.isNotEmpty(newTagName)) {
                        if (realm.where(TagEntity::class.java).equalTo("tagName", newTagName).equalTo("isDelete", 0.toInt()).findFirst() == null) {
                            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                            val curDate = formatter.format(Date(System.currentTimeMillis()))
                            TagEntity().apply {
                                isSelf = 0
                                userID = getUserIdIsLogin()
                                tagId = UUID.randomUUID().toString()
                                tagName = newTagName
                                isDirty = 1
                                createTime = curDate
                                updateTime = curDate
                                realm.copyToRealmOrUpdate(this)
                            }
                            startAutoSync()
                        } else {
                            showMessage(R.string.tag_is_exist)
                        }

                    } else {
                        showMessage(R.string.tag_should_not_be_empty)
                    }

                } else {
                    showMessage(R.string.nickname_toast_symbol)
                }
            }
            refreshUI()
            dialog.dismiss()
        })
        val commonPopup = builder.create()
        dialogEdt = commonPopup.window?.findViewById(R.id.edt)
        commonPopup.show()
    }

    private fun refreshUI(){
        tagEntities = realm!!.where(TagEntity::class.java).equalTo("isDelete", 0.toInt()).distinct("tagName").equalTo("isDefault", 0.toInt()).equalTo("userID",getUserIdIsLogin()).sort("createTime", Sort.ASCENDING).findAll()
        realm!!.executeTransaction { realm ->
            if (tagEntities!!.size > 0) {
                if (isPreview) {
                    val docEntity = realm.where(DocEntity::class.java).equalTo("fileID", fileId).findFirst()
                    val tagId = docEntity!!.tagId
                    for (i in tagEntities!!.indices) {
                        if (tagEntities!![i]!!.tagId.equals(tagId)) {
                            tagEntities!![i]!!.isSelf = 1
                            cancelTagBtn!!.visibility = View.VISIBLE
                        } else {
                            tagEntities!![i]!!.isSelf = 0
                        }
                    }
                } else {
                    cancelTagBtn!!.visibility = View.VISIBLE
                    for (i in tagEntities!!.indices) {
                        tagEntities!![i]!!.isSelf = 0
                    }
                }
            }
        }
        tagAdapter = EditTagAdapter(this, tagEntities, false)
        tagRecyclerView!!.adapter = tagAdapter
        tagAdapter!!.setAddTagListener(addTagListener)
        tagAdapter!!.setOnTagItemClickListener(onTagItemClickListener)
    }
    override fun onClick(v: View) {
        when (v.id) {
            R.id.normalBackBtn -> ActivityUtils.finishActivity(this)
            R.id.cancel_tag_btn -> realm!!.executeTransaction { realm ->
                val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                val curDate = formatter.format(Date(System.currentTimeMillis()))
                if (isPreview) {
                    val docEntity = realm.where(DocEntity::class.java).equalTo("fileID", <EMAIL>).findFirst()
                    docEntity!!.updateTime = curDate
                    docEntity.isDirty = (if (docEntity!!.isDirty === 1) 1 else 2)
                    docEntity.tagId = ""
                    docEntity.tagName = ""
                    EventBus.getDefault().post(ChangeTagEvent(EventType.DELETE_TAG))
                } else {
                    for (pageId in pageIds!!) {
                        val docEntity = realm.where(DocEntity::class.java).equalTo("fileID", pageId).equalTo("isDelete", 0.toInt()).findFirst()
                        if (docEntity != null) {
                            docEntity.updateTime = curDate
                            docEntity.isDirty = (if (docEntity!!.isDirty === 1) 1 else 2)
                            docEntity.tagId = ""
                            docEntity.tagName = ""
                        }

                    }
                    EventBus.getDefault().post(ChangeTagEvent(EventType.DELETE_TAGS))
                }
                startAutoSync()
                ActivityUtils.finishActivity(this@EditTagActivity)
            }
            else -> {
            }
        }
    }


}
