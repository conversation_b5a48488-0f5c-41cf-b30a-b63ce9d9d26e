package com.czur.scanpro.ui.camera;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.hardware.Camera;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.Vibrator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.util.Log;
import android.view.KeyEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.scanpro.R;
import com.czur.scanpro.adapter.CameraPreviewAdapter;
import com.czur.scanpro.alg.Args;
import com.czur.scanpro.alg.CZSaoMiao_Alg;
import com.czur.scanpro.alg.Callbacks;
import com.czur.scanpro.common.Constants;
import com.czur.scanpro.entity.model.ModeEntity;
import com.czur.scanpro.entity.realm.CategoryEntity;
import com.czur.scanpro.entity.realm.DocEntity;
import com.czur.scanpro.event.BaseEvent;
import com.czur.scanpro.event.CategoryEvent;
import com.czur.scanpro.event.EventType;
import com.czur.scanpro.event.FileEvent;
import com.czur.scanpro.preferences.UserPreferences;
import com.czur.scanpro.ui.base.BaseActivity;
import com.czur.scanpro.ui.component.CameraGuideView;
import com.czur.scanpro.ui.component.popup.CloudCommonPopupConstants;
import com.czur.scanpro.ui.component.popup.ScanProCommonPopup;
import com.czur.scanpro.ui.file.FilePreviewActivity;
import com.czur.scanpro.ui.file.adjust.AdjustEdgeBookActivity;
import com.czur.scanpro.ui.file.adjust.AdjustGuideActivity;
import com.czur.scanpro.utils.DeviceLevelUtils;
import com.czur.scanpro.utils.NavBarUtils;
import com.czur.scanpro.utils.ScreenAdaptationUtils;
import com.czur.scanpro.utils.validator.Validator;
import com.facebook.drawee.view.SimpleDraweeView;
import com.jni.bitmap_operations.JniBitmapHolder;
import com.nshmura.snappysmoothscroller.SnapType;
import com.nshmura.snappysmoothscroller.SnappyLinearLayoutManager;
import com.sherlockshi.widget.BaseItemEntity;
import com.sherlockshi.widget.CenterSelectionTabLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

import io.realm.Realm;
import io.realm.RealmList;
import io.realm.RealmResults;
import io.realm.Sort;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class CameraActivity extends BaseActivity implements View.OnClickListener {

    private CameraPreview cameraPreview;
    private CameraBorderView cameraBorderView;
    private BlackShadeView cameraBlackShade;
    private Vibrator vibrator;
    private UserPreferences userPreferences;
    private TextView bottomToast;
    private View animView;
    private TextView cameraPromptTv;
    private ImageView previewNoIconBackBtn;
    private ImageView cameraFlashModeBtn;
    private ImageView flashBtn;
    private SimpleDraweeView cameraCutImg;
    private SimpleDraweeView cameraColorImg;
    private RelativeLayout cameraScanRl;
    private ImageView cameraFlashView;
    private RelativeLayout cameraFlashModeRl;
    private RecyclerView recyclerView;
    private CameraPreviewAdapter cameraPreviewAdapter;
    private ObjectAnimator hideShadeAndShowCutAnim;
    private ObjectAnimator blackShadeShowAnim;
    private Camera.Size size;
    private Bitmap cutBitmap;
    private Bitmap colorBitmap;
    private Handler handler = new Handler();
    private HandlerThread handlerThread;
    private Handler threadHandler;
    //0不开启闪光灯 1自动 2长亮
    private int[] flashMode = new int[]{R.mipmap.close_flash_icon, R.mipmap.auto_flash, R.mipmap.open_flash_icon};
    private int mSgType = 1;
    private int height;
    public long address;
    private float scale;
    private byte[] previewData;
    private boolean isDocRecVisible = false;

    private boolean isListAdd = false;
    private AtomicBoolean isPreview;
    private AtomicBoolean isInCamera;
    private AtomicBoolean isTakingPic;
    private AtomicBoolean isGetColorBitmap;
    private AtomicBoolean isWaitingColor;
    private AtomicBoolean isGetCutBitmap;
    private AtomicBoolean isWaitingCut;
    private String dirPath;
    private SimpleDateFormat formatter;
    private int docsCount = 0;
    private ObjectAnimator showWhiteViewAnim;
    private long lastVibrateTime = 0;
    private String tagId;
    private String tagName;
    private String categoryID;
    private String categoryName;
    private int type = 0;
    private boolean isSingle;

    private CameraGuideView cameraGuideView;
    private CZSaoMiao_Alg scanProAlg;
    private Args args;

    //1:左，2：右，3：上,4；下
    private int oritationType = 4;
    private long oritationTime;
    private SensorManager sensorManager;
    private String finalCategoryName;
    private String finalCategoryId;
    private boolean needShowStatusBar = ScreenAdaptationUtils.hasLiuHaiInVivo();
    private AtomicBoolean isStartPreview;
    private AtomicBoolean isFoucusFinish;
    private AtomicBoolean needAlgFocus;
    private List<Float> czPoints;
    private AtomicBoolean isFast;

    private CenterSelectionTabLayout tabLayout;
    private int currentIndex = 0;

    private JniBitmapHolder bitmapHolder = new JniBitmapHolder();
    private Runnable task;
    private String tempId;

    private AtomicBoolean isAnimating;
    private AtomicBoolean isFastRunning;
    private List<Integer> mWaitAction = new LinkedList<>(); //暂存拍照的队列

    private boolean isSmallScreen;
    private Realm realm;
    private List<BaseItemEntity> mBodyList = new ArrayList<>();

    private int backBtnStatus = 0;//0时候可以点击,为了正确读取拍照后的图片大小,在连续拍照后,预留2秒再读取图片大小

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_camera);
        initComponent();
        initRecyclerView();
        registerEvent();
        resetTemp();
    }

    private void showTip() {
        tabLayout.setSelectedPosition(0).setData(mBodyList).create();
        new ScanProCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_ONE_BUTTON)
                .setTitle(getString(R.string.prompt))
                .setMessage(getString(R.string.fast_mode_tip))
                .setOnPositiveListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        userPreferences.setIsFirstFastModeTip(false);
                        tabLayout.setSelectedPosition(1).setData(mBodyList).create();
                        dialog.cancel();
                    }
                })
                .create()
                .show();

    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        cameraPreviewAdapter = new CameraPreviewAdapter(this, new ArrayList<>(), isSmallScreen);
        cameraPreviewAdapter.setOnItemClickListener(onItemClickListener);
        recyclerView.setAdapter(cameraPreviewAdapter);
        recyclerView.setHasFixedSize(true);
        SnappyLinearLayoutManager linearLayoutManager = new SnappyLinearLayoutManager(this);
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        linearLayoutManager.setSnapType(SnapType.CENTER);
        recyclerView.setLayoutManager(linearLayoutManager);
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        isSingle = getIntent().getBooleanExtra("isSingle", false);
        tagId = getIntent().getStringExtra("tagId");
        tagName = getIntent().getStringExtra("tagName");
        categoryID = getIntent().getStringExtra("categoryID");
        categoryName = getIntent().getStringExtra("categoryName");
        type = getIntent().getIntExtra("type", 0);
        initSensor();
        initGuideView();
        if (NavBarUtils.hasNavBar(this)) {
            hideBottomUIMenu();
        }
        EventBus.getDefault().register(this);
        scanProAlg = CZSaoMiao_Alg.getInstance(this);
        czPoints = new ArrayList<>();
        scanProAlg.set_sensitivity(10f);

        isFoucusFinish = new AtomicBoolean(true);
        isStartPreview = new AtomicBoolean(false);
        needAlgFocus = new AtomicBoolean(false);
        isInCamera = new AtomicBoolean(false);
        isTakingPic = new AtomicBoolean(false);
        isPreview = new AtomicBoolean(false);
        isFast = new AtomicBoolean(false);
        isWaitingColor = new AtomicBoolean(false);
        isGetColorBitmap = new AtomicBoolean(false);
        isWaitingCut = new AtomicBoolean(false);
        isGetCutBitmap = new AtomicBoolean(false);
        isAnimating = new AtomicBoolean(false);
        isFastRunning = new AtomicBoolean(false);
        //震动
        vibrator = (Vibrator) this.getSystemService(VIBRATOR_SERVICE);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        cameraFlashView = (ImageView) findViewById(R.id.camera_flash_view);
        cameraScanRl = (RelativeLayout) findViewById(R.id.camera_scan_rl);
        cameraCutImg = findViewById(R.id.camera_cut_img);
        cameraColorImg = findViewById(R.id.camera_color_img);
        animView = (View) findViewById(R.id.anim_view);
        cameraFlashModeRl = (RelativeLayout) findViewById(R.id.camera_flash_mode_rl);
        previewNoIconBackBtn = (ImageView) findViewById(R.id.preview_no_icon_back_btn);
        cameraFlashModeBtn = (ImageView) findViewById(R.id.camera_flash_mode_btn);
        recyclerView = (RecyclerView) findViewById(R.id.camera_list);
        recyclerView.setHasFixedSize(true);
        RelativeLayout cameraContainer = (RelativeLayout) findViewById(R.id.camera_preview_layout);
        FocusView focusView = (FocusView) findViewById(R.id.focus_view);
        cameraBorderView = (CameraBorderView) findViewById(R.id.preview_point_view);
        PreviewSurfaceView previewSurfaceView = (PreviewSurfaceView) findViewById(R.id.surface_view);
        flashBtn = (ImageView) findViewById(R.id.button_flash);
        cameraBlackShade = (BlackShadeView) findViewById(R.id.camera_black_shade);
        cameraPromptTv = (TextView) findViewById(R.id.camera_prompt_tv);
        bottomToast = (TextView) findViewById(R.id.bottom_toast);


        if (ScreenAdaptationUtils.getPhoneModel()) {
            height = ScreenUtils.getScreenWidth();
            isSmallScreen = true;
        } else {
            height = ScreenUtils.getScreenWidth() / 3 * 4;
            isSmallScreen = false;
        }

        RelativeLayout.LayoutParams cutImgLayoutParams = (RelativeLayout.LayoutParams) cameraCutImg.getLayoutParams();
        cutImgLayoutParams.height = ScreenUtils.getScreenWidth() * 4 / 5;
        cutImgLayoutParams.width = ScreenUtils.getScreenWidth() * 4 / 5;
        cameraCutImg.setLayoutParams(cutImgLayoutParams);
        RelativeLayout.LayoutParams colorImgLayoutParams = (RelativeLayout.LayoutParams) cameraColorImg.getLayoutParams();
        colorImgLayoutParams.height = ScreenUtils.getScreenWidth() * 4 / 5;
        colorImgLayoutParams.width = ScreenUtils.getScreenWidth() * 4 / 5;
        cameraColorImg.setLayoutParams(colorImgLayoutParams);
        RelativeLayout.LayoutParams cameraContainerLayoutParams = (RelativeLayout.LayoutParams) cameraContainer.getLayoutParams();
        cameraContainerLayoutParams.height = height;
        cameraContainer.setLayoutParams(cameraContainerLayoutParams);
        cameraPreview = new CameraPreview(CameraActivity.this);
        cameraPreview.setOnCameraReadyListener(onCameraReadyListener);
        SurfaceHolder camHolder = previewSurfaceView.getHolder();
        camHolder.addCallback(cameraPreview);
        camHolder.setKeepScreenOn(true);
        camHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        previewSurfaceView.setListener(cameraPreview);
        previewSurfaceView.setFocusView(focusView);
        dirPath = getFilesPath();
        tabLayout = findViewById(R.id.center_tab);
        if (isSingle) {
            tabLayout.setVisibility(View.VISIBLE);
            mBodyList.add(new ModeEntity("0", "单页"));
            mBodyList.add(new ModeEntity("1", "连续拍摄"));
            tabLayout.setData(mBodyList)
                    .setSelectedPosition(0)
                    .setOnItemSelectListener(new CenterSelectionTabLayout.onItemSelectListener() {
                        @Override
                        public void onItemSelect(int position) {
                            currentIndex = position;
                            if (position == 0) {
                                if (!isFastRunning.get()) {
                                    isFast.set(false);
                                } else {
                                    ToastUtils.showShort("请等待图片处理完成后切换模式");
                                    tabLayout.setData(mBodyList).setSelectedPosition(1).create();
                                }
                            } else {
                                if (userPreferences.isFirstFastModeTip()) {
                                    showTip();
                                } else {
                                    if (!isAnimating.get()) {
                                        isFast.set(true);
                                    }
                                }
                            }
                        }
                    }).create();
        } else {
            tabLayout.setVisibility(View.GONE);
        }
    }


    /**
     * 初始化陀螺仪
     *
     * @param: []
     * @return: []
     */

    private void initSensor() {
        sensorManager = (SensorManager) getSystemService(SENSOR_SERVICE);
        Sensor sensor = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
        Sensor sensor1 = sensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
        sensorManager.registerListener(sensorEventListener, sensor, SensorManager.SENSOR_DELAY_UI);
        sensorManager.registerListener(sensorEventListener, sensor1, SensorManager.SENSOR_DELAY_UI);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case ROTATE:
            case CUT:
            case EDGE_ADJUST_IN_CAMERA:
                isListAdd = true;
                refreshList();
                LogUtils.e("EDGE_ADJUST_IN_CAMERA");
                break;
            default:
                break;
        }
    }


    private void startAnimate(int angle, String docIdUUid) {
        float rotateAngle = 0;
        long duration = 1;
        if (angle == 90) {
            rotateAngle = -90;
            duration = 500;
        } else if (angle == 180) {
            rotateAngle = -180;
            duration = 500;
        } else if (angle == 270) {
            rotateAngle = 90;
            duration = 500;
        }
        ObjectAnimator rotationAnim = ObjectAnimator.ofFloat(cameraCutImg, "rotation", 0, rotateAngle);
        rotationAnim.setDuration(duration);
        rotationAnim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                //根据动画设置扫描白图片高度
                ValueAnimator scanAnim = ValueAnimator.ofInt(0, height);
                scanAnim.setDuration(1000);
                scanAnim.start();
                scanAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {

                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        //获取当前的height值
                        int currentHeight = (Integer) animation.getAnimatedValue();
                        ViewGroup.LayoutParams layoutParams = animView.getLayoutParams();
                        layoutParams.height = currentHeight;
                        animView.setLayoutParams(layoutParams);
                        if (currentHeight == height) {
                            waitForAnimToColor(docIdUUid);
                        }
                    }
                });
            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
        rotationAnim.start();
    }

    private class AlgCallBacks extends Callbacks {
        private String docIdUUid;

        public AlgCallBacks(String docIdUUid) {
            this.docIdUUid = docIdUUid;
        }

        @Override
        public void on_cut_finished(boolean isSucess, Bitmap cuttedImg, String savedPath) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (isSucess) {
                        if (!isFast.get()) {
                            bitmapHolder.storeBitmap(cuttedImg);
                            float scale = (float) (720.0 / cuttedImg.getWidth());
                            bitmapHolder.scaleBitmap(720, (int) (cuttedImg.getHeight() * scale), JniBitmapHolder.ScaleMethod.NearestNeighbour);
                            cutBitmap = bitmapHolder.getBitmapAndFree();
                            waitForJniToCut(docIdUUid);
                        }
                    } else {
                        restartPreview(true, true);
                        showMessage(R.string.take_photo_error);
                    }
                }
            });

        }

        @Override
        public void on_color_trans_finished_with_keypoints(boolean isSucess, Bitmap dstImg, String savedPath, float leftTopX, float leftTopY, float rightTopX, float rightTopY, float leftDownX, float leftDownY, float rightDownX, float rightDownY) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (isSucess) {
                        if (!isFast.get()) {
                            czPoints = new ArrayList<>();
                            czPoints.add(leftTopX);
                            czPoints.add(leftTopY);
                            czPoints.add(rightTopX);
                            czPoints.add(rightTopY);
                            czPoints.add(rightDownX);
                            czPoints.add(rightDownY);
                            czPoints.add(leftDownX);
                            czPoints.add(leftDownY);
                            bitmapHolder.storeBitmap(dstImg);
                            float scale = (float) (720.0 / dstImg.getWidth());
                            bitmapHolder.scaleBitmap(720, (int) (dstImg.getHeight() * scale), JniBitmapHolder.ScaleMethod.NearestNeighbour);
                            colorBitmap = bitmapHolder.getBitmapAndFree();
                            waitForJniToColor(docIdUUid);
                        } else {
                            saveToRealm(docIdUUid);
                        }
                    } else {
                        restartPreview(true, true);
                        showMessage(R.string.take_photo_error);
                    }
                }
            });
        }

        @Override
        public void on_notify_angle(int angle) {
            super.on_notify_angle(angle);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (!isFast.get()) {
                        startAnimate(angle, docIdUUid);
                    }
                }
            });
        }

        @Override
        public void on_save_success() {
            super.on_save_success();
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    hideProgressDialog();
                }
            });
            if (userPreferences.getEdgeGuideFirst()) {
                Intent intent = new Intent(CameraActivity.this, AdjustEdgeBookActivity.class);
                intent.putExtra("fileID", docIdUUid);
                intent.putExtra("originalImagePath", dirPath + docIdUUid + Constants.ORIGINAL_JPG);
                intent.putExtra("type", type);
                intent.putExtra("finalCategoryId", finalCategoryId);
                intent.putExtra("finalCategoryName", finalCategoryName);
                if (type == 2) {
                    intent.putExtra("tagName", tagName);
                    intent.putExtra("tagId", tagId);
                } else if (type == 1) {
                    intent.putExtra("categoryID", categoryID);
                    intent.putExtra("categoryName", categoryName);
                }
                intent.putExtra("isCamera", true);
                startActivityForResult(intent, ADJUST_BOOK_CODE);
            } else {
                Intent intent = new Intent(CameraActivity.this, AdjustGuideActivity.class);
                intent.putExtra("fileID", docIdUUid);
                intent.putExtra("originalImagePath", dirPath + docIdUUid + Constants.ORIGINAL_JPG);
                intent.putExtra("type", type);
                intent.putExtra("finalCategoryId", finalCategoryId);
                intent.putExtra("finalCategoryName", finalCategoryName);
                if (type == 2) {
                    intent.putExtra("tagName", tagName);
                    intent.putExtra("tagId", tagId);
                } else if (type == 1) {
                    intent.putExtra("categoryID", categoryID);
                    intent.putExtra("categoryName", categoryName);
                }
                intent.putExtra("isCamera", true);
                startActivityForResult(intent, ADJUST_BOOK_CODE);
            }
        }


        @Override
        public void on_need_focus() {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (needAlgFocus.get()) {
                        LogUtils.d("算法ccc");
                        checkStaticTime();
                    }
                }
            });
        }
    }

    private final int ADJUST_BOOK_CODE = 999;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == ADJUST_BOOK_CODE) {
            restartPreview(true, true);
        }
    }

    private void initGuideView() {
        userPreferences = UserPreferences.getInstance(this);
        cameraGuideView = (CameraGuideView) findViewById(R.id.camera_guide_view);
        if (!isSingle) {
            if (userPreferences.isBookModeGuide()) {
                cameraGuideView.setOnCameraGuideViewNextBtnOnClickListener(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        userPreferences.setIsBookModeGuide(false);
                        cameraGuideView.setVisibility(View.GONE);
                        return null;
                    }
                });
                cameraGuideView.startAnimBookMode();
            } else {
                cameraGuideView.setVisibility(View.GONE);
            }
        } else {
            if (userPreferences.isEdgeModeGuide()) {
                cameraGuideView.setOnCameraGuideViewNextBtnOnClickListener(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        userPreferences.setIsEdgeModeGuide(false);
                        cameraGuideView.setVisibility(View.GONE);
                        return null;
                    }
                });
                cameraGuideView.startAnimEdgeMode();
            } else {
                cameraGuideView.setVisibility(View.GONE);
            }
        }
    }

    private void registerEvent() {
        flashBtn.setOnClickListener(this);
        cameraFlashModeRl.setOnClickListener(this);
        previewNoIconBackBtn.setOnClickListener(this);
    }

    private CameraPreview.OnCameraReadyListener onCameraReadyListener = new CameraPreview.OnCameraReadyListener() {
        @Override
        public void cameraIsReady() {
            size = cameraPreview.getCamera().getParameters().getPreviewSize();
            scale = ((float) ScreenUtils.getScreenWidth()) / ((float) size.height);
            cameraPreview.getCamera().setPreviewCallback(previewCallback);
            previewToDraw();
            isStartPreview.set(true);
        }
    };


    private void previewToDraw() {
        isPreview.set(true);
        if (task == null) {
            int delay = 500;
            switch (DeviceLevelUtils.judgeDeviceLevel(CameraActivity.this)) {
                case DeviceLevelUtils.DEVICE_LEVEL_LOW:
                    delay = 700;
                    break;
                case DeviceLevelUtils.DEVICE_LEVEL_MID:
                    delay = 500;
                    break;
                case DeviceLevelUtils.DEVICE_LEVEL_HIGH:
                    delay = 330;
                    break;
                default:
                    delay = 500;
                    break;
            }
            int finalDelay = delay;
            task = new Runnable() {
                @Override
                public void run() {
                    if (previewData != null && isPreview.get()) {
                        args = scanProAlg.miao_detect(previewData, size.width, size.height, isSingle ? CZSaoMiao_Alg.ScanType.SINGLE : CZSaoMiao_Alg.ScanType.BOOK);
                        drawRedRec(args);
                    }
                    threadHandler.postDelayed(task, finalDelay);
                }
            };
        }
        if (handlerThread == null) {
            handlerThread = new HandlerThread("scan");
            handlerThread.start();
        }
        if (threadHandler == null) {
            threadHandler = new Handler(handlerThread.getLooper());
        }
        threadHandler.removeCallbacks(task);
        threadHandler.post(task);

    }


    /**
     * @des: 预览回调
     * @params:
     * @return:
     */

    private Camera.PreviewCallback previewCallback = new Camera.PreviewCallback() {
        @Override
        public void onPreviewFrame(final byte[] data, final Camera camera) {
            previewData = data;
        }
    };


    /**
     * @des: 画页码和边框
     * @params:
     * @return:
     */

    private void drawRedRec(Args args) {
        //页码边框显示
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (args.isHasObject()) {
                    cameraBorderView.setVisibility(View.VISIBLE);
                    cameraBorderView.show(args, scale, size.height, isSingle);
                    if (!isSingle) {
                        bottomToast.setVisibility(View.GONE);
                    }
                    isDocRecVisible = true;
                } else {
                    if (!isSingle) {
                        bottomToast.setVisibility(View.VISIBLE);
                        bottomToast.setText(R.string.can_not_find_book);
                    }
                    isDocRecVisible = false;
                    cameraBorderView.setVisibility(View.GONE);
                }
            }

        });
    }

    /**
     * @des: 自动对焦后拍照
     * @params:
     * @return:
     */

    private void takePicture(String docIdUUid) {
        if (isTakingPic.get()) {   //判断是否处于拍照，如果正在拍照，则将请求放入缓存队列
            mWaitAction.add(1);
        } else {
            doTakeAction(docIdUUid);
        }
    }

    private void doTakeAction(String docIdUUid) {   //拍照方法
        isTakingPic.set(true);
        if (isSingle) {
            showCameraFlashAnim();
            takePictureAndDoColorMode(docIdUUid);
            if (!isFast.get()) {
                tempId = docIdUUid;
            } else {
                isFastRunning.set(true);
            }
        } else {
            showProgressDialog(true);
            takePictureAndGoAdjust(docIdUUid);
        }
    }

    private void takePictureAndGoAdjust(String docIdUUid) {
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                createCategory(realm);
            }
        });
        try {
            cameraPreview.getCamera().takePicture(null, null, new Camera.PictureCallback() {
                @Override
                public void onPictureTaken(final byte[] data, final Camera camera) {
                    if (mWaitAction.size() > 0) {
                        mWaitAction.remove(0);   //移除队列中的第一条拍照请求，并执行拍照请求
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                doTakeAction(docIdUUid);
                            }
                        });
                    } else {  //队列中没有拍照请求，走正常流程
                        isTakingPic.set(false);
                    }
                    camera.stopPreview();
                    if (data != null) {
                        cameraPreview.getCamera().cancelAutoFocus();
                        isStartPreview.set(false);
                        ThreadUtils.executeByIo(new ThreadUtils.Task<Void>() {
                            @Override
                            public Void doInBackground() throws Throwable {
                                saveOriginalImage(docIdUUid, data);
                                return null;
                            }

                            @Override
                            public void onSuccess(Void result) {

                            }

                            @Override
                            public void onCancel() {

                            }

                            @Override
                            public void onFail(Throwable t) {

                            }
                        });
                    } else {
                        hideProgressDialog();
                        restartPreview(true, true);
                        showMessage(R.string.take_photo_error);
                    }
                    isTakingPic.set(false);
                }
            });
        } catch (Exception e) {
            LogUtils.e(e);
            restartPreview(true, true);
            showMessage(R.string.take_photo_error);
            isTakingPic.set(false);
        }
    }

    /**
     * @des: 拍照、裁剪、色彩模式
     * @params:
     * @return:
     */
    private synchronized void takePictureAndDoColorMode(String docIdUUid) {
        try {
            cameraPreview.getCamera().takePicture(null, null, new Camera.PictureCallback() {
                @Override
                public void onPictureTaken(final byte[] data, final Camera camera) {
                    if (mWaitAction.size() > 0) {
                        mWaitAction.remove(0);   //移除队列中的第一条拍照请求，并执行拍照请求
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                doTakeAction(docIdUUid);
                            }
                        });
                        return;
                    } else {  //队列中没有拍照请求，走正常流程
                        isTakingPic.set(false);
                    }
                    camera.stopPreview();
                    if (data != null) {
                        isStartPreview.set(false);
                        cameraPreview.getCamera().cancelAutoFocus(); // 先要取消掉进程中所有的聚焦功能
                        showBlackShade(docIdUUid, data);
                    } else {
                        restartPreview(true, true);
                        showMessage(R.string.take_photo_error);
                    }
                }
            });
        } catch (
                Exception e) {
            LogUtils.e(e);
            restartPreview(true, true);
            showMessage(R.string.take_photo_error);
            isTakingPic.set(false);
        }

    }

    /**
     * @des: 重新开始预览画面
     * @params:
     * @return:
     */
    private synchronized void restartPreview(final boolean isHideScanRl, boolean isError) {
        if (isError) {
            isInCamera.set(false);
        }
        previewData = null;
        resetBitmap();
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (isHideScanRl) {
                    cameraScanRl.setVisibility(View.GONE);
                    //捕获异常取消正在执行的动画
                    cancelAnim();
                }
                cameraFlashView.setVisibility(View.GONE);
                cameraFlashView.setAlpha(0);
                previewNoIconBackBtn.setVisibility(View.VISIBLE);
                cameraFlashModeBtn.setVisibility(View.VISIBLE);
                previewNoIconBackBtn.setEnabled(true);
                previewNoIconBackBtn.setClickable(true);
                cameraFlashModeRl.setEnabled(true);
                cameraFlashModeRl.setClickable(true);
                cameraBlackShade.setVisibility(View.GONE);
                cameraCutImg.setVisibility(View.GONE);
                cameraColorImg.setVisibility(View.GONE);
                flashBtn.setVisibility(View.VISIBLE);
                Camera camera = cameraPreview.getCamera();
                if (camera != null) {
                    camera.stopPreview();
                    camera.startPreview();
                    isStartPreview.set(true);
                    camera.setPreviewCallback(previewCallback);
                    previewToDraw();
                }
            }
        });
    }

    /**
     * @des: 裁剪并作色彩模式
     * @params:
     * @return:
     */
    private void cutAndDoColorMode(String docIdUUid, byte[] data) {
        //拍照的时候的页码为最后页码
        if (FileUtils.createOrExistsDir(dirPath)) {
            Args args = new Args();
            String originalPath = dirPath + docIdUUid + Constants.ORIGINAL_JPG;
            String basePath = dirPath + docIdUUid + Constants.BASE_JPG;
            String baseSmallPath = dirPath + docIdUUid + Constants.BASE_SMALL_JPG;
            String processPath = dirPath + docIdUUid + Constants.JPG;
            String processSmallPah = dirPath + docIdUUid + Constants.SMALL_JPG;
            args.setScanType(isSingle ? CZSaoMiao_Alg.ScanType.SINGLE : CZSaoMiao_Alg.ScanType.BOOK);
            args.setColorType(CZSaoMiao_Alg.ColorType.AUTO);
            scanProAlg.setCallbacks(new AlgCallBacks(docIdUUid));
            scanProAlg.miao_single_pipeline(data, data.length, originalPath, basePath, processPath, baseSmallPath, processSmallPah, "CZUR", true, args);
        } else {
            restartPreview(true, true);
            showMessage(R.string.camera_error);
        }
    }

    /**
     * @des: 裁剪并作色彩模式
     * @params:
     * @return:
     */
    private void saveOriginalImage(String docIdUUid, byte[] data) {
        //拍照的时候的页码为最后页码
        if (FileUtils.createOrExistsDir(dirPath)) {
            scanProAlg.setCallbacks(new AlgCallBacks(docIdUUid));
            scanProAlg.save_picture(data, data.length, dirPath + docIdUUid + Constants.ORIGINAL_JPG);
        } else {
            restartPreview(true, true);
            showMessage(R.string.camera_error);
        }
    }

    /**
     * @des: 隐藏页码边框，显示黑色遮罩,然后消失
     * @params:
     * @return:
     */
    private synchronized void showBlackShade(String docIdUUid, byte[] data) {
        ThreadUtils.executeBySingle(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() throws Throwable {
                cutAndDoColorMode(docIdUUid, data);
                return null;
            }

            @Override
            public void onSuccess(Void result) {
            }
        });
        if (!isFast.get()) {
            isAnimating.set(true);
            cameraBorderView.setVisibility(View.GONE);
            cameraBlackShade.setVisibility(View.VISIBLE);
            cameraBlackShade.show(args, size.height, scale);
            blackShadeShowAnim = ObjectAnimator.ofFloat(cameraBlackShade, "alpha", 0, 1.0f);
            blackShadeShowAnim.setDuration(500);
            blackShadeShowAnim.start();
            blackShadeShowAnim.addListener(blackShadeShowAnimListener);
        } else {
            restartPreview(false, false);
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    isInCamera.set(false);
                }
            }, 1000);
        }
        saveTempToRealm(docIdUUid);
    }

    /**
     * @des: 隐藏黑色遮罩并且开始显示扫描画面
     * @params:
     * @return:
     */
    private void hideBlackShadeAndShowRotate() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideShadeAndShowCutAnim = ObjectAnimator.ofFloat(cameraScanRl, "alpha", 0, 1.0f);
                hideShadeAndShowCutAnim.setDuration(1000);
                hideShadeAndShowCutAnim.addListener(hideShadeAndShowRotateAnimListener);
                hideShadeAndShowCutAnim.start();
            }
        });
    }


    /**
     * @des: 拍照显示动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener showWhiteViewAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
        }

        @Override
        public void onAnimationEnd(Animator animation) {


        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };
    /**
     * @des: 黑色遮罩显示动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener blackShadeShowAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            waitForAnimToCut();
        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };
    /**
     * @des: 黑色遮罩显示动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener roateAnim = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            //根据动画设置扫描白图片高度
            ValueAnimator scanAnim = ValueAnimator.ofInt(0, height);
            scanAnim.addUpdateListener(scanUpdateListener);
            scanAnim.setDuration(1000);
            scanAnim.start();
        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };

    /**
     * @des: 扫描动画监听
     * @params:
     * @return:
     */
    private Animator.AnimatorListener hideShadeAndShowRotateAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
            //重置上一次裁剪图片alpha值从0到1
            cameraCutImg.setRotation(0);
            cameraCutImg.setAlpha(1.0f);
            //设置扫描的白色高为0
            ViewGroup.LayoutParams layoutParams = animView.getLayoutParams();
            layoutParams.height = 0;
            animView.setLayoutParams(layoutParams);
            cameraColorImg.setVisibility(View.GONE);
            cameraScanRl.setVisibility(View.VISIBLE);
            cameraCutImg.setVisibility(View.VISIBLE);
            cameraCutImg.setImageBitmap(cutBitmap);
        }

        @Override
        public void onAnimationEnd(Animator animation) {

        }

        @Override
        public void onAnimationCancel(Animator animation) {
        }

        @Override
        public void onAnimationRepeat(Animator animation) {
        }
    };


    /**
     * @des: 扫描动画更新的监听
     * @params:
     * @return:
     */
    private ValueAnimator.AnimatorUpdateListener scanUpdateListener = new ValueAnimator.AnimatorUpdateListener() {

        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            //获取当前的height值
            int currentHeight = (Integer) animation.getAnimatedValue();
            ViewGroup.LayoutParams layoutParams = animView.getLayoutParams();
            layoutParams.height = currentHeight;
            animView.setLayoutParams(layoutParams);
            if (currentHeight == height) {
                waitForAnimToColor(tempId);
            }
        }
    };

    /**
     * @des: 捕捉异常后取消动画
     * @params:
     * @return:
     */
    private void cancelAnim() {
        if (null != blackShadeShowAnim) {
            if (blackShadeShowAnim.isRunning()) {
                LogUtils.i("blackShadeShowAnim.cancel");
                blackShadeShowAnim.cancel();
            }
        }
        if (null != hideShadeAndShowCutAnim) {
            if (hideShadeAndShowCutAnim.isRunning()) {
                LogUtils.i("hideShadeAndShowCutAnim.cancel");
                hideShadeAndShowCutAnim.cancel();
            }
        }
    }

    /**
     * @des: 保存到数据库
     * @params:
     */

    private void saveToRealm(String docIdUUid) {
        LogUtils.i("CameraActivity.saveToRealm.docIdUUid="+docIdUUid);
        realm.executeTransactionAsync(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                DocEntity docEntity = realm.where(DocEntity.class).equalTo("fileID", docIdUUid).findFirst();
                if (docEntity != null) {
                    createCategory(realm);
                    String originalPath = dirPath + docIdUUid + Constants.ORIGINAL_JPG;
                    String basePath = dirPath + docIdUUid + Constants.BASE_JPG;
                    String baseSmallPath = dirPath + docIdUUid + Constants.BASE_SMALL_JPG;
                    String processPath = dirPath + docIdUUid + Constants.JPG;
                    String processSmallPah = dirPath + docIdUUid + Constants.SMALL_JPG;
                    LogUtils.i("CameraActivity.saveToRealm.basePath="+basePath);
                    docEntity.setBucket(Constants.BUCKET);
                    docEntity.setTemp(1);
                    docEntity.setUuid(UUID.randomUUID().toString());
                    docEntity.setEnhanceMode(0);
                    docEntity.setUserID(getUserIdIsLogin());
                    if (isSingle) {
                        RealmList<Float> floats = new RealmList<>();
                        for (Float czPoint : czPoints) {
                            floats.add(czPoint);
                        }
                        for (float aFloat : floats) {
                            LogUtils.e(aFloat);
                        }
                        docEntity.setRect(floats);
                    }
                    if (type == 0) {
                        docEntity.setCategoryID(finalCategoryId);
                        docEntity.setCategoryName(finalCategoryName);
                    } else if (type == 1) {
                        docEntity.setNameTemp(1);
                        docEntity.setCategoryID(categoryID);
                        docEntity.setCategoryName(categoryName);
                    } else if (type == 2) {
                        docEntity.setTagId(tagId);
                        docEntity.setTagName(tagName);
                        docEntity.setCategoryID(finalCategoryId);
                        docEntity.setCategoryName(finalCategoryName);
                    }
                    docEntity.setNewAdd(1);
                    docEntity.setBucket(Constants.SCAN_PRO);
                    docEntity.setFileType(isSingle ? 0 : 1);
                    docEntity.setDirty(1);
                    docEntity.setBaseImagePath(basePath);
                    docEntity.setBaseSmallImagePath(baseSmallPath);
                    docEntity.setOriginalImagePath(originalPath);
                    docEntity.setProcessSmallImagePath(processSmallPah);
                    docEntity.setProcessImagePath(processPath);
                    Log.d("chucunkongjian", "保存 " + FileUtils.getFileLength(basePath));
//                    docEntity.setFileSize(FileUtils.getFileLength(basePath) + "");
                    saveFileSizeDelay(basePath, docIdUUid);
                }
            }
        }, new Realm.Transaction.OnSuccess() {
            @Override
            public void onSuccess() {
                isInCamera.set(false);
                isListAdd = true;
                releaseBitmap();
                RealmResults<DocEntity> docEntities = realm.where(DocEntity.class)
                        .equalTo("isTemp", 1)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("takePhotoTime", Sort.ASCENDING);
                docsCount = docEntities.size();
                checkPromptTvShow(docEntities);
                sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(new File(dirPath + docIdUUid + Constants.JPG))));
                cameraPreviewAdapter.refreshData(docEntities, isListAdd, docEntities.size() - 1);
                isFastRunning.set(false);
                for (DocEntity entity : docEntities) {
                    if (entity.getProcessSmallImagePath() == null) {
                        isFastRunning.set(true);
                        break;
                    }
                }
                if (currentIndex == 0 && !isFastRunning.get()) {
                    isFast.set(false);
                }
                isListAdd = false;
                if (!ActivityUtils.isActivityAlive(CameraActivity.this) && !isFastRunning.get()) {
                    resetTemp();
                    EventBus.getDefault().post(new FileEvent(EventType.ADD_FILES, 0));
                }
                System.gc();
                Log.d("chucunkongjian", "保存 完成");
            }
        });
    }

    //2秒之后获取图片大小
    private void saveFileSizeDelay(String basePath, String docIdUUid) {
        backBtnStatus++;
        MyHandler handler = new MyHandler(getMainLooper(), this);
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                realm.executeTransactionAsync(
                        new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                DocEntity docEntity = realm.where(DocEntity.class).equalTo("fileID", docIdUUid).findFirst();
//                                DocEntity docEntity = new DocEntity();
//                                docEntity.setFileID(docIdUUid);
                                if (docEntity != null) {
                                    docEntity.setFileSize(FileUtils.getFileLength(basePath) + "");
                                }

//                                realm.copyToRealmOrUpdate(docEntity);
                            }

                        }
                        , new Realm.Transaction.OnSuccess() {
                            @Override
                            public void onSuccess() {
                                DocEntity docEntity = realm.where(DocEntity.class).equalTo("fileID", docIdUUid).findFirst();

                                Log.d("chucunkongjian", "延迟保存getFileLength " + FileUtils.getFileLength(basePath));
                                Log.d("chucunkongjian", "延迟保存getFileSize " + docEntity.getFileSize());

                                backBtnStatus--;
                            }
                        }
                );
//                DocEntity docEntity = realm.where(DocEntity.class).equalTo("fileID", docIdUUid).findFirst();
//                if (docEntity != null) {


//                }
            }
        }, 2000);
    }


    static class MyHandler extends Handler {
        WeakReference<CameraActivity> weakReference;

        public MyHandler(@NonNull Looper looper, CameraActivity activity) {
            super(looper);
            weakReference = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message msg) {

        }
    }

    private void saveTempToRealm(String docIdUUid) {
        //使用队列保存
        realm.executeTransactionAsync(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                DocEntity docEntity = new DocEntity();
                docEntity.setFileID(docIdUUid);
                docEntity.setTemp(1);
                docEntity.setDelete(0);
                String curDate = formatter.format(new Date());
                docEntity.setCreateTime(curDate);
                docEntity.setUpdateTime(curDate);
                docEntity.setTakePhotoTime(curDate);
                realm.copyToRealmOrUpdate(docEntity);
            }
        }, new Realm.Transaction.OnSuccess() {
            @Override
            public void onSuccess() {
                isListAdd = true;
                RealmResults<DocEntity> docEntities = realm.where(DocEntity.class)
                        .equalTo("isTemp", 1)
                        .equalTo("isDelete", 0)
                        .findAll()
                        .sort("takePhotoTime", Sort.ASCENDING);
                docsCount = docEntities.size();
                checkPromptTvShow(docEntities);
                cameraPreviewAdapter.refreshData(docEntities, isListAdd, docEntities.size() - 1);
                recyclerView.smoothScrollToPosition(docEntities.size() - 1);
                isListAdd = false;
            }
        });
    }

    /**
     * @des: 释放bitmap
     * @params:
     * @return:
     */
    private void releaseBitmap() {
        if (null != cutBitmap) {
            //释放bitmap
            if (!cutBitmap.isRecycled()) {
                cutBitmap.recycle();
                cutBitmap = null;
            }
        }
        //释放色彩模式Bitmap
        if (null != colorBitmap) {
            if (!colorBitmap.isRecycled()) {
                colorBitmap.recycle();
                colorBitmap = null;
            }
        }
    }

    /**
     * @des: 显示最后的色彩模式的图
     * @params:
     * @return:
     */
    private void showColorImg(String docIdUUid) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //扫描进度100的时候展示色彩模式之后的图片500ms
                ObjectAnimator hideCutImgAnim = ObjectAnimator.ofFloat(cameraCutImg, "alpha", 1.0f, 0);
                hideCutImgAnim.setDuration(350);
                hideCutImgAnim.start();
                ObjectAnimator showColorImgAnim = ObjectAnimator.ofFloat(cameraColorImg, "alpha", 0, 1.0f);
                showColorImgAnim.setDuration(600);
                showColorImgAnim.setInterpolator(new AccelerateDecelerateInterpolator());
                showColorImgAnim.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        cameraBlackShade.setVisibility(View.GONE);
                        cameraColorImg.setVisibility(View.VISIBLE);
                        cameraColorImg.setImageBitmap(colorBitmap);
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                ObjectAnimator hideScanToFinishAnim = ObjectAnimator.ofFloat(cameraScanRl, "alpha", 1.0f, 0);
                                hideScanToFinishAnim.setDuration(580);
                                hideScanToFinishAnim.setInterpolator(new DecelerateInterpolator());
                                hideScanToFinishAnim.addListener(new Animator.AnimatorListener() {
                                    @Override
                                    public void onAnimationStart(Animator animation) {
                                    }

                                    @Override
                                    public void onAnimationEnd(Animator animation) {
                                        saveToRealm(docIdUUid);
                                        restartPreview(false, false);
                                        isAnimating.set(false);
                                        if (currentIndex == 1) {
                                            isFast.set(true);
                                        }
                                    }

                                    @Override
                                    public void onAnimationCancel(Animator animation) {

                                    }

                                    @Override
                                    public void onAnimationRepeat(Animator animation) {

                                    }
                                });
                                hideScanToFinishAnim.start();
                            }
                        }, 400);
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {
                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {
                    }
                });
                showColorImgAnim.start();
            }
        });
    }

    private void refreshList() {
        RealmResults<DocEntity> docEntities = realm.where(DocEntity.class)
                .equalTo("isTemp", 1)
                .equalTo("isDelete", 0)
                .findAll()
                .sort("takePhotoTime", Sort.ASCENDING);
        docsCount = docEntities.size();
        if (docEntities.size() > 0 && docEntities.get(docEntities.size() - 1).getProcessImagePath() != null) {
            sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(new File(docEntities.get(docEntities.size() - 1).getProcessImagePath()))));
        }
        checkPromptTvShow(docEntities);
        cameraPreviewAdapter.refreshData(docEntities, isListAdd, docEntities.size() - 1);
        recyclerView.smoothScrollToPosition(docEntities.size() - 1);
        isListAdd = false;
    }

    private void checkPromptTvShow(List<DocEntity> list) {
        if (list.size() > 0) {
            cameraPromptTv.setVisibility(View.GONE);
        } else {
            cameraPromptTv.setVisibility(View.VISIBLE);
        }
    }

    /**
     * @des: 相机列表点击监听
     * @params:
     * @return:
     */

    private CameraPreviewAdapter.OnItemClickListener onItemClickListener = new CameraPreviewAdapter.OnItemClickListener() {
        @Override
        public void onItemClick(DocEntity docEntity, int position) {
            Intent intent = new Intent(CameraActivity.this, FilePreviewActivity.class);
            intent.putExtra("isCamera", true);
            intent.putExtra("type", 4);
            intent.putExtra("isItemTag", Validator.isNotEmpty(docEntity.getTagName()) ? 1 : 0);
            intent.putExtra("fileType", docEntity.getFileType());
            intent.putExtra("categoryName", docEntity.getCategoryName());
            intent.putExtra("categoryID", docEntity.getCategoryID());
            intent.putExtra("tagId", docEntity.getTagId());
            intent.putExtra("tagName", docEntity.getTagName());
            intent.putExtra("index", position);
            intent.putExtra("currentPageId", docEntity.getFileID());
            startActivity(intent);
        }
    };

    /**
     * @des: 返回时设置temp为0
     * @params:
     * @return:
     */
    private void resetTemp() {
        Realm realm = Realm.getDefaultInstance();
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                List<DocEntity> docEntities = realm.where(DocEntity.class)
                        .equalTo("isTemp", 1)
                        .findAll();
                for (DocEntity docEntity : docEntities) {
                    docEntity.setTemp(0);
                    docEntity.setTakePhotoTime(Constants.TAKE_PHOTO_INIT_TIME);
                }
            }
        });
        realm.close();
    }

    /**
     * @des: 如果是jni色彩模式先做完就等动画做完再进行下一步
     * @params:
     * @return:
     */
    private void waitForAnimToColor(String docIdUUid) {
        if (isGetColorBitmap.get()) {
            LogUtils.i("get colorBitmap faster than anim");
            showColorImg(docIdUUid);
        } else {
            isGetColorBitmap.set(false);
            isWaitingColor.set(true);
            isAnimating.set(false);
        }
    }


    /**
     * @des: 如果是动画先做完就等得到色彩模式的图片再去做下一步
     * @params:
     * @return:
     */
    private void waitForJniToColor(String docIdUUid) {
        if (isWaitingColor.get()) {
            LogUtils.i("get colorBitmap slowed than anim");
            showColorImg(docIdUUid);
        } else {
            isWaitingColor.set(false);
            isGetColorBitmap.set(true);
        }
    }

    /**
     * @des: 如果是jni色彩模式先做完就等动画做完再进行下一步
     * @params:
     * @return:
     */
    private void waitForAnimToCut() {
        if (isGetCutBitmap.get()) {
            LogUtils.i("get cutBitmap faster than anim");
            hideBlackShadeAndShowRotate();
        } else {
            isGetCutBitmap.set(false);
            isWaitingCut.set(true);
        }
    }


    /**
     * @des: 如果是动画先做完就等得到色彩模式的图片再去做下一步
     * @params:
     * @return:
     */
    private void waitForJniToCut(String docIdUUid) {
        if (isWaitingCut.get()) {
            LogUtils.i("get cutBitmap slowed than anim");
            hideBlackShadeAndShowRotate();
        } else {
            isWaitingCut.set(false);
            isGetCutBitmap.set(true);
        }
    }

    /**
     * @des: 屏蔽返回键
     * @params:
     * @return:
     */
    private void shieldBack() {
        previewNoIconBackBtn.setVisibility(View.GONE);
        cameraFlashModeBtn.setVisibility(View.GONE);
    }

    /**
     * @des: 显示返回键
     * @params:
     * @return:
     */
    private void showBack() {
        previewNoIconBackBtn.setVisibility(View.VISIBLE);
        cameraFlashModeBtn.setVisibility(View.VISIBLE);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.button_flash) {
            if (!isInCamera.get()) {
                //拍照的时候把预览设成false
                if (isDocRecVisible) {
                    previewNoIconBackBtn.setEnabled(false);
                    previewNoIconBackBtn.setClickable(false);
                    cameraFlashModeRl.setEnabled(false);
                    cameraFlashModeRl.setClickable(false);
                    isPreview.set(false);
                    isInCamera.set(true);
                    takePicture(UUID.randomUUID().toString());
                } else {
                    vibrateNotify();
                    if (isSingle) {
                        showMessage(R.string.can_not_find_single);
                    }
                }
            }
        } else if (id == R.id.preview_no_icon_back_btn) {
            if (backBtnStatus != 0) {
                return;
            }
            ActivityUtils.finishActivity(this);
        } else if (id == R.id.camera_flash_mode_rl) {
            switchFlashMode();
        }
    }

    /**
     * @des: 震动提醒
     * @params:
     * @return:
     */
    private void vibrateNotify() {
        if (System.currentTimeMillis() - lastVibrateTime <= 1000) {
            return;
        }
        vibrator.vibrate(200);
        lastVibrateTime = System.currentTimeMillis();
    }

    /**
     * @des: 显示照相闪光view
     * @params:
     * @return:
     */

    private void showCameraFlashAnim() {
        cameraFlashView.setVisibility(View.VISIBLE);
        cameraFlashView.setAlpha(0);
        showWhiteViewAnim = ObjectAnimator.ofFloat(cameraFlashView, "alpha", 0, 1.0f, 0);
        showWhiteViewAnim.setDuration(300);
        showWhiteViewAnim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {

            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        showWhiteViewAnim.start();
    }

    /**
     * @des: 切换闪关灯模式
     * @params:
     * @return:
     */
    private void switchFlashMode() {
        switch (mSgType) {
            case 0:
                cameraFlashModeBtn.setBackgroundResource(flashMode[0]);
                cameraPreview.setIsOpenFlashMode(Camera.Parameters.FLASH_MODE_OFF);
                mSgType = 1;
                break;
            case 1:
                cameraFlashModeBtn.setBackgroundResource(flashMode[1]);
                cameraPreview.setIsOpenFlashMode(Camera.Parameters.FLASH_MODE_AUTO);
                mSgType = 2;
                break;
            case 2:
                cameraFlashModeBtn.setBackgroundResource(flashMode[2]);
                cameraPreview.setIsOpenFlashMode(Camera.Parameters.FLASH_MODE_ON);
                mSgType = 0;
                break;
            default:
                break;
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        previewToDraw();
    }


    @Override
    protected void onPause() {
        super.onPause();
        isPreview.set(false);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        sensorManager.unregisterListener(sensorEventListener);
        refreshUI();
        isAutoSync();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        realm.close();
    }


    private void refreshUI() {
        List<DocEntity> tempList = new ArrayList<>(realm.where(DocEntity.class)
                .equalTo("isTemp", 1)
                .equalTo("isDelete", 0)
                .findAll());
        if (tempList.size() > 0) {
            if (type == 0) {
                createCategory(realm);
                EventBus.getDefault().post(new CategoryEvent(EventType.ADD_CATEGORY));
            }
            EventBus.getDefault().post(new FileEvent(EventType.ADD_FILES, tempList.size()));
        } else {
            EventBus.getDefault().post(new CategoryEvent(EventType.DELETE_CATEGORY));
            realm.executeTransaction(new Realm.Transaction() {
                @Override
                public void execute(Realm realm) {
                    CategoryEntity sameCategory = realm.where(CategoryEntity.class)
                            .equalTo("categoryID", finalCategoryId)
                            .equalTo("isDelete", 0)
                            .findFirst();
                    if (sameCategory != null) {
                        sameCategory.setDelete(1);
                    }
                }
            });
        }
        resetTemp();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private void createCategory(Realm realm) {
        if (Validator.isEmpty(finalCategoryName) && (type == 0 || type == 2)) {
            EventBus.getDefault().post(new CategoryEvent(EventType.ADD_CATEGORY_HIDE));
            int i = 0;
            String bookName = Constants.CATEGORY_DEFAULT_NAME;
            String finalName = bookName;
            CategoryEntity sameCategory = realm.where(CategoryEntity.class)
                    .equalTo("categoryName", Constants.CATEGORY_DEFAULT_NAME)
                    .equalTo("isDelete", 0)
                    .findFirst();
            while (Validator.isNotEmpty(sameCategory)) {
                long current = System.currentTimeMillis();
                i++;
                finalName = bookName + i;
                sameCategory = realm.where(CategoryEntity.class)
                        .equalTo("categoryName", finalName)
                        .equalTo("isDelete", 0)
                        .findFirst();
                LogUtils.i(System.currentTimeMillis() - current);
            }
            finalCategoryName = finalName;
            finalCategoryId = UUID.randomUUID().toString();
            CategoryEntity categoryEntity = realm.createObject(CategoryEntity.class, finalCategoryId);
            categoryEntity.setUserID(getUserIdIsLogin());
            categoryEntity.setDirty(1);
            categoryEntity.setCategoryName(finalCategoryName);
            String curDate = formatter.format(new Date());
            categoryEntity.setCreateTime(curDate);
            categoryEntity.setUpdateTime(curDate);
        }
    }

    /**
     * @des: 是否自动同步
     * @params:
     * @return:
     */

    private void isAutoSync() {
        if (docsCount > 0) {
            startSyncNow();
        }
    }

    public static final int STATUS_NONE = 0;
    public static final int STATUS_STATIC = 1;
    public static final int STATUS_MOVE = 2;
    private int mX, mY, mZ;
    private int STATUE = STATUS_NONE;
    boolean canFocusIn = false;
    boolean isFocusing = false;
    private Calendar mCalendar;
    private final double moveIs = 1.4;
    private long lastStaticStamp = 0;
    public static final int DELAY_DURATION = 500;
    private long staticTime = 0;

    private void restParams() {
        STATUE = STATUS_NONE;
        canFocusIn = false;
        mX = 0;
        mY = 0;
        mZ = 0;
    }

    private void checkStaticTime() {
        LogUtils.d(System.currentTimeMillis(), staticTime);
        if (System.currentTimeMillis() - staticTime > 800) {
            if (cameraPreview.getCamera() != null) {
                checkStartPreview();
            }
        }
    }

    private SensorEventListener sensorEventListener = new SensorEventListener() {
        @Override
        public void onSensorChanged(SensorEvent sensorEvent) {
            if (isFocusing) {
                restParams();
                return;
            }
            if (sensorEvent.sensor.getType() == Sensor.TYPE_ORIENTATION) {
                float yAngle = sensorEvent.values[1];
                // 获取与Z轴的夹角
                float zAngle = sensorEvent.values[2];
                if (Math.abs(zAngle - yAngle) > 40 && zAngle > 3f && zAngle <= 180f && Math.abs(yAngle) < 45) {
                    if (System.currentTimeMillis() - oritationTime > 100) {
                        if (oritationType == 3 || oritationType == 4) {
                            turnRight();
                        }
                        oritationType = 1;
                        oritationTime = System.currentTimeMillis();
//                    LogUtils.i("向左转");
                    }

                } else if (Math.abs(zAngle - yAngle) > 40 && zAngle < -3f && zAngle >= -180f && Math.abs(yAngle) < 60) {
                    if (System.currentTimeMillis() - oritationTime > 100) {
                        if (oritationType == 3 || oritationType == 4) {
                            turnLeft();
                        }
                        oritationType = 2;
                        oritationTime = System.currentTimeMillis();
//                    LogUtils.i("向右转");
                    }

                } else {
                    if (System.currentTimeMillis() - oritationTime > 100) {
                        if (oritationType == 1) {
                            turnBackLeft();
                        } else if (oritationType == 2) {
                            turnBackRight();
                        }
                        oritationType = 4;
                        oritationTime = System.currentTimeMillis();
//                    LogUtils.i("向下转");
                    }

                }
//            LogUtils.i(yAngle, zAngle);
            } else if (sensorEvent.sensor.getType() == Sensor.TYPE_ACCELEROMETER) {
                int x = (int) sensorEvent.values[0];
                int y = (int) sensorEvent.values[1];
                int z = (int) sensorEvent.values[2];
                mCalendar = Calendar.getInstance();
                long stamp = mCalendar.getTimeInMillis();
                int second = mCalendar.get(Calendar.SECOND);
                if (STATUE != STATUS_NONE) {
                    int px = Math.abs(mX - x);
                    int py = Math.abs(mY - y);
                    int pz = Math.abs(mZ - z);
                    double value = Math.sqrt(px * px + py * py + pz * pz);
                    if (value > moveIs) {
                        STATUE = STATUS_MOVE;
                    } else {
                        if (STATUE == STATUS_MOVE) {
                            lastStaticStamp = stamp;
                            canFocusIn = true;
                        }

                        if (canFocusIn) {
//                            LogUtils.d("传感器ccc");
                            needAlgFocus.set(false);
//                            checkStaticTime();
                            if (stamp - lastStaticStamp > DELAY_DURATION) {
                                //移动后静止一段时间，可以发生对焦行为
                                if (!isFocusing) {
                                    canFocusIn = false;
                                    checkStaticTime();
                                }
                            }
                        }
                        STATUE = STATUS_STATIC;
                        needAlgFocus.set(true);
                    }
                } else {
                    LogUtils.d("STATUS_STATIC");
                    lastStaticStamp = stamp;
                    STATUE = STATUS_STATIC;
                    needAlgFocus.set(true);
                }
                mX = x;
                mY = y;
                mZ = z;
            }
        }

        @Override
        public void onAccuracyChanged(Sensor sensor, int i) {
//            LogUtils.d(sensor, i);

        }
    };

    private void checkStartPreview() {
        if (isStartPreview.get()) {
            isFoucusFinish.set(false);
            try {
                final Camera camera = cameraPreview.getCamera();
                if (camera != null) {
                    camera.autoFocus(new Camera.AutoFocusCallback() {
                        @Override
                        public void onAutoFocus(boolean b, Camera camera) {
                            staticTime = System.currentTimeMillis();
                            camera.cancelAutoFocus();
                            isFoucusFinish.set(true);
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    private void turnLeft() {
        float rotation = previewNoIconBackBtn.getRotation();
        float rotation1 = cameraFlashModeBtn.getRotation();
        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", 0, -90f);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", 0, -90f);
        animator1.setDuration(300);
        animator1.start();
    }

    private void turnBackLeft() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", 90f, 0f);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", 90f, 0f);
        animator1.setDuration(300);
        animator1.start();
    }

    private void turnRight() {
        float rotation = previewNoIconBackBtn.getRotation();
        float rotation1 = cameraFlashModeBtn.getRotation();
        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", 0, 90f);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", 0, 90f);
        animator1.setDuration(300);
        animator1.start();
    }

    private void turnBackRight() {
        float rotation = previewNoIconBackBtn.getRotation();
        float rotation1 = cameraFlashModeBtn.getRotation();
        ObjectAnimator animator = ObjectAnimator.ofFloat(previewNoIconBackBtn, "rotation", -90f, 0);
        animator.setDuration(300);
        animator.start();

        ObjectAnimator animator1 = ObjectAnimator.ofFloat(cameraFlashModeBtn, "rotation", -90f, 0);
        animator1.setDuration(300);
        animator1.start();
    }


    /**
     * 隐藏虚拟按键，并且全屏
     */
    protected void hideBottomUIMenu() {
        if (!needShowStatusBar) {
            //隐藏虚拟按键，并且全屏
            if (Build.VERSION.SDK_INT > 11 && Build.VERSION.SDK_INT < 19) {
                View v = this.getWindow().getDecorView();
                v.setSystemUiVisibility(View.GONE);
            } else if (Build.VERSION.SDK_INT >= 19) {
                //for new api versions.
                View decorView = getWindow().getDecorView();
                int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
                        | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
                        | View.SYSTEM_UI_FLAG_IMMERSIVE;
                decorView.setSystemUiVisibility(uiOptions);
                getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            }
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (!needShowStatusBar) {
            if (hasFocus && Build.VERSION.SDK_INT >= 19) {
                View decorView = getWindow().getDecorView();
                decorView.setSystemUiVisibility(
                        View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                                | View.SYSTEM_UI_FLAG_FULLSCREEN
                                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
            }
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        //屏蔽back按键
        return (keyCode == KeyEvent.KEYCODE_BACK) && isInCamera.get() ? true : super.onKeyDown(keyCode, event);
    }

    private void resetBitmap() {
        isGetColorBitmap.set(false);
        isWaitingColor.set(false);
        isGetCutBitmap.set(false);
        isWaitingCut.set(false);
        cameraColorImg.setImageBitmap(null);
        if (colorBitmap != null && !colorBitmap.isRecycled()) {
            colorBitmap.recycle();
        }
        colorBitmap = null;
        cameraCutImg.setImageBitmap(null);
        if (cutBitmap != null && !cutBitmap.isRecycled()) {
            cutBitmap.recycle();
        }
        cutBitmap = null;
        previewData = null;
    }

    @Override
    public void onBackPressed() {
        if (backBtnStatus != 0) {
            return;
        }
        super.onBackPressed();
    }
}