package com.czur.scanpro.adapter

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ScreenUtils
import com.czur.scanpro.R
import com.czur.scanpro.entity.model.ColorSelectModel
import java.util.*

/**
 * Created by s<PERSON><PERSON> on 7/13/2020.
 */
class ColorPickerAdapter internal constructor(private var context: Context, private var colorCode: Int, private var isBrush: Boolean, colorPickerColors: List<ColorSelectModel>) : RecyclerView.Adapter<ColorPickerAdapter.ViewHolder>() {
    private var inflater: LayoutInflater
    private val colorPickerColors: List<ColorSelectModel>
    private var onColorPickerClickListener: OnColorPickerClickListener? = null
    private val width = ScreenUtils.getScreenWidth() / 9

    constructor(context: Context, colorCode: Int, isBrush: Boolean) : this(context, colorCode, isBrush, getDefaultColors(context, colorCode, isBrush)) {
        this.context = context
        this.isBrush = isBrush
        this.colorCode = colorCode
        inflater = LayoutInflater.from(context)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = inflater.inflate(R.layout.color_picker_item_list, parent, false)
        val lp = view.layoutParams
        lp.width = width
        lp.height = width
        view.layoutParams = lp
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.colorPickerView.background = colorPickerColors[position].drawable
        if (colorPickerColors[position].select) {
            holder.selectBg.visibility = View.VISIBLE
        } else {
            holder.selectBg.visibility = View.GONE
        }
        holder.itemView.setOnClickListener {
            colorPickerColors.forEachIndexed { index, value ->
                value.select = index == position
            }
            onColorPickerClickListener?.onColorPickerClickListener(colorPickerColors[position].color)
            notifyDataSetChanged()
        }
    }

    override fun getItemCount(): Int {
        return colorPickerColors.size
    }

    fun setOnColorPickerClickListener(onColorPickerClickListener: OnColorPickerClickListener?) {
        this.onColorPickerClickListener = onColorPickerClickListener
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var colorPickerView: View = itemView.findViewById(R.id.color_picker_view)
        var selectBg: View = itemView.findViewById(R.id.select_bg)
    }

    interface OnColorPickerClickListener {
        fun onColorPickerClickListener(colorCode: Int)
    }


    companion object {
        fun getDefaultColors(context: Context, colorCode: Int, isBrush: Boolean): List<ColorSelectModel> {
            val colorPickerColors: MutableList<ColorSelectModel> = ArrayList()
            (if (isBrush) context.getDrawable(R.drawable.oval_black_brush) else context.getDrawable(R.drawable.oval_black))?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.black),
                    it, ColorUtils.getColor(R.color.black) == colorCode)
            }?.let { colorPickerColors.add(it) }
            (if (isBrush) context.getDrawable(R.drawable.oval_white_brush) else context.getDrawable(R.drawable.oval_white))?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.white),
                    it, ColorUtils.getColor(R.color.white) == colorCode)
            }?.let { colorPickerColors.add(it) }
            context.getDrawable(R.drawable.oval_red)?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.red_de4d4d),
                    it, ColorUtils.getColor(R.color.red_de4d4d) == colorCode)
            }?.let { colorPickerColors.add(it) }
            context.getDrawable(R.drawable.oval_orange)?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.orange_ffa400),
                    it, ColorUtils.getColor(R.color.orange_ffa400) == colorCode)
            }?.let { colorPickerColors.add(it) }
            context.getDrawable(R.drawable.oval_yellow)?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.yellow_ffeb00),
                    it, ColorUtils.getColor(R.color.yellow_ffeb00) == colorCode)
            }?.let { colorPickerColors.add(it) }
            context.getDrawable(R.drawable.oval_green)?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.green_79d48d),
                    it, ColorUtils.getColor(R.color.green_79d48d) == colorCode)
            }?.let { colorPickerColors.add(it) }
            context.getDrawable(R.drawable.oval_blue)?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.blue_04ccee),
                    it, ColorUtils.getColor(R.color.blue_04ccee) == colorCode)
            }?.let { colorPickerColors.add(it) }
            context.getDrawable(R.drawable.oval_dark_blue)?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.dark_blue_0474ee),
                    it, ColorUtils.getColor(R.color.dark_blue_0474ee) == colorCode)
            }?.let { colorPickerColors.add(it) }
            context.getDrawable(R.drawable.oval_purple)?.let {
                ColorSelectModel(ColorUtils.getColor(R.color.purple_bb63db),
                    it, ColorUtils.getColor(R.color.purple_bb63db) == colorCode)
            }?.let { colorPickerColors.add(it) }
            return colorPickerColors
        }
    }

    init {
        inflater = LayoutInflater.from(context)
        this.colorPickerColors = colorPickerColors
    }
}