package com.czur.scanpro.ui.component.adjustEdge

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import androidx.core.content.ContextCompat
import android.view.View
import com.blankj.utilcode.util.SizeUtils
import com.czur.scanpro.R
import com.czur.scanpro.alg.Vec4f

class AdjustEdgeSimpleView(context: Context?) : View(context) {

    private var fillPaint = Paint()
    private var linePaint = Paint()
    var points: MutableList<PointF> = mutableListOf()
    var pointsWithMiddle: MutableList<PointF> = mutableListOf()
    var pointRect: MutableList<RectF> = mutableListOf()
    var pointCrossLines: MutableList<Vec4f> = mutableListOf()

    val dp20 = SizeUtils.dp2px(20f)

    init {
        fillPaint.run {
            isAntiAlias = true
            color = ContextCompat.getColor(getContext(), R.color.adjustEdgeSimpleRectFillColor)
            style = Paint.Style.FILL
        }
        linePaint.run {
            isAntiAlias = true
            color = ContextCompat.getColor(getContext(), R.color.adjustEdgeSimpleLineColor)
            style = Paint.Style.STROKE
            strokeWidth = SizeUtils.dp2px(2f).toFloat()
        }
    }

    fun showPoints() {
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (pointsWithMiddle.size > 0) {
            pointRect.forEach {

                val rectF = RectF(it.left + dp20, it.top + dp20, it.right + dp20, it.bottom + dp20)
                // 矩形半透填充背景
                canvas!!.drawRect(rectF, fillPaint)
                // 矩形红色边框
                canvas.drawRect(rectF, linePaint)
            }
            pointCrossLines.forEach {
                // 矩形红色连接线
                canvas!!.drawLine(it.point1.x + dp20, it.point1.y + dp20, it.point2.x + dp20, it.point2.y + dp20, linePaint)
            }
        }
    }
}