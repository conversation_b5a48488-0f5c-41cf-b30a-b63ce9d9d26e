package com.czur.scanpro.alg

import android.content.res.AssetManager
import android.graphics.Bitmap

/**
 * Created by Yz on 2018/12/27.
 * Email：<EMAIL>
 */
class SaomiaoNative {


    //初始化并且读取模型文件识别页码
    external fun initNativeAndReadXml(assetManager: AssetManager): Long

//    //获取4个角 12个点XY坐标
//    external fun getBorderPoints(AlgAdd: Long, buf: ByteArray, w: Int, h: Int): JniEntity

    //返回预览最后一帧的截图Bitmap
    external fun getPrintScreenImg(AlgAdd: Long, buf: ByteArray, w: Int, h: Int, config: Bitmap.Config): Bitmap

    //拍照获取4个角 4个点XY坐标并且返回裁剪后的Bitmap
    external fun getPointsAndCut(AlgAdd: Long, buf: ByteArray, w: Int, h: Int, bitmap: Bitmap): Bitmap

    //通过点剪裁图片
    external fun doColorMode(AlgAdd: Long, bitmap: Bitmap, path: String, smallPath: String)

    companion object {//被companion object包裹的语句都是private的

        private var instance: SaomiaoNative?= null

        @Synchronized fun getInstance(): SaomiaoNative?{
            if (instance == null){
                instance = SaomiaoNative()
            }
            return instance
        }
        init {
            System.loadLibrary("saomiao_native")
        }
    }

  

}
