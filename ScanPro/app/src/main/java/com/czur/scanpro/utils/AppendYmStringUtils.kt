package com.czur.scanpro.utils

import com.czur.scanpro.entity.model.ym.BusinessLicenceEntity
import com.czur.scanpro.entity.model.ym.DriveEntity
import com.czur.scanpro.entity.model.ym.IDCardEntity

/**
 * Created by Yz on 2019/5/25.
 * Email：<EMAIL>
 */
object AppendYmStringUtils {
    fun idCardToString(idCardEntity: IDCardEntity): StringBuffer {
        val strBuf = StringBuffer()
        if (!idCardEntity.name.isNullOrEmpty()) strBuf.append("姓名：").append(idCardEntity.name).append("\n")
        if (!idCardEntity.sex.isNullOrEmpty()) strBuf.append("性别：").append(idCardEntity.sex).append("\n")
        if (!idCardEntity.folk.isNullOrEmpty()) strBuf.append("民族：").append(idCardEntity.folk).append("\n")
        if (!idCardEntity.birt.isNullOrEmpty()) strBuf.append("出生：").append(idCardEntity.birt).append("\n")
        if (!idCardEntity.addr.isNullOrEmpty()) strBuf.append("住址：").append(idCardEntity.addr).append("\n")
        if (!idCardEntity.num.isNullOrEmpty()) strBuf.append("身份号码：").append(idCardEntity.num).append("\n")
        if (!idCardEntity.issue.isNullOrEmpty()) strBuf.append("签发机关：").append(idCardEntity.issue).append("\n")
        if (!idCardEntity.valid.isNullOrEmpty()) strBuf.append("有效期限：").append(idCardEntity.valid).append("\n")
        return strBuf
    }

    fun businessLicenceToString(businessLicenceEntity: BusinessLicenceEntity): StringBuffer {
        val strBuf = StringBuffer()
        if (!businessLicenceEntity.title.isNullOrEmpty()) strBuf.append("标题：").append(businessLicenceEntity.title).append("\n")
        if (!businessLicenceEntity.regeditNo.isNullOrEmpty()) strBuf.append("注册号：").append(businessLicenceEntity.regeditNo).append("\n")
        if (!businessLicenceEntity.name.isNullOrEmpty()) strBuf.append("名称：").append(businessLicenceEntity.name).append("\n")
        if (!businessLicenceEntity.type.isNullOrEmpty()) strBuf.append("类型：").append(businessLicenceEntity.type).append("\n")
        if (!businessLicenceEntity.address.isNullOrEmpty()) strBuf.append("住址：").append(businessLicenceEntity.address).append("\n")
        if (!businessLicenceEntity.legal.isNullOrEmpty()) strBuf.append("法定代表人：").append(businessLicenceEntity.legal).append("\n")
        if (!businessLicenceEntity.registeredCapital.isNullOrEmpty()) strBuf.append("注册资本：").append(businessLicenceEntity.registeredCapital).append("\n")
        if (!businessLicenceEntity.paidCapital.isNullOrEmpty()) strBuf.append("实收资本：").append(businessLicenceEntity.paidCapital).append("\n")
        if (!businessLicenceEntity.establishDate.isNullOrEmpty()) strBuf.append("成立日期：").append(businessLicenceEntity.establishDate).append("\n")
        if (!businessLicenceEntity.regeditDate.isNullOrEmpty()) strBuf.append("注册日期：").append(businessLicenceEntity.regeditDate).append("\n")
        if (!businessLicenceEntity.period.isNullOrEmpty()) strBuf.append("营业期限：").append(businessLicenceEntity.period).append("\n")
        if (!businessLicenceEntity.business.isNullOrEmpty()) strBuf.append("经营范围：").append(businessLicenceEntity.business).append("\n")
        if (!businessLicenceEntity.issueUnit.isNullOrEmpty()) strBuf.append("颁发单位：").append(businessLicenceEntity.issueUnit).append("\n")
        if (!businessLicenceEntity.date.isNullOrEmpty()) strBuf.append("日期：").append(businessLicenceEntity.date).append("\n")
        if (!businessLicenceEntity.form.isNullOrEmpty()) strBuf.append("组成形式：").append(businessLicenceEntity.form).append("\n")
        return strBuf
    }

    fun driveToString(driveEntity:  DriveEntity): StringBuffer {
        val strBuf = StringBuffer()
        if (!driveEntity.name.isNullOrEmpty()) strBuf.append("姓名：").append(driveEntity.name).append("\n")
        if (!driveEntity.cardNo.isNullOrEmpty()) strBuf.append("证号：").append(driveEntity.cardNo).append("\n")
        if (!driveEntity.sex.isNullOrEmpty()) strBuf.append("性别：").append(driveEntity.sex).append("\n")
        if (!driveEntity.nation.isNullOrEmpty()) strBuf.append("国籍：").append(driveEntity.nation).append("\n")
        if (!driveEntity.address.isNullOrEmpty()) strBuf.append("住址：").append(driveEntity.address).append("\n")
        if (!driveEntity.birthday.isNullOrEmpty()) strBuf.append("出生日期：").append(driveEntity.birthday).append("\n")
        if (!driveEntity.issueDate.isNullOrEmpty()) strBuf.append("初次领证日期：").append(driveEntity.issueDate).append("\n")
        if (!driveEntity.drivingType.isNullOrEmpty()) strBuf.append("准驾车型：").append(driveEntity.drivingType).append("\n")
        if (!driveEntity.registerDate.isNullOrEmpty()) strBuf.append("有效起始日期：").append(driveEntity.registerDate).append("\n")
        if (!driveEntity.validperiod.isNullOrEmpty()) strBuf.append("有效期限：").append(driveEntity.validperiod).append("\n")
        if (!driveEntity.fileNo.isNullOrEmpty()) strBuf.append("档案编号：").append(driveEntity.fileNo).append("\n")
        if (!driveEntity.record.isNullOrEmpty()) strBuf.append("记录：").append(driveEntity.record).append("\n")
        return strBuf
    }


}