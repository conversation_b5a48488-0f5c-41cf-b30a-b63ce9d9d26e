<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="47dp"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">


    <RelativeLayout
        android:id="@+id/camera_blum"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_centerVertical="true">
        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/no_back_top_bar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/black_24"
            android:text="@string/camera_and_album"
            android:textSize="18sp" />
        <ImageView
            android:id="@+id/top_bar_iv"
            android:layout_marginLeft="2dp"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerInParent="true"
            android:layout_toRightOf="@+id/no_back_top_bar_title"
            android:background="@mipmap/ic_up_expand"/>
    </RelativeLayout>
        <RelativeLayout
            android:id="@+id/no_back_top_bar_cancel"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17dp"
                android:text="@string/cancel"
                android:textColor="@color/red_d54146"
                android:textSize="17sp" />

        </RelativeLayout>


    </RelativeLayout>


</LinearLayout>