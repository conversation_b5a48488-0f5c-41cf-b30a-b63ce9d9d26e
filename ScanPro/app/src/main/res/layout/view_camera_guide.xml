<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layoutBody"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <!-- step 1 -->
    <View
        android:id="@+id/whiteBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="70dp"
        android:background="@color/whiteOpaque100"
        app:layout_constraintDimensionRatio="750:858"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/firstAlertText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0"
        android:textColor="@color/blackOpaque80"
        android:textSize="24sp"

        app:layout_constraintBottom_toBottomOf="@+id/whiteBg"
        app:layout_constraintEnd_toEndOf="@+id/whiteBg"
        app:layout_constraintStart_toStartOf="@+id/whiteBg"
        app:layout_constraintTop_toTopOf="@+id/whiteBg" />

    <!-- step 2 -->
    <ImageView
        android:id="@+id/wrongBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:alpha="0"
        android:contentDescription="@null"
        android:src="@mipmap/camera_guide_wrong_bg"
        app:layout_constraintBottom_toBottomOf="@+id/whiteBg"
        app:layout_constraintEnd_toEndOf="@+id/whiteBg"
        app:layout_constraintStart_toStartOf="@+id/whiteBg"
        app:layout_constraintTop_toTopOf="@+id/whiteBg" />

    <LinearLayout
        android:id="@+id/alertBg"
        android:layout_width="wrap_content"
        android:layout_height="29dp"
        android:layout_marginBottom="27dp"
        android:alpha="0"
        android:background="@drawable/camera_guide_alert_bg"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/wrongBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/alertIcon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="19dp"
            android:contentDescription="@null"
            android:src="@mipmap/camera_guide_alert_icon_wrong" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/alertText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="19dp"
            android:textColor="#fafafa"
            android:textSize="18sp" />
    </LinearLayout>

    <!-- step 3 -->
    <View
        android:id="@+id/rightBg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:alpha="0"
        android:background="#212529"
        android:contentDescription="@null"
        app:layout_constraintBottom_toBottomOf="@+id/whiteBg"
        app:layout_constraintEnd_toEndOf="@+id/whiteBg"
        app:layout_constraintStart_toStartOf="@+id/whiteBg"
        app:layout_constraintTop_toTopOf="@+id/whiteBg" />

    <!-- step 4 -->
    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/secondAlertText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="9dp"
        android:layout_marginEnd="19dp"
        android:alpha="0"
        android:background="@drawable/camera_guide_alert_bg"
        android:paddingStart="19dp"
        android:paddingEnd="19dp"
        android:textColor="#fafafa"
        android:textSize="18sp"

        app:layout_constraintBottom_toBottomOf="@id/alertBg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- step 6 -->
    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/replayBtn"
        android:layout_width="50dp"
        android:layout_height="20dp"
        android:layout_marginTop="18dp"
        android:alpha="0"
        android:background="@drawable/camera_guide_replay_btn_bg"
        android:gravity="center"
        android:text="@string/camera_guide_replay_btn"
        android:textColor="#ffffff"
        android:textSize="12sp"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rightBg" />

    <View
        android:id="@+id/realReplayBtn"
        android:layout_width="100dp"
        android:layout_height="46dp"
        app:layout_constraintBottom_toBottomOf="@+id/replayBtn"
        app:layout_constraintEnd_toEndOf="@+id/replayBtn"
        app:layout_constraintStart_toStartOf="@+id/replayBtn"
        app:layout_constraintTop_toTopOf="@+id/replayBtn" />

    <View
        android:id="@+id/nextBtn"
        android:layout_width="161dp"
        android:layout_height="36dp"
        android:alpha="0"
        android:background="@drawable/camera_guide_next_btn_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/replayBtn" />

    <LinearLayout
        android:id="@+id/nextBtnInsideBody"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:alpha="0"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/nextBtn"
        app:layout_constraintEnd_toEndOf="@+id/nextBtn"
        app:layout_constraintStart_toStartOf="@+id/nextBtn"
        app:layout_constraintTop_toTopOf="@+id/nextBtn">

        <ImageView
            android:id="@+id/nextBtnIcon"
            android:layout_width="13.5dp"
            android:layout_height="12.5dp"
            android:layout_gravity="center_vertical"
            android:contentDescription="@null"
            android:src="@mipmap/camera_guide_next_btn_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="7dp"
            android:text="@string/camera_guide_next_btn"
            android:textColor="#ffffff"
            android:textSize="15sp" />
    </LinearLayout>

    <View
        android:id="@+id/realNextBtn"
        android:layout_width="161dp"
        android:layout_height="46dp"
        app:layout_constraintBottom_toBottomOf="@+id/nextBtn"
        app:layout_constraintEnd_toEndOf="@+id/nextBtn"
        app:layout_constraintStart_toStartOf="@+id/nextBtn"
        app:layout_constraintTop_toTopOf="@+id/nextBtn" />
</androidx.constraintlayout.widget.ConstraintLayout>