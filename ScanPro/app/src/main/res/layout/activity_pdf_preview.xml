<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/black_2a"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/pdf_preview_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/black_2a">

        <ImageView
            android:id="@+id/preview_no_icon_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:padding="10dp"
            android:src="@mipmap/white_back_icon" />


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/preview_no_icon_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="80dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="18sp" />


        <RelativeLayout
            android:id="@+id/preview_no_icon_share"
            android:layout_width="42.5dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_marginRight="8.5dp">

            <ImageView

                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_centerInParent="true"
                android:background="@mipmap/share_icon"
                android:padding="10dp" />

        </RelativeLayout>

    </RelativeLayout>


    <com.github.barteksc.pdfviewer.PDFView
        android:id="@+id/pdfView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/pdf_preview_top_bar"
        android:background="@color/black" />

</RelativeLayout>