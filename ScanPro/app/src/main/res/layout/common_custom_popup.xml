<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rect_6_bg_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:layout_marginLeft="29dp"
            android:layout_marginRight="29dp">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_24"
                android:textSize="16sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/title"
                android:layout_marginTop="12.5dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black_24"
                android:textSize="12sp" />


        </RelativeLayout>

        <EditText
            android:id="@+id/edt"
            android:layout_width="216.5dp"
            android:layout_height="22dp"
            android:layout_marginBottom="10dp"
            android:maxLength="20"
            android:paddingLeft="3dp"
            android:singleLine="true"

            android:textColor="@color/gray_ec"
            android:textSize="12sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_ec"></View>
        <!-- button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/negative_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"

                android:textColor="@color/black_24"
                android:textSize="14sp" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_ec"></View>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/positive_button"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text=""
                android:textSize="14sp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>