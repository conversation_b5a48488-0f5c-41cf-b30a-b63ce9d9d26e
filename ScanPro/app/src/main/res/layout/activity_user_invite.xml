<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    tools:ignore="MissingPrefix">
    <include
        android:id="@+id/normal_top_bar"
        layout="@layout/layout_normal_f9_top_bar" />
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        android:scrollbars="none"
        android:layout_below="@+id/normal_top_bar">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="16dp"
                android:lineSpacingExtra="3dp"
                android:textSize="13sp"
                android:text="@string/share_detail" />
            <LinearLayout
                android:id="@+id/share_rule_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="20dp">
                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/info_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/share_rule"
                    android:textColor="@color/blue_0497f4"
                    android:textSize="12sp" />
                <ImageView
                    android:id="@+id/info_img"
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:src="@mipmap/blue_arrow" />
            </LinearLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:id="@+id/rl_parent"
                android:layout_gravity="center_horizontal"
                android:layout_height="wrap_content">
                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/tv_code"
                    android:layout_centerInParent="true"
                    android:layout_width="wrap_content"
                    android:textSize="20sp"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:id="@+id/invite_img"
                    android:layout_width="275dp"
                    android:layout_height="400dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center_horizontal"
                    android:src="@mipmap/invite" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/share_rl"
                android:layout_marginTop="35dp"
                android:layout_width="match_parent"
                android:layout_height="120dp">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true">
                    <ImageView
                        android:id="@+id/wechat_btn"
                        android:layout_width="36dp"
                        android:layout_height="30dp"
                        android:src="@mipmap/invite_wechat" />
                    <ImageView
                        android:id="@+id/moment_btn"
                        android:layout_width="31dp"
                        android:layout_height="31dp"
                        android:layout_marginStart="105dp"
                        android:src="@mipmap/invite_moment" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true">
                    <View
                        android:layout_width="70dp"
                        android:layout_height="0.5dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/gray_dd" />
                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="16dp"
                        android:layout_marginRight="16dp"
                        android:text="@string/share_to"
                        android:textColor="@color/gray_c4"
                        android:textSize="10sp" />
                    <View
                        android:layout_width="70dp"
                        android:layout_height="0.5dp"
                        android:layout_gravity="center_vertical"
                        android:background="@color/gray_dd" />
                </LinearLayout>
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>

</RelativeLayout>