<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    android:orientation="vertical">

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/et_move_prompt_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12.5dp"
        android:text="@string/choose_move"
        android:textColor="@color/black_24"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/fileMoveBackBtn"
        android:layout_width="29.5dp"
        android:layout_height="36dp"
        android:layout_marginLeft="6dp"
        android:layout_marginTop="6dp"
        android:padding="10dp"
        android:src="@mipmap/black_back_icon"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/fileMoveCreateBtn"
        app:layout_constraintTop_toBottomOf="@+id/et_move_prompt_tv" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/file_move_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/document"
        android:textColor="@color/black_24"
        android:textSize="18sp"

        app:layout_constraintBottom_toBottomOf="@+id/fileMoveBackBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_move_prompt_tv"
        app:layout_constraintTop_toTopOf="@+id/fileMoveBackBtn" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/fileMoveCreateBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="15dp"
        android:text="@string/create_folder"
        android:textColor="@color/black_24"
        android:textSize="15sp"
        app:layout_constraintBottom_toBottomOf="@+id/fileMoveBackBtn"
        app:layout_constraintLeft_toRightOf="@+id/fileMoveBackBtn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/fileMoveBackBtn" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/fileMoveRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12.5dp"
        android:background="@color/gray_f9"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/file_move_title_tv"
        app:layout_constraintVertical_bias="0" />


    <RelativeLayout
        android:id="@+id/moveEmptyLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12.5dp"
        android:background="@color/gray_f9"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/file_move_title_tv"
        app:layout_constraintVertical_bias="0"
        tools:ignore="MissingPrefix">


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="150dp"
            android:orientation="vertical">

            <ImageView
                android:layout_width="133dp"
                android:layout_height="138.5dp"
                android:src="@mipmap/empty_logo" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="31dp"
                android:text="@string/has_no_folder"
                android:textColor="@color/gray_e4"
                android:textSize="12sp" />

        </LinearLayout>


    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

