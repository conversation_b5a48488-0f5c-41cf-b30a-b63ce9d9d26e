<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_file_move_folder_rl"
    android:layout_width="match_parent"
    android:layout_height="49.5dp"
    android:background="@color/gray_f9"
    android:orientation="vertical">

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/item_file_move_folder_img"
        android:layout_width="60dp"
        android:layout_height="35dp"
        android:layout_marginStart="17dp"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundedCornerRadius="5dp" />


    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/item_file_move_folder_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="17dp"
        android:singleLine="true"
        android:ellipsize="end"
        android:textColor="@color/black_24"
        android:textSize="15sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/item_file_move_folder_img"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="17dp"
        android:layout_marginRight="17dp"
        android:background="@color/gray_e4"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

