<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    android:id="@+id/equipment_rl"
    android:layout_width="match_parent"
    android:layout_height="87.5dp">

    <RelativeLayout
        android:id="@+id/item_album_folder_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/album_folder_first_photo"
            android:layout_width="59.5dp"
            android:layout_height="81.5dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="17dp"
            fresco:placeholderImage="@mipmap/default_gallery_img"
            fresco:placeholderImageScaleType="centerCrop"
            fresco:roundedCornerRadius="10dp" />

        <View
            android:layout_width="358dp"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@color/gray_e6" />

        <TextView
            android:id="@+id/album_folder_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/album_folder_first_photo"
            android:layout_marginLeft="20dp"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/album_folder_right_arrow"
            android:layout_width="20dp"
            android:layout_height="18dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="17dp"
            android:visibility="gone"
            android:src="@mipmap/ic_selected" />

    </RelativeLayout>


</RelativeLayout>
