<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:ignore="MissingPrefix">


    <LinearLayout
        android:id="@+id/change_keyboard_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView
                android:id="@+id/login_back_btn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/black_back_icon" />

        </LinearLayout>


        <RelativeLayout
            android:id="@+id/login_user_head_rl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="33dp"
            android:layout_marginBottom="33.5dp">

            <ImageView
                android:layout_width="132dp"
                android:layout_height="139dp"
                android:src="@mipmap/welcome_main_icon" />


        </RelativeLayout>

        <LinearLayout
            android:id="@+id/login_input_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <com.czur.scanpro.ui.component.NoHintEditText
                android:id="@+id/loginUserNameEdt"
                android:layout_width="match_parent"
                android:layout_height="39.5dp"
                android:background="@null"
                android:digits="@string/account_digits"
                android:gravity="center"
                android:hint="@string/login_hint"
                android:maxLines="1"
                android:textColor="@color/black_24"
                android:textColorHint="@color/gray_e3dd"
                android:textSize="18sp" />

            <View
                android:layout_width="170dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/gray_e3dd" />

            <com.czur.scanpro.ui.component.NoHintEditText
                android:id="@+id/loginUserPasswordEdt"
                android:layout_width="match_parent"
                android:layout_height="39.5dp"
                android:layout_marginTop="25dp"
                android:background="@null"
                android:digits="@string/password_digits"
                android:gravity="center"
                android:hint="@string/login_password_hint"
                android:maxLength="20"
                android:maxLines="1"
                android:textColor="@color/black_24"
                android:textColorHint="@color/gray_e3dd"
                android:textSize="18sp" />

            <View
                android:layout_width="170dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/gray_e3dd" />

            <LinearLayout
                android:id="@+id/user_privacy_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_gravity="center_horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/user_privacy_img"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:padding="10dp"
                    android:src="@mipmap/sitting_no_select"
                    />

                <TextView
                    android:id="@+id/user_privacy_agree_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/user_privacy_register"
                    android:textColor="@color/gray_c4"
                    android:textStyle="bold"
                    android:textSize="15sp" />
                <TextView
                    android:id="@+id/user_terms_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/privacy_terms"
                    android:textColor="@color/identifying_code"
                    android:textStyle="bold"
                    android:textSize="15sp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/and"
                    android:textColor="@color/gray_c4"
                    android:textStyle="bold"
                    android:textSize="15sp" />
                <TextView
                    android:id="@+id/user_privacy_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/privacy_policy1"
                    android:textColor="@color/identifying_code"
                    android:textStyle="bold"
                    android:textSize="15sp" />

            </LinearLayout>
            <RelativeLayout
                android:id="@+id/login_btn"
                android:layout_width="161dp"
                android:layout_height="36dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="14dp"
                app:bl_corners_radius="36dp"
                app:bl_solid_color="@color/gray_e6">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/login_btn_img"
                        android:layout_width="14dp"
                        android:layout_height="13dp"
                        android:layout_gravity="center_vertical"
                        android:src="@mipmap/login_icon_unselected" />

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/login_btn_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="7dp"
                        android:text="@string/login_btn_text"
                        android:textColor="@color/gray_fa"
                        android:textSize="15sp" />

                </LinearLayout>

            </RelativeLayout>


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="15dp"
                android:orientation="horizontal">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/login_forget_password_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/forget_password"
                    android:textColor="@color/gray_c4"
                    android:textSize="15sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/login_new_user_register_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="35dp"
                    android:text="@string/new_user_to_register"
                    android:textColor="@color/gray_c4"
                    android:textSize="15sp" />

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/ll_other_login"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="35dp">

        <View
            android:layout_width="50dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center_vertical"
            android:background="@color/gray_c4" />

        <TextView
            android:id="@+id/social_account_login_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/other_login"
            android:textColor="@color/gray_c4"
            android:layout_marginLeft="11dp"
            android:layout_marginRight="11dp"
            android:textSize="15sp" />

        <View
            android:layout_width="50dp"
            android:layout_height="0.5dp"
            android:layout_gravity="center_vertical"
            android:background="@color/gray_c4" />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/ll_other_login"
        android:layout_width="match_parent"
        android:layout_height="84dp"
        android:layout_alignParentBottom="true"
        android:layout_centerInParent="true"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/weixin_account"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@mipmap/weixin_icon" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/qq_account"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@mipmap/qq_icon" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/weibo_account"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@mipmap/weibo_icon" />
        </LinearLayout>
    </LinearLayout>


</RelativeLayout>