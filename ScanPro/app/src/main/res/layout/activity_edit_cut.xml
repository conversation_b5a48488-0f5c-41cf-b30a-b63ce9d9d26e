<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_body"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- Image Cropper fill the remaining available height -->
    <com.czur.scanpro.ui.component.cropper.CropImageView
        android:id="@+id/cropImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="108dp"
        android:background="@color/black"
        app:cropBorderCornerColor="@color/transparent"
        app:cropBorderLineColor="@color/gray_e4"
        app:cropBorderLineThickness="1dp"
        app:cropGuidelines="on"
        app:cropTouchRadius="100dp" />


    <LinearLayout
        android:id="@+id/cut_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black_2a"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/cancelBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:padding="10dp"
                android:text="@string/cancel"
                android:textColor="@color/white"
                android:textSize="15sp" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/userTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/black_24"
                android:textSize="18sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/commitBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="5dp"
                android:padding="10dp"
                android:text="@string/ok"
                android:textColor="@color/red_de4d4d"
                android:textSize="15sp" />

        </RelativeLayout>


    </LinearLayout>

</RelativeLayout>