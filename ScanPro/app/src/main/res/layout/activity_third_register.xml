<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/register_top_bar"
        layout="@layout/layout_normal_white_top_bar" />

    <LinearLayout
        android:id="@+id/change_keyboard_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="130dp"
        android:gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/register_input_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <com.czur.scanpro.ui.component.NoHintEditText
                android:id="@+id/pswEdt"
                android:layout_width="match_parent"
                android:layout_height="39.5dp"
                android:width="170dp"
                android:background="@null"
                android:digits="@string/account_digits"
                android:gravity="center"
                android:hint="@string/input_new_password"
                android:maxLines="1"
                android:textColor="@color/black_24"
                android:textColorHint="@color/gray_e3dd"
                android:textSize="18sp" />

            <View
                android:layout_width="170dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/gray_e3dd" />


            <View
                android:layout_width="170dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/gray_e3dd" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/confirmBtn"
                android:layout_width="161dp"
                android:layout_height="36dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="7dp"
                android:layout_marginTop="34dp"
                android:gravity="center"
                android:text="@string/confirm"
                android:textColor="@color/gray_fa"
                android:textSize="15sp"

                app:bl_corners_radius="36dp"
                app:bl_solid_color="@color/gray_e6" />


        </LinearLayout>

    </LinearLayout>


</RelativeLayout>