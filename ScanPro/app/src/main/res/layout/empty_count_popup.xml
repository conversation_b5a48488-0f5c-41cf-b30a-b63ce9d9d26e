<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingPrefix">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:bl_corners_radius="8dp"
        app:bl_solid_color="@color/gray_fa">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="38dp">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:gravity="center"
                android:textColor="@color/black_24"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentRight="true"
                android:padding="10dp"
                android:src="@mipmap/pdf_close" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="100dp">

            <ImageView
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_centerInParent="true"
                android:src="@mipmap/no_count_icon" />


        </RelativeLayout>

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_marginBottom="40dp"
            android:gravity="center"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/confirm_advanced_btn"
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="1"
            android:background="@drawable/btn_rect_30_bg_black_24"
            android:gravity="center"
            android:text="@string/to_know_vip"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/cloud_btn"
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="1"
            android:background="@drawable/btn_rect_30_bg_red_24"
            android:gravity="center"
            android:visibility="gone"
            android:text="@string/cloud_ocr"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</RelativeLayout>