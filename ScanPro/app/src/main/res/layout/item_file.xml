<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/file_inner_item"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:bl_corners_radius="6dp"
    app:bl_solid_color="@color/white"
    app:bl_stroke_color="@color/gray_c4"
    app:bl_stroke_width="0.5dp">


    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/file_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="0.5dp"
        app:actualImageScaleType="fitCenter" />

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/bg_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:background="@color/whiteTransparent50"
        android:visibility="gone"
        app:roundedCornerRadius="6dp" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/tv_index"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="28dp"
        android:minHeight="28dp"
        android:singleLine="true"
        android:layout_centerInParent="true"
        android:enabled="false"
        android:background="@drawable/red_oval"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="14sp" />


</RelativeLayout>

