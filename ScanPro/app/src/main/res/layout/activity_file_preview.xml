<?xml version="1.0" encoding="UTF-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/black_2a"
    android:orientation="vertical">

    <com.czur.scanpro.ui.component.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        android:layout_above="@+id/file_preview_bottom_ll"
        android:layout_below="@+id/file_preview_top_bar" />

    <RelativeLayout
        android:id="@+id/addTagRl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/file_preview_top_bar"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/btn_rec_5_bg_with_white"
        android:padding="3dp">

        <ImageView
            android:id="@+id/previewAddTagImg"
            android:layout_width="10dp"
            android:layout_height="12dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="7dp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/previewAddTagTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="7dp"
            android:layout_toEndOf="@+id/previewAddTagImg"
            android:text="@string/add_tag"
            android:textColor="@color/red_de4d4d"
            android:textSize="14sp" />

    </RelativeLayout>

    <LinearLayout

        android:id="@+id/file_preview_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/black_2a"
        android:paddingTop="5dp"
        android:paddingBottom="5dp">

        <RelativeLayout
            android:id="@+id/filePreviewEditRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/file_preview_edit_img"
                android:layout_width="22.5dp"
                android:layout_height="22.5dp"
                android:layout_centerHorizontal="true"
                android:background="@mipmap/preview_edit" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/file_preview_editr_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/file_preview_edit_img"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/edit"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/filePreviewOcrRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/file_preview_ocr_img"
                android:layout_width="22.5dp"
                android:layout_height="22.5dp"
                android:layout_centerHorizontal="true"
                android:background="@mipmap/preview_ocr" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/filePreviewOcrTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/file_preview_ocr_img"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/ocr"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/filePreviewHandwritingRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/filePreviewHandwritingImg"
                android:layout_width="22.5dp"
                android:layout_height="22.5dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/selector_handwriting" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/filePreviewHandwritingTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/filePreviewHandwritingImg"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/handwriting"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/filePreviewMarkRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/file_preview_mark_img"
                android:layout_width="22.5dp"
                android:layout_height="22.5dp"
                android:layout_centerHorizontal="true"
                android:background="@mipmap/preview_mark" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/file_preview_mark_img"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/mark"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/filePreviewMoreRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <ImageView
                android:id="@+id/file_preview_more_img"
                android:layout_width="22.5dp"
                android:layout_height="22.5dp"
                android:layout_centerHorizontal="true"
                android:background="@mipmap/file_more_icon" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/file_preview_more_img"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:text="@string/more"
                android:textColor="@color/white"
                android:textSize="9sp" />
        </RelativeLayout>

    </LinearLayout>


    <RelativeLayout
        android:id="@+id/file_preview_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/black_2a">

        <ImageView
            android:id="@+id/previewFileBackBtn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:padding="10dp"
            android:src="@mipmap/white_back_icon" />


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/previewFileTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/white"
            android:textSize="18sp" />


        <RelativeLayout
            android:id="@+id/previewFileShare"
            android:layout_width="42.5dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_marginRight="8.5dp">

            <ImageView
                android:layout_width="22.5dp"
                android:layout_height="22.5dp"
                android:layout_centerInParent="true"
                android:background="@mipmap/share_icon"
                android:padding="10dp" />

        </RelativeLayout>

    </RelativeLayout>


</RelativeLayout>