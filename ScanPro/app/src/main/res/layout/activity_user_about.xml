<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    tools:ignore="MissingPrefix">

    <LinearLayout
        android:id="@+id/user_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView

                android:id="@+id/userBackBtn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/black_back_icon" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/userTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/black_24"
                android:textSize="18sp" />


        </RelativeLayout>


    </LinearLayout>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">

        <LinearLayout
            android:id="@+id/about_icon_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"

            android:orientation="vertical">

            <ImageView
                android:layout_width="132.5dp"
                android:layout_height="136dp"
                android:src="@mipmap/about_scan_pro_icon" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/versionTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:textColor="@color/gray_c4"
                android:textSize="15sp" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/updateRl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_below="@+id/about_icon_ll"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="80dp"
            android:layout_marginRight="16dp"
            app:bl_corners_radius="5dp"
            app:bl_solid_color="@color/white"
            app:bl_stroke_color="@color/gray_e1"
            app:bl_stroke_width="0.5dp">

            <ImageView
                android:id="@+id/user_pdf_img"
                android:layout_width="21dp"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:adjustViewBounds="true"
                android:scaleType="fitXY"
                android:src="@mipmap/user_about" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/user_pdf_img"
                android:text="@string/app_update"
                android:textSize="15sp"
                android:textColor="@color/black_24" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/newVersionTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toLeftOf="@+id/right_img"
                android:text="@string/new_version_find"
                android:textColor="@color/red_de4d4d"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/right_img"
                android:layout_width="6.5dp"
                android:layout_height="12.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="14dp"
                android:src="@mipmap/user_navigate_icon" />


        </RelativeLayout>

    </RelativeLayout>


</RelativeLayout>