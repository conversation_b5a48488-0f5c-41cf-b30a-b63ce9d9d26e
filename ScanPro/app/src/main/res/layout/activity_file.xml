<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:id="@+id/blurView"
    android:layout_width="match_parent"
    android:background="@color/gray_f9"
    android:layout_height="match_parent">


    <RelativeLayout
        android:id="@+id/file_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <RelativeLayout
            android:id="@+id/file_inside_top_bar"
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView
                android:id="@+id/fileBackBtn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/black_back_icon" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/fileTitleTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="80dp"
                android:layout_marginRight="80dp"
                android:ellipsize="end"
                android:maxLength="20"
                android:singleLine="true"
                android:textColor="@color/black_24"
                android:textSize="18sp" />


            <RelativeLayout
                android:id="@+id/file_unselected_top_bar_rl"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true">

                <ImageView
                    android:id="@+id/fileMultiSelectBtn"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="11dp"
                    android:padding="10dp"
                    android:src="@mipmap/file_check_icon" />


                <RelativeLayout
                    android:id="@+id/fileSettingBtn"
                    android:layout_width="32.5dp"
                    android:layout_height="match_parent"
                    android:layout_toRightOf="@+id/fileMultiSelectBtn"
                    android:background="@color/gray_f9">

                    <ImageView
                        android:layout_width="17dp"
                        android:layout_height="17dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="16dp"
                        android:background="@mipmap/file_setting_icon" />

                </RelativeLayout>


            </RelativeLayout>
        </RelativeLayout>


    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/multiRl"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/gray_f9"
        android:visibility="gone">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/fileSelectAllBtn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingLeft="18.5dp"
            android:textColor="@color/red_de4d4d"
            android:textSize="17sp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/fileTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/black_24"
            android:textSize="18sp" />


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/fileCancelBtn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center_vertical"
            android:paddingRight="18.5dp"
            android:textColor="@color/red_de4d4d"
            android:textSize="17sp" />

    </RelativeLayout>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableAutoLoadMore="false"
        app:srlEnableLoadMore="false"
        android:layout_below="@+id/file_top_bar"
        app:srlEnableOverScrollBounce="true"
        app:srlEnableOverScrollDrag="true"
        app:srlEnableRefresh="false">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/file_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:scrollbars="none" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>

    <RelativeLayout
        android:id="@+id/emptyLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/file_top_bar"
        android:background="@color/gray_f9"
        tools:ignore="MissingPrefix">


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="150dp"
            android:orientation="vertical">

            <ImageView
                android:layout_width="133dp"
                android:layout_height="138.5dp"
                android:src="@mipmap/empty_logo" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="31dp"
                android:text="@string/has_not_file"
                android:textColor="@color/gray_e4"
                android:textSize="12sp" />

        </LinearLayout>


    </RelativeLayout>

    <ImageView
        android:id="@+id/fileGuideImg"
        android:layout_width="12dp"
        android:layout_height="9dp"
        android:layout_marginTop="33dp"
        android:layout_marginEnd="25dp"
        android:layout_alignEnd="@+id/fileGuideTv"
        android:src="@drawable/vector_drawable_gray_triangle" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/fileGuideTv"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="30dp"
        android:layout_below="@+id/fileGuideImg"
        android:background="@drawable/btn_rec_5_bg_with_gray_without_bottom"
        android:lineSpacingExtra="4dp"
        android:paddingLeft="12dp"
        android:paddingTop="10dp"
        android:layout_alignParentEnd="true"
        android:paddingRight="12dp"
        android:paddingBottom="23dp"
        android:text="@string/file_prompt"
        android:textColor="@color/gray_93"
        android:textSize="12sp" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/fileHideTv"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/fileGuideTv"
        android:layout_alignEnd="@+id/fileGuideTv"
        android:background="@drawable/btn_rec_5_bg_with_gray_without_top"
        android:gravity="center"
        android:paddingBottom="5dp"
        android:text="@string/not_show"
        android:textColor="@color/red_de4d4d"
        android:textSize="12sp" />

    <RelativeLayout
        android:id="@+id/fileScanBtn"
        android:layout_width="150dp"
        android:layout_height="36dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="14dp"
        android:background="@color/black"
        app:bl_corners_radius="20dp"
        app:bl_solid_color="@color/black_24"
        tools:ignore="MissingPrefix">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="8dp"
                android:src="@mipmap/main_scan_icon" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/scan"
                android:textColor="@color/red_d54146"
                android:textSize="16sp" />
        </LinearLayout>


    </RelativeLayout>

    <LinearLayout
        android:id="@+id/fileBottomLl"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:visibility="gone">

        <RelativeLayout
            android:id="@+id/fileShareRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileShareImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_share" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileShareTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileShareImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/share"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/filePdfRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/filePdfImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_pdf" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/filePdfTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/filePdfImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/PDF"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/fileSaveRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileSaveImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_save" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileSaveTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileSaveImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/save"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/fileDeleteRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileDeleteImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileDeleteTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileDeleteImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/fileTagRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileTagImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_tag" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileTagTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileTagImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/edit_tag"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/cancelFileTagRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/cancelFileTagImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_cancel_tag" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/cancelFileTagTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/cancelFileTagImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/cancel_tag"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/fileMoreRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileMoreImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_more" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileMoreTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileMoreImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/more"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


    </LinearLayout>


</RelativeLayout>