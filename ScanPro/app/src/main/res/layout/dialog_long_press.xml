<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/long_press_dialog"
    android:layout_width="168dp"
    android:layout_height="49dp"
    android:layout_gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/btn_rect_6_bg_black_24">

        <LinearLayout
            android:id="@+id/edit_ll"
            android:layout_width="83.5dp"
            android:layout_height="match_parent"
            android:gravity="center_horizontal">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/edit_img"
                    android:layout_width="13dp"
                    android:layout_height="12dp"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/long_press_edit" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toRightOf="@+id/edit_img"
                    android:text="@string/rename"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

            </RelativeLayout>
        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:background="@color/gray_99" />

        <LinearLayout
            android:id="@+id/delete_ll"
            android:layout_width="83.5dp"
            android:layout_height="match_parent"
            android:gravity="center_horizontal">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/delete_img"
                    android:layout_width="13dp"
                    android:layout_height="12dp"
                    android:layout_centerVertical="true"
                    android:src="@mipmap/long_press_delete" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:layout_toRightOf="@+id/delete_img"
                    android:text="@string/delete"
                    android:textColor="@color/red_de4d4d"
                    android:textSize="12sp" />

            </RelativeLayout>
        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/bookshelf_page_guide_img"
        android:layout_width="15dp"
        android:layout_height="8dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/vector_drawable_black_triangle" />


</LinearLayout>
