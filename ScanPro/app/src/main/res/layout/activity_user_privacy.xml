<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_fa"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/user_top_bar"
        layout="@layout/layout_normal_transparent_top_bar" />

    <RelativeLayout
        android:id="@+id/userPrivacyRl"
        android:layout_width="match_parent"
        android:background="@color/white"
        android:layout_height="45dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="@string/privacy_policy1"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:layout_width="6.5dp"
            android:layout_height="12.5dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:src="@mipmap/user_navigate_icon" />



    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:background="@color/gray_e1" />

    <RelativeLayout
        android:id="@+id/user_privacy_rl2"
        android:layout_width="match_parent"
        android:background="@color/white"
        android:layout_height="45dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="@string/privacy_terms"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:layout_width="6.5dp"
            android:layout_height="12.5dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:src="@mipmap/user_navigate_icon" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:background="@color/gray_e1" />

    <RelativeLayout
        android:id="@+id/private_checklist_rl"
        android:layout_width="match_parent"
        android:background="@color/white"
        android:layout_height="45dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="@string/private_checklist"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:layout_width="6.5dp"
            android:layout_height="12.5dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:src="@mipmap/user_navigate_icon" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:background="@color/gray_e1" />

    <RelativeLayout
        android:id="@+id/thrid_sharelist_rl"
        android:layout_width="match_parent"
        android:background="@color/white"
        android:layout_height="45dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:text="@string/thrid_sharelist"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:layout_width="6.5dp"
            android:layout_height="12.5dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="16dp"
            android:src="@mipmap/user_navigate_icon" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/gray_e9" />

    </RelativeLayout>

            <LinearLayout
                android:id="@+id/btn_cancel_account"
                android:layout_width="170dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/btn_rec_20_bg"
                android:orientation="horizontal"
                android:paddingStart="25dp"
                android:paddingEnd="25dp"
                android:gravity="center">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/user_remove_account"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

</LinearLayout>