<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:id="@+id/indexLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    tools:ignore="MissingPrefix">

    <com.czur.scanpro.ui.component.MainTabLayout
        android:id="@+id/tablayout"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginStart="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tab_normal_textSize="13sp"
        android:layout_marginTop="10dp"
        app:tab_select_textSize="30sp" />

    <ImageView
        android:id="@+id/indexAddBtn"
        android:layout_width="47dp"
        android:layout_height="47dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="25dp"
        android:padding="15dp"
        android:src="@mipmap/main_add_icon"
        app:layout_constraintBottom_toBottomOf="@+id/indexUserHeadImg"
        app:layout_constraintRight_toLeftOf="@+id/indexUserHeadImg"
        app:layout_constraintTop_toTopOf="@+id/indexUserHeadImg" />

    <ImageView
        android:id="@+id/multiCheckBtn"
        android:layout_width="47dp"
        android:layout_height="47dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="25dp"
        android:padding="15dp"
        android:src="@mipmap/index_check"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/indexUserHeadImg"
        app:layout_constraintRight_toLeftOf="@+id/indexUserHeadImg"
        app:layout_constraintTop_toTopOf="@+id/indexUserHeadImg" />

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/indexUserHeadImg"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toBottomOf="@+id/tablayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="5dp"
        app:roundAsCircle="true" />

    <RelativeLayout
        android:id="@+id/line_body"
        android:layout_width="0dp"
        android:layout_height="3dp"
        app:layout_constraintEnd_toEndOf="@+id/tablayout"
        app:layout_constraintStart_toStartOf="@+id/tablayout"
        app:layout_constraintTop_toBottomOf="@+id/tablayout">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/red_line_1"
            android:layout_width="wrap_content"
            android:layout_height="2.5dp"
            android:background="@drawable/red_line_with_round"
            android:visibility="gone" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/red_line_2"
            android:layout_width="wrap_content"
            android:layout_height="2.5dp"
            android:background="@drawable/red_line_with_round"
            android:visibility="gone" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/red_line_3"
            android:layout_width="wrap_content"
            android:layout_height="2.5dp"
            android:background="@drawable/red_line_with_round"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/multiRl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/gray_f9"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/line_body"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tablayout">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/fileSelectAllBtn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingStart="18.5dp"
            android:textColor="@color/red_de4d4d"
            android:textSize="17sp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/fileTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/black_24"
            android:textSize="18sp" />


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/fileCancelBtn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:gravity="center_vertical"
            android:paddingEnd="18.5dp"
            android:textColor="@color/red_de4d4d"
            android:textSize="17sp" />

    </RelativeLayout>


    <FrameLayout
        android:id="@+id/index_frameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="80dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.scanpro.ui.component.NoScrollViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="80dp"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/indexScanBtn"
        android:layout_width="150dp"
        android:layout_height="36dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="14dp"
        android:background="@color/black"
        app:bl_corners_radius="20dp"
        app:bl_solid_color="@color/black_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="8dp"
                android:src="@mipmap/main_scan_icon" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/scan"
                android:textColor="@color/red_d54146"
                android:textSize="16sp" />
        </LinearLayout>


    </RelativeLayout>

    <LinearLayout
        android:id="@+id/fileBottomLl"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent">

        <RelativeLayout
            android:id="@+id/fileShareRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileShareImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_share" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileShareTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileShareImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/share"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/filePdfRl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/filePdfImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_pdf" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/filePdfTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/filePdfImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/PDF"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/fileTagRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileTagImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_tag" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileTagTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileTagImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/edit_tag"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/fileMoreRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/fileMoreImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_more" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/fileMoreTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileMoreImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/more"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


    </LinearLayout>

    <View
        android:id="@+id/guide_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/blackOpaque60"
        android:clickable="true"
        android:focusable="true" />

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/guide_header"
        android:layout_width="26.5dp"
        android:layout_height="26.5dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="20dp"
        android:src="@mipmap/user_not_login_icon"
        app:layout_constraintBottom_toBottomOf="@+id/tablayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintStart_toStartOf="parent"
        app:roundAsCircle="true" />

    <ImageView
        android:id="@+id/guide_arrow"
        android:layout_width="11dp"
        android:layout_height="11dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="1.5dp"
        android:src="@mipmap/guide_arrow"
        app:layout_constraintRight_toLeftOf="@+id/guide_header"
        app:layout_constraintTop_toBottomOf="@+id/guide_header" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/guide_tv_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="3dp"
        android:text="@string/guide_tv_1"
        android:textColor="@color/white"
        android:textSize="15sp"

        app:layout_constraintRight_toLeftOf="@+id/guide_arrow"
        app:layout_constraintTop_toBottomOf="@+id/guide_arrow" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/guide_tv_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="3dp"
        android:text="@string/guide_tv_2"
        android:textColor="@color/white"
        android:textSize="15sp"

        app:layout_constraintLeft_toLeftOf="@+id/guide_tv_1"
        app:layout_constraintTop_toBottomOf="@+id/guide_tv_1" />


    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/guideBtn"
        android:layout_width="161dp"
        android:layout_height="36dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="@string/known"
        android:textColor="@color/red_de4d4d"
        android:textSize="15sp"

        app:bl_corners_radius="36dp"
        app:bl_solid_color="@color/white"
        app:layout_constraintRight_toRightOf="@+id/guide_header"
        app:layout_constraintTop_toBottomOf="@+id/guide_tv_2" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/guideGroup"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        app:constraint_referenced_ids="guide_bg,guide_header,guide_arrow,guide_tv_1,guide_tv_2,guideBtn" />


</androidx.constraintlayout.widget.ConstraintLayout>