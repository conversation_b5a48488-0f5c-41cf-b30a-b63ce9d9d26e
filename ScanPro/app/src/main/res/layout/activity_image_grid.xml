<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:orientation="vertical">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/white">

        <LinearLayout
            android:id="@+id/btn_back_ll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/camera_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/camera_and_album"
                android:textColor="@color/black_24"
                android:textSize="18sp" />
            <ImageView
                android:id="@+id/btn_back"
                android:layout_marginLeft="2dp"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:gravity="center_vertical"
                android:background="@mipmap/ic_up_expand" />
        </LinearLayout>


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tv_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/black_24"
            android:textSize="18sp" />

        <RelativeLayout
            android:id="@+id/album_top_bar_cancel"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17dp"
                android:text="@string/cancel"
                android:textColor="@color/red_d54146"
                android:textSize="17sp" />

        </RelativeLayout>


    </RelativeLayout>




<RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:paddingLeft="12dp"
        android:paddingRight="12dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/album_folder_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:overScrollMode="never"></androidx.recyclerview.widget.RecyclerView>

    <RelativeLayout
        android:id="@+id/file_ok_btn"
        android:layout_width="150dp"
        android:layout_height="36dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:visibility="gone"
        android:layout_marginBottom="14dp"
        android:background="@color/red_d54146"
        app:bl_corners_radius="20dp"
        app:bl_solid_color="@color/red_d54146"
        tools:ignore="MissingPrefix">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tvImagesize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/confirm_text"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </RelativeLayout>
</RelativeLayout>
</LinearLayout>
