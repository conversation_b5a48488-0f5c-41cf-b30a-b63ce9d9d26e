<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="108dp"
    android:layout_height="100dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginTop="8dp"
        android:id="@+id/rl_price"
        android:background="@drawable/selector_red_bg">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tv_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="14dp"
            android:textColor="@color/black_24"
            android:textSize="12sp" />

        <LinearLayout
            android:id="@+id/ll_current_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_duration"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="13dp"
            android:orientation="horizontal">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥"
                android:id="@+id/tv_unit"
                android:textColor="@color/red_de4d4d"
                android:textSize="12sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/tv_current_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/red_de4d4d"
                android:textSize="18sp" />
        </LinearLayout>

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tv_original_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_current_price"
            android:layout_centerHorizontal="true"
            android:textSize="10sp"
            android:textColor="@color/gray_bb" />

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:id="@+id/img_select"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:src="@mipmap/red_select" />
    </RelativeLayout>
    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/tv_discount"
        android:layout_width="31dp"
        android:layout_height="16dp"
        android:textSize="9sp"
        android:text="@string/discount"
        android:gravity="center"
        android:layout_alignParentEnd="true"
        android:background="@drawable/view_rec_3_red_without_right_bottom"
        android:textColor="@color/white"/>
</RelativeLayout>