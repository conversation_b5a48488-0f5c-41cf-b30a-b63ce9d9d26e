<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:layout_gravity="bottom"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="37.5dp"
            android:orientation="vertical">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="36.5dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/et_ocr_language"
                android:textColor="@color/black_24"
                android:textSize="12sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_gravity="bottom"
                android:background="@color/gray_ee" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="22dp"
            android:orientation="horizontal">


            <RelativeLayout
                android:id="@+id/ocr_chinese_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_chinese_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_chinese"
                    android:textColor="@color/red_de4d4d"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_chinese_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_chinese_tv"
                    android:src="@mipmap/red_right_pdf" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_chinese_taiwan_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_chinese_taiwan_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_chinese_taiwan"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_chinese_taiwan_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_chinese_taiwan_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/ocr_english_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_english_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_english"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_english_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_english_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal">


            <RelativeLayout
                android:id="@+id/ocr_french_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_french_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_french"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_french_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_french_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_italian_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_italian_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_italian"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_italian_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_italian_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_spanish_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_spanish_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_spanish"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_spanish_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_spanish_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ocr_portuguese_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_portuguese_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_portuguese"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_portuguese_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_portuguese_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_swedish_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_swedish_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_swedish"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_swedish_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_swedish_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_danish_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_danish_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_danish"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_danish_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_danish_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="39dp"
            android:layout_centerInParent="true"
            android:layout_marginBottom="39dp"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ocr_russian_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_russian_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_russian"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_russian_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_russian_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ocr_japaneses_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/ocr_japaneses_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/et_ocr_japaneses"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


                <ImageView
                    android:id="@+id/ocr_japaneses_img"
                    android:layout_width="14dp"
                    android:layout_height="10dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="9.5dp"
                    android:layout_toRightOf="@+id/ocr_japaneses_tv"
                    android:src="@mipmap/red_right_pdf"
                    android:visibility="gone" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">


            </RelativeLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_e4" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/ocr_cancel_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/cancel"
                    android:textColor="@color/black_24"
                    android:textSize="17sp" />


            </RelativeLayout>

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/gray_e4" />

            <RelativeLayout
                android:id="@+id/ocr_confirm_btn"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/confirm_text"
                    android:textColor="@color/red_de4d4d"
                    android:textSize="17sp" />


            </RelativeLayout>


        </LinearLayout>

    </LinearLayout>


</RelativeLayout>
