<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    android:id="@+id/image_inner_item"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/iv_thumb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="centerCrop"
        fresco:placeholderImage="@mipmap/default_gallery_img"
        fresco:roundedCornerRadius="10dp" />
    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/bg_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:background="@color/whiteTransparent50"
        android:visibility="gone"
        fresco:roundedCornerRadius="6dp" />
    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/tv_index"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="28dp"
        android:minHeight="28dp"
        android:singleLine="true"
        android:layout_alignParentRight="true"
        android:enabled="false"
        android:background="@drawable/red_oval"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="14sp" />
    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/circle_flag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="26dp"
        android:minHeight="26dp"
        android:singleLine="true"
        android:layout_alignParentRight="true"
        android:enabled="false"
        android:background="@drawable/ring_shape_white" />
</RelativeLayout>