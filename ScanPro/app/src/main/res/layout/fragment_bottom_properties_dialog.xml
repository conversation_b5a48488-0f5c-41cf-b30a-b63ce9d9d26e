<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black_24"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvColors"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/color_picker_item_list" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="18dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/txtBrushSize"
            android:layout_width="65dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/brush"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <SeekBar
            android:id="@+id/sbSize"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="5dp"
            android:layout_toStartOf="@+id/tvBrushSize"
            android:layout_toEndOf="@+id/txtBrushSize"
            android:maxHeight="5dp"
            android:minHeight="5dp"
            android:progress="50"
            android:progressDrawable="@drawable/seek_bar_bg"
            android:thumb="@drawable/seek_bar_thumb" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tvBrushSize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="50"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="18dp"
        android:layout_marginBottom="30dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/txtOpacity"
            android:layout_width="65dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/opacity"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <SeekBar
            android:id="@+id/sbOpacity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="5dp"
            android:layout_toStartOf="@+id/tvOpacity"
            android:layout_toEndOf="@+id/txtOpacity"
            android:maxHeight="5dp"
            android:minHeight="5dp"
            android:progress="50"
            android:progressDrawable="@drawable/seek_bar_bg"
            android:thumb="@drawable/seek_bar_thumb" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tvOpacity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="50"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>

</LinearLayout>