<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">


        <RelativeLayout
            android:id="@+id/camera_preview_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#000000">


            <com.czur.scanpro.ui.camera.CardPreviewSurfaceView
                android:id="@+id/surface_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />

            <com.czur.scanpro.ui.camera.CameraBorderView
                android:id="@+id/preview_point_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />

            <com.czur.scanpro.ui.camera.CardFocusView
                android:id="@+id/focus_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />


            <com.czur.scanpro.ui.camera.BlackShadeView
                android:id="@+id/camera_black_shade"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />

            <ImageView
                android:id="@+id/camera_flash_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/camera_scan_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black"
                android:visibility="gone">


                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/camera_cut_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true" />

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/camera_color_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <View
                        android:id="@+id/anim_view"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:background="@color/transparent" />

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/blackOpaque45" />
                </LinearLayout>


            </RelativeLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/middle_toast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="@drawable/btn_rec_5_bg_with_toast"
                android:paddingLeft="7dp"
                android:paddingTop="5dp"
                android:paddingRight="7dp"
                android:paddingBottom="5dp"
                android:text="@string/business_license"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/id_card_img"
                android:layout_width="209.5dp"
                android:layout_height="109.5dp"
                android:layout_above="@+id/bottom_toast"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="15dp"
                android:src="@mipmap/id_card_front" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/bottom_toast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="18dp"
                android:background="@drawable/btn_rec_5_bg_with_toast"
                android:paddingLeft="7dp"
                android:paddingTop="5dp"
                android:paddingRight="7dp"
                android:paddingBottom="5dp"
                android:textColor="@color/white"
                android:textSize="14sp"

                android:visibility="gone" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/camera_function_rl"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_below="@+id/camera_preview_layout"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/preview_no_icon_back_btn"
                    android:layout_width="31dp"
                    android:layout_height="39dp"
                    android:layout_centerInParent="true"
                    android:padding="10dp"
                    android:src="@mipmap/white_back_icon" />

            </RelativeLayout>


            <ImageView
                android:id="@+id/button_flash"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:contentDescription="@null"
                android:src="@mipmap/take_photo_icon" />

            <RelativeLayout
                android:id="@+id/camera_flash_mode_rl"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/camera_flash_mode_btn"
                    android:layout_width="16dp"
                    android:layout_height="24dp"
                    android:layout_centerInParent="true"
                    android:background="@mipmap/auto_flash" />

            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/camera_tab_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_below="@+id/camera_function_rl">

            <LinearLayout
                android:id="@+id/id_card_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:orientation="vertical">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/id_card_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/id_card"
                    android:textColor="@color/white"
                    android:textSize="15sp" />

                <View
                    android:id="@+id/id_card_tab"
                    android:layout_width="28dp"
                    android:layout_height="3dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/red_line_with_round" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/business_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:orientation="vertical">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/business_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/business_license"
                    android:textColor="@color/white"
                    android:textSize="15sp" />

                <View
                    android:id="@+id/business_tab"
                    android:layout_width="28dp"
                    android:layout_height="3dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/red_line_with_round" />
            </LinearLayout>
            <!--<LinearLayout-->
            <!--android:id="@+id/drive_ll"-->
            <!--android:orientation="vertical"-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content">-->

            <!--<com.czur.scanpro.ui.component.MediumBoldTextView-->
            <!--android:id="@+id/drive_tv"-->
            <!--android:textSize="12sp"-->
            <!---->
            <!--android:textColor="@color/white"-->
            <!--android:text="@string/drive_license"-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content" />-->

            <!--<View-->
            <!--android:layout_gravity="center_horizontal"-->
            <!--android:id="@+id/drive_tab"-->
            <!--android:layout_marginTop="2dp"-->
            <!--android:layout_width="28dp"-->
            <!--android:layout_height="2dp"-->
            <!--android:background="@drawable/red_line_with_round"/>-->
            <!--</LinearLayout>-->
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/camera_tab_ll"
            android:layout_alignParentBottom="true">


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/camera_prompt_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/camera_prompt"
                android:textColor="@color/white"
                android:textSize="15sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/camera_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:orientation="horizontal" />


        </RelativeLayout>
    </RelativeLayout>

    <com.czur.scanpro.ui.component.CameraGuideView
        android:id="@+id/camera_guide_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

    </com.czur.scanpro.ui.component.CameraGuideView>

</RelativeLayout>