<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gray_f9">


    <RelativeLayout
        android:id="@+id/my_pdf_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <RelativeLayout
            android:id="@+id/bookshelf_inside_top_bar"
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView
                android:id="@+id/my_pdf_back_btn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/black_back_icon" />

            <com.czur.scanpro.ui.component.MediumBoldTextView

                android:id="@+id/my_pdf_top_select_all_btn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="18.5dp"
                android:textColor="@color/red_de4d4d"
                android:textSize="17sp"

                android:visibility="gone" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/my_pdf_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/black_24"
                android:textSize="18sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/my_pdf_cancel_btn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:gravity="center_vertical"
                android:paddingRight="18.5dp"
                android:textColor="@color/red_de4d4d"
                android:textSize="17sp"

                android:visibility="gone" />

            <RelativeLayout

                android:id="@+id/my_pdf_multi_select_btn"
                android:layout_width="62dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true">

                <ImageView
                    android:layout_width="17dp"
                    android:layout_height="17dp"
                    android:layout_centerInParent="true"
                    android:background="@mipmap/index_check" />

            </RelativeLayout>


        </RelativeLayout>
    </RelativeLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/my_pdf_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/my_pdf_top_bar"
        android:overScrollMode="never"
        android:scrollbars="none">

    </androidx.recyclerview.widget.RecyclerView>

    <RelativeLayout
        android:id="@+id/pdf_empty_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/my_pdf_top_bar"
        android:background="@color/gray_f9"
        android:visibility="gone">


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="200dp"
            android:orientation="vertical">

            <ImageView
                android:layout_width="133dp"
                android:layout_height="138.5dp"
                android:src="@mipmap/empty_logo" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="31dp"
                android:text="@string/has_not_file"
                android:textColor="@color/gray_e4"
                android:textSize="12sp" />

        </LinearLayout>


    </RelativeLayout>


    <LinearLayout
        android:id="@+id/my_pdf_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:visibility="gone">

        <RelativeLayout
            android:id="@+id/myPdfDeleteRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:clickable="false"
            android:enabled= "false"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/myPdfDeleteImg"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/selector_delete" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/myPdfDeleteTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/myPdfDeleteImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/delete"
                    android:textColor="@color/dark_text"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>


    </LinearLayout>
</RelativeLayout>

