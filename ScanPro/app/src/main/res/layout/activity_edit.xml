<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:fresco="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_2a"
    tools:ignore="MissingPrefix">

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/editBigImg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/edit_small_ll"
        android:layout_below="@+id/editTopBar"
        android:background="@color/black"
        fresco:actualImageScaleType="fitCenter" />

    <RelativeLayout
        android:id="@+id/editTopBar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/black_2a">


        <ImageView
            android:id="@+id/normalBackBtn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:padding="10dp"
            android:src="@mipmap/white_back_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/normalTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/edit"
            android:textColor="@color/white"
            android:textSize="18sp" />

    </RelativeLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="136dp"
        android:layout_above="@+id/edit_bottom_ll"
        android:background="@color/black_2a">

    </View>

    <LinearLayout
        android:id="@+id/edit_small_ll"
        android:layout_width="match_parent"
        android:layout_height="136dp"
        android:layout_above="@+id/edit_bottom_ll"
        android:layout_marginLeft="10.5dp"
        android:layout_marginRight="10.5dp"
        android:background="@color/black_2a"
        android:orientation="horizontal">


        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingLeft="7.5dp"
            android:paddingRight="7.5dp">

            <RelativeLayout
                android:id="@+id/color_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/colorTv"
                android:layout_marginTop="10dp"
                android:padding="2dp">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/colorImg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1dp"
                    android:layout_marginRight="1dp"
                    android:background="@color/black"
                    fresco:actualImageScaleType="fitCenter"
                    fresco:roundedCornerRadius="6dp" />


            </RelativeLayout>

            <LinearLayout
                android:id="@+id/colorLl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/colorTv"
                android:layout_marginTop="10dp"
                android:orientation="vertical"></LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/colorTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="11dp"
                android:layout_marginBottom="13dp"
                android:text="@string/optimization"
                android:textColor="@color/white"
                android:textSize="12sp" />


        </RelativeLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingLeft="7.5dp"
            android:paddingRight="7.5dp">

            <RelativeLayout
                android:id="@+id/black_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/blackTv"
                android:layout_marginTop="10dp"
                android:padding="2dp">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/blackImg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1dp"
                    android:layout_marginRight="1dp"
                    android:background="@color/black"
                    fresco:actualImageScaleType="fitCenter"
                    fresco:roundedCornerRadius="6dp" />


            </RelativeLayout>

            <LinearLayout
                android:id="@+id/blackLl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/blackTv"
                android:layout_marginTop="10dp"
                android:orientation="vertical"></LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/blackTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="11dp"
                android:layout_marginBottom="13dp"
                android:text="@string/black"
                android:textColor="@color/white"
                android:textSize="12sp" />


        </RelativeLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingLeft="7.5dp"
            android:paddingRight="7.5dp">

            <RelativeLayout
                android:id="@+id/purify_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/purifyTv"
                android:layout_marginTop="10dp"
                android:padding="2dp">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/purifyImg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1dp"
                    android:layout_marginRight="1dp"
                    android:background="@color/black"
                    fresco:actualImageScaleType="fitCenter"
                    fresco:roundedCornerRadius="6dp" />


            </RelativeLayout>

            <LinearLayout
                android:id="@+id/purifyLl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/purifyTv"
                android:layout_marginTop="10dp"
                android:orientation="vertical"></LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/purifyTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="11dp"
                android:layout_marginBottom="13dp"
                android:text="@string/purify"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingLeft="7.5dp"
            android:paddingRight="7.5dp">

            <RelativeLayout
                android:id="@+id/original_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/originalTv"
                android:layout_marginTop="10dp"
                android:padding="2dp">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/originalImg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="1dp"
                    android:layout_marginRight="1dp"
                    android:background="@color/black"
                    fresco:actualImageScaleType="fitCenter"
                    fresco:roundedCornerRadius="6dp" />


            </RelativeLayout>

            <LinearLayout
                android:id="@+id/originalLl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/originalTv"
                android:layout_marginTop="10dp"
                android:orientation="vertical"></LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/originalTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="11dp"
                android:layout_marginBottom="13dp"
                android:text="@string/original"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/edit_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black_2a">

        <RelativeLayout
            android:id="@+id/handRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/handImg"
                    android:layout_width="22dp"
                    android:layout_height="27dp"
                    android:background="@drawable/selector_adjust" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/handTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileShareImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="2dp"
                    android:text="@string/adjust_by_hand"
                    android:textColor="@color/white"
                    android:textSize="9sp" />

            </LinearLayout>


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rotateRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/rotate_img"
                    android:layout_width="22dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@mipmap/rotate_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/rotate_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rotate_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="2dp"
                    android:text="@string/rotate"
                    android:textColor="@color/white"
                    android:textSize="9sp" />

            </LinearLayout>


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/cutRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/cut_img"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@mipmap/cut_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/cut_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/fileShareImg"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="2dp"
                    android:text="@string/cut"
                    android:textColor="@color/white"
                    android:textSize="9sp" />

            </LinearLayout>


        </RelativeLayout>
    </LinearLayout>


</RelativeLayout>