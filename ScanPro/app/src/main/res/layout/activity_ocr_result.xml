<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_2a"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <ImageView
        android:id="@+id/fileGuideImg"
        android:layout_width="12dp"
        android:layout_height="9dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="46dp"
        android:layout_marginRight="35dp"
        android:src="@drawable/vector_drawable_black24_triangle" />


    <LinearLayout
        android:id="@+id/page_preview_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black_2a">


            <RelativeLayout
                android:id="@+id/recognizeAgain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6.5dp"
                android:layout_centerInParent="true">

                  <ImageView
                          android:id="@+id/recognize_img"
                          android:layout_width="22.5dp"
                          android:layout_height="22.5dp"
                          android:layout_centerHorizontal="true"
                          android:background="@mipmap/preview_ocr" />

                      <com.czur.scanpro.ui.component.MediumBoldTextView
                          android:layout_width="wrap_content"
                          android:layout_height="wrap_content"
                          android:layout_below="@+id/recognize_img"
                          android:layout_centerHorizontal="true"
                          android:layout_marginTop="6.5dp"
                          android:text="@string/ocr_again"
                          android:textColor="@color/white"
                          android:textSize="9sp" />

            </RelativeLayout>


    </LinearLayout>

    <RelativeLayout
        android:id="@+id/handwriting_result_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/handwritingResultFinishBtn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:padding="10dp"
            android:src="@mipmap/white_back_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/ocr_result"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/cloudOcrTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:text="@string/cloud_ocr"
            android:textColor="@color/white"
            android:textSize="15sp" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/result_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/handwritingResultCopyRl"
        android:layout_below="@+id/handwriting_result_top_bar"
        android:background="@color/black"
        android:focusable="true">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/handwritingResultText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="vertical"
            android:focusable="true"
            android:scrollbarStyle="outsideInset"
            android:scrollbarTrackVertical="@color/black"
            android:scrollbarThumbVertical="@color/gray_c4"
            android:layout_gravity="center"
            android:layout_marginLeft="29.5dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="25.5dp"
            android:textColor="@color/gray_c4"
            android:textIsSelectable="true"
            android:textSize="15sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/page_preview_bottom_ll"
        android:layout_below="@+id/result_rl"
        android:background="@color/black" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/fileGuideTv"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/fileGuideImg"
        android:layout_alignParentRight="true"
        android:layout_marginRight="25dp"
        android:background="@drawable/btn_rec_5_bg_with_black24_without_bottom"
        android:lineSpacingExtra="4dp"
        android:paddingLeft="12dp"
        android:paddingTop="10dp"
        android:paddingRight="12dp"
        android:paddingBottom="23dp"
        android:text="@string/cloud_ocr_tip"
        android:textColor="@color/gray_93"
        android:textSize="12sp" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/fileHideTv"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/fileGuideTv"
        android:layout_alignParentRight="true"
        android:layout_marginRight="25dp"
        android:background="@drawable/btn_rec_5_bg_with_black24_without_top"
        android:gravity="center"
        android:paddingBottom="5dp"
        android:text="@string/not_show"
        android:textColor="@color/red_de4d4d"
        android:textSize="12sp" />


    <RelativeLayout
        android:id="@+id/handwritingResultCopyRl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:layout_above="@+id/page_preview_bottom_ll"
        android:layout_centerInParent="true">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:text="@string/copy_text"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:layout_marginBottom="6.5dp"
            app:bl_corners_radius="36dp"
            app:bl_solid_color="@color/red_de4d4d" />



    </RelativeLayout>

</RelativeLayout>

