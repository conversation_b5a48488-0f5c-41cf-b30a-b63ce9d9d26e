<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#60000000">

    <TextView
        android:id="@+id/addTextTextView"
        android:layout_width="280dp"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_above="@+id/add_text_color_picker_relative_layout"
        android:textSize="25sp" />

    <RelativeLayout
        android:id="@+id/add_text_color_picker_relative_layout"
        android:layout_width="match_parent"
        android:layout_height="110dp"
        android:layout_alignParentBottom="true"
        android:background="@color/gray_fa"
        android:paddingBottom="10dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/add_text_color_picker_recycler_view"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal" />

        <EditText
            android:id="@+id/addTextEditText"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:layout_toStartOf="@+id/rlDone"
            android:background="@drawable/edit_text_bg"
            android:focusable="true"
            android:paddingLeft="10dp"
            android:paddingRight="10dp" />

        <RelativeLayout
            android:id="@+id/rlDone"
            android:layout_width="65dp"
            android:layout_height="35dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="16dp"
            android:background="@drawable/btn_rec_5_red_bg">

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerInParent="true"
                android:src="@mipmap/done" />
        </RelativeLayout>


    </RelativeLayout>

</RelativeLayout>