<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tag_add_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:ignore="MissingPrefix">

    <RelativeLayout
        android:id="@+id/tag_inner_item"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:layout_marginLeft="16dp"
        android:layout_marginBottom="13dp"
        android:background="@drawable/btn_rect_6_bg_gray_ec"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/tag_item_name"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_centerInParent="true"
            android:src="@mipmap/tag_add_icon" />


    </RelativeLayout>


</RelativeLayout>