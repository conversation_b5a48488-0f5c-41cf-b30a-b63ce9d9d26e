<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/category_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:ignore="MissingPrefix">

    <RelativeLayout
        android:id="@+id/category_inner_item"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="10dp"
        app:bl_corners_radius="6dp"
        app:bl_solid_color="@color/white"
        app:bl_stroke_color="@color/gray_c4"
        app:bl_stroke_width="0.5dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/category_black_left_line"
            android:layout_width="6dp"
            android:layout_height="match_parent"
            app:bl_corners_bottomLeftRadius="6dp"
            app:bl_corners_topLeftRadius="6dp"
            app:bl_solid_color="@color/black" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toLeftOf="@+id/category_image"
            android:layout_toRightOf="@+id/category_black_left_line">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/category_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="7dp"
                android:layout_marginTop="7dp"
                android:text=""
                android:textColor="@color/black_24"
                android:textSize="18sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/category_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/category_date"
                android:layout_marginLeft="7dp"
                android:layout_marginTop="7dp"
                android:layout_marginBottom="6dp"
                android:textColor="@color/black_24"
                android:textSize="9sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/category_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="7dp"
                android:layout_marginBottom="14dp"
                android:text=""
                android:textColor="@color/black_24"
                android:textSize="9sp" />

        </RelativeLayout>

        <com.facebook.drawee.view.SimpleDraweeView
            android:id="@+id/category_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:scaleType="centerCrop"
            app:actualImageScaleType="centerCrop"
            app:failureImageScaleType="centerCrop"
            app:placeholderImageScaleType="centerCrop"
            app:roundedCornerRadius="6dp"
            app:roundingBorderColor="@color/gray_ec"
            app:roundingBorderWidth="0.5dp" />
    </RelativeLayout>


</RelativeLayout>