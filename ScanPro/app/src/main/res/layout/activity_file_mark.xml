<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/black_24">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">

        <FrameLayout
            android:id="@+id/photoView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/ll_bottom_bar"
            android:layout_below="@+id/rl_top_bar" />

        <LinearLayout
            android:id="@+id/ll_bottom_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/black_24"
            android:baselineAligned="false"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:weightSum="2">

            <LinearLayout
                android:id="@+id/llWaterMark"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:src="@mipmap/water_mark" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="@string/water_mark"
                    android:textColor="@color/white"
                    android:textSize="9sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMarker"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:src="@mipmap/marker" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="@string/marker"
                    android:textColor="@color/white"
                    android:textSize="9sp" />

            </LinearLayout>

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_top_bar"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/black_24">

            <LinearLayout
                android:id="@+id/llBack"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="6dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="29.5dp"
                    android:layout_height="36dp"
                    android:padding="10dp"
                    android:src="@mipmap/white_back_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/back"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/tvCancel"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingEnd="16dp"
                android:paddingStart="16dp"
                android:gravity="center_vertical"
                android:text="@string/cancel"
                android:visibility="gone"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/tvFinish"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:gravity="center_vertical"
                android:text="@string/finish"
                android:textColor="@color/red_de4d4d"
                android:textSize="16sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/white"
                android:textSize="18sp" />

        </RelativeLayout>

    </RelativeLayout>

</RelativeLayout>