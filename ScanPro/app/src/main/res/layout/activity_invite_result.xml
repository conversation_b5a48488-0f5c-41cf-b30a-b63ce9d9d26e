<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/register_top_bar"
        layout="@layout/layout_normal_f9_top_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="181dp">

                <ImageView
                    android:layout_width="100dp"
                    android:layout_height="70dp"
                    android:layout_centerInParent="true"
                    android:src="@mipmap/invite_success_icon" />
            </RelativeLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="25dp"
                android:layout_marginRight="25dp"
                android:background="@color/white"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="133dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="vertical">

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/inviteTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/gray_c4"
                            android:textSize="30sp" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/vipDaysTv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:textColor="@color/gray_c4"
                            android:textSize="30sp" />
                    </LinearLayout>

                </RelativeLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:nestedScrollingEnabled="false" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/confirmBtn"
                    android:layout_width="220dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="35dp"
                    android:gravity="center"
                    android:text="@string/got_advanced"
                    android:textColor="@color/white"
                    android:textSize="15sp"
                    android:visibility="gone"
                    app:bl_corners_radius="36dp"
                    app:bl_solid_color="@color/black_24" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/inviteResultPrompt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="30dp"
                    android:layout_marginBottom="30dp"
                    android:textColor="@color/gray_e6c4"
                    android:textSize="12sp"
                    android:visibility="gone" />
            </LinearLayout>


        </LinearLayout>

    </ScrollView>


</LinearLayout>