<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:id="@+id/adjustEdgeBody2"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#141414"
    tools:ignore="MissingPrefix">


    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/adjust_edge_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/black_2a"
        android:gravity="center"
        android:text="@string/adjust_by_hand"
        android:textColor="@color/white"
        android:textSize="18sp"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/adjustEdgeBackBtn"
        android:layout_width="29.5dp"
        android:layout_height="36dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="6dp"
        android:contentDescription="@null"
        android:padding="10dp"
        android:src="@mipmap/white_back_icon"
        app:layout_constraintBottom_toBottomOf="@+id/adjust_edge_top_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/adjust_edge_top_bar" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/finishBtn"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:gravity="center"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:text="@string/finish"
        android:textColor="@color/white"
        android:textSize="18sp"

        app:layout_constraintEnd_toEndOf="@+id/adjust_edge_top_bar"
        app:layout_constraintTop_toTopOf="@+id/adjust_edge_top_bar" />


    <com.czur.scanpro.ui.component.ViewPager
        android:id="@+id/viewPager2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="20dp"
        app:layout_constraintBottom_toTopOf="@+id/previewFileTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/adjust_edge_top_bar"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintVertical_bias="0.0"/>

    <View
        android:id="@+id/imageMaxRegion2"
        android:layout_width="0dp"
        android:visibility="invisible"
        android:layout_height="0dp"
        android:layout_margin="40dp"
        android:persistentDrawingCache="animation"
        app:layout_constraintBottom_toTopOf="@+id/previewFileTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/adjust_edge_top_bar"
        app:layout_constraintVertical_bias="0.0" />

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/magnifier"
        android:layout_width="130dp"
        android:layout_height="130dp"
        app:civ_border_color="#e1e1e1"
        app:civ_border_width="2dp"
        app:civ_circle_background_color="#141414"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/hView"
        android:layout_width="30dp"
        android:layout_height="2dp"
        android:background="#de4a4d"
        app:layout_constraintBottom_toBottomOf="@+id/magnifier"
        app:layout_constraintEnd_toEndOf="@+id/magnifier"
        app:layout_constraintStart_toStartOf="@+id/magnifier"
        app:layout_constraintTop_toTopOf="@id/magnifier" />

    <View
        android:id="@+id/vView"
        android:layout_width="2dp"
        android:layout_height="30dp"
        android:background="#de4a4d"
        app:layout_constraintBottom_toBottomOf="@+id/magnifier"
        app:layout_constraintEnd_toEndOf="@+id/magnifier"
        app:layout_constraintStart_toStartOf="@+id/magnifier"
        app:layout_constraintTop_toTopOf="@id/magnifier" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/magnifierGroup2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:constraint_referenced_ids="magnifier, hView, vView" />

    <View
        android:id="@+id/adjustBottomBar"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:background="@color/black_2a"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />



    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/previewFileTitle"
        app:layout_constraintBottom_toTopOf="@+id/adjustBottomBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="MissingConstraints" />

    <RadioButton
        android:id="@+id/radioButton"
        android:theme="@style/MyRadioButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/adjustBottomBar"
        app:layout_constraintBottom_toBottomOf="@+id/adjustBottomBar"
        tools:layout_editor_absoluteX="45dp"
        tools:layout_editor_absoluteY="698dp"
        android:layout_marginLeft="20dp"
        android:button="@drawable/radio_button_style"
        tools:ignore="MissingConstraints" />
    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/import_all"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/all_page_import"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/adjustBottomBar"
        app:layout_constraintTop_toTopOf="@+id/adjustBottomBar"
        app:layout_constraintStart_toEndOf="@+id/radioButton"
        android:layout_marginLeft="10dp"

        app:layout_constraintHorizontal_bias="0.5" />



    <RelativeLayout
        android:id="@+id/tvImagesize"
        android:layout_width="100dp"
        android:layout_height="36dp"
        app:layout_constraintTop_toTopOf="@+id/adjustBottomBar"
        app:layout_constraintBottom_toBottomOf="@+id/adjustBottomBar"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="10dp"
        android:background="@color/red_d54146"
        app:bl_corners_radius="20dp"
        app:bl_solid_color="@color/red_d54146"
        android:gravity="center"
        tools:ignore="MissingPrefix">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/adjustCtrlTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/all_page"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:layout_alignParentTop="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="5dp"
            android:layout_alignParentRight="true"
            app:layout_constraintHorizontal_bias="0.5"
            android:gravity="center"/>

        <ImageView
            android:id="@+id/adjustCtrlBtn"
            android:layout_width="15.5dp"
            android:layout_height="15.5dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="5dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:layout_toLeftOf="@+id/adjustCtrlTv"
            android:gravity="center"
            android:visibility="invisible"
            app:layout_constraintHorizontal_bias="0.5" />
    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>