<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true">

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        app:srlEnableAutoLoadMore="false"
        app:srlEnableLoadMore="false"
        app:srlEnableOverScrollBounce="true"
        app:srlEnableOverScrollDrag="true"
        app:srlEnableRefresh="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/category_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:scrollbars="none" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>


    <RelativeLayout
        android:id="@+id/category_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_f9">


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="155dp"
            android:orientation="vertical">

            <ImageView
                android:layout_width="133dp"
                android:layout_height="138.5dp"
                android:src="@mipmap/empty_logo" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="31dp"
                android:text="@string/has_not_file"
                android:textColor="@color/gray_e4"
                android:textSize="12sp" />

        </LinearLayout>


    </RelativeLayout>

</RelativeLayout>