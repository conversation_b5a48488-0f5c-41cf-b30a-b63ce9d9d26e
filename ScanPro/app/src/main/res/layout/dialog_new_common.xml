<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingPrefix">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:bl_corners_radius="8dp"
        app:bl_solid_color="@color/gray_fa">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:layout_marginBottom="19dp">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:gravity="center"
                android:textColor="@color/black_24"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/add_tag_back_btn"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentRight="true"
                android:padding="10dp"
                android:src="@mipmap/pdf_close" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="26dp">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/message"
                android:layout_width="175dp"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="@null"
                android:gravity="center"
                android:textColor="@color/black_24"
                android:textCursorDrawable="@drawable/gray_dd_cursor"
                android:textIsSelectable="false"
                android:textSize="15sp" />

        </RelativeLayout>


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/positive_button"
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="1"
            android:background="@drawable/btn_rect_30_bg_black_24"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </LinearLayout>
</RelativeLayout>