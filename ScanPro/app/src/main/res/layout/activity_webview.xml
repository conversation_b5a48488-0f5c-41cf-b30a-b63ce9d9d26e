<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <RelativeLayout
        android:id="@+id/webview_title"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/webview_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:padding="10dp"
            android:src="@mipmap/black_back_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/webview_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/black_24"
            android:textSize="18sp" />
    </RelativeLayout>


    <FrameLayout
        android:id="@+id/web_frame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/webview_title"
        android:focusable="true"
        android:focusableInTouchMode="true"></FrameLayout>

    <include
        android:id="@+id/reload_webview_rl"
        layout="@layout/layout_reload_webview"
        android:visibility="gone" />

</RelativeLayout>