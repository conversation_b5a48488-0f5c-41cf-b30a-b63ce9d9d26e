<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/register_top_bar"
        layout="@layout/layout_normal_white_top_bar" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="160dp">

        <ImageView
            android:layout_width="48.5dp"
            android:layout_height="34.5dp"
            android:layout_centerInParent="true"
            android:src="@mipmap/input_invite_icon" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:bl_corners_radius="36dp"
        app:bl_solid_color="@color/gray_ec">

        <com.czur.scanpro.ui.component.NoHintEditText
            android:id="@+id/codeEdt"
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:background="@null"
            android:gravity="center"
            android:hint="@string/input_invite_code"
            android:textColor="@color/black_24"
            android:textColorHint="@color/gray_c4"
            android:textSize="15sp" />

    </LinearLayout>


    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/confirmBtn"
        android:layout_width="161dp"
        android:layout_height="36dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="15dp"
        android:gravity="center"
        android:text="@string/get_advanced"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:bl_corners_radius="36dp"
        app:bl_solid_color="@color/black_24" />
</LinearLayout>