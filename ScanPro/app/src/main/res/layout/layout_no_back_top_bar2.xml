<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="47dp"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/no_back_top_bar_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/black_24"
            android:textSize="18sp"
            android:gravity="center"
            android:ellipsize="end"
            android:layout_marginStart="70dp"
            android:layout_marginEnd="70dp"/>

        <RelativeLayout
            android:id="@+id/no_back_top_bar_cancel"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:text="@string/cancel"
                android:textColor="@color/black_24"
                android:textSize="17sp" />

        </RelativeLayout>


    </RelativeLayout>


</LinearLayout>