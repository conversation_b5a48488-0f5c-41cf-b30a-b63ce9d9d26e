<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fadingEdge="none"
        android:scrollbars="none">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <include
                android:id="@+id/user_top_bar"
                layout="@layout/layout_normal_f9_top_bar" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="100dp">

                <RelativeLayout
                    android:id="@+id/advance_vip_rl"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="25dp">

                    <ImageView
                        android:id="@+id/advance_vip_img"
                        android:layout_width="21dp"
                        android:layout_height="10dp"
                        android:src="@mipmap/vip_unselected" />

                    <LinearLayout
                        android:id="@+id/advance_vip_tv_ll"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/advance_vip_img"
                        android:layout_marginTop="6dp"
                        android:orientation="horizontal">

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/advance_vip_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:text="@string/my_advanced"
                            android:textColor="@color/gray_dd"
                            android:textSize="12sp" />

                        <ImageView
                            android:id="@+id/vip_arrow_img"
                            android:layout_width="6dp"
                            android:layout_height="9dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="4dp"
                            android:src="@mipmap/no_vip_arrow" />

                    </LinearLayout>

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/advance_vip_info_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/advance_vip_tv_ll"
                        android:layout_centerHorizontal="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="6dp"
                        android:text="@string/has_not_advanced"
                        android:textColor="@color/black_24"
                        android:textSize="10sp" />


                </RelativeLayout>


            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/vip_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="15dp"
                android:overScrollMode="never"
                android:scrollbars="none" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1">

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="25dp"
                        android:text="@string/cost"
                        android:textColor="@color/black_24"
                        android:textSize="15sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1">

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="25dp"
                        android:text="@string/free_forever"
                        android:textColor="@color/black_24"
                        android:textSize="15sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1">

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="25dp"
                        android:text="@string/twelve_per_month"
                        android:textColor="@color/black_24"
                        android:textSize="15sp" />
                </LinearLayout>
            </LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/confirmBtn"
                android:layout_width="161dp"
                android:layout_height="36dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="7dp"
                android:layout_marginTop="30dp"
                android:gravity="center"
                android:text="@string/buy_vip"
                android:textColor="@color/white"
                android:textSize="15sp"

                app:bl_corners_radius="36dp"
                app:bl_solid_color="@color/red_de4d4d" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/get_vip_btn"
                android:layout_width="161dp"
                android:layout_height="36dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="7dp"
                android:layout_marginTop="15dp"
                android:gravity="center"
                android:text="@string/buy_vip"
                android:textColor="@color/white"
                android:textSize="15sp"

                app:bl_corners_radius="36dp"
                app:bl_solid_color="@color/black_24" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/invite_prompt_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="7dp"
                android:layout_marginTop="13dp"
                android:text="@string/invite_prompt"
                android:textColor="@color/gray_c4"
                android:textSize="12sp" />

        </LinearLayout>
    </ScrollView>


</LinearLayout>