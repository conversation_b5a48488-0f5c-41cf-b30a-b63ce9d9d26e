<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_2a"
    tools:ignore="MissingPrefix">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adjustEdgeBody"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="#141414">

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/adjust_edge_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/black_2a"
        android:gravity="center"
        android:text="@string/adjust_by_hand"
        android:textColor="@color/white"
        android:textSize="18sp"

        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/adjustEdgeBackBtn"
        android:layout_width="29.5dp"
        android:layout_height="36dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="6dp"
        android:contentDescription="@null"
        android:padding="10dp"
        android:src="@mipmap/white_back_icon"
        app:layout_constraintBottom_toBottomOf="@+id/adjust_edge_top_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/adjust_edge_top_bar" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/finishBtn"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:gravity="center"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:text="@string/finish"
        android:textColor="@color/white"
        android:textSize="18sp"

        app:layout_constraintEnd_toEndOf="@+id/adjust_edge_top_bar"
        app:layout_constraintTop_toTopOf="@+id/adjust_edge_top_bar" />

    <View
        android:id="@+id/imageMaxRegion"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="20dp"
        app:layout_constraintBottom_toTopOf="@+id/adjustBottomBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/adjust_edge_top_bar" />

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/magnifier"
        android:layout_width="130dp"
        android:layout_height="130dp"
        app:civ_border_color="#e1e1e1"
        app:civ_border_width="2dp"
        app:civ_circle_background_color="#141414"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/hView"
        android:layout_width="30dp"
        android:layout_height="2dp"
        android:background="#de4a4d"
        app:layout_constraintBottom_toBottomOf="@+id/magnifier"
        app:layout_constraintEnd_toEndOf="@+id/magnifier"
        app:layout_constraintStart_toStartOf="@+id/magnifier"
        app:layout_constraintTop_toTopOf="@id/magnifier" />

    <View
        android:id="@+id/vView"
        android:layout_width="2dp"
        android:layout_height="30dp"
        android:background="#de4a4d"
        app:layout_constraintBottom_toBottomOf="@+id/magnifier"
        app:layout_constraintEnd_toEndOf="@+id/magnifier"
        app:layout_constraintStart_toStartOf="@+id/magnifier"
        app:layout_constraintTop_toTopOf="@id/magnifier" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/magnifierGroup"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:constraint_referenced_ids="magnifier, hView, vView" />

    <View
        android:id="@+id/adjustBottomBar"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:background="@color/black_2a"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/adjustCtrlBtn"
        android:layout_width="22.5dp"
        android:layout_height="22.5dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="3dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/adjustCtrlTv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/adjustBottomBar" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/adjustCtrlTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/all_page"
        android:textColor="@color/white"
        android:textSize="12sp"

        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/adjustBottomBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/adjustCtrlBtn" />


</androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>