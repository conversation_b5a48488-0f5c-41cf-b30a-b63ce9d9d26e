<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_my_pdf_rl"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="49dp">

        <ImageView
            android:id="@+id/pdf_icon_img"
            android:layout_width="28.5dp"
            android:layout_height="32.5dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="18dp"
            android:background="@mipmap/pdf_list_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/my_pdf_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="80dp"
            android:layout_toRightOf="@+id/pdf_icon_img"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/black_24"
            android:textSize="15sp" />


        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp">

            <CheckBox
                android:id="@+id/check"
                style="@style/BookCheckBox"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:minHeight="20dp" />
        </RelativeLayout>

        <ImageView
            android:id="@+id/my_pdf_left_arrow"
            android:layout_width="6dp"
            android:layout_height="11dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:background="@mipmap/user_right_gray_arrow" />


    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/gray_ec" />

</LinearLayout>
