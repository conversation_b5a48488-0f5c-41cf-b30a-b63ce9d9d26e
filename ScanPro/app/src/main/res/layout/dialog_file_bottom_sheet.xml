<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:layout_gravity="bottom"
    android:background="@color/white">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical">


        <RelativeLayout
            android:id="@+id/file_bottom_sheet_save_btn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/file_bottom_sheet_save_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/save"
                android:textColor="@color/black_24"
                android:textSize="17sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:background="@color/gray_f9" />


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/file_bottom_sheet_move_btn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/file_bottom_sheet_move_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/move"
                android:textColor="@color/black_24"
                android:textSize="17sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:background="@color/gray_f9" />


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/file_bottom_sheet_delete_btn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/file_bottom_sheet_delete_tv"

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/delete"
                android:textColor="@color/black_24"
                android:textSize="17sp" />


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:background="@color/gray_f9" />


        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@color/gray_f9" />

        <RelativeLayout
            android:id="@+id/file_bottom_sheet_cancel_btn"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/cancel"
                android:textColor="@color/black_24"
                android:textSize="17sp" />


        </RelativeLayout>


    </LinearLayout>


</RelativeLayout>
