<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/register_top_bar"
        layout="@layout/normal_back_top_bar" />

    <LinearLayout
        android:id="@+id/change_keyboard_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="130dp"
        android:gravity="center"
        android:orientation="vertical">


        <LinearLayout
            android:id="@+id/register_input_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <com.czur.scanpro.ui.component.NoHintEditText
                android:id="@+id/register_account_edt"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:width="170dp"
                android:background="@null"
                android:inputType="phone"
                android:gravity="center"
                android:hint="@string/user_input_phone"
                android:maxLines="1"
                android:textColor="@color/black_24"
                android:textColorHint="@color/gray_e3dd"
                android:textSize="18sp" />

            <View
                android:layout_width="170dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/gray_e3dd" />

            <com.czur.scanpro.ui.component.NoHintEditText
                android:id="@+id/register_password_edt"
                android:layout_width="match_parent"
                android:layout_height="39.5dp"
                android:layout_marginTop="25dp"
                android:width="170dp"
                android:background="@null"
                android:digits="@string/password_digits"
                android:gravity="center"
                android:hint="@string/login_password_hint"
                android:inputType="textPassword"
                android:maxLength="20"
                android:maxLines="1"
                android:textColor="@color/black_24"
                android:textColorHint="@color/gray_e3dd"
                android:textSize="18sp" />

            <View
                android:layout_width="170dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/gray_e3dd" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="39.5dp"
                android:layout_marginTop="25dp">

                <com.czur.scanpro.ui.component.NoHintEditText
                    android:id="@+id/register_code_edt"
                    android:layout_width="170dp"
                    android:layout_height="match_parent"
                    android:layout_centerHorizontal="true"
                    android:background="@null"
                    android:gravity="center"
                    android:hint="@string/code"
                    android:inputType="number"
                    android:maxLength="6"
                    android:maxLines="1"
                    android:textColor="@color/black_24"
                    android:textColorHint="@color/gray_e3dd"
                    android:textSize="18sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/getCodeBtn"
                    android:layout_width="66.5dp"
                    android:layout_height="23dp"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="5dp"
                    android:layout_toRightOf="@+id/register_code_edt"
                    android:gravity="center"

                    android:text="@string/gain"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    app:bl_corners_radius="23dp"
                    app:bl_solid_color="@color/red_de4d4d" />

            </RelativeLayout>


            <View
                android:layout_width="170dp"
                android:layout_height="0.5dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/gray_e3dd" />

            <LinearLayout
                android:id="@+id/user_privacy_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:layout_gravity="center_horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/user_privacy_img"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:padding="10dp"
                    android:src="@mipmap/sitting_no_select"
                    />

                <TextView
                    android:id="@+id/user_privacy_agree_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/user_privacy_register"
                    android:textColor="@color/gray_c4"
                    android:textStyle="bold"
                    android:textSize="15sp" />
                <TextView
                    android:id="@+id/user_terms_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/privacy_terms"
                    android:textColor="@color/identifying_code"
                    android:textStyle="bold"
                    android:textSize="15sp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/and"
                    android:textColor="@color/gray_c4"
                    android:textStyle="bold"
                    android:textSize="15sp" />
                <TextView
                    android:id="@+id/user_privacy_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/privacy_policy1"
                    android:textColor="@color/identifying_code"
                    android:textStyle="bold"
                    android:textSize="15sp" />

            </LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/register_btn"
                android:layout_width="161dp"
                android:layout_height="36dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="34dp"
                android:gravity="center"
                android:text="@string/register_btn_text"
                android:textColor="@color/gray_fa"
                android:textSize="15sp"
                app:bl_corners_radius="36dp"
                app:bl_solid_color="@color/gray_e6" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/had_account_login_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="35dp"
                android:text="@string/had_account_to_login"
                android:textColor="@color/gray_c4"
                android:textSize="15sp" />


        </LinearLayout>

    </LinearLayout>


</RelativeLayout>