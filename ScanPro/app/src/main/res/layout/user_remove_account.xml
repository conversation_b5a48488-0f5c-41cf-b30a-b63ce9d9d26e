<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include
            android:id="@+id/user_top_bar"
            layout="@layout/layout_normal_transparent_top_bar" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp"
            android:visibility="visible"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/user_remove_account_msg"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="30dp"
            android:textSize="16sp"
            android:textColor="@color/black_24"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/user_remove_account_msg2"
            android:textColor="@color/red_color_picker"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="30dp"
            android:textSize="14sp"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/next_setp_btn_ll"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_alignParentBottom="true"
        android:gravity="center">

        <Button
            android:id="@+id/next_setp_btn"
            android:layout_width="250dp"
            android:layout_height="40dp"
            android:background="@drawable/btn_rec_5_bg_with_blue"
            android:gravity="center"
            android:text="@string/next_step"
            android:textColor="@color/white" />

    </LinearLayout>

</RelativeLayout>

