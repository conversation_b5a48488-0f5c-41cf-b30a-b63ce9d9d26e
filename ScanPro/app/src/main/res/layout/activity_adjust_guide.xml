<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:ignore="MissingPrefix">

    <ImageView
        android:id="@+id/adjustGuideImg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:contentDescription="@null"
        app:layout_constraintDimensionRatio="750:1080"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/knownBtn"
        android:layout_width="161dp"
        android:layout_height="36dp"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="60dp"
        android:gravity="center"
        android:text="@string/known"
        android:textColor="@color/red_de4d4d"
        android:textSize="15sp"

        app:bl_corners_radius="36dp"
        app:bl_solid_color="@color/black_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/adjustGuideTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="50dp"
        android:textColor="@color/white"
        android:textSize="18sp"

        app:layout_constraintBottom_toTopOf="@+id/knownBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>