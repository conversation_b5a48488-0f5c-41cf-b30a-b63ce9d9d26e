<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/btn_rec_10_bg_with_white"
    tools:ignore="MissingPrefix">

    <RelativeLayout
        android:layout_width="270dp"
        android:layout_height="390dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:bl_corners_radius="8dp"
        app:bl_solid_color="@color/white">

        <RelativeLayout
            android:id="@+id/title_rl"
            android:layout_width="match_parent"
            android:layout_height="55dp">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text="@string/privacy_policy1"
                android:textColor="@color/black_24"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/add_tag_back_btn"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentRight="true"
                android:padding="10dp"
                android:src="@mipmap/pdf_close" />

        </RelativeLayout>

        <FrameLayout

            android:id="@+id/web_frame"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/button_ll"
            android:layout_below="@+id/title_rl"
            android:focusable="true"
            android:focusableInTouchMode="true"></FrameLayout>

        <RelativeLayout
            android:id="@+id/reload_webview_rl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/button_ll"
            android:layout_below="@+id/title_rl"
            android:background="@color/white">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="90dp"
                    android:layout_height="64dp"
                    android:src="@mipmap/webview_error_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="31.5dp"
                    android:text="@string/network_error"
                    android:textColor="@color/black_2a"
                    android:textSize="15sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/check_network"
                    android:textColor="@color/gray_65"
                    android:textSize="12sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/reload_btn"
                    android:layout_width="120dp"
                    android:layout_height="40dp"
                    android:layout_marginTop="31.5dp"
                    android:background="@drawable/btn_rec_5_bg_webview"
                    android:gravity="center"
                    android:text="@string/reload"
                    android:textColor="@color/gray_93"
                    android:textSize="15sp" />


            </LinearLayout>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/button_ll"
            android:layout_width="match_parent"
            android:layout_height="66dp"
            android:layout_alignParentBottom="true">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/negative_button"
                    android:layout_width="110dp"
                    android:layout_height="36dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/btn_rect_30_bg_red_24"
                    android:gravity="center"
                    android:text="@string/privacy_reject"
                    android:textColor="@color/white"
                    android:textSize="18sp" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/positive_button"
                    android:layout_width="110dp"
                    android:layout_height="36dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/btn_rect_30_bg_black_24"
                    android:gravity="center"
                    android:text="@string/privacy_agree"
                    android:textColor="@color/white"
                    android:textSize="18sp" />
            </RelativeLayout>


        </LinearLayout>


    </RelativeLayout>
</RelativeLayout>