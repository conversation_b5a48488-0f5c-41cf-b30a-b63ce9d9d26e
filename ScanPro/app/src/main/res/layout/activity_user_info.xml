<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_fa"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/user_top_bar"
        layout="@layout/layout_normal_transparent_top_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fadingEdge="none"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="14dp"
                android:background="@color/gray_f9" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e9" />

            <RelativeLayout
                android:id="@+id/userInfoHeadRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="75dp">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/userInfoHeadImg"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="16dp"
                    android:layout_centerVertical="true"
                    app:roundAsCircle="true" />

                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/userNameRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="45dp">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:text="@string/nick_name"
                    android:textColor="@color/black_24"
                    android:textSize="15sp" />

                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/nameTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="32dp"
                    android:text=""
                    android:textColor="@color/black_24"
                    android:textSize="12sp" />
                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:background="@color/gray_e9" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/userPhoneRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="45dp">
                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:text="@string/phone"
                    android:textColor="@color/black_24"
                    android:textSize="15sp" />
                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/phoneTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="32dp"
                    android:text=""
                    android:textColor="@color/black_24"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:background="@color/gray_e9" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/userEmailRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="45dp">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:text="@string/email"
                    android:textColor="@color/black_24"
                    android:textSize="15sp" />

                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/emailTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="32dp"
                    android:text=""
                    android:textColor="@color/black_24"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:background="@color/gray_e9" />


            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/userPswRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="45dp">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:text="@string/password"
                    android:textColor="@color/black_24"
                    android:textSize="15sp" />

                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:background="@color/gray_e9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/gray_e9" />

            </RelativeLayout>

            <View
                android:id="@+id/vip_empty_view"
                android:layout_width="match_parent"
                android:layout_height="14dp" />

            <RelativeLayout
                android:id="@+id/userAboutRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="45dp">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:text="@string/about_scan_pro"
                    android:textColor="@color/black_24"
                    android:textSize="15sp" />

                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@color/gray_e9" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/userFeedbackRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="45dp">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:text="@string/feedback"
                    android:textColor="@color/black_24"
                    android:textSize="15sp" />

                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@color/gray_e9" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/userPrivacyRl"
                android:layout_width="match_parent"
                android:background="@color/white"
                android:layout_height="45dp">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="16dp"
                    android:text="@string/privacy_policy"
                    android:textColor="@color/black_24"
                    android:textSize="15sp" />

                <ImageView
                    android:layout_width="6.5dp"
                    android:layout_height="12.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"
                    android:src="@mipmap/user_navigate_icon" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/gray_e9" />

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/btnLogout"
                android:layout_width="170dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="30dp"
                android:background="@drawable/btn_rec_20_bg"
                android:orientation="horizontal"
                android:paddingStart="25dp"
                android:paddingEnd="25dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="12dp"
                    android:src="@mipmap/user_logout" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:text="@string/logout"
                    android:textColor="@color/white"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>
    </ScrollView>


</LinearLayout>