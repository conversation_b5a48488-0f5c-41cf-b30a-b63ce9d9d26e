<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/userBackBtn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="6dp"
            android:padding="10dp"
            android:src="@mipmap/black_back_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/userTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/my_vip"
            android:textColor="@color/black_24"
            android:textSize="18sp" />
    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/rl_top_bar"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="15dp"
                android:background="@color/gray_e9" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:layout_gravity="center_horizontal"
                android:background="@color/white">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/payUserHeadImg"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="17dp"
                    app:roundAsCircle="true" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_toStartOf="@+id/payAdvanceDate"
                    android:layout_toEndOf="@+id/payUserHeadImg"
                    android:orientation="vertical">

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/payUserName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black_24"
                        android:singleLine="true"
                        android:ellipsize="end"
                        android:textSize="14sp" />

                    <ImageView
                        android:id="@+id/payUserVipImg"
                        android:layout_width="26dp"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:visibility="gone" />

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/tvNoVip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:layout_marginEnd="17dp"
                        android:text="@string/pay_date_normal"
                        android:textColor="@color/black_24"
                        android:textSize="10sp"
                        android:visibility="gone" />

                </LinearLayout>

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/payAdvanceDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="17dp"
                    android:textColor="@color/black_24"
                    android:textSize="10sp" />

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e9" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="15dp"
                android:background="@color/gray_e9" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="250dp"
                android:background="@color/white"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/rlVip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/img_vip"
                            android:layout_width="26dp"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/img_vip" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="5dp"
                            android:layout_toEndOf="@+id/img_vip"
                            android:text="VIP"
                            android:textColor="@color/red_de4d4d"
                            android:textSize="14sp" />

                        <View
                            android:id="@+id/viewVipIndexp"
                            android:layout_width="20dp"
                            android:layout_height="3dp"
                            android:layout_alignParentBottom="true"
                            android:layout_centerHorizontal="true"
                            android:background="@drawable/view_rec_2_bg_with_red" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rlSvip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="50dp">

                        <ImageView
                            android:id="@+id/img_svip"
                            android:layout_width="40dp"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/img_svip" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="5dp"
                            android:layout_toEndOf="@+id/img_svip"
                            android:text="SVIP"
                            android:textColor="@color/gold_ecc382"
                            android:textSize="14sp" />

                        <View
                            android:id="@+id/viewSvipIndex"
                            android:layout_width="20dp"
                            android:layout_height="3dp"
                            android:layout_alignParentBottom="true"
                            android:layout_centerHorizontal="true"
                            android:background="@drawable/view_rec_2_bg_with_gold"
                            android:visibility="gone" />
                    </RelativeLayout>
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerviewProduct"
                    android:layout_width="match_parent"
                    android:layout_height="110dp"
                    android:layout_marginTop="20dp"
                    android:paddingStart="17dp"
                    android:paddingEnd="17dp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/tvSubscribe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="17dp"
                    android:layout_marginTop="20dp"
                    android:textSize="12sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content">

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="17dp"
                        android:layout_marginTop="8dp"
                        android:text="@string/user_agree_vip_str_tips"
                        android:textSize="12sp" />

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/tvVipAgreement"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:layout_marginTop="8dp"
                        android:text="@string/user_agree_vip_str"
                        android:textColor="@color/gold_ecc382"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e9" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="15dp"
                android:background="@color/gray_e9" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="265dp"
                android:background="@color/white"
                android:orientation="horizontal"
                android:paddingStart="17dp"
                android:paddingTop="22dp"
                android:weightSum="4">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview0"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.2" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.0" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview2"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.0" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview3"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.8" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e9" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="15dp"
                android:background="@color/gray_e9" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:background="@color/white"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/wechatRl"
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <ImageView
                        android:id="@+id/img_wechat"
                        android:layout_width="25dp"
                        android:layout_height="23dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="16dp"
                        android:src="@mipmap/wechat_pay" />

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_toEndOf="@+id/img_wechat"
                        android:text="@string/we_pay"
                        android:textColor="@color/black_24"
                        android:textSize="15sp" />

                    <ImageView
                        android:id="@+id/wechatImg"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="16dp"
                        android:src="@mipmap/red_check_selected" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/alipayRl"
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <ImageView
                        android:id="@+id/img_ali"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="16dp"
                        android:src="@mipmap/alipay" />

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_toEndOf="@+id/img_ali"
                        android:text="@string/alipay"
                        android:textColor="@color/black_24"
                        android:textSize="15sp" />

                    <ImageView
                        android:id="@+id/alipayImg"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="16dp"
                        android:src="@mipmap/red_check_unselected" />
                </RelativeLayout>

            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e9" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="15dp"
                android:background="@color/gray_e9" />
           <!-- <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginBottom="80dp"
                android:background="@color/gray_e9" />-->


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:background="@color/white"
                android:orientation="vertical">

              <!--  <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/gray_e9" />-->

                <LinearLayout
                    android:id="@+id/llBuy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:orientation="horizontal">

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/tvBuy"
                        android:layout_width="160dp"
                        android:layout_height="36dp"
                        android:background="@drawable/btn_rec_18_bg_red"
                        android:gravity="center"
                        android:text="@string/subscribe_right_now"
                        android:textColor="@color/white"
                        android:textSize="15sp" />

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/tvGetFree"
                        android:layout_width="160dp"
                        android:layout_height="36dp"
                        android:layout_marginStart="10dp"
                        android:background="@drawable/btn_rec_18_bg_black"
                        android:gravity="center"
                        android:text="@string/get_free_vip"
                        android:textColor="@color/white"
                        android:textSize="15sp" />
                </LinearLayout>

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/tvRenewal"
                    android:layout_width="160dp"
                    android:layout_height="36dp"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="10dp"
                    android:background="@drawable/btn_rec_18_bg_black"
                    android:gravity="center"
                    android:text="@string/renewal"
                    android:textColor="@color/white"
                    android:textSize="15sp"
                    android:visibility="gone" />

            </RelativeLayout>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="10dp"
                android:textAlignment="center"
                android:background="@color/gray_e9"
                android:autoLink="phone"
                android:text="如果您在使用过程中遇到问题，\n可以加入官方QQ群进行交流：1101986490"
                android:textSize="8sp"
                />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_support"
                    android:paddingBottom="10dp"
                    android:gravity="right"
                    android:layout_toRightOf="@+id/tv_support"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:background="@color/gray_e9"
                    android:autoLink="phone"
                    android:text="客服支持电话："
                    android:textSize="8sp"
                    />
            <TextView
                android:id="@+id/callPhone"
                android:paddingBottom="10dp"
                android:gravity="left"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:textColor="@color/blue_29b0d7"
                android:background="@color/gray_e9"
                android:autoLink="phone"
                android:text="************"
                android:textSize="8sp"
                />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</RelativeLayout>