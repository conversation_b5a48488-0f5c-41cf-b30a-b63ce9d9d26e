<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9">

    <LinearLayout
        android:id="@+id/user_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView

                android:id="@+id/userBackBtn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/black_back_icon" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/userTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/buy_vip_title"
                android:textColor="@color/black_24"
                android:textSize="18sp" />


        </RelativeLayout>


    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/pay_rl"
        android:layout_below="@+id/user_top_bar"
        android:fadingEdge="none"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="50dp"
            android:background="@color/gray_f9"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="343dp"
                android:layout_height="55dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="14dp"
                android:background="@drawable/btn_rec_10_bg_with_white_with_stroke_1">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/payUserHeadImg"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_centerVertical="true"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="12dp"
                    app:roundAsCircle="true" />

                <LinearLayout

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="10dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/payUserName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <ImageView
                            android:id="@+id/payUserVipImg"
                            android:layout_width="15sp"
                            android:layout_height="7dp"
                            android:layout_gravity="bottom"
                            android:layout_marginLeft="5dp"
                            android:layout_marginBottom="4dp" />
                    </LinearLayout>

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/payAdvanceDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:textSize="9sp" />

                </LinearLayout>

            </LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="15dp"
                android:text="@string/pay_1"
                android:textColor="@color/black_24"
                android:textSize="16sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/pay_cache_list"
                android:layout_width="345dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:overScrollMode="never"
                android:scrollbars="none" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/pay_list"
                android:layout_width="343dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="15dp"
                android:overScrollMode="never"
                android:scrollbars="none" />

            <LinearLayout
                android:layout_width="343dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:layout_marginBottom="50dp"
                android:background="@drawable/btn_rec_10_bg_with_white_with_stroke_1"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/wechatRl"
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp">

                        <ImageView
                            android:layout_width="25dp"
                            android:layout_height="23dp"
                            android:layout_gravity="center_vertical"
                            android:src="@mipmap/wechat_pay" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="15dp"
                            android:text="@string/we_pay"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />


                    </LinearLayout>

                    <ImageView
                        android:id="@+id/wechatImg"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="20dp" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/alipayRl"
                    android:layout_width="match_parent"
                    android:layout_height="55dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp">

                        <ImageView
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_gravity="center_vertical"
                            android:src="@mipmap/alipay" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginLeft="15dp"
                            android:text="@string/alipay"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />


                    </LinearLayout>

                    <ImageView
                        android:id="@+id/alipayImg"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="20dp"
                        android:src="@mipmap/red_check_unselected" />
                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <RelativeLayout
        android:id="@+id/pay_rl"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_alignParentBottom="true"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@string/total"
                android:textColor="@color/black_24"
                android:textSize="15sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/total_price_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="6dp"
                android:textColor="@color/red_de4d4d"
                android:textSize="18sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/offer_price_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp"
                android:textColor="@color/gray_bb"
                android:textSize="12sp" />


        </LinearLayout>

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/confirmBtn"
            android:layout_width="100dp"
            android:layout_height="50dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="7dp"
            android:background="@color/red_de4d4d"
            android:gravity="center"
            android:text="@string/confirm_pay"
            android:textColor="@color/white"
            android:textSize="15sp" />

    </RelativeLayout>
</RelativeLayout>