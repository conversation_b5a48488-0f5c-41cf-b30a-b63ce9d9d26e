<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">



    <LinearLayout
        android:id="@+id/user_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/white"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/white">

            <ImageView
                android:id="@+id/userBackBtn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/black_back_icon" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/userTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/black_24"
                android:textSize="18sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/commitBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="5dp"
                android:padding="10dp"
                android:text="@string/commit"
                android:textColor="@color/gray_c4"
                android:textSize="15sp" />
        </RelativeLayout>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/user_mail_ll"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        app:bl_stroke_color="@color/gray_e1"
        app:bl_stroke_width="0.5dp"
        tools:ignore="MissingPrefix">

        <EditText
            android:id="@+id/user_mail_edt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="10dp"
            android:singleLine="true"
            android:layout_marginRight="17dp"
            android:background="@null"
            android:digits="@string/account_digits"
            android:hint="@string/input_mail_feedback"
            android:textColor="@color/black_24"
            android:textColorHint="@color/gray_c4"
            android:textSize="15sp" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/user_feedback_ll"
        android:layout_width="match_parent"
        android:layout_height="318dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        app:bl_corners_radius="10dp"
        app:bl_solid_color="@color/white"
        app:bl_stroke_color="@color/gray_e1"
        app:bl_stroke_width="0.5dp"
        tools:ignore="MissingPrefix">

        <EditText
            android:id="@+id/user_feedback_edt"
            android:layout_width="match_parent"
            android:layout_height="295dp"
            android:layout_marginLeft="17dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="17dp"
            android:background="@null"
            android:gravity="top"
            android:hint="@string/input_feedback"
            android:textColor="@color/black_24"
            android:textColorHint="@color/gray_c4"
            android:textSize="15sp" />

    </LinearLayout>

    </LinearLayout>
</LinearLayout>