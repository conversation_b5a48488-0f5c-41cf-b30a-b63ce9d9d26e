<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/blackOpaque80">

        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:padding="10dp"
            android:src="@mipmap/white_back_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_toRightOf="@+id/btn_back"
            android:text="@string/album"
            android:textColor="@color/white"
            android:textSize="17sp" />


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tv_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/white"
            android:textSize="18sp" />


    </RelativeLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <com.czur.scanpro.ui.album.CropImageView
            android:id="@+id/cv_crop_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/btn_ok"
            android:layout_width="250dp"

            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="30dp"
            android:background="@drawable/btn_rect_5_bg_red"
            android:gravity="center"
            android:text="@string/ok"
            android:textColor="@color/white" />

    </RelativeLayout>


</LinearLayout>