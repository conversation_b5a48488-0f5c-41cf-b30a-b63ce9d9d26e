<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_camera_rl"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/black"
    android:gravity="center_horizontal">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:layout_marginLeft="1dp"
        android:layout_marginRight="1dp"
        android:background="@color/black_24" />

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/camera_preview_item_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_margin="5dp" />

    <ImageView
        android:id="@+id/img_loading"
        android:layout_width="50dp"
        android:layout_centerInParent="true"
        android:src="@mipmap/red_loading"
        android:scaleType="centerInside"
        android:layout_height="50dp"/>
</RelativeLayout>

