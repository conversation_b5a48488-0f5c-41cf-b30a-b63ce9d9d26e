<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/pay_rl"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:background="@drawable/rec_with_gray_stroke_without_bottom">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="12dp"
        android:orientation="vertical">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/pay_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_24"
            android:textSize="15sp" />


    </LinearLayout>

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/pay_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="17dp"

        android:textColor="@color/red_de4d4d"
        android:textSize="18sp" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/pay_delete_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="12dp"
        android:layout_toLeftOf="@+id/pay_show_tv"
        android:textColor="@color/gray_d9"
        android:textSize="12sp" />


    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/pay_show_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginRight="12dp"
        android:layout_toLeftOf="@+id/pay_tv"
        android:textColor="@color/red_de4d4d"
        android:textSize="12sp" />

    <ImageView
        android:id="@+id/pay_img"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:src="@mipmap/pay_right"
        android:visibility="gone" />
</RelativeLayout>