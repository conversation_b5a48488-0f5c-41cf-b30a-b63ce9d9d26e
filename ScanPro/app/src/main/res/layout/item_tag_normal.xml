<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tag_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:ignore="MissingPrefix">

    <RelativeLayout
        android:id="@+id/tag_inner_item"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:layout_marginLeft="16dp"
        android:layout_marginBottom="13dp"
        android:orientation="horizontal"
        app:bl_corners_radius="6dp"
        app:bl_solid_color="@color/gray_99">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tag_item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"

            android:ellipsize="end"
            android:maxLines="1" />


    </RelativeLayout>

    <ImageView
        android:id="@+id/tag_delete_btn"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentRight="true"
        android:paddingLeft="15dp"
        android:paddingBottom="15dp"
        android:src="@mipmap/tag_delete_icon" />


</RelativeLayout>