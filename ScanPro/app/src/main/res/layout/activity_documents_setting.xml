<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingPrefix">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/gray_f9">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="6dp"
                android:padding="10dp"
                android:src="@mipmap/black_back_icon" />


            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/document_setting"
                android:textColor="@color/black_24"
                android:textSize="18sp" />


        </RelativeLayout>

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@color/gray_fa"
            android:gravity="bottom"
            android:paddingLeft="16dp"
            android:paddingBottom="10dp"
            android:text="@string/document_information"
            android:textColor="@color/gray_c4"
            android:textSize="12sp" />

        <include layout="@layout/line_dd" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="42dp"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="3dp">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/pageCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:text=""
                    android:textColor="@color/black_24"
                    android:textSize="36sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="5dp"
                    android:layout_toRightOf="@+id/pageCount"
                    android:text="@string/pageCount"
                    android:textColor="@color/black_24"
                    android:textSize="12sp" />


            </RelativeLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/pageSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="16dp"
                android:text=""
                android:textColor="@color/black_24"
                android:textSize="15sp" />
        </RelativeLayout>

        <include layout="@layout/line_dd" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@color/gray_fa"
            android:gravity="bottom"
            android:paddingLeft="16dp"
            android:paddingBottom="10dp"
            android:text="@string/rename"
            android:textColor="@color/gray_c4"
            android:textSize="12sp" />

        <include layout="@layout/line_dd" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="16dp"
                android:text="@string/title"
                android:textColor="@color/black_24"
                android:textSize="15sp" />

            <EditText
                android:id="@+id/pageNewName"
                android:layout_width="250dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="16dp"
                android:background="@null"
                android:gravity="right|center_vertical"
                android:maxLength="20"
                android:singleLine="true"
                android:textColor="@color/black_24"
                android:textSize="15sp" />
        </RelativeLayout>


        <include layout="@layout/line_dd" />
    </LinearLayout>

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/deleteBtn"
        android:layout_width="161dp"
        android:layout_height="36dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="34dp"
        android:layout_marginBottom="30dp"
        android:gravity="center"
        android:text="@string/delete_category"
        android:textColor="@color/white"
        android:textSize="15sp"

        app:bl_corners_radius="36dp"
        app:bl_solid_color="@color/red_de4d4d" />
</RelativeLayout>
