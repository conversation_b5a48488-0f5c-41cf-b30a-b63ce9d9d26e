<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">

        <RelativeLayout
            android:id="@+id/camera_preview_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#000000">

            <com.czur.scanpro.ui.camera.PreviewSurfaceView
                android:id="@+id/surface_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />

            <com.czur.scanpro.ui.camera.CameraBorderView
                android:id="@+id/preview_point_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />

            <com.czur.scanpro.ui.camera.FocusView
                android:id="@+id/focus_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />

            <com.czur.scanpro.ui.camera.BlackShadeView
                android:id="@+id/camera_black_shade"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true" />

            <ImageView
                android:id="@+id/camera_flash_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/camera_scan_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black"
                android:visibility="gone">

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/camera_cut_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true" />

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/camera_color_img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <View
                        android:id="@+id/anim_view"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:background="@color/transparent" />

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/blackOpaque45" />
                </LinearLayout>


            </RelativeLayout>

            <EditText
                android:id="@+id/sensitivity_edt"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:background="@drawable/btn_rec_5_bg_with_toast"
                android:paddingLeft="7dp"
                android:paddingTop="5dp"
                android:paddingRight="7dp"
                android:paddingBottom="5dp"
                android:textColor="@color/white"
                android:textSize="36sp"
                android:visibility="gone" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/focus_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="@drawable/btn_rec_5_bg_with_toast"
                android:paddingLeft="7dp"
                android:paddingTop="5dp"
                android:paddingRight="7dp"
                android:paddingBottom="5dp"
                android:text="REAL_FOCUS"
                android:textColor="@color/white"
                android:textSize="36sp"
                android:visibility="gone" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/focus_tv1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:background="@drawable/btn_rec_5_bg_with_toast"
                android:paddingLeft="7dp"
                android:paddingTop="5dp"
                android:paddingRight="7dp"
                android:paddingBottom="5dp"
                android:text="ALG_FOCUS"
                android:textColor="@color/white"
                android:textSize="36sp"
                android:visibility="gone" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/bottom_toast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="18dp"
                android:background="@drawable/btn_rec_5_bg_with_toast"
                android:paddingLeft="7dp"
                android:paddingTop="5dp"
                android:paddingRight="7dp"
                android:paddingBottom="5dp"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="gone" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/camera_function_rl"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_below="@+id/camera_preview_layout"
            android:orientation="horizontal">


            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/preview_no_icon_back_btn"
                    android:layout_width="31dp"
                    android:layout_height="39dp"
                    android:layout_centerInParent="true"
                    android:padding="10dp"
                    android:src="@mipmap/white_back_icon" />

            </RelativeLayout>


            <ImageView
                android:id="@+id/button_flash"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:contentDescription="@null"
                android:src="@mipmap/take_photo_icon" />

            <RelativeLayout
                android:id="@+id/camera_flash_mode_rl"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <ImageView
                    android:id="@+id/camera_flash_mode_btn"
                    android:layout_width="16dp"
                    android:layout_height="24dp"
                    android:layout_centerInParent="true"
                    android:background="@mipmap/auto_flash" />

            </RelativeLayout>

        </LinearLayout>

        <com.sherlockshi.widget.CenterSelectionTabLayout
            android:id="@+id/center_tab"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_below="@+id/camera_function_rl"
            android:layout_marginTop="10dp"
            app:backgroundColor="@color/black"
            app:selectionBackgroundHeight="35dp"
            app:selectionBackgroundWidth="20dp"
            app:selectionBackground="@drawable/tab_select_bg"
            app:normalStateTextColor="@color/gray_33"
            app:normalStateTextSize="15sp"
            app:itemWidth="70dp"
            app:selectedStateTextColor="@color/white"
            app:selectedStateTextSize="15sp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="15dp"
            android:layout_below="@+id/center_tab">

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:id="@+id/camera_prompt_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/camera_prompt"
                android:textColor="@color/white"
                android:textSize="15sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/camera_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal" />


        </RelativeLayout>
    </RelativeLayout>

    <com.czur.scanpro.ui.component.CameraGuideView
        android:id="@+id/camera_guide_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </com.czur.scanpro.ui.component.CameraGuideView>

</RelativeLayout>