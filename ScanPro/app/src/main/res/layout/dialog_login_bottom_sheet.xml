<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/social_account_login_dialog"
    android:layout_width="match_parent"
    android:layout_height="116dp"
    android:layout_alignParentBottom="true"
    android:background="@color/white">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="84dp"
        android:layout_centerInParent="true">

        <LinearLayout
            android:id="@+id/weixin_account"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@mipmap/weixin_icon" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/qq_account"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@mipmap/qq_icon" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/weibo_account"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:src="@mipmap/weibo_icon" />
        </LinearLayout>
    </LinearLayout>


</RelativeLayout>
