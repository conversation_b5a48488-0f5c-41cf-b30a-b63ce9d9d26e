<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_body"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_2a"
    android:fitsSystemWindows="true">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:background="@color/black">

    <RelativeLayout
        android:id="@+id/layout_title"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_alignParentTop="true"
        android:background="@color/black_2a">

        <ImageView
            android:id="@+id/imgBack"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_centerVertical="true"
            android:contentDescription="@null"
            android:paddingStart="15dp"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingEnd="15dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:src="@mipmap/white_back_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/ocrTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginRight="8.5dp"
            android:text="@string/crop_image_ocr"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/fileOcrChooseBtn"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="8.5dp"
            android:gravity="center"
            android:paddingStart="0dp"
            android:paddingLeft="0dp"
            android:paddingEnd="10dp"
            android:paddingRight="10dp"
            android:text="@string/et_ocr_chinese"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </RelativeLayout>


    <!-- Image Cropper fill the remaining available height -->
    <com.czur.scanpro.ui.component.cropper.CropImageView
        android:id="@+id/cropImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/layout_title"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="65dp"
        android:layout_marginBottom="108dp"
        android:background="@color/black"
        app:cropBorderCornerColor="@color/transparent"
        app:cropBorderLineColor="@color/gray_e4"
        app:cropBorderLineThickness="1dp"
        app:cropGuidelines="on"
        app:cropTouchRadius="100dp" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/layout_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="25dp"
        android:text="@string/et_ocr_prompt"
        android:textColor="@color/white"
        android:textSize="15sp" />

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/etOcrBtn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:background="@drawable/btn_rec_5_bg_with_red_de4d4d"
        android:gravity="center"
        android:text="@string/ocr"
        android:textColor="@color/white" />
    </RelativeLayout>
</RelativeLayout>