<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/register_top_bar"
        layout="@layout/layout_normal_white_top_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="100dp"
                android:layout_height="70dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="60dp"
                android:src="@mipmap/vip_info_icon" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="58dp">

                <View
                    android:layout_width="45dp"
                    android:layout_height="0.5dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="11dp"
                    android:background="@color/gray_c4" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/rule_advanced"
                    android:textColor="@color/gray_c4"
                    android:textSize="15sp" />

                <View
                    android:layout_width="45dp"
                    android:layout_height="0.5dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="11dp"
                    android:background="@color/gray_c4" />
            </LinearLayout>

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="25dp"
                android:gravity="center"
                android:text="@string/rule_1"
                android:textColor="@color/gray_c4"
                android:textSize="12sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:text="@string/rule_2"
                android:textColor="@color/gray_c4"
                android:textSize="12sp" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:text="@string/rule_3"
                android:textColor="@color/gray_c4"
                android:textSize="12sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="50dp">

                <View
                    android:layout_width="45dp"
                    android:layout_height="0.5dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="11dp"
                    android:background="@color/gray_c4" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/rule_permission"
                    android:textColor="@color/gray_c4"
                    android:textSize="15sp" />

                <View
                    android:layout_width="45dp"
                    android:layout_height="0.5dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="11dp"
                    android:background="@color/gray_c4" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:background="@color/gray_f3">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="25dp"
                    android:text="@string/generate_pdf_count"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="25dp"
                    android:layout_marginRight="22dp"
                    android:text="@string/many_times"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:background="@color/gray_f3">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="25dp"
                    android:text="@string/ocr"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="25dp"
                    android:layout_marginRight="22dp"
                    android:text="@string/many_times"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:background="@color/gray_f3">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="25dp"
                    android:text="@string/card_ocr"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="25dp"
                    android:layout_marginRight="22dp"
                    android:text="@string/fifty_times_per_month"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:background="@color/gray_f3">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="25dp"
                    android:text="@string/handwriting_1"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="25dp"
                    android:layout_marginRight="22dp"
                    android:text="@string/fifty_times_per_month"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:background="@color/gray_f3">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="25dp"
                    android:text="@string/shared_by_encryption"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <ImageView
                    android:layout_width="10dp"
                    android:layout_height="9.5dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="30dp"
                    android:src="@mipmap/gray_right_icon" />
            </RelativeLayout>


            <RelativeLayout
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="50dp"
                android:background="@color/gray_f3">

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="25dp"
                    android:text="@string/cloud_ocr"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="25dp"
                    android:layout_marginRight="22dp"
                    android:text="@string/hundred_times_per_month"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />
            </RelativeLayout>
        </LinearLayout>

    </ScrollView>


</LinearLayout>