<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_2a"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <RelativeLayout
        android:id="@+id/handwriting_result_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:text="@string/ocr_result"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/handwritingResultFinishBtn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:padding="10dp"
            android:src="@mipmap/white_back_icon" />


    </RelativeLayout>


    <RelativeLayout

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/page_preview_bottom_ll"
        android:layout_below="@+id/handwriting_result_top_bar"
        android:background="@color/black">


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/handwritingResultText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="29.5dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="29.5dp"
            android:textColor="@color/gray_c4"
            android:textIsSelectable="true"
            android:textSize="15sp" />

    </RelativeLayout>

    <com.czur.scanpro.ui.component.MediumBoldTextView
        android:id="@+id/recognizeAgain"
        android:layout_width="161dp"
        android:layout_height="36dp"
        android:layout_above="@+id/page_preview_bottom_ll"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="7dp"
        android:layout_marginTop="34dp"
        android:layout_marginBottom="15dp"
        android:gravity="center"
        android:text="@string/ocr_again"
        android:textColor="@color/gray_fa"
        android:textSize="15sp"

        app:bl_corners_radius="36dp"
        app:bl_solid_color="@color/red_de4d4d" />

    <LinearLayout
        android:id="@+id/page_preview_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:background="@color/black_2a">


        <RelativeLayout
            android:id="@+id/handwritingResultCopyRl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/handwriting_result_copy_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@mipmap/copy_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/handwriting_result_copy_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/copy"
                    android:textColor="@color/white"
                    android:textSize="9sp" />
            </RelativeLayout>

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>

