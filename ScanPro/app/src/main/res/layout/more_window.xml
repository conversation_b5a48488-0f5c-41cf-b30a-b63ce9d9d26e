<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rel"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

<!--com.ms_square.etsyblur.BlurringView-->
    <ImageView
        android:id="@+id/more_blurring_imageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white" />


    <LinearLayout
        android:id="@+id/lin"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/card_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/card_btn"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@mipmap/index_normal_card_icon" />

            <com.czur.scanpro.ui.component.MediumBoldTextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/card"
                android:textColor="@color/blue_color_text"
                android:textSize="18sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/surface_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/surface_btn"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@mipmap/index_surface_icon" />

            <com.czur.scanpro.ui.component.MediumBoldTextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/surface"
                android:textColor="@color/black_24"
                android:textSize="18sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/single_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/single_btn"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@mipmap/index_single_icon" />

            <com.czur.scanpro.ui.component.MediumBoldTextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/single"
                android:textColor="@color/colorAccent"
                android:textSize="18sp" />

        </LinearLayout>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/bottom_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/import_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="22dp"
            android:orientation="vertical">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="29dp"
                android:src="@mipmap/add_into" />

            <com.czur.scanpro.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:text="@string/into"
                android:textColor="@color/black_24"
                android:textSize="12sp" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rel_close"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="10dp"
                android:src="@mipmap/index_cancel" />
        </RelativeLayout>
    </RelativeLayout>


</RelativeLayout>