<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/cancelBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:padding="10dp"
            android:text="@string/cancel"
            android:textColor="@color/black_24"
            android:textSize="15sp" />


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/userTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/share_secret"
            android:textColor="@color/black_24"
            android:textSize="18sp" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/commitBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="5dp"
            android:padding="10dp"
            android:text="@string/share"
            android:textColor="@color/red_de4d4d"
            android:textSize="15sp" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="158dp">

        <RelativeLayout
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_centerInParent="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            app:bl_corners_radius="36dp"
            app:bl_solid_color="@color/gray_ee"
            tools:ignore="MissingPrefix">

            <com.czur.scanpro.ui.component.NoHintEditText
                android:layout_centerInParent="true"
                android:id="@+id/codeEdt"
                android:layout_width="161dp"
                android:layout_height="36dp"
                android:background="@null"
                android:ellipsize="end"
                android:gravity="center"
                android:hint="@string/set_share_password"
                android:maxLength="20"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:singleLine="true"
                android:textColor="@color/black_24"
                android:textColorHint="@color/gray_c4"
                android:textSize="15sp" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/oneDayRl"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginLeft="22.5dp"
        android:layout_marginRight="22.5dp">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_ec" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/one_day_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/stay_one_day"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/oneDayImg"
            android:layout_width="18dp"
            android:layout_height="12dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/black_right" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/sevenDayRl"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginLeft="22.5dp"
        android:layout_marginRight="22.5dp">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gray_ec" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/gray_ec" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/seven_day_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="@string/stay_seven_day"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/sevenDayImg"
            android:layout_width="18dp"
            android:layout_height="12dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/black_right"
            android:visibility="gone" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/thirtyDayRl"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_marginLeft="22.5dp"
        android:layout_marginRight="22.5dp">


        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/thirty_day_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"

            android:text="@string/stay_thirty_day"
            android:textColor="@color/black_24"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/thirtyDayImg"
            android:layout_width="18dp"
            android:layout_height="12dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/black_right"
            android:visibility="gone" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/gray_ec" />
    </RelativeLayout>


</LinearLayout>