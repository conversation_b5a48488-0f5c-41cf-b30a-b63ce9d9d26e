<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray_f9"
    android:orientation="vertical"
    tools:ignore="MissingPrefix">

    <include
        android:id="@+id/user_top_bar"
        layout="@layout/layout_normal_transparent_top_bar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="50dp">

            <LinearLayout
                android:id="@+id/ll_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/user_head_rl"
                    android:layout_width="match_parent"
                    android:layout_height="125dp">

                    <com.facebook.drawee.view.SimpleDraweeView
                        android:id="@+id/user_head_img"
                        android:layout_width="57.5dp"
                        android:layout_height="57.5dp"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginStart="28dp"
                        app:roundAsCircle="true" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_toStartOf="@+id/tv_my_setting"
                        android:layout_toEndOf="@+id/user_head_img"
                        android:orientation="vertical">

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/login_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:singleLine="true"
                            android:textColor="@color/black_24"
                            android:textSize="18sp" />

                        <ImageView
                            android:id="@+id/vip_icon"
                            android:layout_width="40dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/img_vip"
                            android:visibility="gone" />
                    </LinearLayout>

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:id="@+id/tv_my_setting"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_toStartOf="@+id/arrow"
                        android:text="@string/my_setting"
                        android:textColor="@color/black_24" />

                    <ImageView
                        android:id="@+id/arrow"
                        android:layout_width="6.5dp"
                        android:layout_height="12.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="32dp"
                        android:src="@mipmap/user_navigate_icon" />


                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/user_pdf"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    app:bl_corners_radius="5dp"
                    app:bl_solid_color="@color/white"
                    app:bl_stroke_color="@color/gray_e1"
                    app:bl_stroke_width="0.5dp">

                    <ImageView
                        android:id="@+id/user_pdf_img"
                        android:layout_width="21dp"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:src="@mipmap/user_pdf" />

                    <com.czur.scanpro.ui.component.MediumBoldTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_toEndOf="@+id/user_pdf_img"
                        android:text="@string/my_pdf"
                        android:textColor="@color/black_24" />

                    <ImageView
                        android:layout_width="6.5dp"
                        android:layout_height="12.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="14dp"
                        android:src="@mipmap/user_navigate_icon" />

                </RelativeLayout>

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="14dp"
                    android:gravity="center"
                    android:text="@string/cloud_size"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <LinearLayout
                    android:id="@+id/user_sync_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="16dp"
                    android:orientation="vertical"
                    app:bl_corners_radius="5dp"
                    app:bl_solid_color="@color/white"
                    app:bl_stroke_color="@color/gray_e1"
                    app:bl_stroke_width="0.5dp">

                    <RelativeLayout
                        android:id="@+id/user_cloud_rl"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">

                        <ImageView
                            android:id="@+id/user_cloud_img"
                            android:layout_width="21dp"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/cloud" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_toEndOf="@+id/user_cloud_img"
                            android:text="@string/cloud_size"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/tv_cloud_size"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:textColor="@color/gray_c4"
                            android:textSize="15sp" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="14dp"
                        android:layout_marginRight="14dp"
                        android:background="@color/gray_e1" />

                    <RelativeLayout
                        android:id="@+id/user_sync_now_rl"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/sync_now"
                            android:textColor="@color/red_de4d4d"
                            android:textSize="15sp" />


                        <ImageView
                            android:id="@+id/sync_now_img"
                            android:layout_width="22dp"
                            android:layout_height="22dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/sync_loading_icon"
                            android:visibility="gone" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/book_menu_last_sync_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:gravity="end"
                            android:text="@string/last_sync_time"
                            android:textColor="@color/gray_c4"
                            android:textSize="10sp"
                            android:visibility="gone" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="14dp"
                        android:layout_marginRight="14dp"
                        android:background="@color/gray_e1" />

                    <RelativeLayout
                        android:id="@+id/user_auto_sync_rl"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/auto_sync"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <com.suke.widget.SwitchButton
                            android:id="@+id/sync_switch_btn"
                            android:layout_width="50dp"
                            android:layout_height="30dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="10dp"
                            app:sb_checked="true"
                            app:sb_checked_color="@color/black_24"
                            app:sb_shadow_effect="true"
                            app:sb_show_indicator="false" />
                    </RelativeLayout>
                </LinearLayout>

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="14dp"
                    android:gravity="center"
                    android:text="@string/vip"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <LinearLayout
                    android:id="@+id/user_vip_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginRight="16dp"
                    android:orientation="vertical"
                    app:bl_corners_radius="5dp"
                    app:bl_solid_color="@color/white"
                    app:bl_stroke_color="@color/gray_e1"
                    app:bl_stroke_width="0.5dp">

                    <RelativeLayout
                        android:id="@+id/user_vip_rl"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">

                        <ImageView
                            android:id="@+id/vip_img"
                            android:layout_width="21dp"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/user_vip" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/tv_my_vip"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_toEndOf="@+id/vip_img"
                            android:text="@string/my_vip"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <ImageView
                            android:id="@+id/user_vip_red_point"
                            android:layout_width="6dp"
                            android:layout_height="6dp"
                            android:layout_marginTop="15dp"
                            android:layout_toEndOf="@+id/tv_my_vip"
                            android:src="@mipmap/red_point"
                            android:visibility="gone" />

                        <ImageView
                            android:layout_width="6.5dp"
                            android:layout_height="12.5dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/user_navigate_icon" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rl_my_invite"
                        android:visibility="visible"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginLeft="14dp"
                            android:layout_marginRight="14dp"
                            android:background="@color/gray_e1" />
                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/tv_invite"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/my_invite_code"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <ImageView
                            android:id="@+id/user_input_invite_red_point"
                            android:layout_width="6dp"
                            android:layout_height="6dp"
                            android:layout_marginTop="15dp"
                            android:layout_toEndOf="@+id/tv_invite"
                            android:src="@mipmap/red_point"
                            android:visibility="gone" />

                        <ImageView
                            android:layout_width="6.5dp"
                            android:layout_height="12.5dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/user_navigate_icon" />
                    </RelativeLayout>


                    <RelativeLayout
                        android:id="@+id/user_input_invite_rl"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:visibility="visible">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginLeft="14dp"
                            android:layout_marginRight="14dp"
                            android:background="@color/gray_e1" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/input_invite_code_detail"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <ImageView
                            android:layout_width="6.5dp"
                            android:layout_height="12.5dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/user_navigate_icon" />
                    </RelativeLayout>
                </LinearLayout>

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="14dp"
                    android:gravity="center"
                    android:text="@string/permission"
                    android:textColor="@color/gray_c4"
                    android:textSize="12sp" />

                <LinearLayout
                    android:id="@+id/user_permission_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginRight="16dp"
                    android:orientation="vertical"
                    app:bl_corners_radius="5dp"
                    app:bl_solid_color="@color/white"
                    app:bl_stroke_color="@color/gray_e1"
                    app:bl_stroke_width="0.5dp">

                    <RelativeLayout
                        android:id="@+id/user_info_pdf_rl"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:visibility="gone">

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/create_new_pdf"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/pdf_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="14dp"
                            android:text="@string/day_convert_times"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/user_ocr_rl"
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:visibility="gone">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginLeft="14dp"
                            android:layout_marginRight="14dp"
                            android:background="@color/gray_e1" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/ocr_count"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/ocr_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="14dp"
                            android:text="@string/day_convert_times"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/user_card_ocr_rl"
                        android:layout_width="match_parent"
                        android:visibility="gone"
                        android:layout_height="45dp">

                        <View
                            android:id="@+id/line"
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginLeft="14dp"
                            android:layout_marginRight="14dp"
                            android:background="@color/gray_e1" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/card_ocr"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/card_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="14dp"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:id="@+id/user_hand_ocr_rl"
                        android:visibility="gone"
                        android:layout_height="45dp">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginLeft="14dp"
                            android:layout_marginRight="14dp"
                            android:background="@color/gray_e1" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/handwriting"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/handwriting_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="14dp"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/user_cloud_ocr_rl"
                        android:layout_width="match_parent"
                        android:visibility="gone"
                        android:layout_height="45dp">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginLeft="14dp"
                            android:layout_marginRight="14dp"
                            android:background="@color/gray_e1" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:text="@string/cloud_ocr"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:id="@+id/cloudOcrTv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="14dp"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />
                    </RelativeLayout>

                </LinearLayout>

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/btn_update_vip"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/btn_rec_20_bg"
                    android:gravity="center"
                    android:paddingStart="25dp"
                    android:paddingEnd="25dp"
                    android:text="@string/update_vip"
                    android:visibility="gone"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_no_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="100dp"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/img_login"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="center_horizontal"
                    android:src="@mipmap/user_unlogin_icon" />

                <com.czur.scanpro.ui.component.MediumBoldTextView
                    android:id="@+id/tv_login"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="10dp"
                    android:text="@string/guide_tv_1"
                    android:textColor="@color/black_24"
                    android:textSize="18sp" />

                <LinearLayout
                    android:id="@+id/ll_about"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginRight="16dp"
                    android:orientation="vertical"
                    app:bl_corners_radius="5dp"
                    app:bl_solid_color="@color/white"
                    app:bl_stroke_color="@color/gray_e1"
                    app:bl_stroke_width="0.5dp">

                    <RelativeLayout
                        android:id="@+id/rl_about"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">

                        <ImageView
                            android:id="@+id/img_about"
                            android:layout_width="21dp"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/user_about" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_toEndOf="@+id/img_about"
                            android:text="@string/about_scan_pro"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <ImageView
                            android:layout_width="6.5dp"
                            android:layout_height="12.5dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/user_navigate_icon" />
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="14dp"
                        android:layout_marginRight="14dp"
                        android:background="@color/gray_e1" />

                    <RelativeLayout
                        android:id="@+id/rl_privacy"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">

                        <ImageView
                            android:id="@+id/img_privacy"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/user_privacy" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_toEndOf="@+id/img_privacy"
                            android:text="@string/privacy_policy1"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <ImageView
                            android:layout_width="6.5dp"
                            android:layout_height="12.5dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/user_navigate_icon" />
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="14dp"
                        android:layout_marginRight="14dp"
                        android:background="@color/gray_e1" />

                    <RelativeLayout
                        android:id="@+id/rl_privacy2"
                        android:layout_width="match_parent"
                        android:layout_height="45dp">

                        <ImageView
                            android:id="@+id/img_privacy2"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:adjustViewBounds="true"
                            android:scaleType="fitXY"
                            android:src="@mipmap/user_privacy2" />

                        <com.czur.scanpro.ui.component.MediumBoldTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="10dp"
                            android:layout_toEndOf="@+id/img_privacy2"
                            android:text="@string/privacy_terms"
                            android:textColor="@color/black_24"
                            android:textSize="15sp" />

                        <ImageView
                            android:layout_width="6.5dp"
                            android:layout_height="12.5dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="14dp"
                            android:src="@mipmap/user_navigate_icon" />
                    </RelativeLayout>

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>


</LinearLayout>