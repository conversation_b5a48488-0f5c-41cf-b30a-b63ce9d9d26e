<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/bg"
    android:layout_width="match_parent"
    android:layout_height="40dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tv_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="25dp"
            android:textColor="@color/black_24"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tv_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="25dp"
            android:textColor="@color/black_24"
            android:textSize="16sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tv_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="25dp"
            android:textColor="@color/black_24"
            android:textSize="16sp" />
    </LinearLayout>
</LinearLayout>
