<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:ignore="MissingPrefix">

    <RelativeLayout
        android:id="@+id/tag_title_rl"
        android:layout_width="match_parent"
        android:layout_height="30dp">

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tag_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:text="@string/my_tag"
            android:textColor="@color/black_24"
            android:textSize="14sp" />

        <ImageView
            android:layout_width="9dp"
            android:layout_height="9dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:layout_toRightOf="@+id/tag_title_tv"
            android:src="@mipmap/main_tag_icon" />

        <com.czur.scanpro.ui.component.MediumBoldTextView
            android:id="@+id/tag_edit_btn"
            android:layout_width="51dp"
            android:layout_height="21dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="16dp"
            android:gravity="center"
            android:text="@string/edit"
            android:textColor="@color/black_24"
            android:textSize="14sp"
            app:bl_corners_radius="21dp"
            app:bl_stroke_color="@color/black_24"
            app:bl_stroke_width="0.5dp" />
    </RelativeLayout>


    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableAutoLoadMore="false"
        app:srlEnableLoadMore="false"
        app:srlEnableOverScrollBounce="true"
        app:srlEnableOverScrollDrag="true"
        android:layout_below="@+id/tag_title_rl"
        app:srlEnableRefresh="false">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/tag_recyclerview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginRight="8dp"
            android:layout_marginLeft="8dp"
            android:overScrollMode="never"
            android:scrollbars="none" />

    </com.scwang.smartrefresh.layout.SmartRefreshLayout>


</RelativeLayout>