<?xml version="1.0" encoding="utf-8"?>
<resources>


    <declare-styleable name="RoundedRectProgressBar">
        <attr name="backColor" format="color" />
        <attr name="barColor" format="color" />
    </declare-styleable>


    <declare-styleable name="HeadCropImageView">
        <attr name="cropMaskColor" format="color"/>
        <attr name="cropBorderColor" format="color"/>
        <attr name="cropBorderWidth" format="dimension"/>
        <attr name="cropFocusWidth" format="dimension"/>
        <attr name="cropFocusHeight" format="dimension"/>
        <attr name="cropStyle" format="enum">
            <enum name="rectangle" value="0"/>
            <enum name="circle" value="1"/>
        </attr>
    </declare-styleable>

    <declare-styleable name="RingProgressBar">
        <attr name="ringColor" format="color" />
        <attr name="ringProgressColor" format="color" />
        <attr name="ringWidth" format="dimension" />
        <attr name="textColor" format="color" />
        <attr name="textSize" format="dimension" />
        <attr name="max" format="integer" />
        <attr name="textIsShow" format="boolean" />
        <attr name="progress" format="integer" />
        <attr name="ringPadding" format="dimension" />
        <attr name="style">
            <enum name="STROKE" value="0" />
            <enum name="FILL" value="1" />
        </attr>
    </declare-styleable>


</resources>