<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Dialog -->
    <style name="TransparentDialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
    </style>

    <style name="TransparentProgressDialog" parent="TransparentDialog">
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="Dialog_Fullscreen">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/BottomAnimation</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="Dialog_Fullscreen_Low">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@style/BottomAnimation</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="BottomAnimation">
        <item name="android:windowEnterAnimation">@anim/bottom_sheet_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/bottom_sheet_exit_anim</item>
    </style>

</resources>
