<resources>
    <!--BaseActivity全屏主题-->
    <style name="Theme.AppCompat.NoActionBar.HideWindow" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowDisablePreview">true</item>
    </style>
    <!--BaseActivity全屏主题-->
    <style name="Theme.AppCompat.NoActionBar.EtFullscreen" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowDisablePreview">true</item>
    </style>
    <!--BaseActivity全屏主题-->
    <style name="Theme.AppCompat.NoActionBar.Fullscreen" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <!--闪屏页主题-->
    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDisablePreview">true</item>
    </style>


    <style name="ChangeLineColorEditText" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorControlNormal">@color/gray_e3dd</item>
        <item name="colorControlActivated">@color/gray_e3dd</item>
    </style>


    <!-- 登录页面底部弹出框-->
    <style name="SocialAccountDialogStyle" parent="@android:style/Theme.Dialog">
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 没有标题 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:layout_width">match_parent</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">false</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 不透明百分比-->
        <item name="android:backgroundDimAmount">0.2</item>
        <!-- 模糊 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 对话框的背景-->
        <item name="android:windowBackground">@color/blackOpaque50</item>
        <item name="android:windowAnimationStyle">@style/BottomSheetAnimation</item>
    </style>

    <!-- 登录页面底部弹出框-->
    <style name="LongPressStyle" parent="@android:style/Theme.Dialog">
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 没有标题 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:layout_width">wrap_content</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">false</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 不透明百分比-->
        <item name="android:backgroundDimAmount">0.0</item>
        <!-- 模糊 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 对话框的背景-->
        <item name="android:windowBackground">@color/transparent</item>
        <!--<item name="android:windowAnimationStyle">@style/BottomSheetAnimation</item>-->
    </style>

    <!-- 登录页面底部弹出框动画-->
    <style name="BottomSheetAnimation">
        <item name="android:windowEnterAnimation">@anim/bottom_sheet_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/bottom_sheet_exit_anim</item>
    </style>
    <style name="BookCheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">@drawable/selector_checkbox</item>
        <item name="android:button">@null</item>
    </style>


    <!--图片放大镜-->
    <declare-styleable name="FitImageView">
        <!--图片资源-->
        <attr name="z_fit_src" format="reference"/>
    </declare-styleable>
    <!--图片放大镜-->
    <declare-styleable name="BiggerView">
        <!--半径-->
        <attr name="z_bv_radius" format="dimension"/>
        <!--边线宽-->
        <attr name="z_bv_outline_width" format="dimension"/>
        <!--进度色-->
        <attr name="z_bv_outline_color" format="color"/>
        <!--放大倍率-->
        <attr name="z_bv_rate" format="float"/>
    </declare-styleable>

    <declare-styleable name="MainTabLayout">
        <attr name="tab_normal_textSize" format="dimension"/>
        <attr name="tab_select_textSize" format="dimension"/>
    </declare-styleable>


    <style name="NewCheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">@drawable/selector_checkbox_new</item>
        <item name="android:button">@null</item>
    </style>


    <style name="MyRadioButton" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/white</item>
        <item name="colorControlActivated">@color/red_d54146</item>
    </style>
</resources>
