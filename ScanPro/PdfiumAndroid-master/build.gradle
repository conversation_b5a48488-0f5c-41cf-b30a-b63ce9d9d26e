buildscript {
    repositories {
        google()
        mavenCentral()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
//        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.8.4'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
    }
}

apply plugin: 'com.android.library'

ext {
    bintrayRepo = 'maven'
    bintrayName = 'pdfium-android'

    publishedGroupId = 'com.github.barteksc'
    libraryName = 'PdfiumAndroid'
    artifact = 'pdfium-android'

    libraryDescription = 'Fork of library for rendering PDFs on Android\'s Surface or Bitmap'

    siteUrl = 'https://github.com/barteksc/PdfiumAndroid'
    gitUrl = 'https://github.com/barteksc/PdfiumAndroid.git'

    libraryVersion = '1.8.2'

    developerId = 'barteksc'
    developerName = '<PERSON><PERSON><PERSON>'
    developerEmail = '<EMAIL>'

    licenseName = 'The Apache Software License, Version 2.0'
    licenseUrl = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
    allLicenses = ["Apache-2.0"]
}

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion
    namespace 'com.czur.scanpro'
    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        releaseTest {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

        }
        debug {


        }
    }


    sourceSets{
        main {
            jni.srcDirs = []
            jniLibs.srcDir 'src/main/libs'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation rootProject.ext.deps.supportV4
}
