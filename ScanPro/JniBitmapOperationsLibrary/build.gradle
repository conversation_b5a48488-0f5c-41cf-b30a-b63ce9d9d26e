apply plugin: 'com.android.library'

version '0.0.1'
group 'com.jni.bitmap_operations'

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion
    buildToolsVersion rootProject.ext.android.buildToolsVersion

    buildTypes {
        release {
            minifyEnabled false
        }
        releaseTest {
            minifyEnabled false

        }
        debug {


        }
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src']
            resources.srcDirs = ['src']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            jni.srcDirs = ['jni']
        }
    }

    defaultConfig {
//        ndk {
//            moduleName "JniBitmapOperationsLibrary"
//            ldLibs "log", "jnigraphics"
//            cFlags "-DANDROID_NDK_HOME"
//            stl "stlport_shared"
//            abiFilters 'armeabi-v7a', 'arm64-v8a'
//        }
        minSdkVersion 8
        targetSdkVersion rootProject.ext.android.compileSdkVersion
    }

    namespace "com.jni.bitmap_operations"

//    externalNativeBuild {
//        ndkBuild {
//            path 'jni/Android.mk'
//        }
//    }

/*
    task buildNative(type: Exec, description: 'Compile JNI source via NDK') {
        def ndkDir = android.plugin.ndkFolder
        commandLine "$ndkDir/ndk-build",
                '-C', file('jni').absolutePath,
                '-j', Runtime.runtime.availableProcessors(),
                'all',
                'NDK_DEBUG=1'
    }

    task cleanNative(type: Exec, description: 'Clean JNI object files') {
        def ndkDir = android.plugin.ndkFolder
        commandLine "$ndkDir/ndk-build",
                '-C', file('jni').absolutePath,
                'clean'
    }

    clean.dependsOn 'cleanNative'

    tasks.withType(JavaCompile) {
        compileTask -> compileTask.dependsOn buildNative
    }
    */
}
