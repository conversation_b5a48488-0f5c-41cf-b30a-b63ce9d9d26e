apply plugin: 'com.android.library'

android {

    compileSdkVersion COMPILE_SDK_VERSION as int

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION as int
        targetSdkVersion TARGET_SDK_VERSION as int
    }

    namespace 'cn.hzw.doodle'


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        releaseTest {
            minifyEnabled false
        }
        debug {


        }
    }
}

dependencies {
    // https://github.com/1993hzw/Androids
    api 'com.github.1993hzw:Androids:1.3'
}