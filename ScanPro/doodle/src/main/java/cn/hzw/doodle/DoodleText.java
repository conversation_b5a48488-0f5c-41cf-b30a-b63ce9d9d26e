package cn.hzw.doodle;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;

import cn.hzw.doodle.core.IDoodle;
import cn.hzw.doodle.core.IDoodleColor;

/**
 * 文字item
 * Created by s<PERSON><PERSON> on 2020/7/16.
 */

public class DoodleText extends DoodleRotatableItemBase {

    private Rect mRect = new Rect();
    private final TextPaint mPaint = new TextPaint();
    private String mText;
    private StaticLayout layout;
    private float x ,y;

    public DoodleText(Context context, IDoodle doodle, String text, float size, IDoodleColor color, float x, float y) {
        super(context, doodle, -doodle.getDoodleRotation(), x, y);
        setPen(DoodlePen.TEXT);
        mText = text;
        this.x = x;
        this.y = y;
        setSize(size);
        setColor(color);
        setCenter();
    }

    public String getText() {
        return mText;
    }

    public void setText(String text) {
        mText = text;
        resetBounds(mRect);
        setPivotX(getLocation().x + mRect.width() / 2);
        setPivotY(getLocation().y + mRect.height() / 2);
        resetBoundsScaled(getBounds());
        refresh();
        setCenter();
    }

    private void setCenter(){
        int textWidth = (int) mPaint.measureText(mText);
        if (textWidth > getDoodle().getUnitSize() * 280) {
            textWidth = (int) (getDoodle().getUnitSize() * 280);
        }
        layout = new StaticLayout(mText, mPaint, textWidth, Layout.Alignment.ALIGN_NORMAL, 1.0F, 0.0F, true);
        setLocation((float) (x - 0.5 * layout.getWidth()), (float) (y - 0.5 * layout.getHeight()));
    }
    @Override
    public void resetBounds(Rect rect) {
        if (TextUtils.isEmpty(mText)) {
            return;
        }
        mPaint.setTextSize(getSize());
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.FILL);
        int textWidth = (int) mPaint.measureText(mText);
        if (textWidth > getDoodle().getUnitSize() * 280) {
            textWidth = (int) (getDoodle().getUnitSize() * 280);
        }
        layout = new StaticLayout(mText, mPaint, textWidth, Layout.Alignment.ALIGN_NORMAL, 1.0F, 0.0F, true);
        rect.set(0, 0, textWidth, layout.getHeight());
    }

    @Override
    public void doDraw(Canvas canvas) {
        getColor().config(this, mPaint);
        mPaint.setTextSize(getSize());
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.FILL);
        canvas.save();
        int textWidth = (int) mPaint.measureText(mText);
        if (textWidth > getDoodle().getUnitSize() * 280) {
            textWidth = (int) (getDoodle().getUnitSize() * 280);
        }
        layout = new StaticLayout(mText, mPaint, textWidth, Layout.Alignment.ALIGN_NORMAL, 1.0F, 0.0F, true);
        layout.draw(canvas);
        canvas.restore();
    }

}


