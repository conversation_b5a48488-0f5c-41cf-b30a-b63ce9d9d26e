package cn.hzw.doodle;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.Rect;
import android.util.TypedValue;

import cn.hzw.doodle.core.IDoodle;
import cn.hzw.doodle.util.DrawUtil;

import static cn.hzw.doodle.util.DrawUtil.rotatePoint;

/**
 * 可旋转的item
 */
public abstract class DoodleRotatableItemBase extends DoodleSelectableItemBase {

    private PointF mTemp = new PointF();
    private Rect mRectTemp = new Rect();
    private Rect mRectTemp2 = new Rect();

    private Rect mRotateRect = new Rect();
    private Rect mDeleteRect = new Rect();

    private boolean mIsRotating = false;
    private Paint mPaint = new Paint();
    private Context context;
    private Bitmap rotateBitmap, deleteBitmap;

    public DoodleRotatableItemBase(Context context, IDoodle doodle, int itemRotate, float x, float y) {
        super(context, doodle, itemRotate, x, y);
        this.context = context;
        initBitmap();
    }

    public DoodleRotatableItemBase(Context context, IDoodle doodle, DoodlePaintAttrs attrs, int itemRotate, float x, float y) {
        super(context, doodle, attrs, itemRotate, x, y);
        this.context = context;
        initBitmap();
    }

    @Override
    public void doDrawAtTheTop(Canvas canvas) {
        if (isSelected()) {
            // 反向缩放画布，使视觉上选中边框不随图片缩放而变化
            int count = canvas.save();
            canvas.scale(1 / getDoodle().getDoodleScale(), 1 / getDoodle().getDoodleScale(), getPivotX() - getLocation().x, getPivotY() - getLocation().y);
            mRectTemp.set(getBounds());
            mRectTemp2.set(getBounds());
            DrawUtil.scaleRect(mRectTemp, getDoodle().getDoodleScale(), getPivotX() - getLocation().x, getPivotY() - getLocation().y);
            DrawUtil.scaleRect(mRectTemp2, getDoodle().getDoodleScale(), getPivotX() - getLocation().x, getPivotY() - getLocation().y);
            float unit = getDoodle().getUnitSize();
            mRectTemp.left -= ITEM_PADDING * unit;
            mRectTemp.top -= ITEM_PADDING * unit;
            mRectTemp.right += ITEM_PADDING * unit;
            mRectTemp.bottom += ITEM_PADDING * unit;

            mRectTemp2.left -= 2 * ITEM_PADDING * unit;
            mRectTemp2.top -= 2 * ITEM_PADDING * unit;
            mRectTemp2.right += 2 * ITEM_PADDING * unit;
            mRectTemp2.bottom += 2 * ITEM_PADDING * unit;


            mRotateRect.left = (int) (mRectTemp.left - 13 * unit - 0.5 * ITEM_PADDING * unit);
            mRotateRect.right = (int) (mRectTemp.left + 13 * unit - 0.5 * ITEM_PADDING * unit);
            mRotateRect.top = (int) (mRectTemp.bottom - 13 * unit + 0.5 * ITEM_PADDING * unit);
            mRotateRect.bottom = (int) (mRectTemp.bottom + 13 * unit + 0.5 * ITEM_PADDING * unit);

            mDeleteRect.left = (int) (mRectTemp.right - 13 * unit + 0.5 * ITEM_PADDING * unit);
            mDeleteRect.right = (int) (mRectTemp.right + 13 * unit + 0.5 * ITEM_PADDING * unit);
            mDeleteRect.top = (int) (mRectTemp.top - 13 * unit - 0.5 * ITEM_PADDING * unit);
            mDeleteRect.bottom = (int) (mRectTemp.top + 13 * unit - 0.5 * ITEM_PADDING * unit);

            mPaint.setColor(Color.parseColor("#cccccc"));
            mPaint.setStyle(Paint.Style.STROKE);
            mPaint.setStrokeWidth(1 * unit);
            mPaint.setAntiAlias(true);
            mPaint.setFilterBitmap(true);
            canvas.drawRect(mRectTemp, mPaint);
            canvas.drawRect(mRectTemp2, mPaint);

            canvas.drawBitmap(rotateBitmap, new Rect(0, 0, rotateBitmap.getWidth(), rotateBitmap.getHeight()), mRotateRect, mPaint);
            canvas.drawBitmap(deleteBitmap, new Rect(0, 0, deleteBitmap.getWidth(), deleteBitmap.getHeight()), mDeleteRect, mPaint);
            canvas.restoreToCount(count);
        }
    }


    private void initBitmap() {
        rotateBitmap = getBitmap(context, R.mipmap.icon_rotate);
        deleteBitmap = getBitmap(context, R.mipmap.icon_remove);
    }

    private Bitmap getBitmap(Context context, int resId) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        TypedValue value = new TypedValue();
        context.getResources().openRawResource(resId, value);
        options.inTargetDensity = value.density;
        options.inScaled = false;
        return BitmapFactory.decodeResource(context.getResources(), resId, options);
    }


    /**
     * 是否可以旋转
     */
    public boolean canMove(float x, float y) {
        PointF location = getLocation();
        // 把触摸点转换成在item坐标系（即以item起始点作为坐标原点）内的点
        x = x - location.x;
        y = y - location.y;
        // 把变换后矩形中的触摸点，还原回未变换前矩形中的点，然后判断是否矩形中
        PointF xy = rotatePoint(mTemp, (int) -getItemRotate(), x, y, getPivotX() - getLocation().x, getPivotY() - getLocation().y);
        // 计算旋转把柄的位置，由于绘制时反向缩放了画布，所以这里也应算上相应的getDoodle().getDoodleScale()
        float padding = 10 * getDoodle().getUnitSize() / getDoodle().getDoodleScale();
        mRectTemp.set(getBounds());
        mRectTemp.top -= padding;
        mRectTemp.left -= padding;
        mRectTemp.right += padding;
        mRectTemp.bottom += padding;

        return xy.x >= mRectTemp.left
                && xy.x <= mRectTemp.right
                && xy.y >= mRectTemp.top
                && xy.y <= mRectTemp.bottom;
    }

    /**
     * 是否可以旋转
     */
    public boolean canRotate(float x, float y) {
        PointF location = getLocation();
        // 把触摸点转换成在item坐标系（即以item起始点作为坐标原点）内的点
        x = x - location.x;
        y = y - location.y;
        // 把变换后矩形中的触摸点，还原回未变换前矩形中的点，然后判断是否矩形中
        PointF xy = rotatePoint(mTemp, (int) -getItemRotate(), x, y, getPivotX() - getLocation().x, getPivotY() - getLocation().y);
        // 计算旋转把柄的位置，由于绘制时反向缩放了画布，所以这里也应算上相应的getDoodle().getDoodleScale()
        float padding = 10 * getDoodle().getUnitSize() / getDoodle().getDoodleScale();
        mRotateRect.top -= padding;
        mRotateRect.left -= padding;
        mRotateRect.right += padding;
        mRotateRect.bottom += padding;

        return xy.x >= mRotateRect.left
                && xy.x <= mRotateRect.right
                && xy.y >= mRotateRect.top
                && xy.y <= mRotateRect.bottom;
    }

    /**
     * 是否可以删除
     */
    public boolean canDelete(float x, float y) {
        PointF location = getLocation();
        // 把触摸点转换成在item坐标系（即以item起始点作为坐标原点）内的点
        x = x - location.x;
        y = y - location.y;
        // 把变换后矩形中的触摸点，还原回未变换前矩形中的点，然后判断是否矩形中
        PointF xy = rotatePoint(mTemp, (int) -getItemRotate(), x, y, getPivotX() - getLocation().x, getPivotY() - getLocation().y);
        // 计算旋转把柄的位置，由于绘制时反向缩放了画布，所以这里也应算上相应的getDoodle().getDoodleScale()
        float padding = 10 * getDoodle().getUnitSize() / getDoodle().getDoodleScale();
        mDeleteRect.top -= padding;
        mDeleteRect.left -= padding;
        mDeleteRect.right += padding;
        mDeleteRect.bottom += padding;
        return xy.x >= mDeleteRect.left
                && xy.x <= mDeleteRect.right
                && xy.y >= mDeleteRect.top
                && xy.y <= mDeleteRect.bottom;
    }


    public boolean isRotating() {
        return mIsRotating;
    }

    public void setIsRotating(boolean isRotating) {
        mIsRotating = isRotating;
    }
}
